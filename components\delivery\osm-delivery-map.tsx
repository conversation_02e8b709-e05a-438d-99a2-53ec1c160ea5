"use client"

import { useState, useEffect, useRef, memo } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface OSMDeliveryMapProps {
  restaurantLng: number
  restaurantLat: number
  customerLng: number
  customerLat: number
  height?: string
  className?: string
  interactive?: boolean
  showRoute?: boolean
}

function OSMDeliveryMap({
  restaurantLng,
  restaurantLat,
  customerLng,
  customerLat,
  height = "300px",
  className = "",
  interactive = true,
  showRoute = true,
}: OSMDeliveryMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const routingControlRef = useRef<any>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    // Create map instance
    const map = L.map(mapContainer.current, {
      center: [(restaurantLat + customerLat) / 2, (restaurantLng + customerLng) / 2],
      zoom: 13,
      dragging: interactive,
      touchZoom: interactive,
      scrollWheelZoom: interactive,
      doubleClickZoom: interactive,
      boxZoom: interactive,
      keyboard: interactive,
      zoomControl: interactive,
    })

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    mapRef.current = map
    setMapLoaded(true)

    return () => {
      if (routingControlRef.current) {
        routingControlRef.current.remove()
        routingControlRef.current = null
      }
      map.remove()
      mapRef.current = null
    }
  }, [interactive])

  // Add markers and route when map is loaded or when props change
  useEffect(() => {
    if (!mapRef.current || !mapLoaded) return

    const map = mapRef.current

    // Clear previous markers
    map.eachLayer((layer) => {
      if (layer instanceof L.Marker || layer instanceof L.Polyline) {
        map.removeLayer(layer)
      }
    })

    // Remove previous routing control
    if (routingControlRef.current) {
      routingControlRef.current.remove()
      routingControlRef.current = null
    }

    // Create custom icons
    const restaurantIcon = L.divIcon({
      html: `<div style="background-color: #f97316; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                <path d="M12 21v-9"></path>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    const customerIcon = L.divIcon({
      html: `<div style="background-color: #10b981; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Add restaurant marker
    const restaurantMarker = L.marker([restaurantLat, restaurantLng], { icon: restaurantIcon }).addTo(map)

    // Add customer marker
    const customerMarker = L.marker([customerLat, customerLng], { icon: customerIcon }).addTo(map)

    // Add route if requested
    if (showRoute) {
      try {
        // Use Leaflet Routing Machine for routing
        if (typeof L.Routing !== 'undefined') {
          routingControlRef.current = L.Routing.control({
            waypoints: [
              L.latLng(restaurantLat, restaurantLng),
              L.latLng(customerLat, customerLng)
            ],
            routeWhileDragging: false,
            showAlternatives: false,
            fitSelectedRoutes: true,
            lineOptions: {
              styles: [
                { color: '#0077ff', opacity: 0.8, weight: 5 }
              ]
            },
            createMarker: function() { return null; }, // Don't create default markers
            addWaypoints: false,
            draggableWaypoints: false,
            show: false // Don't show the routing instructions panel
          }).addTo(map)
        } else {
          // Fallback to simple line if routing machine is not available
          const polyline = L.polyline(
            [
              [restaurantLat, restaurantLng],
              [customerLat, customerLng]
            ],
            { color: '#0077ff', weight: 5, opacity: 0.8 }
          ).addTo(map)

          // Fit bounds to the polyline
          map.fitBounds(polyline.getBounds(), { padding: [50, 50] })
        }
      } catch (error) {
        console.error("Error creating route:", error)

        // Fallback to simple line if routing fails
        const polyline = L.polyline(
          [
            [restaurantLat, restaurantLng],
            [customerLat, customerLng]
          ],
          { color: '#0077ff', weight: 5, opacity: 0.8 }
        ).addTo(map)

        // Fit bounds to the polyline
        map.fitBounds(polyline.getBounds(), { padding: [50, 50] })
      }
    } else {
      // If not showing route, fit bounds to include both markers
      const bounds = L.latLngBounds(
        [restaurantLat, restaurantLng],
        [customerLat, customerLng]
      )
      map.fitBounds(bounds, { padding: [50, 50] })
    }

  }, [mapLoaded, restaurantLat, restaurantLng, customerLat, customerLng, showRoute])

  return (
    <div
      ref={mapContainer}
      className={`rounded-md overflow-hidden ${className}`}
      style={{ height }}
    />
  )
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(OSMDeliveryMap)
