import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    // Create admin client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get the most recent order with more details
    const { data: lastOrder, error } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        created_at,
        status,
        total,
        delivery_fee,
        customer_name,
        customer_phone,
        delivery_address,
        delivery_method,
        cart_id,
        business_name
      `)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error('Error fetching last order:', error)
      return NextResponse.json({ error: 'Failed to fetch last order' }, { status: 500 })
    }

    // Get cart items for this order if cart_id exists
    let cartItems = []
    if (lastOrder && lastOrder.cart_id) {
      const { data: items, error: itemsError } = await supabase
        .from('cart_items')
        .select(`
          id,
          name,
          quantity,
          price,
          product_id,
          variant_id,
          image_url,
          business_id
        `)
        .eq('cart_id', lastOrder.cart_id)

      if (itemsError) {
        console.error('Error fetching cart items:', itemsError)
      } else {
        cartItems = items || []
      }
    }

    // Calculate items count and total items
    const itemsCount = cartItems.length
    const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 0), 0)

    // Return order with cart items
    return NextResponse.json({
      ...lastOrder,
      cart_items: cartItems,
      items_count: itemsCount,
      total_items: totalItems
    })

  } catch (error) {
    console.error('Error in last-order API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
