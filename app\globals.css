@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import custom scrollbar styles */
@import './styles/scrollbar.css';

body {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Smart image fitting for product cards */
  .product-image-smart {
    object-fit: cover;
    object-position: center;
    transition: object-fit 0.2s ease;
    background-color: transparent;
  }

  /* For images that need breathing room (logos, products with text) */
  .product-image-contain {
    object-fit: contain;
    object-position: center;
    padding: 0.5rem;
  }

  /* For images that should fill completely */
  .product-image-fill {
    object-fit: cover;
    object-position: center;
  }

  /* Responsive image container that maintains aspect ratio */
  .product-image-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0;
  }

  /* Ensure images don't have white backgrounds bleeding through */
  .product-image-container img {
    background-color: transparent;
  }

  /* Handle loading states */
  .product-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    z-index: -1;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 160 84% 39%; /* Changed to emerald-600 */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 160 84% 39%; /* Keep emerald-600 in dark mode too */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-white text-foreground;
  }
  main {
    width: 100%;
    max-width: 100%;
    background-color: white;
  }
}

@layer utilities {
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Flexible container with consistent max-width across all pages */
  .container-fluid {
    width: 100%;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Responsive container widths with consistent max-width */
  @media (min-width: 640px) {
    .container-fluid {
      width: 100%;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .container-fluid {
      width: 100%;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-fluid {
      width: 100%;
      max-width: 1957px; /* Increased to match page width */
    }
  }

  @media (min-width: 1280px) {
    .container-fluid {
      width: 100%;
      max-width: 1957px;
    }
  }

  @media (min-width: 1536px) {
    .container-fluid {
      width: 100%;
      max-width: 1957px;
    }
  }

  @media (min-width: 1920px) {
    .container-fluid {
      width: 100%;
      max-width: 1957px;
    }
  }

  /* Ultra-wide screens */
  @media (min-width: 2560px) {
    .container-fluid {
      width: 100%;
      max-width: 1957px;
    }
  }
}

/* Removed fadeSlideIn animation */

.animate-in {
  animation: scaleIn 0.5s forwards ease-out;
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  70% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes jiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

@keyframes spin-hover {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.wheel-logo:hover svg {
  animation: spin-hover 1.5s cubic-bezier(0.2, 0.8, 0.3, 1);
}

/* Order confirmation wheel celebration spin */
@keyframes wheel-celebration {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(1.05);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  75% {
    transform: rotate(270deg) scale(1.05);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.animate-wheel-celebration {
  animation: wheel-celebration 2s ease-in-out;
}

/* Copy animation keyframes */
@keyframes copy-ripple {
  0% {
    opacity: 0.7;
    transform: scale(0);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes copy-success {
  0% {
    background-color: transparent;
  }
  30% {
    background-color: rgba(16, 185, 129, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

@keyframes check-mark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animation classes */
.animate-copy-ripple {
  animation: copy-ripple 1s ease-out forwards;
}

.animate-copy-success {
  animation: copy-success 1s ease-out forwards;
}

.animate-check-mark {
  animation: check-mark 0.5s ease-out forwards;
}

/* Add a class for respecting reduced motion preferences */
/* New animations for homepage */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-in-delay-1 {
  0%, 30% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-in-delay-2 {
  0%, 60% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-slow {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes pulse-slower {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes slide-in-from-top {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  30% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  30% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-right {
  0% {
    opacity: 0;
    transform: translateX(200px);
    visibility: visible;
  }
  10% {
    opacity: 0.1;
  }
  40% {
    opacity: 0.4;
  }
  70% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

.animate-fade-in-delay-1 {
  animation: fade-in-delay-1 1.5s ease-out forwards;
}

.animate-fade-in-delay-2 {
  animation: fade-in-delay-2 2s ease-out forwards;
}

.animate-slide-in-from-top {
  animation: slide-in-from-top 0.8s ease-out forwards;
  animation-fill-mode: both;
}

.animate-slide-in-from-bottom {
  animation: slide-in-from-bottom 0.8s ease-out forwards;
  animation-fill-mode: both;
}

.animate-slide-in-from-right {
  animation: slide-in-from-right 1.5s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  animation-fill-mode: both;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-pulse-slower {
  animation: pulse-slower 6s ease-in-out infinite;
}

@media (prefers-reduced-motion: reduce) {
  .animate-in,
  .group-hover\:rotate-12,
  .group-hover\:scale-105,
  .group-hover\:scale-110,
  .animate-copy-ripple,
  .animate-copy-success,
  .animate-check-mark,
  .animate-fade-in,
  .animate-fade-in-delay-1,
  .animate-fade-in-delay-2,
  .animate-float,
  .animate-pulse-slow,
  .animate-pulse-slower,
  .animate-slide-in-from-top,
  .animate-slide-in-from-bottom,
  .animate-slide-in-from-right,
  .animate-wheel-celebration {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}
