"use client"

import { useAuth } from "@/context/unified-auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export default function LoginStatus() {
  const { user, userProfile, isLoading, session, signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push("/")
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Authentication Status</CardTitle>
        <CardDescription>Debug information about your current login state</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Loading State</h3>
            <p className={isLoading ? "text-amber-600 font-medium" : "text-green-600 font-medium"}>
              {isLoading ? "Loading..." : "Completed"}
            </p>
            {isLoading && (
              <div>
                <p className="text-xs text-gray-500 mt-1">
                  If loading persists for more than a few seconds, try refreshing the page or clicking "Refresh Session" below.
                </p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="sm"
                  className="mt-2 text-xs h-7 px-2 py-1"
                >
                  Force Refresh Page
                </Button>
              </div>
            )}
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Session</h3>
            <p className={session ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
              {session ? "Active" : "No active session"}
            </p>
            {session && (
              <p className="text-xs text-gray-500 mt-1">
                Expires: {new Date(session.expires_at! * 1000).toLocaleString()}
              </p>
            )}
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">User</h3>
            {user ? (
              <div>
                <p className="text-green-600 font-medium">{user.email}</p>
                <p className="text-xs text-gray-500 mt-1">
                  ID: {user.id}
                </p>
              </div>
            ) : (
              <p className="text-red-600 font-medium">Not logged in</p>
            )}
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">User Profile</h3>
            {userProfile ? (
              <div>
                <p className="text-green-600 font-medium">{userProfile.name}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Role: {userProfile.role}
                </p>
              </div>
            ) : (
              <p className="text-red-600 font-medium">No profile found</p>
            )}
          </div>

          <div className="pt-4 flex space-x-2">
            <Button
              onClick={() => router.push("/login")}
              variant="outline"
              className="flex-1"
            >
              Login Page
            </Button>
            {user && (
              <Button
                onClick={handleSignOut}
                variant="destructive"
                className="flex-1"
              >
                Sign Out
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
