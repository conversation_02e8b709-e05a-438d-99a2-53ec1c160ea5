import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface WeightClassData {
  weight_class: string // 'undeclared', '0kg', '1kg', '2kg', etc.
  weight_class_kg: number | null // null, 0, 1, 2, etc.
  hot_items: number
  cold_items: number
  none_items: number
  undeclared_thermal_items: number
  total_items: number
}

export interface OrderAnalysisData {
  order_id: number
  order_number: string
  business_name: string
  total_items: number
  weight_distribution: WeightClassData[]
  thermal_summary: {
    hot_items: number
    cold_items: number
    none_items: number
    undeclared_thermal_items: number
    requires_thermal_bag: boolean
  }
  size_summary: {
    small_items: number
    medium_items: number
    large_items: number
    very_large_items: number
    undeclared_size_items: number
    largest_size: string
  }
}

function formatWeightClass(weightClassKg: number | null): string {
  if (weightClassKg === null) return 'undeclared'
  if (weightClassKg === 0) return '0kg'
  return `${weightClassKg}kg`
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const orderId = params.orderId

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      )
    }

    // Get order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        cart_id
      `)
      .or(`order_number.eq.${orderId},id.eq.${parseInt(orderId, 10) || 0}`)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (!order.cart_id) {
      return NextResponse.json(
        { error: "Order has no associated cart" },
        { status: 400 }
      )
    }

    // Get cart items with delivery attributes
    const { data: cartItems, error: cartError } = await supabase
      .from('cart_items')
      .select(`
        id,
        name,
        quantity,
        weight_class_kg,
        thermal_requirement,
        size_category
      `)
      .eq('cart_id', order.cart_id)

    if (cartError) {
      console.error('Error fetching cart items:', cartError)
      return NextResponse.json(
        { error: "Failed to fetch order items" },
        { status: 500 }
      )
    }

    if (!cartItems || cartItems.length === 0) {
      return NextResponse.json(
        { error: "No items found for this order" },
        { status: 404 }
      )
    }

    // Calculate total items
    const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0)

    // Group items by weight class and thermal requirement
    const weightGroups = new Map<string, { weight_class_kg: number | null, hot: number, cold: number, none: number, undeclared_thermal: number }>()

    cartItems.forEach(item => {
      const weightKey = item.weight_class_kg === null ? 'undeclared' : `${item.weight_class_kg}kg`

      if (!weightGroups.has(weightKey)) {
        weightGroups.set(weightKey, {
          weight_class_kg: item.weight_class_kg,
          hot: 0,
          cold: 0,
          none: 0,
          undeclared_thermal: 0
        })
      }

      const group = weightGroups.get(weightKey)!
      const quantity = item.quantity

      switch (item.thermal_requirement) {
        case 'hot':
          group.hot += quantity
          break
        case 'cold':
          group.cold += quantity
          break
        case 'none':
          group.none += quantity
          break
        default: // null or any other value = undeclared
          group.undeclared_thermal += quantity
          break
      }
    })

    // Convert to array and sort (undeclared first, then by weight class)
    const weightDistribution: WeightClassData[] = Array.from(weightGroups.entries())
      .map(([weightClass, data]) => ({
        weight_class: weightClass,
        weight_class_kg: data.weight_class_kg,
        hot_items: data.hot,
        cold_items: data.cold,
        none_items: data.none,
        undeclared_thermal_items: data.undeclared_thermal,
        total_items: data.hot + data.cold + data.none + data.undeclared_thermal
      }))
      .sort((a, b) => {
        // Sort: undeclared first, then by weight class ascending
        if (a.weight_class_kg === null && b.weight_class_kg === null) return 0
        if (a.weight_class_kg === null) return -1
        if (b.weight_class_kg === null) return 1
        return a.weight_class_kg - b.weight_class_kg
      })

    // Calculate thermal summary
    const thermalSummary = {
      hot_items: cartItems.reduce((sum, item) =>
        item.thermal_requirement === 'hot' ? sum + item.quantity : sum, 0),
      cold_items: cartItems.reduce((sum, item) =>
        item.thermal_requirement === 'cold' ? sum + item.quantity : sum, 0),
      none_items: cartItems.reduce((sum, item) =>
        item.thermal_requirement === 'none' ? sum + item.quantity : sum, 0),
      undeclared_thermal_items: cartItems.reduce((sum, item) =>
        item.thermal_requirement === null ? sum + item.quantity : sum, 0),
      requires_thermal_bag: cartItems.some(item =>
        item.thermal_requirement === 'hot' || item.thermal_requirement === 'cold')
    }

    // Calculate size summary
    const sizeSummary = {
      small_items: cartItems.reduce((sum, item) =>
        item.size_category === 'small' ? sum + item.quantity : sum, 0),
      medium_items: cartItems.reduce((sum, item) =>
        item.size_category === 'medium' ? sum + item.quantity : sum, 0),
      large_items: cartItems.reduce((sum, item) =>
        item.size_category === 'large' ? sum + item.quantity : sum, 0),
      very_large_items: cartItems.reduce((sum, item) =>
        item.size_category === 'very_large' ? sum + item.quantity : sum, 0),
      undeclared_size_items: cartItems.reduce((sum, item) =>
        item.size_category === null ? sum + item.quantity : sum, 0),
      largest_size: cartItems.reduce((largest, item) => {
        const sizes = ['small', 'medium', 'large', 'very_large']
        const currentIndex = sizes.indexOf(item.size_category || '')
        const largestIndex = sizes.indexOf(largest)
        return currentIndex > largestIndex ? item.size_category || 'undeclared' : largest
      }, 'undeclared')
    }

    const analysisData: OrderAnalysisData = {
      order_id: order.id,
      order_number: order.order_number,
      business_name: order.business_name,
      total_items: totalItems,
      weight_distribution: weightDistribution,
      thermal_summary: thermalSummary,
      size_summary: sizeSummary
    }

    return NextResponse.json({
      success: true,
      data: analysisData
    })

  } catch (error) {
    console.error('Error in order analysis API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
