import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in orders API")
    // Allow guest orders without authentication in both development and production
    console.log("Allowing guest order without authentication")
    return {
      authorized: true,
      // Return null user to indicate a guest order
      user: null
    };
  }

  console.log("Found authorization header in orders API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in orders API:", error)

      // Allow guest orders without authentication in both development and production
      console.log("Allowing guest order despite invalid token")
      return {
        authorized: true,
        // Return null user to indicate a guest order
        user: null
      };
    }

    console.log("Token verified for user in orders API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in orders API:", authError)
    // Allow guest orders without authentication in both development and production
    console.log("Allowing guest order despite auth error")
    return {
      authorized: true,
      // Return null user to indicate a guest order
      user: null
    };
  }
}

// GET endpoint to fetch a user's orders
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');

    // Fetch the user's orders
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items(*),
        order_status_history(*),
        order_payment_allocations(*)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status) {
      query = query.eq('order_status', status);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      orders: data || [],
      count: count || 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// Import the transaction helpers
import { createOrderWithTransaction } from './transaction-helper';
// REMOVED: multi-business-transaction-helper.ts - obsolete with new cart architecture

// POST endpoint to create a new order
export async function POST(request: NextRequest) {
  console.log('🔵 POST /api/orders - Order creation endpoint called');

  try {
    // Verify user access
    console.log('🔐 API: Verifying user access');
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      console.error('❌ API: User access verification failed:', accessCheck.error);
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;
    console.log('✅ API: User access verified:', user && user.email ? user.email : 'Guest user (no email)');

    // Parse the request body
    console.log('📦 API: Parsing request body');
    const body = await request.json();

    // Validate required fields - make customerAddress conditional based on delivery method
    // Phone is now optional as we send notifications via the app
    const baseRequiredFields = [
      'customerName',
      'paymentMethod', 'items', 'businesses', 'subtotal',
      'deliveryFee', 'serviceFee', 'total'
    ];

    // Check if any business requires delivery
    const hasDelivery = body.businesses && body.businesses.some(business =>
      business.delivery_method === 'delivery' || business.deliveryMethod === 'delivery'
    );

    // Only require address if delivery is needed
    const requiredFields = hasDelivery
      ? [...baseRequiredFields, 'customerAddress']
      : baseRequiredFields;

    console.log(`Order validation: hasDelivery=${hasDelivery}, requiring address=${hasDelivery}`);

    // Log the received body for debugging
    console.log('Order API received body keys:', Object.keys(body));
    console.log('Order API received body:', {
      customerName: body.customerName,
      customerPhone: body.customerPhone,
      customerAddress: body.customerAddress,
      paymentMethod: body.paymentMethod,
      itemCount: body.items?.length || 0,
      businessCount: body.businesses?.length || 0,
      subtotal: body.subtotal,
      deliveryFee: body.deliveryFee,
      serviceFee: body.serviceFee,
      total: body.total,
      deliveryType: body.deliveryType
    });

    // Check for missing fields
    const missingFields = requiredFields.filter(field => {
      const value = body[field];
      const isMissing = value === undefined || value === null ||
                       (typeof value === 'string' && value.trim() === '') ||
                       (Array.isArray(value) && value.length === 0);

      if (isMissing) {
        console.log(`Order API: Missing required field: ${field}`);
      }

      return isMissing;
    });

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: `Missing required fields: ${missingFields.join(', ')}`,
          receivedFields: Object.keys(body)
        },
        { status: 400 }
      );
    }

    // Validate delivery instructions length for security
    if (body.instructions && body.instructions.length > 5000) {
      return NextResponse.json(
        { error: 'Delivery instructions must be 5,000 characters or less' },
        { status: 400 }
      );
    }

    // Validate array fields
    if (!Array.isArray(body.items)) {
      return NextResponse.json(
        { error: 'items must be an array' },
        { status: 400 }
      );
    }

    if (!Array.isArray(body.businesses)) {
      return NextResponse.json(
        { error: 'businesses must be an array' },
        { status: 400 }
      );
    }

    // Prepare customer coordinates
    let customerCoordinates = null;
    if (body.customerCoordinates &&
        Array.isArray(body.customerCoordinates) &&
        body.customerCoordinates.length === 2) {
      customerCoordinates = `(${body.customerCoordinates[0]},${body.customerCoordinates[1]})`;
    }

    // Log the order parameters for debugging
    console.log('Order API parameters:', {
      customerName: body.customerName,
      customerPhone: body.customerPhone,
      customerEmail: body.customerEmail || (user?.email || null),
      customerAddress: body.customerAddress,
      paymentMethod: body.paymentMethod,
      // Don't log everything to keep the logs clean
    });

    // Get the primary business ID from the first business in the array
    // First try to use businessNumericId, then fall back to id if it's a number
    let businessId = null;
    if (body.businesses && body.businesses.length > 0) {
      const firstBusiness = body.businesses[0];

      // First try to use businessNumericId if available
      if (firstBusiness.businessNumericId && !isNaN(parseInt(firstBusiness.businessNumericId.toString()))) {
        businessId = parseInt(firstBusiness.businessNumericId.toString());
        console.log(`Using businessNumericId: ${businessId} for primary business`);
      }
      // Then try to use id if it's a number
      else if (firstBusiness.id && !isNaN(parseInt(firstBusiness.id))) {
        businessId = parseInt(firstBusiness.id);
        console.log(`Using numeric id: ${businessId} for primary business`);
      } else {
        console.log(`Neither businessNumericId nor id is a valid integer for business "${firstBusiness.name || firstBusiness.id}", setting to null`);
      }
    }

    // Prepare order items for the transaction helper with simplified business ID handling
    const orderItems = body.items.map(item => {
      // Get product ID - simplified approach
      let productId = null;

      // First try to parse the item.id as an integer
      if (item.id && !isNaN(parseInt(item.id))) {
        productId = parseInt(item.id);
        console.log(`Using numeric product ID ${productId} from item.id for ${item.name || 'unknown item'}`);
      }
      // Then try product_id if available
      else if (item.product_id && !isNaN(parseInt(item.product_id.toString()))) {
        productId = parseInt(item.product_id.toString());
        console.log(`Using numeric product ID ${productId} from item.product_id for ${item.name || 'unknown item'}`);
      }
      // If no valid ID found, log an error
      else {
        console.error(`ERROR: No valid product ID found for item ${item.name || 'unknown'}`);
        // Use a placeholder ID for now, but this should be fixed in the product data
        productId = 0;
      }

      // Get business ID - simplified approach using numeric IDs directly
      let businessId = null;

      // First check if businessId is already a number
      if (item.businessId && !isNaN(Number(item.businessId))) {
        businessId = Number(item.businessId);
        console.log(`Using numeric businessId ${businessId} for item ${item.name || 'unknown'}`);
      }
      // Then try business_id if available
      else if (item.business_id && !isNaN(Number(item.business_id))) {
        businessId = Number(item.business_id);
        console.log(`Using business_id ${businessId} for item ${item.name || 'unknown'}`);
      }
      // Then try businessNumericId if available
      else if (item.businessNumericId && !isNaN(Number(item.businessNumericId))) {
        businessId = Number(item.businessNumericId);
        console.log(`Using businessNumericId ${businessId} for item ${item.name || 'unknown'}`);
      }
      // If we still don't have a valid business ID, log an error
      else {
        console.error(`ERROR: No valid business ID found for item ${item.name || 'unknown'}`);
        console.error(`Item data: ${JSON.stringify(item)}`);
        // Use a placeholder ID for now, but this should be fixed in the product data
        businessId = 0;
      }

      // Get product name
      const productName = item.name || 'Unknown Product';

      return {
        product_id: productId,
        product_name: productName,
        business_id: businessId,
        quantity: item.quantity,
        price: item.price,
        notes: item.notes || '',
        options: item.options || null
      };
    });

    // Filter out items with invalid business IDs
    const validOrderItems = orderItems.filter(item => item.business_id !== 0);

    if (validOrderItems.length !== orderItems.length) {
      console.warn(`Filtered out ${orderItems.length - validOrderItems.length} items with invalid business IDs`);
    }

    console.log(`Created ${validOrderItems.length} order items with valid business IDs`);

    // Prepare order businesses for the transaction helper
    // First, look up the actual business IDs from the businesses table using the slugs
    const businessSlugs = body.businesses.map(business => business.id);
    let businessIdMap = {};

    if (businessSlugs.length > 0) {
      try {
        console.log('Looking up business IDs for slugs:', businessSlugs);

        // Try to look up by slug first
        const { data: businessDataBySlug, error: slugLookupError } = await supabase
          .from('businesses')
          .select('id, slug, name')
          .in('slug', businessSlugs);

        if (slugLookupError) {
          console.error('Error looking up business IDs by slug:', slugLookupError);
        } else if (businessDataBySlug && businessDataBySlug.length > 0) {
          // Create a mapping from slug to actual ID
          businessIdMap = businessDataBySlug.reduce((map, business) => {
            map[business.slug] = business.id;
            console.log(`Mapped slug ${business.slug} to ID ${business.id} (${business.name})`);
            return map;
          }, {});
        } else {
          console.log('No businesses found by slug, trying to look up by name...');

          // If no businesses found by slug, try looking up by name
          const businessNames = body.businesses.map(business => business.name).filter(Boolean);

          if (businessNames.length > 0) {
            const { data: businessDataByName, error: nameLookupError } = await supabase
              .from('businesses')
              .select('id, slug, name')
              .in('name', businessNames);

            if (nameLookupError) {
              console.error('Error looking up business IDs by name:', nameLookupError);
            } else if (businessDataByName && businessDataByName.length > 0) {
              // Create a mapping from name to actual ID
              const nameToIdMap = businessDataByName.reduce((map, business) => {
                map[business.name.toLowerCase()] = business.id;
                console.log(`Mapped name ${business.name} to ID ${business.id}`);
                return map;
              }, {});

              // Add name mappings to the businessIdMap
              body.businesses.forEach(business => {
                if (business.name && nameToIdMap[business.name.toLowerCase()]) {
                  businessIdMap[business.id] = nameToIdMap[business.name.toLowerCase()];
                  console.log(`Mapped slug ${business.id} to ID ${nameToIdMap[business.name.toLowerCase()]} via name ${business.name}`);
                }
              });
            } else {
              console.log('No businesses found by name either');
            }
          }
        }

        console.log('Final business ID mapping:', businessIdMap);
      } catch (err) {
        console.error('Exception looking up business IDs:', err);
      }
    }

    // Now create the order businesses with simplified business ID handling
    // Use Promise.all with async map function to handle async operations inside the map
    const orderBusinesses = await Promise.all(body.businesses.map(async (business, index) => {
      // Get business ID - simplified approach using numeric IDs directly
      let numericBusinessId = null;

      // First, check if we have a valid numeric business ID
      if (business.business_id && !isNaN(Number(business.business_id))) {
        // Use the numeric business ID directly
        numericBusinessId = Number(business.business_id);
        console.log(`Using numeric business_id: ${numericBusinessId} for business ${business.business_name || business.name || 'unknown'}`);
      }
      // Then check if businessId is a number
      else if (business.businessId && !isNaN(Number(business.businessId))) {
        // Use the numeric business ID directly
        numericBusinessId = Number(business.businessId);
        console.log(`Using numeric businessId: ${numericBusinessId} for business ${business.business_name || business.name || 'unknown'}`);
      }
      // Then check if business.id is a number
      else if (business.id && !isNaN(Number(business.id))) {
        numericBusinessId = Number(business.id);
        console.log(`Using numeric ID from business.id: ${numericBusinessId}`);
      }
      // If we still don't have a valid ID, log an error
      else {
        console.error(`ERROR: No valid numeric business ID found for business: ${business.business_name || business.name || 'unknown'}`);
        console.error(`Business data: ${JSON.stringify(business)}`);
        console.error(`This order may need manual intervention to associate with the correct business.`);
        return null; // Skip this business
      }

      // Fetch additional business details if needed
      let businessName = business.business_name || business.name;
      let businessType = business.business_type || business.type;
      let businessSlug = business.business_slug || business.businessSlug || '';

      // If we're missing any essential details, fetch them from the database
      if (!businessName || !businessType || !businessSlug) {
        try {
          console.log(`Fetching additional details for business ID: ${numericBusinessId}`);

          const { data: businessData, error: businessError } = await supabase
            .from('businesses')
            .select('id, name, type, slug')
            .eq('id', numericBusinessId)
            .single();

          if (!businessError && businessData) {
            // Use the database values for any missing fields
            businessName = businessName || businessData.name;
            businessType = businessType || businessData.type;
            businessSlug = businessSlug || businessData.slug;

            console.log(`Found business details: ${businessData.name} (ID: ${businessData.id}, Type: ${businessData.type})`);
          } else {
            console.error(`Error fetching business details for ID ${numericBusinessId}:`, businessError);
          }
        } catch (err) {
          console.error(`Exception fetching business details for ID ${numericBusinessId}:`, err);
        }
      }

      // Create a record with all the business details
      // Create business record with only the fields that are provided
      const orderBusiness: any = {
        business_id: numericBusinessId,
        business_name: businessName || `Business ${numericBusinessId}`,
        business_type: businessType || 'restaurant',
        status: business.status || 'pending'
      };

      // CRITICAL FIX: Preserve cart_id from the original business object
      if (business.cart_id !== undefined) {
        orderBusiness.cart_id = business.cart_id;
        console.log(`🆔 ORDERS: Preserving cart_id ${business.cart_id} for business ${businessName}`);
      }

      // Add business slug if available (for reference only)
      if (businessSlug) orderBusiness.business_slug = businessSlug;

      // Financial fields - CRITICAL FIX: Check both camelCase and snake_case field names
      if (business.subtotal !== undefined) orderBusiness.subtotal = business.subtotal;

      // Delivery fee - check multiple possible field names
      if (business.delivery_fee !== undefined) {
        orderBusiness.delivery_fee = business.delivery_fee;
      } else if (business.deliveryFee !== undefined) {
        orderBusiness.delivery_fee = business.deliveryFee;
      }

      // Service fee - check multiple possible field names
      if (business.service_fee !== undefined) {
        orderBusiness.service_fee = business.service_fee;
      } else if (business.serviceFee !== undefined) {
        orderBusiness.service_fee = business.serviceFee;
      }

      if (business.total !== undefined) orderBusiness.total = business.total;

      // Time fields - CRITICAL FIX: Check both camelCase and snake_case field names
      if (business.preparation_time !== undefined) {
        orderBusiness.preparation_time = business.preparation_time;
      } else if (business.preparationTime !== undefined) {
        orderBusiness.preparation_time = business.preparationTime;
      }

      if (business.estimated_delivery_time !== undefined) {
        orderBusiness.estimated_delivery_time = business.estimated_delivery_time;
      } else if (business.estimatedDeliveryTime !== undefined) {
        orderBusiness.estimated_delivery_time = business.estimatedDeliveryTime;
      }

      // Delivery method - CRITICAL FIX: Check both camelCase and snake_case field names
      if (business.delivery_method !== undefined) {
        orderBusiness.delivery_method = business.delivery_method;
      } else if (business.deliveryMethod !== undefined) {
        orderBusiness.delivery_method = business.deliveryMethod;
      }

      // Delivery type - check multiple possible field names
      if (business.delivery_type !== undefined) {
        orderBusiness.delivery_type = business.delivery_type;
      } else if (business.deliveryType !== undefined) {
        orderBusiness.delivery_type = business.deliveryType;
      }

      // Scheduled time - check multiple possible field names
      if (business.scheduled_time !== undefined) {
        orderBusiness.scheduled_time = business.scheduled_time;
      } else if (business.scheduledTime !== undefined) {
        orderBusiness.scheduled_time = business.scheduledTime;
      }

      // Debug logging for delivery time
      console.log('DEBUG: Business delivery time data:', {
        business_id: business.business_id || business.businessId,
        snake_case: business.estimated_delivery_time,
        camelCase: business.estimatedDeliveryTime,
        debug_source: business._debug_delivery_time_source
      });

      // Log the business data for debugging - CRITICAL: Check both field name formats
      console.log(`Business data for ID ${numericBusinessId}:`, {
        name: businessName,
        type: businessType,
        slug: businessSlug,
        subtotal: business.subtotal,
        deliveryFee: business.deliveryFee,
        delivery_fee: business.delivery_fee,
        serviceFee: business.serviceFee,
        service_fee: business.service_fee,
        total: business.total,
        preparationTime: business.preparationTime,
        preparation_time: business.preparation_time,
        estimatedDeliveryTime: business.estimatedDeliveryTime,
        estimated_delivery_time: business.estimated_delivery_time,
        deliveryMethod: business.deliveryMethod,
        delivery_method: business.delivery_method,
        deliveryType: business.deliveryType,
        delivery_type: business.delivery_type,
        scheduledTime: business.scheduledTime,
        scheduled_time: business.scheduled_time
      });

      // Log the business record for debugging
      console.log(`Prepared order_business record: ID=${numericBusinessId}, Name=${businessName}, Type=${businessType}, Subtotal=${business.subtotal}, DeliveryFee=${orderBusiness.delivery_fee}, ServiceFee=${orderBusiness.service_fee}, PrepTime=${orderBusiness.preparation_time}, DeliveryMethod=${orderBusiness.delivery_method}`);

      return orderBusiness;
    }));

    // Filter out null values (businesses that couldn't be processed)
    const validOrderBusinesses = orderBusinesses.filter(business => business !== null);

    console.log(`Created ${validOrderBusinesses.length} order business records:`,
      validOrderBusinesses.map(b => ({
        business_id: b.business_id,
        business_name: b.business_name,
        business_type: b.business_type
      }))
    );

    // Use transaction helper to create order and related records
    try {
      console.log('Creating order using transaction helper...');

      // Prepare the order data - include all columns that exist in the orders table
      // Get the first business for the main order data
      const firstBusiness = validOrderBusinesses.length > 0 ? validOrderBusinesses[0] : null;

      // Extract parish and postcode from customer address if not provided separately
      let parish = body.parish || null;
      let postcode = body.postcode || null;

      console.log('🔍 ORDERS: Parish and postcode from request:', { parish, postcode });

      if (body.customerAddress && (!parish || !postcode)) {
        const addressParts = body.customerAddress.split(',').map(part => part.trim());
        console.log('🔍 ORDERS: Address parts for extraction:', addressParts);

        // Try to extract parish and postcode from address
        // Jersey address format is typically: "Street, Parish, Jersey" or "Street, Parish, Postcode, Jersey"
        if (addressParts.length >= 2) {
          if (!parish && addressParts[1] && addressParts[1] !== 'Jersey') {
            parish = addressParts[1];
            console.log('🔍 ORDERS: Extracted parish from address:', parish);
          }
          // Look for Jersey postcode pattern (JE followed by digits)
          if (!postcode) {
            const postcodePattern = /JE\d+\s*\d*[A-Z]*/i;
            for (const part of addressParts) {
              if (postcodePattern.test(part)) {
                postcode = part.toUpperCase();
                console.log('🔍 ORDERS: Extracted postcode from address:', postcode);
                break;
              }
            }
          }
        }
      }

      console.log('🔍 ORDERS: Final parish and postcode:', { parish, postcode });

      // Prepare the order data with all fields
      // Note: order_number will be generated automatically by the database trigger
      const orderInsertData = {
        customer_name: body.customerName,
        // Only use user.email if user exists, otherwise use email from form or null
        customer_email: body.customerEmail || null,
        customer_phone: body.customerPhone,
        // Only set delivery_address if we have delivery orders, otherwise use null for pickup
        delivery_address: hasDelivery ? (body.customerAddress || '') : null,
        payment_method: body.paymentMethod,
        payment_status: body.paymentStatus || 'pending',
        notes: body.instructions || '',
        total: body.total || 0,
        customer_coordinates: body.customerCoordinates ? `(${body.customerCoordinates[0]},${body.customerCoordinates[1]})` : null,
        delivery_instructions: body.instructions || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Add missing fields that should be populated
        cart_id: body.cartId || null,
        session_id: body.sessionId || null,
        delivery_method: body.deliveryMethod || null,
        scheduled_time: body.deliveryTime || body.scheduledTime || null,
        delivery_type: body.deliveryType || '',
        parish: parish,
        postcode: postcode,
      };

      // Add business details from the first business
      if (firstBusiness) {
        console.log('Adding business details from first business:', {
          business_id: firstBusiness.business_id,
          business_name: firstBusiness.business_name,
          business_type: firstBusiness.business_type,
          business_slug: firstBusiness.business_slug,
          subtotal: firstBusiness.subtotal,
          delivery_fee: firstBusiness.delivery_fee,
          service_fee: firstBusiness.service_fee,
          preparation_time: firstBusiness.preparation_time,
          estimated_delivery_time: firstBusiness.estimated_delivery_time
        });

        // Make sure business_id is a number
        const businessId = Number(firstBusiness.business_id);
        if (!isNaN(businessId) && businessId > 0) {
          orderInsertData.business_id = businessId;
          console.log(`Using numeric business_id: ${businessId}`);
        } else {
          console.error(`Invalid business_id: ${firstBusiness.business_id} is not a valid positive number`);
        }

        // Add business details
        orderInsertData.business_name = firstBusiness.business_name;
        orderInsertData.business_type = firstBusiness.business_type;

        if (firstBusiness.business_slug) {
          orderInsertData.business_slug = firstBusiness.business_slug;
        }

        // Add financial details - use values from the business record if available, otherwise use body values
        orderInsertData.subtotal = firstBusiness.subtotal || body.subtotal || 0;
        orderInsertData.delivery_fee = firstBusiness.delivery_fee || body.deliveryFee || 0;
        orderInsertData.service_fee = firstBusiness.service_fee || body.serviceFee || 0;

        // Add status and time details
        orderInsertData.status = firstBusiness.status || 'pending';
        orderInsertData.preparation_time = firstBusiness.preparation_time || 15;

        // Debug logging for delivery time
        console.log('DEBUG: First business delivery time data:', {
          business_id: firstBusiness.business_id,
          snake_case: firstBusiness.estimated_delivery_time,
          camelCase: firstBusiness.estimatedDeliveryTime,
          debug_source: firstBusiness._debug_delivery_time_source
        });

        // Check for delivery time in multiple places
        if (firstBusiness.estimated_delivery_time !== undefined) {
          orderInsertData.estimated_delivery_time = firstBusiness.estimated_delivery_time;
          console.log(`Using estimated_delivery_time from first business: ${firstBusiness.estimated_delivery_time}`);
        } else if (firstBusiness.estimatedDeliveryTime !== undefined) {
          orderInsertData.estimated_delivery_time = firstBusiness.estimatedDeliveryTime;
          console.log(`Using estimatedDeliveryTime from first business: ${firstBusiness.estimatedDeliveryTime}`);
        } else {
          orderInsertData.estimated_delivery_time = 20; // Default value
          console.log(`Using default estimated_delivery_time: 20`);
        }

        console.log('Final business details for order:', {
          business_id: orderInsertData.business_id,
          business_name: orderInsertData.business_name,
          business_type: orderInsertData.business_type,
          business_slug: orderInsertData.business_slug,
          subtotal: orderInsertData.subtotal,
          delivery_fee: orderInsertData.delivery_fee,
          service_fee: orderInsertData.service_fee,
          status: orderInsertData.status,
          preparation_time: orderInsertData.preparation_time,
          estimated_delivery_time: orderInsertData.estimated_delivery_time
        });
      } else {
        console.log('No business details available for order');
      }

      // If we have a valid user with an email, use it
      if (user && user.email) {
        orderInsertData.customer_email = user.email;
        console.log(`Using authenticated user email: ${user.email}`);
      } else if (body.customerEmail) {
        orderInsertData.customer_email = body.customerEmail;
        console.log(`Using email from form: ${body.customerEmail}`);
      } else {
        console.log('No email provided, using null');
      }

      // Log the minimal order data
      console.log('Using minimal order data with only valid columns:', orderInsertData);

      // Only include user_id if we have a valid authenticated user
      if (user && user.id) {
        try {
          // Ensure it's a valid UUID
          if (typeof user.id === 'string' && user.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            orderInsertData['user_id'] = user.id;
            console.log(`Using authenticated user ID: ${user.id}`);
          } else {
            console.log(`Invalid user ID format: ${user.id}, not including in order data`);
          }
        } catch (error) {
          console.error('Error processing user ID:', error);
          console.log('Creating order without user_id due to error');
        }
      } else {
        console.log('No authenticated user, creating a guest order without user_id');
      }

      // Include business_id in the orders table for single-business orders
      // For multi-business orders, we'll create separate order rows for each business
      // Make sure we have the business details from the first business in the order

      console.log('Creating order with transaction helper using data:', {
        orderData: orderInsertData,
        itemCount: orderItems.length,
        businessCount: orderBusinesses.length
      });

      // NEW LOGIC: Create one order per business using the businesses array from cart context
      // This bypasses the need to query user_carts table and uses the rich data from the checkout process
      console.log(`🏪 Creating orders for ${validOrderBusinesses.length} businesses using cart context data`);

      // Validate that we have businesses to process
      if (!validOrderBusinesses || validOrderBusinesses.length === 0) {
        console.log('⚠️ No businesses found in request, creating single order (legacy mode)');

        const orderResult = await createOrderWithTransaction(
          orderInsertData,
          validOrderItems
        );

        if (!orderResult || !orderResult.orderId) {
          throw new Error('Transaction helper did not return a valid order ID');
        }

        console.log('Order created successfully with ID:', orderResult.orderId, 'order number:', orderResult.orderNumber);

        // Return single order response
        const { data: fetchedOrderData, error: orderError } = await supabase
          .from('orders')
          .select(`*, order_items(*)`)
          .eq('id', orderResult.orderId)
          .single();

        return NextResponse.json({
          success: true,
          orderId: orderResult.orderId,
          orderNumber: orderResult.orderNumber,
          order: fetchedOrderData
        });
      }

      // PHASE 1 STEP 2: Validate session_id consistency across all businesses
      const sessionIds = validOrderBusinesses.map(business => body.sessionId);
      const uniqueSessionIds = [...new Set(sessionIds.filter(id => id))];
      if (uniqueSessionIds.length > 1) {
        console.error('❌ Session ID validation failed: Multiple session IDs found', uniqueSessionIds);
        return NextResponse.json(
          { error: 'All businesses in multi-business order must have the same session_id' },
          { status: 400 }
        );
      }
      console.log('✅ Session ID validation passed:', uniqueSessionIds[0] || 'No session ID');

      // CRITICAL: Validate cart items exist and are valid before creating orders
      console.log('🔍 VALIDATION: Starting cart items validation...');
      for (const business of validOrderBusinesses) {
        const cartId = business.cart_id;
        if (!cartId) {
          console.error(`❌ VALIDATION: No cart_id found for business ${business.business_name}`);
          return NextResponse.json(
            { error: `No cart found for ${business.business_name}. Please add items to cart and try again.` },
            { status: 400 }
          );
        }

        // Check if cart items exist for this cart_id
        const { data: cartItems, error: cartItemsError } = await supabase
          .from('cart_items')
          .select('id, created_at, cart_id, user_carts!inner(session_id)')
          .eq('cart_id', cartId);

        if (cartItemsError) {
          console.error(`❌ VALIDATION: Error fetching cart items for ${business.business_name}:`, cartItemsError);
          return NextResponse.json(
            { error: `Failed to validate cart for ${business.business_name}. Please try again.` },
            { status: 500 }
          );
        }

        if (!cartItems || cartItems.length === 0) {
          console.error(`❌ VALIDATION: No cart items found for business ${business.business_name} (cart_id: ${cartId})`);
          return NextResponse.json(
            { error: `Your cart for ${business.business_name} is empty. Please add items to cart and try again.` },
            { status: 400 }
          );
        }

        // Check session_id matches (if we have a session_id)
        const currentSessionId = body.sessionId;
        if (currentSessionId && cartItems[0]?.user_carts?.session_id !== currentSessionId) {
          console.warn(`⚠️ VALIDATION: Session ID mismatch for ${business.business_name}:`, {
            current: currentSessionId,
            cart: cartItems[0]?.user_carts?.session_id
          });

          // Instead of failing immediately, attempt to validate and refresh the cart
          console.log(`🔄 VALIDATION: Attempting cart recovery for ${business.business_name}...`);

          // Validate that cart items are still valid by checking against current products
          const cartItemIds = cartItems.map(item => item.id);
          const { data: validatedItems, error: validationError } = await supabase
            .from('cart_items')
            .select(`
              id,
              product_id,
              quantity,
              price,
              products!inner (
                id,
                name,
                price,
                is_available,
                business_id
              )
            `)
            .in('id', cartItemIds);

          if (validationError) {
            console.error(`❌ VALIDATION: Failed to validate cart items for ${business.business_name}:`, validationError);
            return NextResponse.json(
              { error: `Unable to validate your cart for ${business.business_name}. Please refresh the page and try again.` },
              { status: 400 }
            );
          }

          // Check if any items are no longer available or have price changes
          const unavailableItems = validatedItems?.filter(item => !item.products?.is_available) || [];
          const priceChangedItems = validatedItems?.filter(item =>
            item.products?.price !== undefined &&
            parseFloat(item.price) !== parseFloat(item.products.price)
          ) || [];

          if (unavailableItems.length > 0 || priceChangedItems.length > 0) {
            const issues = [];
            if (unavailableItems.length > 0) {
              issues.push(`${unavailableItems.length} item(s) are no longer available`);
            }
            if (priceChangedItems.length > 0) {
              issues.push(`${priceChangedItems.length} item(s) have price changes`);
            }

            return NextResponse.json(
              {
                error: `Your cart for ${business.business_name} needs attention: ${issues.join(' and ')}. Please review your cart and update it before placing your order.`,
                details: {
                  unavailableItems: unavailableItems.map(item => item.products?.name),
                  priceChangedItems: priceChangedItems.map(item => ({
                    name: item.products?.name,
                    oldPrice: item.price,
                    newPrice: item.products?.price
                  }))
                }
              },
              { status: 400 }
            );
          }

          // If all items are valid, update the cart's session_id to match current session
          console.log(`✅ VALIDATION: Cart items are valid, updating session ID for ${business.business_name}`);
          const { error: updateError } = await supabase
            .from('user_carts')
            .update({ session_id: currentSessionId })
            .eq('id', cartId);

          if (updateError) {
            console.error(`❌ VALIDATION: Failed to update session ID for ${business.business_name}:`, updateError);
            return NextResponse.json(
              { error: `Unable to update your cart session for ${business.business_name}. Please refresh the page and try again.` },
              { status: 400 }
            );
          }

          console.log(`✅ VALIDATION: Successfully recovered cart for ${business.business_name} with updated session ID`);
        }

        // Note: We don't validate cart item age - customers should be able to checkout
        // with old items as long as the products still exist and prices are current

        console.log(`✅ VALIDATION: Cart validation passed for ${business.business_name} - ${cartItems.length} items found`);
      }

      // Create one order per business using the businesses array from cart context
      const createdOrders = [];

      for (const business of validOrderBusinesses) {
        console.log(`🏪 Creating order for business ${business.business_name} (ID: ${business.business_id})`);

        // Get items for this specific business from the validOrderItems array
        const businessOrderItems = validOrderItems.filter(item =>
          item.business_id === business.business_id
        );

        if (!businessOrderItems || businessOrderItems.length === 0) {
          console.log(`⚠️ No items found for business ${business.business_name}, skipping`);
          continue;
        }

        // Get cart_id from business data (passed from checkout context)
        const cartId = business.cart_id || null;
        console.log(`🆔 ORDERS: Using cart_id for business ${business.business_name}: ${cartId}`);
        console.log(`🆔 ORDERS: Cart ID type: ${typeof cartId}`);
        console.log(`🆔 ORDERS: Cart ID is valid UUID: ${cartId ? /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(cartId) : false}`);
        console.log(`🆔 ORDERS: Main order cartId from body: ${body.cartId}`);
        console.log(`🆔 ORDERS: Full business object:`, JSON.stringify(business, null, 2));

        // PHASE 1 STEP 2: Get business delivery fulfillment preference
        let deliveryFulfillment = 'loop'; // default
        try {
          const { data: businessData, error: businessError } = await supabase
            .from('businesses')
            .select('use_loop_delivery')
            .eq('id', business.business_id)
            .single();

          if (businessError) {
            console.warn(`⚠️ Could not fetch business delivery preference for ${business.business_name}:`, businessError);
          } else {
            deliveryFulfillment = businessData.use_loop_delivery ? 'loop' : 'business';
            console.log(`🚚 Business ${business.business_name} delivery fulfillment: ${deliveryFulfillment}`);
          }
        } catch (error) {
          console.warn(`⚠️ Error fetching business delivery preference for ${business.business_name}:`, error);
        }

        // Create order data specific to this business using data from the businesses array
        console.log(`🚚 ORDERS: Business ${business.business_name} delivery method data:`, {
          delivery_method: business.delivery_method,
          deliveryMethod: business.deliveryMethod,
          fallback_used: !business.delivery_method ? 'YES - defaulting to pickup' : 'NO',
          delivery_fulfillment: deliveryFulfillment
        });

        const businessOrderData = {
          ...orderInsertData,
          cart_id: cartId,
          session_id: body.sessionId,
          business_id: business.business_id,
          business_name: business.business_name,
          business_type: business.business_type,
          delivery_method: business.delivery_method || business.deliveryMethod || 'pickup',
          delivery_type: business.delivery_type || business.deliveryType || body.deliveryType || '',
          scheduled_time: business.scheduled_time || business.scheduledTime || body.deliveryTime || body.scheduledTime || null,
          delivery_fee: business.delivery_fee || 0,
          subtotal: business.subtotal || 0,
          service_fee: business.service_fee || 0,
          preparation_time: business.preparation_time || null,
          estimated_delivery_time: business.estimated_delivery_time || null,
          delivery_distance_km: business.delivery_distance_km || business.deliveryDistanceKm || null, // Store the calculated distance
          business_slug: business.business_slug || null,
          delivery_fulfillment: deliveryFulfillment, // PHASE 1 STEP 2: Add delivery fulfillment method
        };

        // Calculate total for this business
        businessOrderData.total = businessOrderData.subtotal + businessOrderData.delivery_fee + businessOrderData.service_fee;

        console.log(`📝 Creating order for business ${business.business_name} with ${businessOrderItems.length} items, subtotal: £${businessOrderData.subtotal}, delivery: £${businessOrderData.delivery_fee}, total: £${businessOrderData.total}`);
        console.log(`📏 Distance data for business ${business.business_name}:`, {
          delivery_distance_km: business.delivery_distance_km,
          deliveryDistanceKm: business.deliveryDistanceKm,
          final_distance: businessOrderData.delivery_distance_km
        });

        // PHASE 3 STEP 7: Validate business delivery options before creating order
        const deliveryMethod = business.delivery_method || 'pickup';

        // Fetch business delivery configuration
        const { data: businessConfig, error: configError } = await supabase
          .from('businesses')
          .select('pickup_available, pickup_asap_available, pickup_scheduled_time_available, pickup_scheduled_period_available, delivery_asap_available, delivery_scheduled_time_available, delivery_scheduled_period_available')
          .eq('id', business.business_id)
          .single();

        if (configError) {
          console.error(`❌ Error fetching business config for ${business.business_id}:`, configError);
          // Continue without validation if we can't fetch config (backward compatibility)
        } else if (businessConfig) {
          // Validate delivery method against business configuration
          if (deliveryMethod === 'pickup' && !businessConfig.pickup_available) {
            throw new Error(`Pickup is not available for ${business.business_name}. Please select a different delivery method.`);
          }

          if (deliveryMethod === 'delivery') {
            const hasDeliveryOption = businessConfig.delivery_asap_available ||
                                     businessConfig.delivery_scheduled_time_available ||
                                     businessConfig.delivery_scheduled_period_available;

            if (!hasDeliveryOption) {
              throw new Error(`Delivery is not available for ${business.business_name}. Please select pickup instead.`);
            }
          }

          console.log(`✅ Delivery validation passed for business ${business.business_name} with method: ${deliveryMethod}`);
        }

        // Create the order for this business
        const orderResult = await createOrderWithTransaction(
          businessOrderData,
          businessOrderItems
        );

        if (orderResult && orderResult.orderId) {
          console.log(`✅ Order created successfully for business ${business.business_name} with ID:`, orderResult.orderId, 'order number:', orderResult.orderNumber);

          // Update cart items with order reference (if cart_id is available)
          if (cartId) {
            try {
              await supabase
                .from('cart_items')
                .update({ order_id: orderResult.orderId })
                .eq('cart_id', cartId);
            } catch (updateError) {
              console.log(`Could not update cart_items for cart ${cartId}, continuing anyway`);
            }
          }

          createdOrders.push({
            orderId: orderResult.orderId,
            orderNumber: orderResult.orderNumber,
            cartId: cartId,
            businessId: business.business_id,
            businessName: business.business_name
          });
        } else {
          console.error(`❌ Failed to create order for business ${business.business_name}`);
        }
      }

      if (createdOrders.length === 0) {
        throw new Error('No orders were created successfully');
      }

      console.log(`🎉 Successfully created ${createdOrders.length} orders:`, createdOrders.map(o => `${o.businessName} (ID: ${o.orderId})`));

      // Return multi-business order response
      return NextResponse.json({
        success: true,
        isMultiBusiness: true,
        orderCount: createdOrders.length,
        orders: createdOrders,
        message: `Successfully created ${createdOrders.length} orders`
      });
    } catch (transactionError) {
      console.error('Error in order transaction:', transactionError);
      console.error('Error stack:', transactionError.stack);

      // Log more details about the error
      if (transactionError.cause) {
        console.error('Error cause:', transactionError.cause);
      }

      // Return detailed error information in development mode
      return NextResponse.json(
        {
          error: 'Failed to create order',
          details: transactionError.message,
          stack: process.env.NODE_ENV === 'development' ? transactionError.stack : undefined,
          orderData: process.env.NODE_ENV === 'development' ? {
            itemCount: validOrderItems.length,
            businessCount: validOrderBusinesses.length,
            businesses: validOrderBusinesses.map(b => ({
              business_id: b.business_id,
              business_name: b.business_name
            }))
          } : undefined
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error in orders API:', error);
    console.error('Error stack trace:', error?.stack);

    // Log more details about the error
    if (error.cause) {
      console.error('Error cause:', error.cause);
    }

    if (error.response) {
      console.error('Error response:', error.response);
    }

    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error?.message || 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ? error?.stack : undefined
      },
      { status: 500 }
    );
  }
}

// PATCH method - for debugging routing issues
export async function PATCH(request: NextRequest) {
  console.log('🚨 PATCH request received at /api/orders - This should NOT happen!');
  console.log('🚨 PATCH requests should go to /api/business-admin/orders');
  console.log('🚨 URL:', request.url);
  console.log('🚨 Headers:', Object.fromEntries(request.headers.entries()));

  return NextResponse.json(
    {
      error: 'PATCH method not supported on /api/orders',
      message: 'PATCH requests should be sent to /api/business-admin/orders',
      receivedUrl: request.url
    },
    { status: 405 }
  );
}
