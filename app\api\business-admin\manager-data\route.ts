import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { verifyBusinessAdminAccess } from "@/lib/simple-auth";

// Function to log detailed information for debugging
function logDebug(message: string, data?: any) {
  console.log(`[MANAGER-DATA-API] ${message}`, data ? data : '');
}

// Function to log errors
function logError(message: string, error?: any) {
  console.error(`[MANAGER-DATA-API ERROR] ${message}`, error ? error : '');
}

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    logDebug("Starting business-admin manager-data API request");

    // Get the URL parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');
    logDebug("Request parameters", { businessId });

    // Verify business admin access using simple auth
    const authResult = await verifyBusinessAdminAccess(request);

    if (!authResult.authorized) {
      return NextResponse.json(
        { error: authResult.error || "Authentication failed" },
        { status: authResult.status || 401 }
      );
    }

    const { user, profile: userProfile } = authResult;
    logDebug("Found user profile", {
      role: userProfile?.role,
      id: userProfile?.id
    });

    // For admin users, return a default business
    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      logDebug("Admin user detected, returning default admin view", {
        role: userProfile.role,
        businessId: businessId
      });

      // If a specific business ID was requested, try to get that business
      if (businessId) {
        logDebug("Admin user requested specific business", { businessId });

        try {
          const { data: business, error: businessError } = await adminClient
            .from("businesses")
            .select("*, business_types(name)")
            .eq("id", businessId)
            .single();

          if (businessError) {
            logError("Error fetching specific business for admin", {
              businessId,
              error: businessError
            });
          } else if (business) {
            logDebug("Found requested business for admin", business);
            return NextResponse.json({
              isAdminUser: true,
              businessData: {
                ...business,
                business_type: business.business_types?.name || "Business"
              }
            });
          }
        } catch (err) {
          logError("Exception fetching specific business for admin", err);
        }
      }

      // Default admin view if no specific business was found
      logDebug("Returning default admin view");
      return NextResponse.json({
        isAdminUser: true,
        businessData: {
          id: 0,
          name: "Admin View",
          business_type_id: 1,
          business_type: "Admin",
          logo_url: null
        }
      });
    }

    // No special cases - all users are treated consistently

    // For other business users, get their business data
    // First try to get the business manager relationship
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    if (managerError) {
      console.error("Error fetching business manager data:", managerError);

      // No special cases - consistent error handling for all users

      // Try to find any approved business directly
      const { data: directBusinesses, error: directError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("is_approved", true)
        .limit(20);

      if (directError || !directBusinesses || directBusinesses.length === 0) {
        console.error("Error fetching businesses directly:", directError);

        // Check for pending business registrations
        const { data: pendingBusiness, error: pendingError } = await adminClient
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (!pendingError && pendingBusiness) {
          console.log("Found pending business registration:", pendingBusiness);
          return NextResponse.json({
            isPendingApproval: true,
            pendingBusiness
          });
        }

        return NextResponse.json({
          error: "No business found for this user",
          errorDetails: managerError
        }, { status: 404 });
      }

      // Find an approved business
      const approvedBusiness = directBusinesses.find(b => b.is_approved === true);
      if (approvedBusiness) {
        console.log("Found approved business:", approvedBusiness);
        return NextResponse.json({
          businessData: {
            ...approvedBusiness,
            business_type: approvedBusiness.business_types?.name || "Business"
          }
        });
      }
    }

    // If we found a manager relationship, get the business details
    if (managerData && managerData.business_id) {
      const { data: business, error: businessError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("id", managerData.business_id)
        .single();

      if (businessError || !business) {
        logError("Error fetching business details", businessError);
        return NextResponse.json({
          error: "Failed to fetch business details",
          errorDetails: businessError
        }, { status: 500 });
      }

      // Check if the business is approved
      if (business.is_approved === false) {
        return NextResponse.json({
          isPendingApproval: true,
          business
        });
      }

      return NextResponse.json({
        businessData: {
          ...business,
          business_type: business.business_types?.name || "Business"
        }
      });
    }

    // If we get here, no business was found
    logError("No business found for this user", {
      userId: userProfile.id,
      userEmail: userEmail,
      userRole: userProfile.role
    });

    // Return a detailed error response
    return NextResponse.json({
      error: "No business found for this user",
      details: {
        userId: userProfile.id,
        userEmail: userEmail,
        userRole: userProfile.role
      }
    }, { status: 404 });

  } catch (error: any) {
    // Ensure we have a valid error message
    const errorMessage = error.message || "An unexpected error occurred";
    const errorStack = error.stack || "No stack trace available";

    logError("Unhandled exception in manager-data API", {
      message: errorMessage,
      stack: errorStack
    });

    // Return a detailed error response
    return NextResponse.json(
      {
        error: errorMessage,
        details: {
          timestamp: new Date().toISOString(),
          path: "/api/business-admin/manager-data",
          type: error.name || "UnknownError"
        }
      },
      { status: 500 }
    );
  }
}
