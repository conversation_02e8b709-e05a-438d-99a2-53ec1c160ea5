"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import { useRouter } from "next/navigation";

export default function TestCheckoutPage() {
  const { addToCart, cart } = useRealtimeCart();
  const router = useRouter();

  const addTestItems = () => {
    // Add some test items to the cart
    const testItem1 = {
      id: "test-item-1",
      name: "Test Pizza",
      price: 12.99,
      quantity: 1,
      businessId: "1",
      businessName: "Test Restaurant",
      businessType: "restaurant",
      businessSlug: "test-restaurant",
      description: "A delicious test pizza",
      image: null,
      category: "Main Course",
      options: []
    };

    const testItem2 = {
      id: "test-item-2", 
      name: "Test Burger",
      price: 8.99,
      quantity: 2,
      businessId: "2",
      businessName: "Test Burger Joint",
      businessType: "restaurant", 
      businessSlug: "test-burger-joint",
      description: "A tasty test burger",
      image: null,
      category: "Main Course",
      options: []
    };

    addToCart(testItem1);
    addToCart(testItem2);
  };

  const goToCheckout = () => {
    router.push("/checkout");
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Test Checkout Page</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4">Current Cart ({cart.length} items)</h2>
        {cart.length === 0 ? (
          <p className="text-gray-500">Cart is empty</p>
        ) : (
          <div className="space-y-2">
            {cart.map((item, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="font-medium">{item.name}</div>
                <div className="text-sm text-gray-600">
                  {item.businessName} - £{item.price} x {item.quantity}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-4">
        <Button onClick={addTestItems} className="w-full">
          Add Test Items to Cart
        </Button>
        
        <Button 
          onClick={goToCheckout} 
          disabled={cart.length === 0}
          className="w-full"
          variant={cart.length === 0 ? "outline" : "default"}
        >
          Go to Checkout
        </Button>
      </div>
    </div>
  );
}
