"use client"

import React from 'react'
import { ChevronDown } from 'lucide-react'

interface FilterButtonProps {
  label: string
  icon?: React.ReactNode
  hasChevron?: boolean
  onClick?: () => void
  active?: boolean
}

export default function FilterButton({
  label,
  icon,
  hasChevron = false,
  onClick,
  active = false
}: FilterButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center whitespace-nowrap px-4 rounded-full text-sm font-medium transition-all ${
        active
          ? 'bg-emerald-600 text-white shadow-md transform scale-105 py-2 pt-2.5'
          : 'bg-gray-50 text-gray-700 hover:bg-gray-100 hover:shadow-sm py-2'
      }`}
      style={{ scrollSnapAlign: 'center' }}
    >
      {icon && <span className="mr-1.5">{icon}</span>}
      {label}
      {hasChevron && <ChevronDown className={`ml-1.5 h-4 w-4 ${active ? 'text-white' : 'text-gray-400'}`} />}
    </button>
  )
}
