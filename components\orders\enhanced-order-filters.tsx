"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import {
  Filter,
  SlidersHorizontal,
  Clock,
  AlertCircle,
  Search,
  X,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import { DatePickerWithRange, DateRange } from "@/components/date-range-picker"

interface EnhancedOrderFiltersProps {
  onSearch: (query: string) => void
  onFilterChange: (filters: OrderFilters) => void
  onDateRangeChange: (range: DateRange | undefined) => void
  dateRange: DateRange | undefined
  activeFilters: OrderFilters
}

export interface OrderFilters {
  status: string[]
  priority: number[]
  deliveryType: string[]
  paymentStatus: string
  overdueOnly: boolean
  notifiedStatus: string
}

export function EnhancedOrderFilters({
  onSearch,
  onFilterChange,
  onDateRangeChange,
  dateRange,
  activeFilters
}: EnhancedOrderFiltersProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showActiveFilters, setShowActiveFilters] = useState(false)

  // Collapsible section states
  const [statusOpen, setStatusOpen] = useState(true)
  const [priorityOpen, setPriorityOpen] = useState(false)
  const [deliveryOpen, setDeliveryOpen] = useState(false)
  const [paymentOpen, setPaymentOpen] = useState(false)
  const [specialOpen, setSpecialOpen] = useState(false)
  
  // Count active filters
  const countActiveFilters = () => {
    let count = 0
    if (activeFilters.status.length > 0) count++
    if (activeFilters.priority.length > 0) count++
    if (activeFilters.deliveryType.length > 0) count++
    if (activeFilters.paymentStatus !== 'all') count++
    if (activeFilters.overdueOnly) count++
    if (activeFilters.notifiedStatus !== 'all') count++
    return count
  }
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery)
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchQuery(value)
    // Trigger search immediately when user types (with debouncing handled by parent)
    onSearch(value)
  }
  
  const handleStatusChange = (status: string) => {
    const newStatuses = [...activeFilters.status]
    
    if (newStatuses.includes(status)) {
      // Remove status if already selected
      const index = newStatuses.indexOf(status)
      newStatuses.splice(index, 1)
    } else {
      // Add status if not selected
      newStatuses.push(status)
    }
    
    onFilterChange({
      ...activeFilters,
      status: newStatuses
    })
  }
  
  const handlePriorityChange = (priority: number) => {
    const newPriorities = [...activeFilters.priority]
    
    if (newPriorities.includes(priority)) {
      // Remove priority if already selected
      const index = newPriorities.indexOf(priority)
      newPriorities.splice(index, 1)
    } else {
      // Add priority if not selected
      newPriorities.push(priority)
    }
    
    onFilterChange({
      ...activeFilters,
      priority: newPriorities
    })
  }
  
  const handleDeliveryTypeChange = (type: string) => {
    const newTypes = [...activeFilters.deliveryType]
    
    if (newTypes.includes(type)) {
      // Remove type if already selected
      const index = newTypes.indexOf(type)
      newTypes.splice(index, 1)
    } else {
      // Add type if not selected
      newTypes.push(type)
    }
    
    onFilterChange({
      ...activeFilters,
      deliveryType: newTypes
    })
  }
  
  const handlePaymentStatusChange = (status: string) => {
    onFilterChange({
      ...activeFilters,
      paymentStatus: status
    })
  }
  
  const handleOverdueChange = (overdue: boolean) => {
    onFilterChange({
      ...activeFilters,
      overdueOnly: overdue
    })
  }
  
  const handleNotifiedStatusChange = (status: string) => {
    onFilterChange({
      ...activeFilters,
      notifiedStatus: status
    })
  }
  
  const clearAllFilters = () => {
    onFilterChange({
      status: [],
      priority: [],
      deliveryType: [],
      paymentStatus: 'all',
      overdueOnly: false,
      notifiedStatus: 'all'
    })
    onDateRangeChange(undefined)
  }
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-2">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search by order number, customer name, email, or phone..."
              className="pl-9"
              value={searchQuery}
              onChange={handleSearchInputChange}
            />
            {searchQuery && (
              <button
                type="button"
                onClick={() => {
                  setSearchQuery("")
                  onSearch("")
                }}
                className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </button>
            )}
          </div>
        </form>
        
        {/* Date Range Picker */}
        <DatePickerWithRange
          onDateChange={onDateRangeChange}
          initialDateRange={dateRange}
          className="w-auto"
        />
        
        {/* Filters */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-1">
              <Filter className="h-4 w-4" />
              <span>Filter</span>
              {countActiveFilters() > 0 && (
                <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                  {countActiveFilters()}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[280px] max-h-[80vh] overflow-y-auto">
            {/* Order Status Section */}
            <Collapsible open={statusOpen} onOpenChange={setStatusOpen}>
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between w-full px-2 py-1.5 text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer">
                  <span>Order Status</span>
                  {statusOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1">
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('pending')}
                  onCheckedChange={() => handleStatusChange('pending')}
                >
                  Pending
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('confirmed')}
                  onCheckedChange={() => handleStatusChange('confirmed')}
                >
                  Confirmed
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('preparing')}
                  onCheckedChange={() => handleStatusChange('preparing')}
                >
                  Preparing
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('ready')}
                  onCheckedChange={() => handleStatusChange('ready')}
                >
                  Ready
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('out_for_delivery')}
                  onCheckedChange={() => handleStatusChange('out_for_delivery')}
                >
                  Out for Delivery
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('delivered')}
                  onCheckedChange={() => handleStatusChange('delivered')}
                >
                  Delivered
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.status.includes('cancelled')}
                  onCheckedChange={() => handleStatusChange('cancelled')}
                >
                  Cancelled
                </DropdownMenuCheckboxItem>
              </CollapsibleContent>
            </Collapsible>

            <DropdownMenuSeparator />

            {/* Priority Section */}
            <Collapsible open={priorityOpen} onOpenChange={setPriorityOpen}>
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between w-full px-2 py-1.5 text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer">
                  <span>Priority</span>
                  {priorityOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1">
                <DropdownMenuCheckboxItem
                  checked={activeFilters.priority.includes(1)}
                  onCheckedChange={() => handlePriorityChange(1)}
                >
                  Urgent
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.priority.includes(2)}
                  onCheckedChange={() => handlePriorityChange(2)}
                >
                  High
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.priority.includes(3)}
                  onCheckedChange={() => handlePriorityChange(3)}
                >
                  Normal
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.priority.includes(4)}
                  onCheckedChange={() => handlePriorityChange(4)}
                >
                  Low
                </DropdownMenuCheckboxItem>
              </CollapsibleContent>
            </Collapsible>

            <DropdownMenuSeparator />

            {/* Delivery Type Section */}
            <Collapsible open={deliveryOpen} onOpenChange={setDeliveryOpen}>
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between w-full px-2 py-1.5 text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer">
                  <span>Delivery Type</span>
                  {deliveryOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1">
                <DropdownMenuCheckboxItem
                  checked={activeFilters.deliveryType.includes('delivery')}
                  onCheckedChange={() => handleDeliveryTypeChange('delivery')}
                >
                  Delivery
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.deliveryType.includes('pickup')}
                  onCheckedChange={() => handleDeliveryTypeChange('pickup')}
                >
                  Pickup
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.deliveryType.includes('scheduled')}
                  onCheckedChange={() => handleDeliveryTypeChange('scheduled')}
                >
                  Scheduled
                </DropdownMenuCheckboxItem>
              </CollapsibleContent>
            </Collapsible>

            <DropdownMenuSeparator />

            {/* Payment Status Section */}
            <Collapsible open={paymentOpen} onOpenChange={setPaymentOpen}>
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between w-full px-2 py-1.5 text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer">
                  <span>Payment Status</span>
                  {paymentOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1">
                <DropdownMenuCheckboxItem
                  checked={activeFilters.paymentStatus === 'all'}
                  onCheckedChange={() => handlePaymentStatusChange('all')}
                >
                  All Payment Statuses
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.paymentStatus === 'paid'}
                  onCheckedChange={() => handlePaymentStatusChange('paid')}
                >
                  Paid
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.paymentStatus === 'unpaid'}
                  onCheckedChange={() => handlePaymentStatusChange('unpaid')}
                >
                  Unpaid
                </DropdownMenuCheckboxItem>
              </CollapsibleContent>
            </Collapsible>

            <DropdownMenuSeparator />

            {/* Special Filters Section */}
            <Collapsible open={specialOpen} onOpenChange={setSpecialOpen}>
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between w-full px-2 py-1.5 text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer">
                  <span>Special Filters</span>
                  {specialOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1">
                <DropdownMenuCheckboxItem
                  checked={activeFilters.overdueOnly}
                  onCheckedChange={() => handleOverdueChange(!activeFilters.overdueOnly)}
                >
                  Overdue Orders Only
                </DropdownMenuCheckboxItem>

                <DropdownMenuSeparator />
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                  Notification Status
                </div>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.notifiedStatus === 'all'}
                  onCheckedChange={() => handleNotifiedStatusChange('all')}
                >
                  All Notification Statuses
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.notifiedStatus === 'pending'}
                  onCheckedChange={() => handleNotifiedStatusChange('pending')}
                >
                  Notifications Pending
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.notifiedStatus === 'sent'}
                  onCheckedChange={() => handleNotifiedStatusChange('sent')}
                >
                  All Notified
                </DropdownMenuCheckboxItem>
              </CollapsibleContent>
            </Collapsible>

            <DropdownMenuSeparator />
            <div className="px-2 py-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={clearAllFilters}
              >
                Clear All Filters
              </Button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
        
        {/* Advanced Filters */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Advanced
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4">
            <div className="space-y-4">
              <h4 className="font-medium">Advanced Filters</h4>
              
              <div className="space-y-2">
                <Label htmlFor="sort-by">Sort By</Label>
                <Select defaultValue="created_at">
                  <SelectTrigger id="sort-by">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Order Date (Newest)</SelectItem>
                    <SelectItem value="created_at_asc">Order Date (Oldest)</SelectItem>
                    <SelectItem value="scheduled_for">Scheduled Time</SelectItem>
                    <SelectItem value="priority_level">Priority (Highest)</SelectItem>
                    <SelectItem value="total">Total Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="time-filter">Time Filter</Label>
                <Select defaultValue="all">
                  <SelectTrigger id="time-filter">
                    <SelectValue placeholder="Time filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="yesterday">Yesterday</SelectItem>
                    <SelectItem value="this_week">This Week</SelectItem>
                    <SelectItem value="last_week">Last Week</SelectItem>
                    <SelectItem value="this_month">This Month</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="items-per-page">Items Per Page</Label>
                <Select defaultValue="10">
                  <SelectTrigger id="items-per-page">
                    <SelectValue placeholder="Items per page" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10 items</SelectItem>
                    <SelectItem value="25">25 items</SelectItem>
                    <SelectItem value="50">50 items</SelectItem>
                    <SelectItem value="100">100 items</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button className="w-full" onClick={clearAllFilters}>
                Clear All Filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Active Filters Display */}
      {countActiveFilters() > 0 && (
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 px-2 text-xs"
            onClick={() => setShowActiveFilters(!showActiveFilters)}
          >
            {showActiveFilters ? "Hide" : "Show"} active filters ({countActiveFilters()})
          </Button>
          
          {showActiveFilters && (
            <div className="flex flex-wrap gap-1">
              {activeFilters.status.map(status => (
                <Badge 
                  key={`status-${status}`}
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Status: {status}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleStatusChange(status)}
                  />
                </Badge>
              ))}
              
              {activeFilters.priority.map(priority => (
                <Badge 
                  key={`priority-${priority}`}
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Priority: {priority === 1 ? 'Urgent' : priority === 2 ? 'High' : priority === 3 ? 'Normal' : 'Low'}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handlePriorityChange(priority)}
                  />
                </Badge>
              ))}
              
              {activeFilters.deliveryType.map(type => (
                <Badge 
                  key={`type-${type}`}
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Type: {type}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleDeliveryTypeChange(type)}
                  />
                </Badge>
              ))}
              
              {activeFilters.paymentStatus !== 'all' && (
                <Badge 
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Payment: {activeFilters.paymentStatus}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handlePaymentStatusChange('all')}
                  />
                </Badge>
              )}
              
              {activeFilters.overdueOnly && (
                <Badge 
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Overdue only
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleOverdueChange(false)}
                  />
                </Badge>
              )}
              
              {activeFilters.notifiedStatus !== 'all' && (
                <Badge 
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  Notification: {activeFilters.notifiedStatus}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleNotifiedStatusChange('all')}
                  />
                </Badge>
              )}
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 px-2 text-xs"
                onClick={clearAllFilters}
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
