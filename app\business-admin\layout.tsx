"use client"

import UnifiedBusinessAdminLayout from "./unified-layout"
import { AuthProvider } from "@/context/unified-auth-context"
import { SupabaseProvider } from "@/components/providers/supabase-provider"

export default function BusinessAdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <SupabaseProvider>
      <AuthProvider>
        <UnifiedBusinessAdminLayout>{children}</UnifiedBusinessAdminLayout>
      </AuthProvider>
    </SupabaseProvider>
  )
}
