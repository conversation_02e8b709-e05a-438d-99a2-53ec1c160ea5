"use client"

import { useState, useRef } from "react"
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { v4 as uuidv4 } from 'uuid'

interface FileUploadProps {
  onUploadComplete: (url: string) => void
  currentImageUrl?: string
  type: "logo" | "banner"
  businessId: string
}

export default function FileUpload({
  onUploadComplete,
  currentImageUrl,
  type,
  businessId
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file (JPEG, PNG, etc.)')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size exceeds 5MB limit')
      return
    }

    setError(null)
    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Create a preview URL
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)

      // Generate a unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${businessId}/${type}_${uuidv4()}.${fileExt}`
      const filePath = `business-assets/${fileName}`

      // Upload to Supabase Storage
      const { error: uploadError, data } = await supabase.storage
        .from('images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
          onUploadProgress: (progress) => {
            setUploadProgress(Math.round((progress.loaded / progress.total) * 100))
          }
        })

      if (uploadError) {
        throw uploadError
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filePath)

      // Call the callback with the URL
      onUploadComplete(publicUrl)
    } catch (error) {
      console.error('Error uploading file:', error)
      setError('Failed to upload file. Please try again.')
      setPreviewUrl(currentImageUrl)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onUploadComplete('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="space-y-4">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        id={`file-upload-${type}`}
      />

      {previewUrl ? (
        <div className="relative">
          <img
            src={previewUrl}
            alt={`${type} preview`}
            className={`rounded-md border border-gray-200 ${
              type === 'logo' ? 'max-h-48 object-contain' : 'w-full h-48 object-cover'
            }`}
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={handleRemoveImage}
            disabled={isUploading}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <label
          htmlFor={`file-upload-${type}`}
          className={`flex flex-col items-center justify-center border-2 border-dashed rounded-md p-6 cursor-pointer hover:bg-gray-50 transition-colors ${
            type === 'logo' ? 'h-48 w-48' : 'w-full h-48'
          }`}
        >
          {isUploading ? (
            <div className="text-center">
              <Loader2 className="h-8 w-8 mx-auto text-gray-400 animate-spin mb-2" />
              <p className="text-sm text-gray-500">Uploading... {uploadProgress}%</p>
            </div>
          ) : (
            <>
              <div className="text-center">
                <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                <p className="font-medium mb-1">
                  {type === 'logo' ? 'Upload Logo' : 'Upload Banner'}
                </p>
                <p className="text-xs text-gray-500 mb-2">
                  {type === 'logo'
                    ? 'Square image recommended (PNG, JPG)'
                    : 'Recommended size: 1200×400px (PNG, JPG)'}
                </p>
                <Button variant="outline" size="sm" type="button">
                  <Upload className="h-4 w-4 mr-2" />
                  Select File
                </Button>
              </div>
            </>
          )}
        </label>
      )}

      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  )
}
