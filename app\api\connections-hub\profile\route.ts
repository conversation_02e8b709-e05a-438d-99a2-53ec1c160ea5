import { NextResponse } from "next/server"
import { adminClient } from "@/lib/supabase-admin"
import { createServerSupabase } from "@/lib/supabase-server"

// Types for our API
interface ProfileData {
  profile_type: 'customer' | 'business' | 'rider'
  display_name?: string
  bio?: string
  avatar_url?: string
  communication_preferences?: Record<string, any>
  availability_info?: Record<string, any>
  specialties?: Record<string, any>
  is_public?: boolean
  allow_direct_messages?: boolean
}

// Helper function to get authenticated user
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

// GET - Retrieve user's connection profiles
export async function GET(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const profile_type = url.searchParams.get('profile_type')

    // Build query
    let query = adminClient
      .from('connection_profiles')
      .select('*')
      .eq('user_id', user.id)

    // Filter by profile type if specified
    if (profile_type) {
      const validTypes = ['customer', 'business', 'rider']
      if (!validTypes.includes(profile_type)) {
        return NextResponse.json(
          { error: "Invalid profile_type. Must be: customer, business, or rider" },
          { status: 400 }
        )
      }
      query = query.eq('profile_type', profile_type)
    }

    const { data: profiles, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error("Error fetching profiles:", error)
      return NextResponse.json(
        { error: "Failed to fetch profiles" },
        { status: 500 }
      )
    }

    // If requesting a specific profile type and none exists, return null
    if (profile_type && (!profiles || profiles.length === 0)) {
      return NextResponse.json({
        data: null,
        message: `No ${profile_type} profile found`
      })
    }

    return NextResponse.json({
      data: profile_type ? (profiles?.[0] || null) : profiles,
      count: profiles?.length || 0
    })

  } catch (error) {
    console.error("Unexpected error in GET /api/connections-hub/profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST - Create new connection profile
export async function POST(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const body: ProfileData = await request.json()
    const {
      profile_type,
      display_name,
      bio,
      avatar_url,
      communication_preferences,
      availability_info,
      specialties,
      is_public,
      allow_direct_messages
    } = body

    // Validate required fields
    if (!profile_type) {
      return NextResponse.json(
        { error: "profile_type is required" },
        { status: 400 }
      )
    }

    // Validate profile type
    const validTypes = ['customer', 'business', 'rider']
    if (!validTypes.includes(profile_type)) {
      return NextResponse.json(
        { error: "Invalid profile_type. Must be: customer, business, or rider" },
        { status: 400 }
      )
    }

    // Check if profile already exists for this user and type
    const { data: existingProfile, error: checkError } = await adminClient
      .from('connection_profiles')
      .select('id')
      .eq('user_id', user.id)
      .eq('profile_type', profile_type)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error("Error checking existing profile:", checkError)
      return NextResponse.json(
        { error: "Failed to check existing profile" },
        { status: 500 }
      )
    }

    if (existingProfile) {
      return NextResponse.json(
        { error: `${profile_type} profile already exists. Use PUT to update.` },
        { status: 409 }
      )
    }

    // Create new profile
    const profileData = {
      user_id: user.id,
      profile_type,
      display_name: display_name || null,
      bio: bio || null,
      avatar_url: avatar_url || null,
      communication_preferences: communication_preferences || {},
      availability_info: availability_info || {},
      specialties: specialties || {},
      is_public: is_public !== undefined ? is_public : true,
      allow_direct_messages: allow_direct_messages !== undefined ? allow_direct_messages : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: newProfile, error: createError } = await adminClient
      .from('connection_profiles')
      .insert([profileData])
      .select()
      .single()

    if (createError) {
      console.error("Error creating profile:", createError)
      return NextResponse.json(
        { error: "Failed to create profile" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: newProfile,
      message: "Profile created successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Unexpected error in POST /api/connections-hub/profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT - Update connection profile
export async function PUT(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const body: ProfileData = await request.json()
    const {
      profile_type,
      display_name,
      bio,
      avatar_url,
      communication_preferences,
      availability_info,
      specialties,
      is_public,
      allow_direct_messages
    } = body

    // Validate required fields
    if (!profile_type) {
      return NextResponse.json(
        { error: "profile_type is required" },
        { status: 400 }
      )
    }

    // Validate profile type
    const validTypes = ['customer', 'business', 'rider']
    if (!validTypes.includes(profile_type)) {
      return NextResponse.json(
        { error: "Invalid profile_type. Must be: customer, business, or rider" },
        { status: 400 }
      )
    }

    // Check if profile exists
    const { data: existingProfile, error: checkError } = await adminClient
      .from('connection_profiles')
      .select('id')
      .eq('user_id', user.id)
      .eq('profile_type', profile_type)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json(
          { error: `${profile_type} profile not found. Use POST to create.` },
          { status: 404 }
        )
      }
      console.error("Error checking existing profile:", checkError)
      return NextResponse.json(
        { error: "Failed to check existing profile" },
        { status: 500 }
      )
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    // Only update fields that are provided
    if (display_name !== undefined) updateData.display_name = display_name
    if (bio !== undefined) updateData.bio = bio
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url
    if (communication_preferences !== undefined) updateData.communication_preferences = communication_preferences
    if (availability_info !== undefined) updateData.availability_info = availability_info
    if (specialties !== undefined) updateData.specialties = specialties
    if (is_public !== undefined) updateData.is_public = is_public
    if (allow_direct_messages !== undefined) updateData.allow_direct_messages = allow_direct_messages

    // Update the profile
    const { data: updatedProfile, error: updateError } = await adminClient
      .from('connection_profiles')
      .update(updateData)
      .eq('user_id', user.id)
      .eq('profile_type', profile_type)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating profile:", updateError)
      return NextResponse.json(
        { error: "Failed to update profile" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: updatedProfile,
      message: "Profile updated successfully"
    })

  } catch (error) {
    console.error("Unexpected error in PUT /api/connections-hub/profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
