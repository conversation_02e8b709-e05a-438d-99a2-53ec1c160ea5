// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function removeUnusedCategories() {
  try {
    console.log('=== REMOVING UNUSED CATEGORIES ===\n');

    // Get all categories
    const { data: allCategories, error: catError } = await supabase
      .from('categories')
      .select('id, name, slug, business_type_id')
      .order('id');

    if (catError) {
      console.error('Error fetching categories:', catError);
      return;
    }

    console.log(`📊 Total categories in database: ${allCategories.length}`);

    // Get all used category IDs from business_categories
    const { data: usedCategories, error: usedError } = await supabase
      .from('business_categories')
      .select('category_id');

    if (usedError) {
      console.error('Error fetching used categories:', usedError);
      return;
    }

    const usedCategoryIds = new Set(usedCategories.map(uc => uc.category_id));
    console.log(`📊 Categories currently in use: ${usedCategoryIds.size}`);

    // Find unused categories
    const unusedCategories = allCategories.filter(cat => !usedCategoryIds.has(cat.id));
    console.log(`📊 Unused categories to remove: ${unusedCategories.length}\n`);

    if (unusedCategories.length === 0) {
      console.log('✅ No unused categories found!');
      return;
    }

    // Show unused categories by business type
    console.log('🗑️  UNUSED CATEGORIES TO BE REMOVED:');
    console.log('='.repeat(60));

    const { data: businessTypes, error: btError } = await supabase
      .from('business_types')
      .select('id, name');

    if (btError) {
      console.error('Error fetching business types:', btError);
      return;
    }

    const businessTypeMap = {};
    businessTypes.forEach(bt => {
      businessTypeMap[bt.id] = bt.name;
    });

    const unusedByType = {};
    unusedCategories.forEach(cat => {
      const typeName = businessTypeMap[cat.business_type_id] || 'Unknown';
      if (!unusedByType[typeName]) {
        unusedByType[typeName] = [];
      }
      unusedByType[typeName].push(cat);
    });

    Object.keys(unusedByType).forEach(typeName => {
      console.log(`\n🏢 ${typeName} (${unusedByType[typeName].length} unused):`);
      unusedByType[typeName].forEach(cat => {
        console.log(`   ❌ ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
      });
    });

    // Confirm removal
    console.log(`\n⚠️  About to remove ${unusedCategories.length} unused categories.`);
    console.log('This action cannot be undone.');
    
    // Remove unused categories in batches
    console.log('\n🗑️  Removing unused categories...');
    
    const unusedIds = unusedCategories.map(cat => cat.id);
    const batchSize = 20;
    let removedCount = 0;

    for (let i = 0; i < unusedIds.length; i += batchSize) {
      const batch = unusedIds.slice(i, i + batchSize);
      
      const { error: deleteError } = await supabase
        .from('categories')
        .delete()
        .in('id', batch);

      if (deleteError) {
        console.error(`❌ Error removing batch ${Math.floor(i / batchSize) + 1}:`, deleteError);
        continue;
      }

      removedCount += batch.length;
      console.log(`✅ Removed batch ${Math.floor(i / batchSize) + 1} (${batch.length} categories)`);
    }

    // Final verification
    const { data: finalCategories, error: finalError } = await supabase
      .from('categories')
      .select('id', { count: 'exact' });

    if (finalError) {
      console.error('Error getting final count:', finalError);
    } else {
      console.log(`\n📊 CLEANUP RESULTS`);
      console.log('='.repeat(50));
      console.log(`Categories before cleanup: ${allCategories.length}`);
      console.log(`Categories removed: ${removedCount}`);
      console.log(`Categories remaining: ${finalCategories.length}`);
      console.log(`Categories in use: ${usedCategoryIds.size}`);
    }

    // Show remaining categories by type
    console.log('\n📋 REMAINING CATEGORIES BY TYPE');
    console.log('='.repeat(50));

    const { data: remainingCategories, error: remainingError } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        business_type_id
      `)
      .order('business_type_id')
      .order('name');

    if (remainingError) {
      console.error('Error fetching remaining categories:', remainingError);
    } else {
      const remainingByType = {};
      remainingCategories.forEach(cat => {
        const typeName = businessTypeMap[cat.business_type_id] || 'Unknown';
        if (!remainingByType[typeName]) {
          remainingByType[typeName] = [];
        }
        remainingByType[typeName].push(cat);
      });

      Object.keys(remainingByType).forEach(typeName => {
        console.log(`\n🏢 ${typeName} (${remainingByType[typeName].length} categories):`);
        remainingByType[typeName].forEach(cat => {
          const inUse = usedCategoryIds.has(cat.id) ? '✅' : '⚪';
          console.log(`   ${inUse} ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
        });
      });
    }

  } catch (error) {
    console.error('Error removing unused categories:', error);
  }
}

removeUnusedCategories()
  .then(() => {
    console.log('\n✨ Category cleanup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  });
