"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { X } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { format } from "date-fns"

interface UserDetailsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: {
    id: number
    name: string
    email: string
    role: string
    auth_id?: string
    created_at?: string
    updated_at?: string
    last_login?: string
  } | null
}

interface UserOrderStats {
  totalOrders: number
  lastOrderDate: string | null
  totalSpent: number
  loginCount: number
}

export function UserDetailsDialog({ open, onOpenChange, user }: UserDetailsProps) {
  if (!user) return null

  const [loading, setLoading] = useState(true)
  const [userStats, setUserStats] = useState<UserOrderStats>({
    totalOrders: 0,
    lastOrderDate: null,
    totalSpent: 0,
    loginCount: 0
  })

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date helper
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A"
    return format(new Date(dateString), 'dd MMM yyyy')
  }

  // Get user's last active time
  const getLastActive = (updated_at: string | undefined, last_login: string | undefined) => {
    if (last_login) return formatDate(last_login)
    if (updated_at) return formatDate(updated_at)
    return "N/A"
  }

  // Fetch user orders and stats
  useEffect(() => {
    if (!user || !open) return

    const fetchUserStats = async () => {
      setLoading(true)
      try {
        // Fetch orders for this user
        const { data: orders, error: ordersError } = await supabase
          .from('orders')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })

        if (ordersError) {
          console.error('Error fetching user orders:', ordersError)
        }

        // Calculate stats
        const totalOrders = orders?.length || 0
        const lastOrderDate = orders && orders.length > 0 ? orders[0].created_at : null
        const totalSpent = orders ? orders.reduce((sum, order) => sum + (parseFloat(order.total) || 0), 0) : 0

        // For login count, we don't have this data in the database yet
        // This would typically come from an auth_sessions table or similar
        const loginCount = 24 // Placeholder until we implement login tracking

        setUserStats({
          totalOrders,
          lastOrderDate,
          totalSpent,
          loginCount
        })
      } catch (error) {
        console.error('Error fetching user stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserStats()
  }, [user, open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-0 overflow-hidden">
        <div className="relative">
          <button
            onClick={() => onOpenChange(false)}
            className="absolute right-4 top-4 rounded-full h-6 w-6 inline-flex items-center justify-center text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <div className="p-6 pb-2">
            <h2 className="text-lg font-semibold">User Details</h2>
            <p className="text-sm text-muted-foreground">
              Detailed information about the selected user.
            </p>
          </div>

          <div className="p-6">
            <div className="flex flex-col items-center text-center mb-6">
              <div className="h-20 w-20 mb-3 rounded-full bg-gray-100 flex items-center justify-center text-2xl text-gray-800 font-semibold">
                {user.name ? user.name.charAt(0) : 'U'}
              </div>
              <h2 className="text-xl font-semibold">{user.name}</h2>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <div className="mt-2">
                <Badge className="bg-emerald-100 text-emerald-800">
                  {user.role}
                </Badge>
                <Badge className="bg-emerald-100 text-emerald-800 ml-2">
                  Active
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-sm font-medium mb-2">Account Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">User ID:</span>
                    <span>{user.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Joined:</span>
                    <span>{formatDate(user.created_at)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Active:</span>
                    <span>{getLastActive(user.updated_at, user.last_login)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <span>Active</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Activity</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Orders:</span>
                    <span>{loading ? '...' : userStats.totalOrders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Order:</span>
                    <span>{loading ? '...' : (userStats.lastOrderDate ? formatDate(userStats.lastOrderDate) : 'N/A')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Spent:</span>
                    <span>{loading ? '...' : formatCurrency(userStats.totalSpent)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Login Count:</span>
                    <span>{loading ? '...' : userStats.loginCount}</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Permissions</h3>
              <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                <div className="flex items-center">
                  <Checkbox
                    id="view-dashboard"
                    className="mr-2"
                    checked={['admin', 'super_admin', 'business_manager'].includes(user.role.toLowerCase())}
                    disabled
                  />
                  <label htmlFor="view-dashboard" className="text-sm">View Dashboard</label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="manage-users"
                    className="mr-2"
                    checked={['admin', 'super_admin'].includes(user.role.toLowerCase())}
                    disabled
                  />
                  <label htmlFor="manage-users" className="text-sm">Manage Users</label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="view-reports"
                    className="mr-2"
                    checked={['admin', 'super_admin', 'business_manager'].includes(user.role.toLowerCase())}
                    disabled
                  />
                  <label htmlFor="view-reports" className="text-sm">View Reports</label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="manage-business"
                    className="mr-2"
                    checked={['admin', 'super_admin', 'business_manager'].includes(user.role.toLowerCase())}
                    disabled
                  />
                  <label htmlFor="manage-business" className="text-sm">Manage Business</label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="process-orders"
                    className="mr-2"
                    checked={['admin', 'super_admin', 'business_manager', 'business_staff'].includes(user.role.toLowerCase())}
                    disabled
                  />
                  <label htmlFor="process-orders" className="text-sm">Process Orders</label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="place-orders"
                    className="mr-2"
                    checked={true} // All users can place orders
                    disabled
                  />
                  <label htmlFor="place-orders" className="text-sm">Place Orders</label>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-gray-50 flex justify-between">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-1">
                <span className="h-4 w-4 flex items-center justify-center">⭐</span>
                Edit User
              </Button>
              <Button variant="destructive" className="flex items-center gap-1">
                <span className="h-4 w-4 flex items-center justify-center">🗑️</span>
                Delete User
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
