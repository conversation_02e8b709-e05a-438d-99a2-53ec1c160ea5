'use client'

import { useState } from 'react'

export default function SimpleNotificationTest() {
  const [status, setStatus] = useState<string>('')

  const testSimpleNotification = async () => {
    setStatus('🔔 Testing simple notification...')

    try {
      // Force permission request
      const permission = await Notification.requestPermission()
      setStatus(`Permission: ${permission}`)

      if (permission === 'granted') {
        setStatus(prev => prev + '\n✅ Permission granted, creating notification...')
        
        const notification = new Notification('🚨 SIMPLE TEST', {
          body: 'This is a basic browser notification test. You should see this!',
          icon: '/android-chrome-192x192.png',
          requireInteraction: true,
          tag: 'simple-test'
        })

        notification.onclick = () => {
          setStatus(prev => prev + '\n🖱️ Notification clicked!')
          notification.close()
        }

        notification.onshow = () => {
          setStatus(prev => prev + '\n📱 Notification shown successfully!')
        }

        notification.onerror = (error) => {
          setStatus(prev => prev + `\n❌ Notification error: ${error}`)
        }

        setStatus(prev => prev + '\n📤 Notification created and should be visible now!')
      } else {
        setStatus(prev => prev + `\n❌ Permission denied: ${permission}`)
        setStatus(prev => prev + '\n💡 Please enable notifications in browser settings')
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
    }
  }

  const checkBrowserSettings = () => {
    setStatus('🔍 Browser notification info:')
    setStatus(prev => prev + `\nSupported: ${'Notification' in window}`)
    setStatus(prev => prev + `\nPermission: ${Notification.permission}`)
    setStatus(prev => prev + `\nUser Agent: ${navigator.userAgent.substring(0, 100)}...`)
    
    setStatus(prev => prev + '\n\n📋 To fix notification issues:')
    setStatus(prev => prev + '\n1. Go to chrome://settings/content/notifications')
    setStatus(prev => prev + '\n2. Find localhost:3000 and set to "Allow"')
    setStatus(prev => prev + '\n3. Or click the lock icon 🔒 in address bar → Site settings → Notifications → Allow')
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          🚨 Simple Notification Test
        </h1>

        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
            <h3 className="font-semibold text-yellow-800 mb-2">Important:</h3>
            <p className="text-yellow-700 text-sm">
              This test uses basic browser notifications (not push notifications). 
              If this doesn't work, the issue is with your browser settings for localhost:3000.
            </p>
          </div>

          <button
            onClick={testSimpleNotification}
            className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 font-semibold"
          >
            🚨 Test Simple Notification
          </button>

          <button
            onClick={checkBrowserSettings}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
          >
            🔍 Check Browser Info
          </button>

          {status && (
            <div className="mt-4">
              <h3 className="font-semibold text-gray-700 mb-2">Status:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm whitespace-pre-wrap">
                {status}
              </pre>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-800 mb-2">If notifications don't work:</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Check if you have notification blocking extensions</li>
              <li>Try incognito/private mode</li>
              <li>Go to chrome://settings/content/notifications</li>
              <li>Make sure localhost:3000 is set to "Allow"</li>
              <li>Try a different browser (Edge, Firefox)</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
