import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  // Create a Supabase client with the service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey)

  try {
    const {
      userId,
      businessTypeId,
      name,
      description,
      address,
      postcode,
      phone,
      logoUrl,
      bannerUrl,
      deliveryFee,
      minimumOrderAmount,
      categories
    } = await request.json()

    // Validate required fields
    if (!userId || !businessTypeId || !name || !address || !postcode || !phone) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Call the register_business procedure
    const { data: businessId, error: businessError } = await supabase.rpc(
      'register_business',
      {
        user_id: userId,
        business_type_id: businessTypeId,
        name,
        description: description || '',
        address,
        postcode,
        phone,
        logo_url: logoUrl || null,
        banner_url: bannerUrl || null,
        delivery_fee: deliveryFee || 0,
        minimum_order_amount: minimumOrderAmount || 0
      }
    )

    if (businessError) {
      console.error("Error registering business:", businessError)
      return NextResponse.json(
        { error: businessError.message },
        { status: 500 }
      )
    }

    // Add business categories if provided
    if (categories && categories.length > 0 && businessId) {
      const categoryInserts = categories.map((categoryId: number, index: number) => ({
        business_id: businessId,
        category_id: categoryId,
        is_primary: index === 0 // First category is primary
      }))

      const { error: categoriesError } = await supabase
        .from("business_categories")
        .insert(categoryInserts)

      if (categoriesError) {
        console.error("Error adding business categories:", categoriesError)
        // Don't fail the whole registration for this
      }
    }

    return NextResponse.json({
      success: true,
      businessId
    })
  } catch (error) {
    console.error("Error in business registration:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
