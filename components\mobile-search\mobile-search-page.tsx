'use client'

import { useState, useEffect } from 'react'
import MobileSearchBar from './mobile-search-bar'
import FilterChips from './filter-chips'
import UnifiedBusinessCard from '../unified-business-card'

import CategoriesAndFilters from '@/components/search/categories-and-filters'
import { cn } from '@/lib/utils'
import { getBusinessTypeColors } from '@/utils/business-colors'
import { useLocation } from '@/context/location-context'

interface MobileSearchPageProps {
  initialQuery?: string
  initialType?: string
  businesses: any[]
  onTypeChange?: (type: string) => void
  onCategorySelect?: (category: string) => void
  className?: string
}

export default function MobileSearchPage({
  initialQuery = '',
  initialType = 'all',
  businesses = [],
  onTypeChange,
  onCategorySelect,
  className
}: MobileSearchPageProps) {


  const [searchQuery, setSearchQuery] = useState(initialQuery)
  const [activeType, setActiveType] = useState(initialType)
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 50])
  const [maxDeliveryTime, setMaxDeliveryTime] = useState<number>(30)
  const [maxPrepTime, setMaxPrepTime] = useState<number>(30)
  const [filteredBusinesses, setFilteredBusinesses] = useState(businesses)

  // Get the location context to access postcode and coordinates
  const { postcode: contextPostcode, coordinates } = useLocation()

  // Update local state when props change (e.g., from header search)
  useEffect(() => {
    if (initialQuery !== searchQuery) {
      setSearchQuery(initialQuery)
    }
    if (initialType !== activeType) {
      setActiveType(initialType)
    }
  }, [initialQuery, initialType, searchQuery, activeType])

  // Refresh businesses when postcode changes to update delivery times
  useEffect(() => {
    if (businesses.length > 0 && contextPostcode) {
      // Force a re-render of the businesses to recalculate delivery times
      // We're using a timeout to ensure this doesn't cause render issues
      const timer = setTimeout(() => {
        setFilteredBusinesses(businesses.slice())
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [contextPostcode])

  // Helper function to check if a business has custom categories matching the search query
  const hasMatchingCustomCategory = (business: any, query: string): boolean => {
    if (!query) return false

    const lowerQuery = query.toLowerCase()

    if ('customCategories' in business && business.customCategories && Array.isArray(business.customCategories)) {
      return business.customCategories.some((category: string) =>
        category.toLowerCase().includes(lowerQuery)
      )
    }

    return false
  }

  // Filter businesses based on search query and filters
  useEffect(() => {
    let filtered = businesses.filter(business => {
      // Filter by search query - include custom categories
      const matchesSearch =
        searchQuery === '' ||
        business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        business.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hasMatchingCustomCategory(business, searchQuery)

      // Filter by business type - handle both formats
      // Some businesses have business_type property, others have type-specific properties
      const matchesType =
        activeType === 'all' ||
        (business.business_type && business.business_type === activeType) ||
        ('cuisines' in business && activeType === 'restaurant') ||
        ('storeTypes' in business && activeType === 'shop') ||
        ('serviceTypes' in business && activeType === 'lift') ||
        ('errandTypes' in business && activeType === 'errand')

      return matchesSearch && matchesType
    })

    // Apply price range filter
    filtered = filtered.filter(business => {
      const passesPrice = business.deliveryFee >= priceRange[0] && business.deliveryFee <= priceRange[1]
      return passesPrice
    })

    // Apply delivery time filter
    filtered = filtered.filter(business => {
      const deliveryTimeNum = typeof business.deliveryTime === 'string' ? parseInt(business.deliveryTime, 10) : business.deliveryTime
      const actualDeliveryTime = deliveryTimeNum || 30
      const passesTime = actualDeliveryTime <= maxDeliveryTime
      return passesTime
    })

    // Apply prep time filter
    filtered = filtered.filter(business => {
      const prepTimeNum = business.preparationTimeMinutes || 15
      const passesPrepTime = prepTimeNum <= maxPrepTime
      return passesPrepTime
    })

    setFilteredBusinesses(filtered)
  }, [businesses, searchQuery, activeType, activeFilters, priceRange, maxDeliveryTime, maxPrepTime])

  // Handle filter changes
  const handleFilterChange = (filterId: string) => {
    setActiveFilters(prev => {
      if (prev.includes(filterId)) {
        return prev.filter(id => id !== filterId)
      } else {
        return [...prev, filterId]
      }
    })
  }

  // Handle clear all filters
  const handleClearFilters = () => {
    setActiveFilters([])
    setPriceRange([0, 50])
    setMaxDeliveryTime(30)
    setMaxPrepTime(30)
    setSearchQuery('')
    setActiveType('all')
  }

  // Handle price range change
  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRange(range)
  }

  // Handle delivery time change
  const handleMaxDeliveryTimeChange = (time: number) => {
    setMaxDeliveryTime(time)
  }

  // Handle prep time change
  const handleMaxPrepTimeChange = (time: number) => {
    setMaxPrepTime(time)
  }

  // Handle business type change
  const handleTypeChange = (type: string) => {
    setActiveType(type)

    // Update URL with new type
    const url = new URL(window.location.href)
    url.searchParams.set('type', type)
    window.history.pushState({}, '', url.toString())

    // Call the parent component's onTypeChange if provided
    if (onTypeChange) {
      onTypeChange(type)
    }
  }

  // Get placeholder text based on business type
  const getPlaceholderText = (type: string) => {
    switch (type) {
      case 'restaurant':
        return "Restaurants, dishes..."
      case 'shop':
        return "Shops, products..."
      case 'pharmacy':
        return "Pharmacies, products..."
      case 'cafe':
        return "Cafes, menu items..."
      case 'lift':
        return "Lifts, services..."
      case 'errand':
        return "Errands, services..."
      default:
        return "Restaurants, groceries, dishes..."
    }
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* Removed MobileHeader to prevent duplicate navbar */}

      <MobileSearchBar
        placeholder={getPlaceholderText(activeType)}
        value={searchQuery}
        onChange={setSearchQuery}
        businessType={activeType}
        activeFilters={activeFilters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        priceRange={priceRange}
        onPriceRangeChange={handlePriceRangeChange}
        maxDeliveryTime={maxDeliveryTime}
        onMaxDeliveryTimeChange={handleMaxDeliveryTimeChange}
        maxPrepTime={maxPrepTime}
        onMaxPrepTimeChange={handleMaxPrepTimeChange}
        onTypeChange={handleTypeChange}
      />

      <FilterChips
        activeFilters={activeFilters}
        onRemoveFilter={handleFilterChange}
      />

      {/* Results Count moved to the filters row */}

      {/* Categories and Filters */}
      <div className="bg-white mb-2">
        <CategoriesAndFilters
          onCategorySelect={(category) => {
            // Pass category selection to parent component
            if (onCategorySelect) {
              onCategorySelect(category)
            }
          }}
          onFilterSelect={(filter) => {
            // Implement filter logic here
            handleFilterChange(filter)
          }}
          resultsCount={filteredBusinesses.length}
          businessType={activeType}
        />
      </div>



      <div className="px-4 py-2">
        <div className="grid grid-cols-2 gap-4 grid-auto-rows-fr">
          {filteredBusinesses.map((business, index) => (
            <UnifiedBusinessCard
              key={business.id}
              business={business as any} // Type assertion for mobile search compatibility
              index={index}
              enableRealTimeUpdates={false} // Mobile search uses cached values for performance
            />
          ))}
        </div>
      </div>
    </div>
  )
}