import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify super admin access
async function verifySuperAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user is super admin
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (profile.role !== 'super_admin') {
      return { authorized: false, error: 'Super admin access required', status: 403 }
    }

    return { authorized: true, user, profile }
  } catch (error) {
    console.error('Error verifying super admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// GET - Fetch all business types
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { data: businessTypes, error } = await adminClient
      .from('business_types')
      .select('id, name, slug, description')
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching business types:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business types' },
        { status: 500 }
      )
    }

    return NextResponse.json(businessTypes)
  } catch (error) {
    console.error('Error in GET /api/super-admin/business-types:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
