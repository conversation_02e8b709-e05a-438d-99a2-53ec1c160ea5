import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessTypeId = searchParams.get('businessTypeId')
    const selectedCategories = searchParams.get('selectedCategories') // comma-separated category IDs

    // Get available attribute types based on business type and context
    let contextualAttributeTypes: string[] = []
    
    if (businessTypeId) {
      const businessType = parseInt(businessTypeId)
      
      // Define context-aware attribute types based on business type
      switch (businessType) {
        case 1: // Restaurant
          contextualAttributeTypes = ['delivery_option', 'restaurant_service_feature', 'service_feature']
          break
        case 2: // Shop
          contextualAttributeTypes = ['delivery_option', 'store_feature', 'service_feature']
          break
        case 3: // Pharmacy
          contextualAttributeTypes = ['delivery_option', 'pharmacy_service_feature', 'service_feature']
          break
        case 4: // Cafe
          contextualAttributeTypes = ['delivery_option', 'cafe_feature', 'service_feature']
          break
        case 38: // Errand
          contextualAttributeTypes = ['errand_type', 'service_feature']
          break
        default:
          contextualAttributeTypes = ['delivery_option', 'service_feature']
      }
    } else {
      // Default attribute types when no business type is selected
      contextualAttributeTypes = ['delivery_option', 'service_feature']
    }

    // Base query to get distinct attribute values
    let query = adminClient
      .from('business_attributes')
      .select(`
        attribute_type,
        attribute_value,
        businesses!inner(
          id,
          business_type_id,
          is_approved
        )
      `)

    // Only include approved businesses
    query = query.eq('businesses.is_approved', true)

    // Filter by business type if provided
    if (businessTypeId) {
      query = query.eq('businesses.business_type_id', parseInt(businessTypeId))
    }

    // Filter by contextual attribute types
    if (contextualAttributeTypes.length > 0) {
      query = query.in('attribute_type', contextualAttributeTypes)
    }

    // Filter by businesses that have selected categories if provided
    if (selectedCategories) {
      const categoryIds = selectedCategories.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      
      if (categoryIds.length > 0) {
        // Get businesses that have any of the selected categories
        const { data: businessesWithCategories } = await adminClient
          .from('business_categories')
          .select('business_id')
          .in('category_id', categoryIds)

        if (businessesWithCategories && businessesWithCategories.length > 0) {
          const businessIds = businessesWithCategories.map(bc => bc.business_id)
          query = query.in('businesses.id', businessIds)
        } else {
          // No businesses have these categories, return empty result
          return NextResponse.json({
            filters: {}
          })
        }
      }
    }

    const { data: attributes, error } = await query

    if (error) {
      console.error('Error fetching search filters:', error)
      return NextResponse.json(
        { error: 'Failed to fetch search filters' },
        { status: 500 }
      )
    }

    // Group attributes by type and get unique values
    const filtersByType: { [key: string]: string[] } = {}
    
    if (attributes) {
      attributes.forEach((attr: any) => {
        if (attr.attribute_type && attr.attribute_value) {
          if (!filtersByType[attr.attribute_type]) {
            filtersByType[attr.attribute_type] = []
          }
          
          // Add unique values only
          if (!filtersByType[attr.attribute_type].includes(attr.attribute_value)) {
            filtersByType[attr.attribute_type].push(attr.attribute_value)
          }
        }
      })
    }

    // Sort values within each type
    Object.keys(filtersByType).forEach(type => {
      filtersByType[type].sort()
    })

    return NextResponse.json({
      filters: filtersByType,
      contextualAttributeTypes: contextualAttributeTypes
    })
  } catch (error) {
    console.error('Unhandled error in search filters API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
