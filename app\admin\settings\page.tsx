'use client'

import { useState, useEffect } from 'react'
import { useAuthDirect } from '@/context/auth-context-direct'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Save, Bell, Database, Users, Store, Truck, AlertCircle } from 'lucide-react'
import { addAuthHeaders } from '@/utils/auth-token'

interface NotificationPreference {
  id: number
  admin_user_id: number
  table_name: string
  notify_insert: boolean
  notify_update: boolean
  notify_delete: boolean
  notify_approval_changes: boolean
  created_at: string
  updated_at: string
}

interface TableInfo {
  table_name: string
  display_name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

const MONITORED_TABLES: TableInfo[] = [
  {
    table_name: 'businesses',
    display_name: 'Businesses',
    description: 'New business registrations, updates, and approval changes',
    icon: Store
  },
  {
    table_name: 'driver_profiles',
    display_name: 'Driver Applications',
    description: 'Driver application submissions and status changes',
    icon: Truck
  },
  {
    table_name: 'users',
    display_name: 'Users',
    description: 'User account changes and role modifications',
    icon: Users
  }
]

export default function AdminSettingsPage() {
  const { userProfile } = useAuthDirect()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [preferences, setPreferences] = useState<NotificationPreference[]>([])

  useEffect(() => {
    if (userProfile?.id) {
      fetchNotificationPreferences()
    }
  }, [userProfile?.id])

  const fetchNotificationPreferences = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/notification-preferences?userId=${userProfile?.id}`, {
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch notification preferences')
      }

      const data = await response.json()
      setPreferences(data.preferences || [])
    } catch (error) {
      console.error('Error fetching notification preferences:', error)
      toast({
        title: 'Error',
        description: 'Failed to load notification preferences',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const updatePreference = async (tableName: string, field: string, value: boolean) => {
    try {
      setSaving(true)
      const response = await fetch('/api/admin/notification-preferences', {
        method: 'PATCH',
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({
          userId: userProfile?.id,
          tableName,
          [field]: value
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update notification preference')
      }

      // Update local state
      setPreferences(prev =>
        prev.map(pref =>
          pref.table_name === tableName
            ? { ...pref, [field]: value, updated_at: new Date().toISOString() }
            : pref
        )
      )

      toast({
        title: 'Success',
        description: 'Notification preference updated',
      })
    } catch (error) {
      console.error('Error updating notification preference:', error)
      toast({
        title: 'Error',
        description: 'Failed to update notification preference',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const getPreferenceForTable = (tableName: string): NotificationPreference | undefined => {
    return preferences.find(pref => pref.table_name === tableName)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Admin Settings</h1>
        <p className="text-gray-500">Configure your admin preferences and notification settings</p>
      </div>

      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>System</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>Notification Preferences</span>
              </CardTitle>
              <CardDescription>
                Configure which database events trigger notifications for you. These settings control when you receive notifications about platform activity.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {MONITORED_TABLES.map((tableInfo) => {
                const pref = getPreferenceForTable(tableInfo.table_name)
                const IconComponent = tableInfo.icon

                return (
                  <div key={tableInfo.table_name} className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <IconComponent className="h-5 w-5 text-gray-600" />
                      <div className="flex-1">
                        <h3 className="font-medium">{tableInfo.display_name}</h3>
                        <p className="text-sm text-gray-500">{tableInfo.description}</p>
                      </div>
                      {!pref && (
                        <Badge variant="outline" className="text-xs">
                          Not configured
                        </Badge>
                      )}
                    </div>

                    {pref && (
                      <div className="ml-8 space-y-3">
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <Label htmlFor={`${tableInfo.table_name}-insert`} className="text-sm font-medium">
                              New Records
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              Get notified when new {tableInfo.display_name.toLowerCase()} are created
                            </p>
                          </div>
                          <Switch
                            id={`${tableInfo.table_name}-insert`}
                            checked={pref.notify_insert}
                            onCheckedChange={(checked) =>
                              updatePreference(tableInfo.table_name, 'notify_insert', checked)
                            }
                            disabled={saving}
                          />
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <Label htmlFor={`${tableInfo.table_name}-update`} className="text-sm font-medium">
                              Updates
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              Get notified when existing {tableInfo.display_name.toLowerCase()} are modified
                            </p>
                          </div>
                          <Switch
                            id={`${tableInfo.table_name}-update`}
                            checked={pref.notify_update}
                            onCheckedChange={(checked) =>
                              updatePreference(tableInfo.table_name, 'notify_update', checked)
                            }
                            disabled={saving}
                          />
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <Label htmlFor={`${tableInfo.table_name}-delete`} className="text-sm font-medium">
                              Deletions
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              Get notified when {tableInfo.display_name.toLowerCase()} are deleted (rarely used)
                            </p>
                          </div>
                          <Switch
                            id={`${tableInfo.table_name}-delete`}
                            checked={pref.notify_delete}
                            onCheckedChange={(checked) =>
                              updatePreference(tableInfo.table_name, 'notify_delete', checked)
                            }
                            disabled={saving}
                          />
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <Label htmlFor={`${tableInfo.table_name}-approval`} className="text-sm font-medium">
                              Approval Changes
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              Get notified when approval status changes (approved/rejected)
                            </p>
                          </div>
                          <Switch
                            id={`${tableInfo.table_name}-approval`}
                            checked={pref.notify_approval_changes}
                            onCheckedChange={(checked) =>
                              updatePreference(tableInfo.table_name, 'notify_approval_changes', checked)
                            }
                            disabled={saving}
                          />
                        </div>
                      </div>
                    )}

                    {tableInfo.table_name !== MONITORED_TABLES[MONITORED_TABLES.length - 1].table_name && (
                      <Separator />
                    )}
                  </div>
                )
              })}


            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>System Settings</span>
              </CardTitle>
              <CardDescription>
                System-wide configuration and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>System settings will be implemented in a future update.</p>
                <p className="text-sm mt-2">This will include database maintenance tools, system monitoring, and global configuration options.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
