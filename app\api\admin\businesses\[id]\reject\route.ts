import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has admin permissions
    const authClient = await createServerSupabase()

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: profileError } = await authClient
      .from("users")
      .select("role")
      .eq("email", session.user.email)
      .single()

    if (profileError || !userProfile) {
      console.error("Error fetching user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", session.user.email, "with role:", userProfile.role)
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      )
    }

    console.log("Admin access verified for user:", session.user.email, "with role:", userProfile.role)

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const businessId = parseInt(params.id)
    console.log("API: Rejecting business with ID:", businessId)

    // Use the admin client to bypass RLS
    const { error } = await supabase
      .from('businesses')
      .update({ is_approved: false })
      .eq('id', businessId)

    if (error) {
      console.error("API: Error rejecting business:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log("API: Business rejected successfully")
    return NextResponse.json({
      success: true,
      message: "Business rejected successfully"
    })
  } catch (error: any) {
    console.error("API: Error in business rejection:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
