import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// PHASE 2 STEP 3: GET endpoint to fetch all orders for a session (multi-business orders)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;
    
    console.log('🔍 SESSION ORDERS API: Fetching orders for session:', sessionId);

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Fetch all orders for this session
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        business_name,
        business_type,
        customer_name,
        customer_phone,
        customer_email,
        delivery_address,
        delivery_method,
        delivery_type,
        scheduled_time,
        subtotal,
        delivery_fee,
        service_fee,
        total,
        status,
        preparation_time,
        estimated_delivery_time,
        delivery_fulfillment,
        created_at,
        cart_id
      `)
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });

    if (ordersError) {
      console.error('❌ SESSION ORDERS API: Error fetching orders:', ordersError);
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: ordersError.message },
        { status: 500 }
      );
    }

    if (!orders || orders.length === 0) {
      console.warn('⚠️ SESSION ORDERS API: No orders found for session:', sessionId);
      return NextResponse.json(
        { error: 'No orders found for session' },
        { status: 404 }
      );
    }

    console.log(`✅ SESSION ORDERS API: Found ${orders.length} orders for session`);

    // Fetch cart items for each order
    const ordersWithItems = await Promise.all(
      orders.map(async (order) => {
        if (!order.cart_id) {
          console.warn(`⚠️ No cart_id for order ${order.id}, skipping cart items`);
          return { ...order, cart_items: [] };
        }

        const { data: cartItems, error: itemsError } = await supabase
          .from('cart_items')
          .select(`
            id,
            product_id,
            name,
            price,
            quantity,
            business_id,
            image_url,
            variant_id,
            businesses!inner(name)
          `)
          .eq('cart_id', order.cart_id);

        if (itemsError) {
          console.warn(`⚠️ Error fetching cart items for order ${order.id}:`, itemsError);
          return { ...order, cart_items: [] };
        }

        // Fetch customizations for each cart item
        const itemsWithCustomizations = await Promise.all(
          (cartItems || []).map(async (item) => {
            const { data: customizations, error: customError } = await supabase
              .from('cart_item_customizations')
              .select(`
                id,
                customization_option_id,
                customization_name,
                customization_price
              `)
              .eq('cart_item_id', item.id);

            if (customError) {
              console.warn(`⚠️ Error fetching customizations for cart item ${item.id}:`, customError);
            }

            return {
              ...item,
              business_name: item.businesses?.name || order.business_name,
              customizations: customizations || [],
              variantName: null, // Will be populated if needed
              options: [] // Will be populated if needed
            };
          })
        );

        return { ...order, cart_items: itemsWithCustomizations };
      })
    );

    return NextResponse.json({
      success: true,
      sessionId: sessionId,
      orderCount: orders.length,
      orders: ordersWithItems
    });

  } catch (error: any) {
    console.error('❌ SESSION ORDERS API: Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
