"use client"

import { useEffect, useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { AlertCircle, Clock } from "lucide-react"

interface BusinessClosedDialogProps {
  isOpen: boolean
  onClose: () => void
  businessName: string
  closureMessage?: string
}

export function BusinessClosedDialog({
  isOpen,
  onClose,
  businessName,
  closureMessage
}: BusinessClosedDialogProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {businessName} is Currently Closed
              </DialogTitle>
            </div>
          </div>
          <DialogDescription className="text-gray-600">
            This business is temporarily not accepting new orders, but you can still browse their menu.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          {closureMessage && closureMessage.trim() && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <Clock className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900 mb-1">
                    Message from {businessName}
                  </h4>
                  <p className="text-sm text-blue-800">
                    {closureMessage}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              What you can do:
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Browse their menu to see what's available</li>
              <li>• Check back later when they reopen for ordering</li>
              <li>• Explore other businesses on Loop Jersey</li>
            </ul>
          </div>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="outline"
            className="flex-1"
          >
            Browse Menu
          </Button>
          <Button
            onClick={() => window.history.back()}
            className="bg-emerald-600 hover:bg-emerald-700 text-white flex-1"
          >
            Go Back
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
