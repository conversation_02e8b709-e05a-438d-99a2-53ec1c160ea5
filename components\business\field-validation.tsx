"use client"

import { CheckCircle, AlertCircle, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface FieldValidationProps {
  value: any
  rules: ValidationRule[]
  className?: string
}

interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom'
  message: string
  value?: any
  validator?: (value: any) => boolean
}

export default function FieldValidation({ value, rules, className = "" }: FieldValidationProps) {
  const validateField = (value: any, rules: ValidationRule[]) => {
    const results = rules.map(rule => {
      let isValid = true
      let message = rule.message

      switch (rule.type) {
        case 'required':
          isValid = value !== null && value !== undefined && value !== ''
          break
        case 'minLength':
          isValid = value && value.length >= rule.value
          break
        case 'maxLength':
          isValid = !value || value.length <= rule.value
          break
        case 'pattern':
          isValid = !value || rule.value.test(value)
          break
        case 'custom':
          isValid = !value || (rule.validator && rule.validator(value))
          break
      }

      return { isValid, message, type: rule.type }
    })

    return results
  }

  const validationResults = validateField(value, rules)
  const hasErrors = validationResults.some(result => !result.isValid)
  const hasValue = value !== null && value !== undefined && value !== ''

  if (!hasValue && !hasErrors) {
    return null
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {validationResults.map((result, index) => {
        if (result.isValid && hasValue) {
          return (
            <div key={index} className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="h-3 w-3" />
              <span>{result.message}</span>
            </div>
          )
        } else if (!result.isValid) {
          return (
            <div key={index} className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="h-3 w-3" />
              <span>{result.message}</span>
            </div>
          )
        }
        return null
      })}
    </div>
  )
}

// Predefined validation rules for common business fields
export const businessValidationRules = {
  businessName: [
    { type: 'required' as const, message: 'Business name is required' },
    { type: 'minLength' as const, value: 2, message: 'Name should be at least 2 characters' },
    { type: 'maxLength' as const, value: 100, message: 'Name should be less than 100 characters' }
  ],
  description: [
    { type: 'required' as const, message: 'Business description is required' },
    { type: 'minLength' as const, value: 20, message: 'Description should be at least 20 characters' },
    { type: 'maxLength' as const, value: 500, message: 'Description should be less than 500 characters' }
  ],
  phone: [
    { type: 'required' as const, message: 'Phone number is required' },
    {
      type: 'pattern' as const,
      value: /^(\+44|0)[1-9]\d{8,10}$/,
      message: 'Please enter a valid UK phone number'
    }
  ],

  address: [
    { type: 'required' as const, message: 'Business address is required' },
    { type: 'minLength' as const, value: 10, message: 'Please enter a complete address' }
  ],
  postcode: [
    { type: 'required' as const, message: 'Jersey postcode is required' },
    {
      type: 'pattern' as const,
      value: /^JE[1-5]\s?\d[A-Z]{2}$/i,
      message: 'Please enter a valid Jersey postcode (e.g., JE2 4WF)'
    }
  ],
  deliveryRadius: [
    { type: 'required' as const, message: 'Delivery radius is required' },
    {
      type: 'custom' as const,
      message: 'Delivery radius should be between 1 and 20 km',
      validator: (value: number) => value >= 1 && value <= 20
    }
  ],
  minimumOrder: [
    { type: 'required' as const, message: 'Minimum order amount is required' },
    {
      type: 'custom' as const,
      message: 'Minimum order should be between £5 and £50',
      validator: (value: number) => value >= 5 && value <= 50
    }
  ],
  deliveryFee: [
    { type: 'required' as const, message: 'Delivery fee is required' },
    {
      type: 'custom' as const,
      message: 'Delivery fee should be between £0 and £10',
      validator: (value: number) => value >= 0 && value <= 10
    }
  ],
  preparationTime: [
    { type: 'required' as const, message: 'Preparation time is required' },
    {
      type: 'custom' as const,
      message: 'Preparation time should be between 5 and 120 minutes',
      validator: (value: number) => value >= 5 && value <= 120
    }
  ]
}

// Helper component for showing field tips
interface FieldTipProps {
  children: React.ReactNode
  className?: string
}

export function FieldTip({ children, className = "" }: FieldTipProps) {
  return (
    <div className={`flex items-start gap-2 text-sm text-blue-600 bg-blue-50 p-2 rounded ${className}`}>
      <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
      <div>{children}</div>
    </div>
  )
}

// Helper component for showing field examples
interface FieldExampleProps {
  children: React.ReactNode
  className?: string
}

export function FieldExample({ children, className = "" }: FieldExampleProps) {
  return (
    <div className={`text-sm text-gray-600 bg-gray-50 p-2 rounded border-l-2 border-gray-300 ${className}`}>
      <span className="font-medium">Example: </span>
      {children}
    </div>
  )
}
