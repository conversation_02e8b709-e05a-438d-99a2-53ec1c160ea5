"use client"

import { useState, useEffect } from 'react'
import { Package, MapPin, Clock, DollarSign, Star, ChevronRight } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DriverDashboardHeader } from '@/components/driver-dashboard-header'
import { DriverMobileNavigation } from '@/components/driver-mobile-navigation'
import { DriverMobileContainer } from '@/components/driver-mobile-container'
import { OrderWeightChart } from '@/components/driver/order-weight-chart'
import { SwipeableOrderCard } from '@/components/driver/swipeable-order-card'
import { EnhancedMobileRealtime } from '@/components/enhanced-mobile-realtime'
import { useSupabaseRealtime } from '@/hooks/use-supabase-realtime'
import { toast } from 'sonner'
import Link from 'next/link'

interface Order {
  id: number
  order_number: string
  business_name: string
  customer_name: string
  delivery_address: string
  postcode: string
  parish: string
  delivery_type: string
  total: number
  delivery_fee: number
  status: string
  distance?: number
  delivery_distance_km?: number
  estimated_delivery_time?: string | number
  itemCount?: number
  totalItems?: number
  created_at: string
  updated_at: string
}

interface OrdersData {
  availableOrders: Order[]
  currentOrder: Order | null
  isOnDelivery: boolean
}

interface DriverDashboardData {
  driver: {
    id: string
    authId: string
    name: string
    isVerified: boolean
    isActive: boolean
    vehicleType: string
    totalDeliveries: number
    averageRating: number
    memberSince: string
    averageDeliveriesPerDay: number
  }
  status: {
    isOnline: boolean
    isOnDelivery: boolean
    isOnShift?: boolean
    lastStatusChange: string | null
    hasLocation: boolean
    locationUpdatedAt: string | null
  }
  recentDeliveries: any[]
}

export default function DriverMobileOrdersPage() {
  const [ordersData, setOrdersData] = useState<OrdersData | null>(null)
  const [dashboardData, setDashboardData] = useState<DriverDashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedOrders, setExpandedOrders] = useState<Set<number>>(new Set())
  const [orderItems, setOrderItems] = useState<Record<number, any[]>>({})
  const [loadingItems, setLoadingItems] = useState<Set<number>>(new Set())
  const [processingOrders, setProcessingOrders] = useState<Set<number>>(new Set())

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  // Fetch driver dashboard data to get status
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/driver/dashboard')
      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.statusText}`)
      }
      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load driver status')
    }
  }

  // Real-time subscription for orders
  const {
    data: realtimeOrders,
    loading: realtimeLoading,
    error: realtimeError,
  } = useSupabaseRealtime<any>(
    'orders',
    `
      id,
      order_number,
      business_id,
      business_name,
      customer_name,
      customer_phone,
      delivery_address,
      postcode,
      parish,
      delivery_type,
      total,
      delivery_fee,
      status,
      driver_id,
      preparation_time,
      estimated_delivery_time,
      ready_time,
      created_at,
      cart_id,
      delivery_distance_km,
      businesses!inner (
        id,
        name,
        address,
        location,
        phone,
        latitude,
        coordinates
      )
    `,
    [
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.offered' },
      { table: 'orders', event: 'INSERT', filter: 'status=eq.offered' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.assigned' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.picked_up' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.out_for_delivery' },
      { table: 'orders', event: 'DELETE' },
    ],
    {
      orderBy: 'created_at',
      orderDirection: 'desc'
    }
  )

  // Fallback API fetch for initial data and error recovery
  const fetchOrdersData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/driver/available-orders')

      if (!response.ok) {
        throw new Error(`Failed to fetch orders: ${response.statusText}`)
      }

      const data = await response.json()
      setOrdersData(data)
    } catch (err) {
      console.error('Error fetching orders:', err)
      setError(err instanceof Error ? err.message : 'Failed to load orders')
    } finally {
      setIsLoading(false)
    }
  }

  // Update orders data when real-time data changes
  useEffect(() => {
    if (realtimeOrders && !realtimeLoading) {
      // Filter for offered orders with no driver assigned
      const offeredOrders = realtimeOrders.filter(order =>
        order.status === 'offered' && !order.driver_id
      )

      // Filter for current order (assigned to this driver)
      const currentOrder = realtimeOrders.find(order =>
        order.driver_id && ['assigned', 'picked_up', 'out_for_delivery'].includes(order.status)
      )

      // Update orders state
      const newOrdersData: OrdersData = {
        availableOrders: offeredOrders.map(order => ({
          ...order,
          itemCount: 0, // Will be calculated when expanded
          totalItems: 0, // Will be calculated when expanded
          distance: order.delivery_distance_km || null
        })),
        currentOrder: currentOrder || null,
        isOnDelivery: !!currentOrder
      }

      // Show toast for new orders (only if we already have data loaded)
      if (ordersData && offeredOrders.length > (ordersData.availableOrders?.length || 0)) {
        const newOrdersCount = offeredOrders.length - (ordersData.availableOrders?.length || 0)
        if (newOrdersCount > 0) {
          toast.success(`${newOrdersCount} new order${newOrdersCount > 1 ? 's' : ''} available!`, {
            description: "Check the available orders below.",
            duration: 4000,
          })
        }
      }

      setOrdersData(newOrdersData)
      setIsLoading(false)
      setError(null)
    }
  }, [realtimeOrders, realtimeLoading])

  // Handle realtime errors
  useEffect(() => {
    if (realtimeError) {
      console.error('Realtime error:', realtimeError)
      setError('Connection error. Trying to reconnect...')
      // Fallback to API fetch if realtime fails
      fetchOrdersData()
    }
  }, [realtimeError])

  // Initial load - fetch both orders and dashboard data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        await Promise.all([fetchOrdersData(), fetchDashboardData()])
      } catch (err) {
        console.error('Error loading initial data:', err)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Fallback if realtime takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      if (realtimeLoading && !ordersData) {
        console.log('Realtime taking too long, falling back to API')
        fetchOrdersData()
      }
    }, 3000) // 3 second timeout

    return () => clearTimeout(timer)
  }, [realtimeLoading, ordersData])

  // Toggle order expansion and fetch items if needed
  const toggleOrderExpansion = async (orderId: number) => {
    const newExpandedOrders = new Set(expandedOrders)

    if (expandedOrders.has(orderId)) {
      newExpandedOrders.delete(orderId)
    } else {
      newExpandedOrders.add(orderId)

      // Fetch order items if not already loaded
      if (!orderItems[orderId]) {
        const newLoadingItems = new Set(loadingItems)
        newLoadingItems.add(orderId)
        setLoadingItems(newLoadingItems)

        try {
          const response = await fetch(`/api/driver/orders/${orderId}/items`)
          if (response.ok) {
            const data = await response.json()
            setOrderItems(prev => ({
              ...prev,
              [orderId]: data.items || []
            }))
          } else {
            console.error('Failed to fetch order items:', response.statusText)
            toast.error("Failed to load order items", {
              description: "Please try again.",
              duration: 3000,
            })
          }
        } catch (err) {
          console.error('Error fetching order items:', err)
          toast.error("Failed to load order items", {
            description: "Please check your connection and try again.",
            duration: 3000,
          })
        } finally {
          const newLoadingItems = new Set(loadingItems)
          newLoadingItems.delete(orderId)
          setLoadingItems(newLoadingItems)
        }
      }
    }

    setExpandedOrders(newExpandedOrders)
  }

  // Accept an order
  const acceptOrder = async (orderId: number) => {
    const newProcessingOrders = new Set(processingOrders)
    newProcessingOrders.add(orderId)
    setProcessingOrders(newProcessingOrders)

    try {
      const response = await fetch('/api/driver/accept-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to accept order: ${response.statusText}`)
      }

      const result = await response.json()

      // Refresh orders data
      await fetchOrdersData()

      // Show success message
      toast.success("Order accepted!", {
        description: "You can now start the delivery.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error accepting order:', err)
      toast.error("Failed to accept order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    } finally {
      const newProcessingOrders = new Set(processingOrders)
      newProcessingOrders.delete(orderId)
      setProcessingOrders(newProcessingOrders)
    }
  }

  // Decline an order
  const declineOrder = async (orderId: number) => {
    const newProcessingOrders = new Set(processingOrders)
    newProcessingOrders.add(orderId)
    setProcessingOrders(newProcessingOrders)

    try {
      const response = await fetch('/api/driver/decline-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to decline order: ${response.statusText}`)
      }

      const result = await response.json()

      // Remove the declined order from the UI immediately
      if (ordersData) {
        const updatedOrders = ordersData.availableOrders.filter(order => order.id !== orderId)
        setOrdersData({
          ...ordersData,
          availableOrders: updatedOrders
        })
      }

      // Also refresh from server to ensure consistency
      await fetchOrdersData()

      // Show success message
      toast.success("Order declined", {
        description: "The order has been returned to the pool.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error declining order:', err)
      toast.error("Failed to decline order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    } finally {
      const newProcessingOrders = new Set(processingOrders)
      newProcessingOrders.delete(orderId)
      setProcessingOrders(newProcessingOrders)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'offered':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'assigned':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'picked_up':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'out_for_delivery':
        return 'bg-orange-50 text-orange-700 border-orange-200'
      case 'delivered':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  if (isLoading || (realtimeLoading && !ordersData) || !dashboardData) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
          </div>
        </div>
      </DriverMobileContainer>
    )
  }

  if (error) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6 text-center">
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={fetchOrdersData} variant="outline" className="border-red-200 text-red-700 hover:bg-red-100">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </DriverMobileContainer>
    )
  }

  if (!ordersData) {
    return null
  }

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader
        driver={dashboardData.driver}
        status={dashboardData.status}
      />

      <div className="p-4 space-y-4">
        {/* Enhanced Real-time Features - Only when online */}
        {dashboardData.status.isOnline && (
          <EnhancedMobileRealtime
            driverId={parseInt(dashboardData.driver.id)}
            availableOrdersCount={ordersData?.availableOrders?.length || 0}
            isOnShift={dashboardData.status.isOnShift || false}
            onNewOrder={() => {
              // Refresh available orders when new order comes in
              fetchOrdersData()
            }}
            onOrderUpdate={() => {
              // Refresh orders data when order updates
              fetchOrdersData()
            }}
          />
        )}
          {/* Current Order */}
          {ordersData.currentOrder && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Current Delivery</h3>
              <Card className="border-2 border-blue-500 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="font-semibold text-blue-900">ACTIVE</span>
                    </div>
                    <Badge className="bg-blue-600 text-white">
                      {ordersData.currentOrder.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div>
                      <p className="text-sm text-blue-700">Order #{ordersData.currentOrder.order_number}</p>
                      <p className="font-medium text-blue-900">{ordersData.currentOrder.business_name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-blue-700">Deliver to:</p>
                      <p className="font-medium text-blue-900">{ordersData.currentOrder.customer_name}</p>
                      <p className="text-sm text-blue-800">{ordersData.currentOrder.parish}</p>
                    </div>
                  </div>

                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium" asChild>
                    <Link href={`/driver-mobile/deliveries/${ordersData.currentOrder.order_number}`}>
                      <Package className="h-4 w-4 mr-2" />
                      Continue Delivery
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Available Orders - Only when on shift */}
          {dashboardData.status.isOnShift && ordersData.availableOrders && ordersData.availableOrders.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-semibold text-gray-900">Available Orders</h3>
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${!realtimeLoading && !realtimeError ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
                    <span className="text-xs text-gray-500">Live</span>
                  </div>
                </div>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {ordersData.availableOrders.length} available
                </Badge>
              </div>

              <div className="space-y-2">
                {ordersData.availableOrders.map((order) => (
                  <SwipeableOrderCard
                    key={order.id}
                    order={order}
                    isExpanded={expandedOrders.has(order.id)}
                    onToggleExpansion={() => toggleOrderExpansion(order.id)}
                    onAccept={() => acceptOrder(order.id)}
                    onDecline={() => declineOrder(order.id)}
                    orderItems={orderItems[order.id]}
                    loadingItems={loadingItems.has(order.id)}
                    formatCurrency={formatCurrency}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Order Count Preview - When online but not on shift */}
          {dashboardData.status.isOnline && !dashboardData.status.isOnShift && ordersData.availableOrders && ordersData.availableOrders.length > 0 && (
            <Card className="border-2 border-blue-200 bg-blue-50">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Package className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <div className="text-2xl font-bold text-blue-900">
                      {ordersData.availableOrders.length}
                    </div>
                    <div className="text-sm text-blue-700 font-medium">
                      Order{ordersData.availableOrders.length !== 1 ? 's' : ''} Available
                    </div>
                  </div>
                </div>
                <p className="text-sm text-blue-600">
                  Start your shift to accept orders
                </p>
              </CardContent>
            </Card>
          )}

          {/* Previous Deliveries - When not on shift */}
          {(!dashboardData.status.isOnShift || !ordersData.availableOrders?.length) && dashboardData.recentDeliveries && dashboardData.recentDeliveries.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Recent Deliveries</h3>
              <div className="space-y-2">
                {dashboardData.recentDeliveries.slice(0, 5).map((delivery) => (
                  <Card key={delivery.id} className="bg-white border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            delivery.status === 'delivered' ? 'bg-green-500' : 'bg-blue-500'
                          }`}></div>
                          <div>
                            <p className="font-medium text-gray-900">#{delivery.order_number}</p>
                            <p className="text-sm text-gray-600">{delivery.business_name}</p>
                            <p className="text-xs text-gray-500 capitalize">{delivery.status.replace('_', ' ')}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">{formatCurrency(delivery.delivery_fee)}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(delivery.updated_at).toLocaleDateString('en-GB')}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* No Orders Available */}
          {(!ordersData.availableOrders || ordersData.availableOrders.length === 0) && !ordersData.currentOrder && (
            <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Package className="h-8 w-8 text-gray-400" />
                </div>
                <p className="font-medium text-gray-700 mb-2">No orders available</p>
                <p className="text-sm text-gray-500 mb-4">You'll be notified when new delivery requests come in</p>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-xs text-blue-700">
                    💡 Make sure you're online in your dashboard to receive orders
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bottom Spacing for Navigation */}
          <div className="h-20"></div>
        </div>

        <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
