"use client"

import { useState, useRef, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  ChevronUp,
  ChevronDown,
  MapPin,
  Clock,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { OrderWeightChart } from '@/components/driver/order-weight-chart'

interface SwipeableOrderCardProps {
  order: any
  isExpanded: boolean
  onToggleExpansion: () => void
  onAccept: () => void
  onDecline: () => void
  orderItems?: any[]
  loadingItems?: boolean
  formatCurrency: (amount: number) => string
}

export function SwipeableOrderCard({
  order,
  isExpanded,
  onToggleExpansion,
  onAccept,
  onDecline,
  orderItems = [],
  loadingItems = false,
  formatCurrency
}: SwipeableOrderCardProps) {
  // Debug logging (only when needed)
  // console.log('SwipeableOrderCard rendered with:', {
  //   orderId: order.id,
  //   isExpanded,
  //   totalItems: order.totalItems,
  //   itemCount: order.itemCount,
  //   onToggleExpansion: typeof onToggleExpansion
  // })
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null)
  const cardRef = useRef<HTMLDivElement>(null)
  const startX = useRef(0)
  const currentX = useRef(0)

  const SWIPE_THRESHOLD = 120 // pixels to trigger action
  const MAX_SWIPE = 200 // maximum swipe distance

  // Handle touch start
  const handleTouchStart = (e: React.TouchEvent) => {
    if (isExpanded) return // Don't allow swipe when expanded

    startX.current = e.touches[0].clientX
    currentX.current = e.touches[0].clientX
    // Don't set isDragging immediately - wait for movement
  }

  // Handle touch move
  const handleTouchMove = (e: React.TouchEvent) => {
    if (isExpanded) return

    currentX.current = e.touches[0].clientX
    const deltaX = currentX.current - startX.current

    // Only start dragging if movement is significant (more than 15px for touch)
    if (!isDragging && Math.abs(deltaX) > 15) {
      setIsDragging(true)
    }

    if (!isDragging) return

    // Limit swipe distance
    const limitedDelta = Math.max(-MAX_SWIPE, Math.min(MAX_SWIPE, deltaX))
    setSwipeOffset(limitedDelta)

    // Determine swipe direction
    if (Math.abs(limitedDelta) > 20) {
      setSwipeDirection(limitedDelta > 0 ? 'right' : 'left')
    } else {
      setSwipeDirection(null)
    }
  }

  // Handle touch end
  const handleTouchEnd = () => {
    if (!isDragging || isExpanded) {
      // Reset state and allow click
      setIsDragging(false)
      setSwipeOffset(0)
      setSwipeDirection(null)
      return
    }

    const deltaX = currentX.current - startX.current

    // Check if swipe threshold is met
    if (Math.abs(deltaX) >= SWIPE_THRESHOLD) {
      // Add haptic feedback for mobile devices
      if ('vibrate' in navigator) {
        navigator.vibrate(50) // Short vibration
      }

      if (deltaX > 0) {
        // Swipe right - Accept
        onAccept()
      } else {
        // Swipe left - Decline
        onDecline()
      }
    }

    // Reset state
    setSwipeOffset(0)
    setSwipeDirection(null)
    setIsDragging(false)
  }

  // Handle mouse events for desktop testing - less aggressive
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isExpanded) return

    // Only start tracking if it's a left click
    if (e.button !== 0) return

    startX.current = e.clientX
    currentX.current = e.clientX
    // Don't set isDragging immediately - wait for significant movement
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isExpanded) return

    currentX.current = e.clientX
    const deltaX = currentX.current - startX.current

    // Only start dragging if movement is significant (more than 20px for desktop)
    if (!isDragging && Math.abs(deltaX) > 20) {
      setIsDragging(true)
      console.log('Started dragging on desktop')
    }

    if (!isDragging) return

    const limitedDelta = Math.max(-MAX_SWIPE, Math.min(MAX_SWIPE, deltaX))
    setSwipeOffset(limitedDelta)

    if (Math.abs(limitedDelta) > 30) {
      setSwipeDirection(limitedDelta > 0 ? 'right' : 'left')
    } else {
      setSwipeDirection(null)
    }
  }

  const handleMouseUp = () => {
    // Always reset state first
    const wasDragging = isDragging
    setIsDragging(false)
    setSwipeOffset(0)
    setSwipeDirection(null)

    if (!wasDragging || isExpanded) {
      // This was a click, not a drag
      return
    }

    const deltaX = currentX.current - startX.current

    if (Math.abs(deltaX) >= SWIPE_THRESHOLD) {
      // Add haptic feedback for mobile devices (even when using mouse)
      if ('vibrate' in navigator) {
        navigator.vibrate(50) // Short vibration
      }

      if (deltaX > 0) {
        onAccept()
      } else {
        onDecline()
      }
    }
  }

  // Add mouse leave handler to reset state
  const handleMouseLeave = () => {
    setSwipeOffset(0)
    setSwipeDirection(null)
    setIsDragging(false)
  }

  // Calculate background color based on swipe
  const getBackgroundColor = () => {
    if (!isDragging || !swipeDirection) return 'bg-blue-50'

    const intensity = Math.min(Math.abs(swipeOffset) / SWIPE_THRESHOLD, 1)

    if (swipeDirection === 'right') {
      // Green for accept
      if (intensity > 0.8) return 'bg-green-200'
      if (intensity > 0.5) return 'bg-green-100'
      return 'bg-green-50'
    } else {
      // Red for decline
      if (intensity > 0.8) return 'bg-red-200'
      if (intensity > 0.5) return 'bg-red-100'
      return 'bg-red-50'
    }
  }

  // Calculate border color
  const getBorderColor = () => {
    if (!isDragging || !swipeDirection) return 'border-blue-200'

    const intensity = Math.min(Math.abs(swipeOffset) / SWIPE_THRESHOLD, 1)

    if (swipeDirection === 'right') {
      return intensity > 0.5 ? 'border-green-400' : 'border-green-200'
    } else {
      return intensity > 0.5 ? 'border-red-400' : 'border-red-200'
    }
  }

  // Show action hint
  const showActionHint = () => {
    if (!isDragging || !swipeDirection) return null

    const intensity = Math.abs(swipeOffset) / SWIPE_THRESHOLD
    const isTriggered = intensity >= 1

    return (
      <div className={`absolute inset-0 flex items-center justify-center pointer-events-none transition-opacity duration-200 ${
        intensity > 0.3 ? 'opacity-100' : 'opacity-0'
      }`}>
        <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${
          swipeDirection === 'right'
            ? `bg-green-600 text-white ${isTriggered ? 'scale-110' : 'scale-100'}`
            : `bg-red-600 text-white ${isTriggered ? 'scale-110' : 'scale-100'}`
        } transition-transform duration-200`}>
          {swipeDirection === 'right' ? (
            <>
              <CheckCircle className="h-5 w-5" />
              <span className="font-semibold">{isTriggered ? 'Release to Accept' : 'Accept Order'}</span>
            </>
          ) : (
            <>
              <XCircle className="h-5 w-5" />
              <span className="font-semibold">{isTriggered ? 'Release to Decline' : 'Decline Order'}</span>
            </>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      <Card
        ref={cardRef}
        className={`border-l-4 border-l-blue-500 ${getBorderColor()} ${getBackgroundColor()} transition-all duration-200 ${
          isDragging ? 'shadow-lg scale-[1.02]' : 'shadow-sm'
        } ${!isExpanded ? 'cursor-grab active:cursor-grabbing' : ''}`}
        style={{
          transform: `translateX(${swipeOffset}px)`,
          transition: isDragging ? 'none' : 'transform 0.3s ease-out'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      >
        <CardContent className="p-4 relative">
          {/* Swipe Action Hint */}
          {showActionHint()}

          <div
            className={`cursor-pointer hover:bg-blue-50 active:bg-blue-100 transition-colors duration-150 ${isDragging ? 'pointer-events-none' : ''}`}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (!isDragging) {
                console.log('Clicking to toggle expansion for order:', order.id)
                onToggleExpansion()
              }
            }}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="font-medium text-blue-900">#{order.order_number}</span>
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4 text-blue-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-blue-600" />
                )}
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-blue-700">{order.totalItems || 0} items</div>
                <div className="text-xs text-blue-600">{formatCurrency(order.delivery_fee)} delivery fee</div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-xs text-blue-600 font-medium">PICKUP</p>
                <p className="text-sm font-semibold text-blue-900">{order.business_name}</p>
                <p className="text-xs text-blue-700">{order.businesses?.location || 'Business Location'}</p>
              </div>
              <div>
                <p className="text-xs text-blue-600 font-medium">DELIVER TO</p>
                <p className="text-sm font-semibold text-blue-900">{order.customer_name}</p>
                <p className="text-xs text-blue-700">{order.parish}</p>
              </div>
            </div>

            <div className="flex items-center justify-between mb-3 text-xs">
              <div className="flex items-center space-x-3">
                {(order.delivery_distance_km || order.distance) && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-3 w-3 text-blue-600" />
                    <span className="text-blue-700 font-medium">
                      {order.delivery_distance_km ? `${order.delivery_distance_km.toFixed(1)} km` : `${order.distance} km`}
                    </span>
                  </div>
                )}
                {(order.estimated_delivery_time || order.estimatedPickupTime) && (
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-blue-600" />
                    <span className="text-blue-700 font-medium">
                      {order.estimated_delivery_time ?
                        (typeof order.estimated_delivery_time === 'string' ?
                          order.estimated_delivery_time :
                          `${order.estimated_delivery_time} min`) :
                        `${order.estimatedPickupTime} min`}
                    </span>
                  </div>
                )}
              </div>
              <div className="text-blue-600 font-medium">
                {order.itemCount || 0} different items
              </div>
            </div>
          </div>

          {/* Expanded order details */}
          {isExpanded && (
            <div className="mt-4 pt-4 border-t border-blue-200 space-y-4">
              {/* Order Weight Chart */}
              <div>
                <h4 className="text-sm font-semibold text-blue-900 mb-2">Delivery Overview:</h4>
                <OrderWeightChart
                  orderId={order.id}
                  compact={true}
                  className="bg-white border border-blue-200 rounded-lg"
                />
              </div>

              {/* Order Items */}
              <div>
                <h4 className="text-sm font-semibold text-blue-900 mb-2">Order Items:</h4>
                {loadingItems ? (
                  <div className="flex items-center justify-center py-4">
                    <RefreshCw className="h-4 w-4 animate-spin text-blue-600 mr-2" />
                    <span className="text-sm text-blue-700">Loading items...</span>
                  </div>
                ) : orderItems && orderItems.length > 0 ? (
                  <div className="space-y-2">
                    {orderItems.map((item, index) => (
                      <div key={index} className="flex justify-between items-center bg-white p-2 rounded border">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{item.name}</p>
                          {item.customizations && item.customizations.length > 0 && (
                            <div className="text-xs text-gray-600 mt-1">
                              {item.customizations.map((custom: any, idx: number) => (
                                <span key={idx} className="inline-block mr-2">
                                  {custom.customization_group_name}: {custom.customization_option_name}
                                </span>
                              ))}
                            </div>
                          )}
                          {item.special_instructions && (
                            <p className="text-xs text-gray-600 italic mt-1">Note: {item.special_instructions}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-semibold text-gray-900">×{item.quantity}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-4">
                    <AlertCircle className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-500">No items found</span>
                  </div>
                )}
              </div>

              {/* Fallback buttons for accessibility */}
              <div className="mt-4 grid grid-cols-2 gap-2">
                <Button
                  className="bg-green-600 hover:bg-green-700 text-white font-medium"
                  onClick={(e) => {
                    e.stopPropagation()
                    onAccept()
                  }}
                >
                  Accept
                </Button>
                <Button
                  variant="outline"
                  className="border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDecline()
                  }}
                >
                  Decline
                </Button>
              </div>
            </div>
          )}

          {/* Swipe Instructions (only show when not expanded and not dragging) */}
          {!isExpanded && !isDragging && (
            <div className="mt-3 text-center">
              <p className="text-xs text-gray-500">
                ← Swipe left to decline • Swipe right to accept →
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
