import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Check if we have the required environment variables
if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables for Supabase client');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const businessId = searchParams.get('businessId');
  const attributeType = searchParams.get('attributeType');

  if (!businessId) {
    return NextResponse.json(
      { error: 'Business ID is required' },
      { status: 400 }
    );
  }

  try {
    console.log(`Fetching attributes for business ${businessId}${attributeType ? ` with type ${attributeType}` : ''}`);

    let query = supabase
      .from('business_attributes')
      .select('attribute_type, attribute_value');

    // Add filters if provided
    if (businessId) {
      query = query.eq('business_id', businessId);
    }

    if (attributeType) {
      query = query.eq('attribute_type', attributeType);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching business attributes:', error);

      // Check if it's an RLS policy error
      if (error.code === '42P17') {
        console.error('RLS policy recursion error. Using service role key should bypass this.');
      }

      return NextResponse.json(
        { error: 'Failed to fetch business attributes', details: error.message },
        { status: 500 }
      );
    }

    console.log(`Found ${data?.length || 0} attributes for business ${businessId}`);

    return NextResponse.json({
      attributes: data || [],
      message: `Found ${data?.length || 0} attributes for business ${businessId}`
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
