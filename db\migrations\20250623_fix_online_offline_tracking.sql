-- Fix comprehensive online/offline and shift time tracking
-- Track every online/offline event and every shift start/end event
-- Uses TIMESTAMPTZ for proper Jersey (UK) timezone handling

-- 1. Add online/offline time tracking columns to driver_status
ALTER TABLE driver_status ADD COLUMN IF NOT EXISTS online_since TIMES<PERSON>MP WITH TIME ZONE;
ALTER TABLE driver_status ADD COLUMN IF NOT EXISTS offline_since TIMESTAMP WITH TIME ZONE;

-- 2. <PERSON>reate table to track all online/offline events (history)
CREATE TABLE IF NOT EXISTS driver_online_history (
  id SERIAL PRIMARY KEY,
  driver_id UUID NOT NULL REFERENCES driver_profiles(id) ON DELETE CASCADE,
  event_type VARCHAR(10) NOT NULL CHECK (event_type IN ('online', 'offline')),
  event_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. <PERSON><PERSON> function to update online/offline status with history tracking
CREATE OR REPLACE FUNCTION update_driver_online_status(
  p_driver_id UUID,
  p_is_online BOOLEAN
) RETURNS VOID AS $$
BEGIN
  IF p_is_online THEN
    -- Going online: set online_since, clear offline_since
    UPDATE driver_status 
    SET 
      is_online = TRUE,
      online_since = NOW(),
      offline_since = NULL,
      last_status_change = NOW()
    WHERE driver_id = p_driver_id;
    
    -- Record online event in history
    INSERT INTO driver_online_history (driver_id, event_type, event_time)
    VALUES (p_driver_id, 'online', NOW());
    
  ELSE
    -- Going offline: set offline_since, clear online_since
    -- Also end any active shift when going offline
    UPDATE driver_status 
    SET 
      is_online = FALSE,
      offline_since = NOW(),
      online_since = NULL,
      is_on_shift = FALSE,  -- Cannot be on shift while offline
      last_status_change = NOW()
    WHERE driver_id = p_driver_id;
    
    -- End any active shift when going offline
    UPDATE driver_shifts 
    SET 
      shift_end = NOW(),
      updated_at = NOW()
    WHERE driver_id = p_driver_id AND shift_end IS NULL;
    
    -- Record offline event in history
    INSERT INTO driver_online_history (driver_id, event_type, event_time)
    VALUES (p_driver_id, 'offline', NOW());
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 4. Update start_driver_shift function to enforce online requirement
CREATE OR REPLACE FUNCTION start_driver_shift(p_driver_id UUID)
RETURNS INTEGER AS $$
DECLARE
  v_shift_id INTEGER;
  v_active_shift_count INTEGER;
  v_is_online BOOLEAN;
BEGIN
  -- Check if driver is online
  SELECT is_online INTO v_is_online
  FROM driver_status
  WHERE driver_id = p_driver_id;
  
  IF NOT COALESCE(v_is_online, FALSE) THEN
    RAISE EXCEPTION 'Driver must be online to start a shift';
  END IF;
  
  -- Check if driver already has an active shift
  SELECT COUNT(*) INTO v_active_shift_count
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_active_shift_count > 0 THEN
    RAISE EXCEPTION 'Driver already has an active shift';
  END IF;
  
  -- Create new shift record with start time
  INSERT INTO driver_shifts (driver_id, shift_start)
  VALUES (p_driver_id, NOW())
  RETURNING id INTO v_shift_id;
  
  -- Update driver status to on shift
  UPDATE driver_status
  SET 
    is_on_shift = TRUE,
    last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN v_shift_id;
END;
$$ LANGUAGE plpgsql;

-- 5. Update end_driver_shift function (driver remains online after ending shift)
CREATE OR REPLACE FUNCTION end_driver_shift(p_driver_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
BEGIN
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Set shift end time
  UPDATE driver_shifts
  SET 
    shift_end = NOW(),
    updated_at = NOW()
  WHERE id = v_shift_id;
  
  -- Update driver status to off shift (but remain online)
  UPDATE driver_status
  SET 
    is_on_shift = FALSE,
    last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 6. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_driver_status_online_since ON driver_status(online_since) WHERE online_since IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_driver_status_offline_since ON driver_status(offline_since) WHERE offline_since IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_driver_online_history_driver_id ON driver_online_history(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_online_history_event_time ON driver_online_history(event_time);
CREATE INDEX IF NOT EXISTS idx_driver_online_history_event_type ON driver_online_history(event_type);

-- 7. Add comments for documentation
COMMENT ON COLUMN driver_status.online_since IS 'When the driver went online in current session (TIMESTAMPTZ for UK timezone)';
COMMENT ON COLUMN driver_status.offline_since IS 'When the driver went offline (TIMESTAMPTZ for UK timezone)';
COMMENT ON TABLE driver_online_history IS 'Complete history of all driver online/offline events';
COMMENT ON FUNCTION update_driver_online_status(UUID, BOOLEAN) IS 'Updates driver online/offline status with history tracking and automatic shift ending when going offline';
