"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { getBusinessTypeColors } from "@/utils/business-colors"

interface SearchFormProps {
  activeType?: string
}

export default function SearchForm({ activeType = "restaurant" }: SearchFormProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const colors = getBusinessTypeColors(activeType)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Redirect to search page with query parameter and active business type
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}&type=${activeType}`)
    } else {
      // If no query, just go to search page with active business type
      router.push(`/search?type=${activeType}`)
    }
  }

  return (
    <form className="flex flex-col sm:flex-row gap-4" onSubmit={handleSubmit}>
      <div className="relative flex-grow">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <Input
          placeholder="Search by name, location, or business type..."
          className="pl-10 h-12 bg-white text-black w-full"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      <Button
        type="submit"
        className={`${colors.bg} ${colors.hover} h-12`}
      >
        Find Nearby
      </Button>
    </form>
  )
}
