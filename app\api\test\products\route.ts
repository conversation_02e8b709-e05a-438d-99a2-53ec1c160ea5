import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== TEST PRODUCTS API (NO AUTH) ===")
    
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Test basic connection
    console.log("Testing basic database connection...")
    
    // Test Jersey Grill business exists
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select("id, name, slug")
      .eq("id", 4)
      .single()

    console.log("Jersey Grill business:", { business, businessError })

    // Test products for Jersey Grill
    const { data: products, error: productsError } = await adminClient
      .from("products")
      .select("id, name, business_id, price, is_available")
      .eq("business_id", 4)
      .limit(5)

    console.log("Jersey Grill products:", { products, productsError })

    // Test products table structure
    const { data: sampleProduct, error: sampleError } = await adminClient
      .from("products")
      .select("*")
      .limit(1)
      .single()

    console.log("Sample product structure:", { sampleProduct, sampleError })

    return NextResponse.json({
      success: true,
      business,
      businessError,
      products,
      productsError,
      sampleProduct,
      sampleError,
      productCount: products?.length || 0
    })

  } catch (error: any) {
    console.error("Error in test products API:", error)
    return NextResponse.json({
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
