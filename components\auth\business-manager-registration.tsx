"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

interface BusinessType {
  id: number
  name: string
  slug: string
}



// Jersey parishes list
const JERSEY_PARISHES = [
  "St Helier",
  "St Brelade",
  "St Clement",
  "Grouville",
  "St John",
  "St Lawrence",
  "St Martin",
  "St Mary",
  "St Ouen",
  "St Peter",
  "St Saviour",
  "<PERSON>"
]

export default function BusinessManagerRegistration() {
  const router = useRouter()
  const { signUp, signIn, user } = useAuth()

  // Personal information
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")

  // Business information
  const [businessName, setBusinessName] = useState("")
  const [businessDescription, setBusinessDescription] = useState("")
  const [businessAddress, setBusinessAddress] = useState("")
  const [businessPostcode, setBusinessPostcode] = useState("")
  const [businessParish, setBusinessParish] = useState("")
  const [businessPhone, setBusinessPhone] = useState("")
  const [businessTypeId, setBusinessTypeId] = useState<number>(1) // Start with 1 (Restaurant) as default

  // UI state
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userExists, setUserExists] = useState(false)
  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([])

  // Load saved form data from localStorage on mount
  useEffect(() => {
    // If user is already logged in, pre-fill email
    if (user?.email) {
      setEmail(user.email);
    }

    // Try to load saved form data
    try {
      const savedData = localStorage.getItem('business_registration_form');
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        // Only restore if we have data
        if (parsedData) {
          // Don't override email if user is logged in
          if (!user?.email && parsedData.email) setEmail(parsedData.email);
          if (parsedData.name) setName(parsedData.name);
          if (parsedData.phone) setPhone(parsedData.phone);
          if (parsedData.businessName) setBusinessName(parsedData.businessName);
          if (parsedData.businessDescription) setBusinessDescription(parsedData.businessDescription);
          if (parsedData.businessAddress) setBusinessAddress(parsedData.businessAddress);
          if (parsedData.businessPostcode) setBusinessPostcode(parsedData.businessPostcode);
          if (parsedData.businessParish) setBusinessParish(parsedData.businessParish);
          if (parsedData.businessPhone) setBusinessPhone(parsedData.businessPhone);
          if (parsedData.businessTypeId) setBusinessTypeId(parsedData.businessTypeId);
        }
      }
    } catch (err) {
      console.error("Error loading saved form data:", err);
      // Clear potentially corrupted data
      localStorage.removeItem('business_registration_form');
    }
  }, [user]);

  // Save form data to localStorage when it changes
  useEffect(() => {
    // Don't save passwords
    const formData = {
      name,
      email,
      phone,
      businessName,
      businessDescription,
      businessAddress,
      businessPostcode,
      businessParish,
      businessPhone,
      businessTypeId
    };

    localStorage.setItem('business_registration_form', JSON.stringify(formData));
  }, [
    name, email, phone, businessName, businessDescription,
    businessAddress, businessPostcode, businessParish, businessPhone,
    businessTypeId
  ]);

  // Fetch business types on component mount
  useEffect(() => {
    async function fetchBusinessTypes() {
      try {
        console.log("Fetching business types...")
        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';

        // Fetch business types using the API
        const response = await fetch('/api/business/types', {
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch business types: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        const data = result.data;
        const error = result.error;

        if (error) {
          console.error("Error fetching business types:", error)
          return
        }

        console.log("Business types fetched:", data)

        if (data && data.length > 0) {
          setBusinessTypes(data)

          // Always set a business type ID to ensure we have a valid value
          setBusinessTypeId(data[0].id)
        } else {
          // If no business types are returned, create default ones
          const defaultTypes = [
            { id: 1, name: "Restaurant", slug: "restaurant" },
            { id: 2, name: "Shop", slug: "shop" },
            { id: 3, name: "Pharmacy", slug: "pharmacy" },
            { id: 4, name: "Cafe", slug: "cafe" }
          ]
          console.log("Using default business types")
          setBusinessTypes(defaultTypes)
        }
      } catch (err) {
        console.error("Failed to fetch business types:", err)

        // Fallback to default business types
        const defaultTypes = [
          { id: 1, name: "Restaurant", slug: "restaurant" },
          { id: 2, name: "Shop", slug: "shop" },
          { id: 3, name: "Pharmacy", slug: "pharmacy" },
          { id: 4, name: "Cafe", slug: "cafe" }
        ]
        console.log("Using default business types due to error")
        setBusinessTypes(defaultTypes)
      }
    }

    fetchBusinessTypes()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)
    setIsSubmitting(true)

    console.log("Starting business registration process...")

    // Form validation (password validation removed since we're not creating auth users here)
    console.log("Form validation - skipping password checks since auth user creation is handled separately...");

    console.log("Form validation - checking required business fields...");
    if (!businessName || !businessAddress || !businessPostcode || !businessParish || !businessPhone) {
      console.log("Missing business fields:", {
        businessName: !!businessName,
        businessAddress: !!businessAddress,
        businessPostcode: !!businessPostcode,
        businessParish: !!businessParish,
        businessPhone: !!businessPhone
      });
      setError("Please fill in all required business information")
      setIsSubmitting(false)
      return
    }



    // Ensure we have a valid business type ID
    console.log("Business type validation:", {
      businessTypeId: businessTypeId,
      isNaN: isNaN(Number(businessTypeId)),
      isLessOrEqual0: Number(businessTypeId) <= 0
    });

    if (!businessTypeId || isNaN(Number(businessTypeId)) || Number(businessTypeId) <= 0) {
      console.log("Invalid business type ID, defaulting to 1");
      setBusinessTypeId(1);
    }

    console.log("All validation passed, proceeding with registration...");
    setIsLoading(true)

    try {
      // Since we're not handling auth user creation here, we don't need to sign in
      console.log("Proceeding with business-only registration...");

      // Use the server-side API for registration
      console.log("Submitting business registration...");

      let response;
      let result;

      try {
        console.log("Sending business registration with business type ID:", businessTypeId);

        // Use the API to create the business record
        response = await fetch('/api/business/register-only', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            // Send all the business information
            email,
            name,
            // No password - auth user creation will be handled separately
            businessName,
            businessDescription,
            businessAddress,
            businessPostcode,
            businessParish,
            businessPhone,
            businessTypeId: businessTypeId || 1 // Default to 1 if not set
          }),
        });

        if (!response.ok) {
          console.error("Business registration API error:", response.status, response.statusText);

          // Try to get more detailed error information
          try {
            const errorText = await response.text();
            console.error("Error response body:", errorText);

            // Try to parse as JSON if possible
            try {
              const errorJson = JSON.parse(errorText);
              if (errorJson.error) {
                throw new Error(`Business registration failed: ${errorJson.error}`);
              }
            } catch (parseErr) {
              // If it's not valid JSON, use the text directly
              throw new Error(`Business registration failed: ${errorText}`);
            }
          } catch (textErr) {
            // If we can't get the response text, use a generic error
            throw new Error(`Business registration failed with status: ${response.status} ${response.statusText}`);
          }
        }

        result = await response.json();
        console.log("Business registration API response:", result);
      } catch (apiError: any) {
        console.error("Exception calling business registration API:", apiError);
        throw new Error(`API error: ${apiError.message || "Failed to connect to registration service"}`);
      }

      // Check if we have a successful result
      if (result && result.success) {
        console.log("Business registration successful!");

        // Store registration data for the success page
        const registrationData = {
          businessName,
          businessType: businessTypes.find(type => type.id === businessTypeId)?.name || 'Business',
          businessAddress,
          businessPostcode,
          businessPhone,
          registrationDate: new Date().toISOString(),
          registrationId: result.registrationId,
          userEmail: email,
          userName: name // Include the user name for pre-filling
        };

        localStorage.setItem('business_registration_success_auth_pending', JSON.stringify(registrationData));

        // Show brief success message before redirecting
        setSuccess("Business registration successful! Redirecting to complete your account setup...");

        setTimeout(() => {
          router.push("/business/registration-success-auth-pending");
        }, 1500);
        return;
      } else {
        // If we got here but don't have a success result, something went wrong
        console.error("Business registration did not return success:", result);
        throw new Error(result?.error || "Business registration failed without a specific error message");
      }
    } catch (err: any) {
      console.error("Registration error:", err)
      setError(err.message || "An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
      setIsSubmitting(false)
    }
  }



  // Helper function to get business type ID
  const getBusinessTypeId = (type: string): number => {
    switch (type) {
      case "restaurant":
        return 1
      case "shop":
        return 2
      case "pharmacy":
        return 3
      case "cafe":
        return 4
      default:
        return 1
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Register Your Business</CardTitle>
        <CardDescription>Register your business with Loop Jersey. You'll create your personal account in the next step.</CardDescription>
      </CardHeader>
      <CardContent>
        {isSubmitting && (
          <div className="mb-4 flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mr-3"></div>
            <p className="text-gray-600">Submitting your registration...</p>
          </div>
        )}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {success && !error && (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Your Name*</Label>
            <Input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email*</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={async (e) => {
                const newEmail = e.target.value;
                setEmail(newEmail);

                // Check if user exists when email is entered
                if (newEmail && newEmail.includes('@')) {
                  try {
                    // Get the authentication token from localStorage
                    const token = localStorage.getItem('loop_jersey_auth_token') || '';

                    // Check if user exists using the API
                    const response = await fetch(`/api/auth/check-user?email=${encodeURIComponent(newEmail)}`, {
                      headers: {
                        'Authorization': token ? `Bearer ${token}` : '',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                      }
                    });

                    if (!response.ok) {
                      throw new Error(`Failed to check user: ${response.status} ${response.statusText}`);
                    }

                    const result = await response.json();
                    const data = result.data;

                    setUserExists(!!data);
                    setSuccess(data ? "Existing user found. You'll be able to use this account to register your business." : null);
                  } catch (err) {
                    // Ignore errors
                    setUserExists(false);
                    setSuccess(null);
                  }
                } else {
                  setUserExists(false);
                  setSuccess(null);
                }
              }}
              required
              className={userExists ? "border-amber-500" : ""}
            />
            {userExists && (
              <p className="text-sm text-amber-600">
                This email is already registered. You'll be signed in with this account.
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number*</Label>
            <Input
              id="phone"
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              required
            />
          </div>

          <div className="border-t pt-4 mt-4">
            <h3 className="font-medium mb-2">Business Information</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name*</Label>
                <Input
                  id="businessName"
                  type="text"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessDescription">Business Description</Label>
                <Textarea
                  id="businessDescription"
                  value={businessDescription}
                  onChange={(e) => setBusinessDescription(e.target.value)}
                  placeholder="Describe your business..."
                  className="min-h-[100px]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessAddress">Business Address*</Label>
                <Input
                  id="businessAddress"
                  type="text"
                  value={businessAddress}
                  onChange={(e) => setBusinessAddress(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessPostcode">Postcode*</Label>
                <Input
                  id="businessPostcode"
                  type="text"
                  value={businessPostcode}
                  onChange={(e) => setBusinessPostcode(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessParish">Parish*</Label>
                <Select
                  value={businessParish}
                  onValueChange={(value) => setBusinessParish(value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parish" />
                  </SelectTrigger>
                  <SelectContent>
                    {JERSEY_PARISHES.map((parish) => (
                      <SelectItem key={parish} value={parish}>
                        {parish}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessPhone">Business Phone*</Label>
                <Input
                  id="businessPhone"
                  type="tel"
                  value={businessPhone}
                  onChange={(e) => setBusinessPhone(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type*</Label>
                <Select
                  value={businessTypeId.toString()}
                  onValueChange={(value) => setBusinessTypeId(parseInt(value))}
                  disabled={businessTypes.length === 0}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.length > 0 ? (
                      businessTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="1">Restaurant</SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {businessTypes.length === 0 && (
                  <p className="text-sm text-amber-600">
                    Using default business type: Restaurant
                  </p>
                )}
              </div>


            </div>
          </div>

          <Button
            type="submit"
            className="w-full bg-emerald-600 hover:bg-emerald-700"
            disabled={isLoading || isSubmitting}
          >
            {isSubmitting ? "Registering..." : "Register Business"}
          </Button>
        </form>
      </CardContent>
      {!user && (
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-500">
            Already have an account?{" "}
            <Button variant="link" className="p-0" onClick={() => router.push("/login?returnUrl=/partners/register-business")}>
              Sign in
            </Button>
          </p>
        </CardFooter>
      )}
    </Card>
  )
}
