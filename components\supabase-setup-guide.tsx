'use client';

import { useState } from 'react';

export default function SupabaseSetupGuide() {
  const [step, setStep] = useState(1);
  const [copied, setCopied] = useState(false);

  const sqlScript = `-- Create restaurants table if it doesn't exist
CREATE TABLE IF NOT EXISTS restaurants (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  logo_url TEXT,
  banner_url TEXT,
  address TEXT NOT NULL,
  location VARCHAR(100) NOT NULL,
  coordinates POINT NOT NULL,
  delivery_radius NUMERIC(5,2) DEFAULT 5.0,
  rating NUMERIC(3,2) DEFAULT 0.0,
  review_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data if the table is empty
INSERT INTO restaurants (name, slug, description, address, location, coordinates, delivery_radius, rating, review_count)
SELECT
  'Jersey Grill',
  'jersey-grill',
  'Classic American grill serving burgers, steaks, and seafood with a Jersey twist.',
  '123 Main Street',
  'St Helier',
  POINT(-2.1053, 49.1805),
  5.0,
  4.8,
  243
WHERE NOT EXISTS (SELECT 1 FROM restaurants WHERE slug = 'jersey-grill');

-- Insert another sample restaurant
INSERT INTO restaurants (name, slug, description, address, location, coordinates, delivery_radius, rating, review_count)
SELECT
  'Pasta Paradise',
  'pasta-paradise',
  'Authentic Italian pasta dishes made with fresh ingredients.',
  '45 Harbor Street',
  'St Helier',
  POINT(-2.1123, 49.1835),
  3.5,
  4.6,
  187
WHERE NOT EXISTS (SELECT 1 FROM restaurants WHERE slug = 'pasta-paradise');

-- Insert a third sample restaurant
INSERT INTO restaurants (name, slug, description, address, location, coordinates, delivery_radius, rating, review_count)
SELECT
  'Sushi Bay',
  'sushi-bay',
  'Fresh sushi and Japanese cuisine with ocean views.',
  '78 Waterfront',
  'St Aubin',
  POINT(-2.1673, 49.1875),
  4.0,
  4.9,
  312
WHERE NOT EXISTS (SELECT 1 FROM restaurants WHERE slug = 'sushi-bay');`;

  const rlsPolicy = `-- Enable row level security
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Allow public read access on restaurants"
  ON restaurants
  FOR SELECT
  TO anon
  USING (true);`;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Supabase Setup Guide</h2>

      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${step === 1 ? 'bg-emerald-500 text-white' : 'bg-gray-200'}`}>
            1
          </div>
          <h3 className="text-lg font-semibold">Create the Restaurants Table</h3>
        </div>

        <div className="ml-11 mb-6">
          <p className="mb-3">Log in to your Supabase dashboard and go to the SQL Editor. Create a new query and paste the following SQL:</p>

          <div className="relative">
            <pre className="bg-gray-50 p-4 rounded text-sm overflow-x-auto mb-3">{sqlScript}</pre>
            <button
              onClick={() => copyToClipboard(sqlScript)}
              className="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded text-xs"
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>

          <button
            onClick={() => setStep(2)}
            className="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded"
          >
            Next Step
          </button>
        </div>
      </div>

      <div className={`mb-8 ${step < 2 ? 'opacity-50' : ''}`}>
        <div className="flex items-center mb-4">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${step === 2 ? 'bg-emerald-500 text-white' : 'bg-gray-200'}`}>
            2
          </div>
          <h3 className="text-lg font-semibold">Set Up Row Level Security</h3>
        </div>

        <div className="ml-11 mb-6">
          <p className="mb-3">By default, Supabase tables are protected by Row Level Security. To allow public access to the restaurants table, run this SQL:</p>

          <div className="relative">
            <pre className="bg-gray-50 p-4 rounded text-sm overflow-x-auto mb-3">{rlsPolicy}</pre>
            <button
              onClick={() => copyToClipboard(rlsPolicy)}
              className="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded text-xs"
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>

          <button
            onClick={() => setStep(3)}
            className={`bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded ${step < 2 ? 'cursor-not-allowed' : ''}`}
            disabled={step < 2}
          >
            Next Step
          </button>
        </div>
      </div>

      <div className={`mb-8 ${step < 3 ? 'opacity-50' : ''}`}>
        <div className="flex items-center mb-4">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${step === 3 ? 'bg-emerald-500 text-white' : 'bg-gray-200'}`}>
            3
          </div>
          <h3 className="text-lg font-semibold">Set Environment Variables</h3>
        </div>

        <div className="ml-11">
          <p className="mb-3">Set up your environment variables:</p>

          <div className="bg-gray-50 p-4 rounded mb-4">
            <pre className="text-sm overflow-x-auto">
{`# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key`}
            </pre>
          </div>

          <p className="mb-3"><strong>For local development:</strong></p>
          <ol className="list-decimal ml-5 mb-4 space-y-2">
            <li>Create a <code>.env.local</code> file in the root directory</li>
            <li>Add the environment variables above with your actual Supabase values</li>
            <li>Restart your development server</li>
          </ol>

          <p className="mb-3"><strong>For Vercel deployment:</strong></p>
          <ol className="list-decimal ml-5 mb-4 space-y-2">
            <li>Go to your Vercel project dashboard</li>
            <li>Navigate to Settings &gt; Environment Variables</li>
            <li>Add each of the environment variables with your actual Supabase values</li>
            <li>Redeploy your application</li>
          </ol>

          <button
            onClick={() => setStep(4)}
            className={`bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded ${step < 3 ? 'cursor-not-allowed' : ''}`}
            disabled={step < 3}
          >
            Next Step
          </button>
        </div>
      </div>

      <div className={`mb-8 ${step < 4 ? 'opacity-50' : ''}`}>
        <div className="flex items-center mb-4">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${step === 4 ? 'bg-emerald-500 text-white' : 'bg-gray-200'}`}>
            4
          </div>
          <h3 className="text-lg font-semibold">Verify Setup</h3>
        </div>

        <div className="ml-11">
          <p className="mb-3">After completing the steps above, check if everything is working:</p>

          <ol className="list-decimal ml-5 mb-4 space-y-2">
            <li>Go to the Table Editor in Supabase and verify the restaurants table exists with sample data</li>
            <li>Visit the <a href="/supabase-debug" className="text-blue-500 hover:underline">Supabase Debug Page</a> to check the connection</li>
            <li>Try the <a href="/supabase-test" className="text-blue-500 hover:underline">Supabase Test Page</a> again to see if restaurants are loading</li>
          </ol>

          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <p className="text-sm text-yellow-700">
              <strong>Note:</strong> If you're still having issues, check that your environment variables are correctly set in <code>.env.local</code> and that you've restarted your development server.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
