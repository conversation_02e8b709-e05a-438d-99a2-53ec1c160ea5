-- Create confirm_pickup function for single-party pickup confirmation
-- This function allows either the driver OR business to confirm pickup
-- When one party confirms, it notifies the other party and the customer
-- Execute this in your Supabase SQL editor

CREATE OR REPLACE FUNCTION public.confirm_pickup(
    order_id INTEGER,
    confirming_party TEXT, -- 'driver' or 'business'
    driver_auth_id UUID DEFAULT NULL,
    business_id INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    order_record RECORD;
    result_message TEXT;
    new_status TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Validate input parameters
    IF confirming_party NOT IN ('driver', 'business') THEN
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'Invalid confirming party. Must be "driver" or "business"'
        );
    END IF;

    -- Get order details
    SELECT * INTO order_record
    FROM orders
    WHERE id = order_id;

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'Order not found'
        );
    END IF;

    -- Check if order is in correct status for pickup confirmation
    IF order_record.status NOT IN ('assigned', 'ready') THEN
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'Order is not ready for pickup confirmation. Current status: ' || order_record.status
        );
    END IF;

    -- Validate confirming party permissions
    IF confirming_party = 'driver' THEN
        -- Verify driver is assigned to this order
        IF order_record.driver_id IS NULL THEN
            RETURN jsonb_build_object(
                'success', FALSE,
                'error', 'No driver assigned to this order'
            );
        END IF;
        
        -- Additional validation could be added here to verify driver_auth_id matches driver_id
        
    ELSIF confirming_party = 'business' THEN
        -- Verify business_id matches order business
        IF business_id IS NULL OR business_id != order_record.business_id THEN
            RETURN jsonb_build_object(
                'success', FALSE,
                'error', 'Invalid business ID for this order'
            );
        END IF;
    END IF;

    -- Update order status to out_for_delivery (combining picked_up and out_for_delivery)
    UPDATE orders 
    SET 
        status = 'out_for_delivery',
        updated_at = NOW()
    WHERE id = order_id;

    -- Create status history entry
    INSERT INTO order_status_history (
        order_id,
        status,
        notes,
        changed_by_type,
        driver_id,
        created_at
    ) VALUES (
        order_id,
        'out_for_delivery',
        'Pickup confirmed by ' || confirming_party || ' - order is now out for delivery',
        confirming_party,
        CASE WHEN confirming_party = 'driver' THEN order_record.driver_id ELSE NULL END,
        NOW()
    );

    -- Trigger notifications via the enhanced status update system
    -- This will be handled by the API layer calling updateOrderStatusWithNotifications

    -- Set success and message
    success := TRUE;
    new_status := 'out_for_delivery';
    
    IF confirming_party = 'driver' THEN
        result_message := 'Driver confirmed pickup. Order is now out for delivery. Business and customer have been notified.';
    ELSE
        result_message := 'Business confirmed handover to driver. Order is now out for delivery. Driver and customer have been notified.';
    END IF;

    -- Return success response
    RETURN jsonb_build_object(
        'success', success,
        'message', result_message,
        'new_status', new_status,
        'confirming_party', confirming_party,
        'order_id', order_id,
        'both_confirmed', TRUE -- Always true now since single confirmation is sufficient
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return failure
        RAISE LOG 'Error in confirm_pickup function: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'Internal error during pickup confirmation: ' || SQLERRM
        );
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.confirm_pickup(INTEGER, TEXT, UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.confirm_pickup(INTEGER, TEXT, UUID, INTEGER) TO service_role;

-- Add comment to the function
COMMENT ON FUNCTION public.confirm_pickup(INTEGER, TEXT, UUID, INTEGER) IS 
'Confirms pickup by either driver or business. Single confirmation moves order to out_for_delivery status and triggers notifications.';

-- Test the function exists
DO $$
BEGIN
    RAISE NOTICE 'confirm_pickup function created successfully';
END $$;
