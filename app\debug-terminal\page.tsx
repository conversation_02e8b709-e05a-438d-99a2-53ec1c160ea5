"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { logToTerminal } from '@/lib/terminal-logger'

interface Log {
  timestamp: string
  message: string
  type: 'info' | 'error'
  data?: any
}

export default function DebugTerminalPage() {
  const [logs, setLogs] = useState<Log[]>([])
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Function to fetch logs
  const fetchLogs = async () => {
    try {
      const response = await fetch('/api/debug/log')
      const data = await response.json()
      setLogs(data.logs || [])
    } catch (error) {
      console.error('Error fetching logs:', error)
    } finally {
      setLoading(false)
    }
  }

  // Function to clear logs
  const clearLogs = async () => {
    try {
      await fetch('/api/debug/log', { method: 'DELETE' })
      setLogs([])
    } catch (error) {
      console.error('Error clearing logs:', error)
    }
  }

  // Fetch logs on component mount and when autoRefresh changes
  useEffect(() => {
    fetchLogs()

    // Set up auto-refresh if enabled
    let intervalId: NodeJS.Timeout | null = null
    if (autoRefresh) {
      intervalId = setInterval(fetchLogs, 2000) // Refresh every 2 seconds
    }

    // Clean up interval on component unmount
    return () => {
      if (intervalId) clearInterval(intervalId)
    }
  }, [autoRefresh])

  // Log a test message
  const logTestMessage = async () => {
    await logToTerminal('Test message from debug terminal', { timestamp: new Date().toISOString() })
    fetchLogs()
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Debug Terminal</h1>
      
      <div className="flex gap-4 mb-6">
        <Button onClick={fetchLogs} variant="outline">
          Refresh Logs
        </Button>
        <Button onClick={clearLogs} variant="destructive">
          Clear Logs
        </Button>
        <Button onClick={logTestMessage} variant="default">
          Log Test Message
        </Button>
        <Button 
          onClick={() => setAutoRefresh(!autoRefresh)} 
          variant={autoRefresh ? "default" : "outline"}
        >
          {autoRefresh ? "Auto-Refresh: ON" : "Auto-Refresh: OFF"}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Log Messages</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading logs...</p>
          ) : logs.length === 0 ? (
            <p>No logs available.</p>
          ) : (
            <div className="bg-black text-white p-4 rounded-md font-mono text-sm overflow-auto max-h-[600px]">
              {logs.map((log, index) => (
                <div 
                  key={index} 
                  className={`mb-2 ${log.type === 'error' ? 'text-red-400' : 'text-green-400'}`}
                >
                  <span className="text-gray-400">[{new Date(log.timestamp).toLocaleTimeString()}]</span>{' '}
                  {log.message}
                  {log.data && (
                    <pre className="ml-8 text-xs text-gray-300 mt-1">
                      {typeof log.data === 'object' 
                        ? JSON.stringify(log.data, null, 2) 
                        : log.data.toString()}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
