"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Eye, EyeOff, Lock, Mail, Fingerprint } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BiometricLogin } from "@/components/biometric-login"
import { biometricAuthService } from "@/services/biometric-auth-service"

export default function DriverMobileLoginPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [biometricSupported, setBiometricSupported] = useState(false)
  const [biometricRegistered, setBiometricRegistered] = useState(false)

  useEffect(() => {
    const checkBiometricSupport = async () => {
      try {
        const supported = await biometricAuthService.isSupported()
        setBiometricSupported(supported)

        if (supported) {
          setBiometricRegistered(biometricAuthService.hasCredentials())
        }
      } catch (err) {
        console.error("Error checking biometric support:", err)
      }
    }

    checkBiometricSupport()
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      router.push("/driver-mobile/dashboard")
    }, 1500)
  }

  return (
    <div className="flex min-h-[calc(100vh-4rem)] items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        <div className="mb-8 text-center">
          <Link href="/" className="inline-block">
            <div className="flex items-center justify-center">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-emerald-600"
              >
                {/* Outer wheel circle */}
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />

                {/* Inner hub circle */}
                <circle cx="12" cy="12" r="2.5" stroke="currentColor" strokeWidth="1.5" fill="none" />

                {/* Wheel spokes */}
                <path d="M12 9.5V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M12 20V14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M9.5 12H4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M20 12H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />

                {/* Diagonal spokes */}
                <path d="M10.1 10.1L6.5 6.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M17.5 17.5L13.9 13.9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M10.1 13.9L6.5 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                <path d="M17.5 6.5L13.9 10.1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
              </svg>
              <span className="ml-2 text-2xl font-bold text-emerald-600">Loop</span>
              <span className="ml-2 rounded-md bg-emerald-100 px-2 py-1 text-xs font-medium text-emerald-800">
                Drivers
              </span>
            </div>
          </Link>
          <h1 className="mt-4 text-2xl font-bold text-gray-900">Driver Portal</h1>
          <p className="mt-2 text-gray-600">Sign in to start delivering with Jersey Eats</p>
        </div>

        {biometricSupported && biometricRegistered ? (
          <BiometricLogin />
        ) : (
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="register">Register</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <Card>
                <CardHeader>
                  <CardTitle>Driver Login</CardTitle>
                  <CardDescription>Enter your credentials to access your driver account</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleLogin}>
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            placeholder="<EMAIL>"
                            type="email"
                            autoCapitalize="none"
                            autoComplete="email"
                            autoCorrect="off"
                            className="pl-10"
                            required
                          />
                        </div>
                      </div>
                      <div className="grid gap-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="password">Password</Label>
                          <Link
                            href="/driver-mobile/reset-password"
                            className="text-sm font-medium text-emerald-600 hover:text-emerald-700"
                          >
                            Forgot password?
                          </Link>
                        </div>
                        <div className="relative">
                          <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            className="pl-10 pr-10"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                      <Button type="submit" className="w-full bg-emerald-600 hover:bg-emerald-700" disabled={isLoading}>
                        {isLoading ? "Signing in..." : "Sign In"}
                      </Button>

                      {biometricSupported && (
                        <div className="mt-2 text-center">
                          <Link
                            href="/driver-mobile/settings/biometric"
                            className="text-sm text-emerald-600 hover:text-emerald-700 flex items-center justify-center gap-1"
                          >
                            <Fingerprint className="h-4 w-4" />
                            Set up biometric login
                          </Link>
                        </div>
                      )}
                    </div>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="register">
              <Card>
                <CardHeader>
                  <CardTitle>Driver Registration</CardTitle>
                  <CardDescription>Create an account to start delivering with Jersey Eats</CardDescription>
                </CardHeader>
                <CardContent>
                  <form>
                    <div className="grid gap-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="grid gap-2">
                          <Label htmlFor="first-name">First name</Label>
                          <Input id="first-name" required />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="last-name">Last name</Label>
                          <Input id="last-name" required />
                        </div>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          placeholder="<EMAIL>"
                          type="email"
                          autoCapitalize="none"
                          autoComplete="email"
                          autoCorrect="off"
                          required
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="phone">Phone number</Label>
                        <Input id="phone" placeholder="+44 7911 123456" type="tel" autoComplete="tel" required />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="password">Password</Label>
                        <Input id="password" type="password" required />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="confirm-password">Confirm password</Label>
                        <Input id="confirm-password" type="password" required />
                      </div>
                      <Button type="submit" className="w-full bg-emerald-600 hover:bg-emerald-700">
                        Create Account
                      </Button>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex flex-col items-start space-y-2 text-sm text-gray-500">
                  <p>
                    By creating an account, you agree to our{" "}
                    <Link href="/terms" className="text-emerald-600 hover:text-emerald-700">
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link href="/privacy" className="text-emerald-600 hover:text-emerald-700">
                      Privacy Policy
                    </Link>
                  </p>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  )
}
