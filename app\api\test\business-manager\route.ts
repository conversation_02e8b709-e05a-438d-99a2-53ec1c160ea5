import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== TEST BUSINESS MANAGER API ===")
    
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()
    
    if (!session) {
      return NextResponse.json({
        error: "No session found",
        sessionError
      })
    }

    console.log("Session user:", session.user.email)

    // Try to get user profile by auth_id
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, email")
      .eq("auth_id", session.user.id)
      .single()

    console.log("User profile lookup:", { userProfile, userError })

    // Try alternative lookup by email
    const { data: userProfileAlt, error: userErrorAlt } = await adminClient
      .from("users")
      .select("id, role, email, auth_id")
      .eq("email", session.user.email)
      .single()

    console.log("User profile alt lookup:", { userProfileAlt, userErrorAlt })

    // If we have a user, check business manager association
    let businessManager = null
    let bmError = null

    if (userProfile?.id) {
      const { data: bm, error: bmErr } = await adminClient
        .from("business_managers")
        .select("business_id, businesses(id, name)")
        .eq("user_id", userProfile.id)
        .single()
      
      businessManager = bm
      bmError = bmErr
      console.log("Business manager lookup by userProfile.id:", { businessManager, bmError })
    }

    if (userProfileAlt?.id && !businessManager) {
      const { data: bm, error: bmErr } = await adminClient
        .from("business_managers")
        .select("business_id, businesses(id, name)")
        .eq("user_id", userProfileAlt.id)
        .single()
      
      businessManager = bm
      bmError = bmErr
      console.log("Business manager lookup by userProfileAlt.id:", { businessManager, bmError })
    }

    // Check all business managers for debugging
    const { data: allManagers, error: allError } = await adminClient
      .from("business_managers")
      .select("user_id, business_id, users(email), businesses(name)")
      .limit(10)

    console.log("All business managers (sample):", { allManagers, allError })

    return NextResponse.json({
      session: {
        userId: session.user.id,
        email: session.user.email
      },
      userProfile,
      userError,
      userProfileAlt,
      userErrorAlt,
      businessManager,
      bmError,
      allManagers
    })

  } catch (error: any) {
    console.error("Error in test business manager API:", error)
    return NextResponse.json({
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
