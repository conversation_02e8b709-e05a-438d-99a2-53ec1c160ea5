"use client"

import { useState } from "react"
import { use } from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import {
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
  ShoppingBag,
  Clock,
  DollarSign,
  Star,
  MessageSquare,
  Tag,
  CheckCircle2,
  XCircle,
  Truck,
  AlertCircle,
  MoreHorizontal,
  FileText,
  Send,
  User,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import { getCustomerById } from "../data/mock-customers"
import { getOrdersByCustomerId } from "../data/mock-orders"
import type { CustomerOrder } from "../data/mock-orders"

export default function CustomerDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const customerId = parseInt(resolvedParams.id)
  const customer = getCustomerById(customerId)
  const customerOrders = getOrdersByCustomerId(customerId)
  const [activeTab, setActiveTab] = useState("overview")
  const [notes, setNotes] = useState(customer?.notes || "")

  if (!customer) {
    notFound()
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "loyal":
        return <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100">Loyal</Badge>
      case "regular":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Regular</Badge>
      case "occasional":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Occasional</Badge>
      case "at-risk":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">At Risk</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Get order status badge
  const getOrderStatusBadge = (status: string) => {
    switch (status) {
      case "delivered":
        return <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100">Delivered</Badge>
      case "preparing":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Preparing</Badge>
      case "out_for_delivery":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Out for Delivery</Badge>
      case "new":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">New</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link href="/restaurant-admin/customers" className="mr-4">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Customer Details</h1>
          <p className="text-gray-500">View and manage customer information</p>
        </div>
      </div>

      {/* Customer Profile */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center mb-6">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarImage src={customer.avatar} alt={customer.name} />
                <AvatarFallback>
                  <User className="h-12 w-12 text-gray-400" />
                </AvatarFallback>
              </Avatar>
              <h2 className="text-xl font-bold">{customer.name}</h2>
              <div className="mt-2">{getStatusBadge(customer.status)}</div>
              <p className="text-sm text-gray-500 mt-2">Customer since {customer.customerSince}</p>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Email</p>
                <p className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  {customer.email}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Phone</p>
                <p className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-500" />
                  {customer.phone}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Address</p>
                <p className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500 mt-0.5" />
                  {customer.address}
                </p>
              </div>
            </div>

            <div className="mt-6 space-y-2">
              <Button className="w-full" variant="outline">
                <Mail className="mr-2 h-4 w-4" />
                Send Email
              </Button>
              <Button className="w-full" variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                Send Message
              </Button>
              <Button className="w-full" variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit Profile
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-2 space-y-6">
          {/* Customer Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <ShoppingBag className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-500">Total Orders</p>
                  <h3 className="text-2xl font-bold">{customer.totalOrders}</h3>
                </div>
                <div className="mt-2 text-xs text-gray-500">Last order {customer.lastOrder}</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="bg-emerald-100 p-2 rounded-lg">
                    <DollarSign className="h-6 w-6 text-emerald-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-500">Total Spent</p>
                  <h3 className="text-2xl font-bold">£{customer.totalSpent.toFixed(2)}</h3>
                </div>
                <div className="mt-2 text-xs text-gray-500">Avg. £{(customer.totalSpent / customer.totalOrders).toFixed(2)} per order</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="bg-amber-100 p-2 rounded-lg">
                    <Star className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-500">Favorite Items</p>
                  <h3 className="text-2xl font-bold">{customer.favoriteItems.length}</h3>
                </div>
                <div className="mt-2 text-xs text-gray-500 truncate">{customer.favoriteItems.join(", ")}</div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="border-b px-3">
                  <TabsList className="h-12">
                    <TabsTrigger value="overview" className="data-[state=active]:bg-transparent">
                      Overview
                    </TabsTrigger>
                    <TabsTrigger value="orders" className="data-[state=active]:bg-transparent">
                      Orders
                    </TabsTrigger>
                    <TabsTrigger value="notes" className="data-[state=active]:bg-transparent">
                      Notes
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="overview" className="p-4 space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">Recent Orders</h3>
                    <div className="space-y-3">
                      {customerOrders.slice(0, 3).map((order) => (
                        <div key={order.id} className="flex items-center justify-between border-b pb-3">
                          <div className="flex items-center">
                            <div className="bg-gray-100 p-2 rounded-lg mr-3">
                              <ShoppingBag className="h-5 w-5 text-gray-600" />
                            </div>
                            <div>
                              <p className="font-medium">{order.id}</p>
                              <p className="text-xs text-gray-500">{order.date}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <p className="font-medium mr-3">£{order.total.toFixed(2)}</p>
                            {getOrderStatusBadge(order.status)}
                          </div>
                        </div>
                      ))}
                    </div>
                    {customerOrders.length > 3 && (
                      <Button variant="ghost" size="sm" className="mt-2" onClick={() => setActiveTab("orders")}>
                        View all orders
                      </Button>
                    )}
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Favorite Items</h3>
                    <div className="flex flex-wrap gap-2">
                      {customer.favoriteItems.map((item, index) => (
                        <Badge key={index} variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {customer.notes && (
                    <div>
                      <h3 className="font-medium mb-2">Notes</h3>
                      <p className="text-sm text-gray-600">{customer.notes}</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="orders" className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left font-medium py-3 px-4">Order ID</th>
                          <th className="text-left font-medium py-3 px-4">Date</th>
                          <th className="text-left font-medium py-3 px-4 hidden md:table-cell">Items</th>
                          <th className="text-center font-medium py-3 px-4">Total</th>
                          <th className="text-center font-medium py-3 px-4">Status</th>
                          <th className="text-right font-medium py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {customerOrders.map((order) => (
                          <tr key={order.id} className="border-b last:border-0 hover:bg-gray-50">
                            <td className="py-3 px-4">
                              <p className="font-medium">{order.id}</p>
                            </td>
                            <td className="py-3 px-4">
                              <p>{order.date}</p>
                            </td>
                            <td className="py-3 px-4 hidden md:table-cell">
                              <div>
                                {order.items.map((item, index) => (
                                  <p key={index} className="text-xs">
                                    {item.quantity}x {item.name}
                                  </p>
                                ))}
                              </div>
                            </td>
                            <td className="text-center py-3 px-4">
                              <p className="font-medium">£{order.total.toFixed(2)}</p>
                            </td>
                            <td className="text-center py-3 px-4">
                              {getOrderStatusBadge(order.status)}
                            </td>
                            <td className="text-right py-3 px-4">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Link href={`/restaurant-admin/orders/${order.id}`} className="flex items-center w-full">
                                      <FileText className="mr-2 h-4 w-4" />
                                      View Details
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Send className="mr-2 h-4 w-4" />
                                    Resend Receipt
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>

                <TabsContent value="notes" className="p-4">
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="notes" className="block text-sm font-medium mb-2">
                        Customer Notes
                      </label>
                      <Textarea
                        id="notes"
                        placeholder="Add notes about this customer..."
                        className="min-h-32"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                      />
                    </div>
                    <Button>Save Notes</Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
