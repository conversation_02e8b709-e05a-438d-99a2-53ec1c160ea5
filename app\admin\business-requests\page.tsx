"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Heart, TrendingUp, Plus, Vote, Users, MapPin, Search, Building2, CheckCircle, XCircle, Eye } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface BusinessRequest {
  id: number
  business_name: string
  business_type: string
  suggested_address: string
  customer_name: string
  customer_email: string
  vote_count: number
  status: string
  created_at: string
  notes?: string
}

export default function AdminBusinessRequestsPage() {
  const [requests, setRequests] = useState<BusinessRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreatePlaceholderDialog, setShowCreatePlaceholderDialog] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<BusinessRequest | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/admin/business-requests')
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests)
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePlaceholder = async (request: BusinessRequest) => {
    setSelectedRequest(request)
    setShowCreatePlaceholderDialog(true)
  }

  const handleUpdateStatus = async (requestId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/business-requests/${requestId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        toast({
          title: "Status updated",
          description: `Request status changed to ${newStatus}`,
        })
        fetchRequests()
      } else {
        throw new Error('Failed to update status')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update request status",
        variant: "destructive"
      })
    }
  }

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.business_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.business_type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.customer_name.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === "all" || request.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Approved</Badge>
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">Rejected</Badge>
      case 'placeholder_created':
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">Placeholder Created</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Business Requests</h1>
        <p className="text-gray-600">Manage customer requests for new businesses</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{requests.length}</div>
            <div className="text-gray-600">Total Requests</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {requests.reduce((sum, req) => sum + req.vote_count, 0)}
            </div>
            <div className="text-gray-600">Total Votes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {requests.filter(r => r.status === 'pending').length}
            </div>
            <div className="text-gray-600">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <Building2 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {requests.filter(r => r.status === 'placeholder_created').length}
            </div>
            <div className="text-gray-600">Placeholders</div>
          </CardContent>
        </Card>
      </div>

      {/* Top Requests */}
      {requests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Top Requested Businesses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {requests
                .filter(r => r.status === 'pending')
                .sort((a, b) => b.vote_count - a.vote_count)
                .slice(0, 5)
                .map((request, index) => (
                  <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold ${
                        index === 0 ? 'bg-yellow-400 text-yellow-900' :
                        index < 3 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{request.business_name}</div>
                        <div className="text-sm text-gray-500">{request.business_type}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-blue-600">{request.vote_count}</span>
                      <span className="text-sm text-gray-500">votes</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search requests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="placeholder_created">Placeholder Created</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Requests List */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredRequests.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== "all" ? 'No requests found' : 'No requests yet'}
            </h3>
            <p className="text-gray-500">
              {searchQuery || statusFilter !== "all"
                ? 'Try adjusting your search or filters'
                : 'Customer requests will appear here'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{request.business_name}</h3>
                      {request.business_type && (
                        <Badge variant="outline">{request.business_type}</Badge>
                      )}
                      {getStatusBadge(request.status)}
                    </div>

                    {request.suggested_address && (
                      <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                        <MapPin className="h-4 w-4" />
                        {request.suggested_address}
                      </div>
                    )}

                    <div className="text-sm text-gray-600 mb-2">
                      Requested by: {request.customer_name} ({request.customer_email})
                    </div>

                    {request.notes && (
                      <div className="text-sm text-gray-600 mb-2">
                        <strong>Notes:</strong> {request.notes}
                      </div>
                    )}

                    <div className="text-sm text-gray-500">
                      {formatDate(request.created_at)}
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-4">
                      <Vote className="h-4 w-4 text-gray-500" />
                      <span className="text-2xl font-bold text-blue-600">
                        {request.vote_count}
                      </span>
                      <span className="text-sm text-gray-500">
                        {request.vote_count === 1 ? 'vote' : 'votes'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      {request.status === 'pending' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCreatePlaceholder(request)}
                            className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            <Building2 className="h-4 w-4 mr-1" />
                            Create Placeholder
                          </Button>
                          <div className="flex gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateStatus(request.id, 'approved')}
                              className="flex-1 text-green-600 border-green-200 hover:bg-green-50"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateStatus(request.id, 'rejected')}
                              className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </>
                      )}

                      {request.status === 'placeholder_created' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`/business/${request.business_name.toLowerCase().replace(/\s+/g, '-')}`, '_blank')}
                          className="w-full"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Page
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Placeholder Dialog */}
      <Dialog open={showCreatePlaceholderDialog} onOpenChange={setShowCreatePlaceholderDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create Placeholder Business</DialogTitle>
            <DialogDescription>
              This will create a placeholder page for {selectedRequest?.business_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Business Details</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div><strong>Name:</strong> {selectedRequest?.business_name}</div>
                <div><strong>Type:</strong> {selectedRequest?.business_type}</div>
                <div><strong>Address:</strong> {selectedRequest?.suggested_address}</div>
                <div><strong>Votes:</strong> {selectedRequest?.vote_count}</div>
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-medium text-yellow-800 mb-2">What happens next?</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• A placeholder business page will be created</li>
                <li>• Customers can view business info but not place orders</li>
                <li>• The page will show "Coming Soon to Loop" messaging</li>
                <li>• Customers can request the business to join</li>
              </ul>
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowCreatePlaceholderDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // TODO: Implement placeholder creation
                  toast({
                    title: "Feature coming soon",
                    description: "Placeholder creation will be implemented next",
                  })
                  setShowCreatePlaceholderDialog(false)
                }}
                disabled={isCreating}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                {isCreating ? 'Creating...' : 'Create Placeholder'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
