'use client'

import { useState, useEffect } from 'react'

export default function DebugNotificationsPage() {
  const [status, setStatus] = useState<string>('Checking...')
  const [permission, setPermission] = useState<string>('unknown')
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null)
  const [subscription, setSubscription] = useState<PushSubscription | null>(null)

  useEffect(() => {
    checkNotificationStatus()
  }, [])

  const checkNotificationStatus = async () => {
    setStatus('🔍 Checking notification status...')

    // 1. Check if notifications are supported
    if (!('Notification' in window)) {
      setStatus('❌ Notifications not supported in this browser')
      return
    }

    // 2. Check permission
    const perm = Notification.permission
    setPermission(perm)
    setStatus(`📋 Notification permission: ${perm}`)

    // 3. Check service worker
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          setSwRegistration(registration)
          setStatus(prev => prev + '\n✅ Service worker registered')
          
          // 4. Check push subscription
          const sub = await registration.pushManager.getSubscription()
          if (sub) {
            setSubscription(sub)
            setStatus(prev => prev + '\n✅ Push subscription exists')
          } else {
            setStatus(prev => prev + '\n⚠️ No push subscription found')
          }
        } else {
          setStatus(prev => prev + '\n❌ No service worker registration')
        }
      } catch (error) {
        setStatus(prev => prev + `\n❌ Service worker error: ${error.message}`)
      }
    } else {
      setStatus(prev => prev + '\n❌ Service workers not supported')
    }
  }

  const requestPermission = async () => {
    setStatus('🔔 Requesting notification permission...')
    
    try {
      const permission = await Notification.requestPermission()
      setPermission(permission)
      
      if (permission === 'granted') {
        setStatus('✅ Permission granted! Now registering service worker...')
        await registerServiceWorker()
      } else {
        setStatus(`❌ Permission ${permission}. Please enable notifications in browser settings.`)
      }
    } catch (error) {
      setStatus(`❌ Error requesting permission: ${error.message}`)
    }
  }

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      await navigator.serviceWorker.ready
      setSwRegistration(registration)
      setStatus(prev => prev + '\n✅ Service worker registered successfully')
      
      // Create push subscription
      await createPushSubscription(registration)
    } catch (error) {
      setStatus(prev => prev + `\n❌ Service worker registration failed: ${error.message}`)
    }
  }

  const createPushSubscription = async (registration: ServiceWorkerRegistration) => {
    try {
      setStatus(prev => prev + '\n🔄 Creating push subscription...')
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: 'BFdydrTTpuMjwiwxkP3VKZs3algrhaZC1VSysPBsyHzM_MGklOxoU-25OS53GxaFs7gZw2SlrhMZTQ8ARec_pmw'
      })
      
      setSubscription(subscription)
      setStatus(prev => prev + '\n✅ Push subscription created')
      
      // Save to server
      await savePushSubscription(subscription)
    } catch (error) {
      setStatus(prev => prev + `\n❌ Push subscription failed: ${error.message}`)
    }
  }

  const savePushSubscription = async (subscription: PushSubscription) => {
    try {
      setStatus(prev => prev + '\n💾 Saving subscription to server...')
      
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.keys?.p256dh || '',
              auth: subscription.keys?.auth || ''
            }
          },
          deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
          browserName: navigator.userAgent.includes('Edg') ? 'Edge' :
                      navigator.userAgent.includes('Chrome') ? 'Chrome' :
                      navigator.userAgent.includes('Firefox') ? 'Firefox' :
                      navigator.userAgent.includes('Safari') ? 'Safari' : 'Other',
          userAgent: navigator.userAgent,
          preferences: {
            order_updates: true,
            delivery_updates: true,
            marketing: false,
            messages: true
          },
          forceActivate: false
        })
      })

      if (response.ok) {
        setStatus(prev => prev + '\n✅ Subscription saved to server')
      } else {
        const errorText = await response.text()
        setStatus(prev => prev + `\n❌ Failed to save subscription: ${errorText}`)
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error saving subscription: ${error.message}`)
    }
  }

  const testBrowserNotification = () => {
    setStatus(prev => prev + '\n🧪 Testing browser notification...')
    
    if (Notification.permission === 'granted') {
      const notification = new Notification('🧪 Browser Test Notification', {
        body: 'This is a direct browser notification test (not push)',
        icon: '/android-chrome-192x192.png',
        requireInteraction: true,
        tag: 'browser-test'
      })
      
      notification.onclick = () => {
        setStatus(prev => prev + '\n✅ Browser notification clicked!')
        notification.close()
      }
      
      setStatus(prev => prev + '\n📤 Browser notification sent')
    } else {
      setStatus(prev => prev + '\n❌ Cannot test - permission not granted')
    }
  }

  const testPushNotification = async () => {
    setStatus(prev => prev + '\n🚀 Testing push notification...')

    // First, let's add a listener to see if we receive push events
    if (swRegistration) {
      setStatus(prev => prev + '\n🔍 Adding push event listener...')

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'PUSH_RECEIVED') {
          setStatus(prev => prev + '\n✅ Service worker received push event!')
          setStatus(prev => prev + `\n📋 Push data: ${JSON.stringify(event.data.data)}`)
        }
      })
    }

    try {
      const response = await fetch('/api/notifications/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: '🚀 Debug Push Test',
          body: 'Testing push notification from debug page',
          data: { type: 'debug_test', timestamp: Date.now() }
        })
      })

      if (response.ok) {
        const result = await response.json()
        setStatus(prev => prev + '\n✅ Push notification API call successful')
        setStatus(prev => prev + `\n📊 Sent to ${result.result?.sent || 0} devices`)

        // Wait a bit to see if we get the push event
        setStatus(prev => prev + '\n⏳ Waiting for push event (10 seconds)...')
        setTimeout(() => {
          setStatus(prev => prev + '\n⏰ If no notification appeared, FCM delivery failed')
        }, 10000)
      } else {
        const errorText = await response.text()
        setStatus(prev => prev + `\n❌ Push notification failed: ${errorText}`)
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Push notification error: ${error.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          🔧 Notification Debug Tool
        </h1>

        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-700">Current Status:</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm whitespace-pre-wrap overflow-auto max-h-64">
              {status}
            </pre>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Permission:</strong> 
              <span className={`ml-2 px-2 py-1 rounded ${
                permission === 'granted' ? 'bg-green-100 text-green-800' :
                permission === 'denied' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {permission}
              </span>
            </div>
            <div>
              <strong>Service Worker:</strong> 
              <span className={`ml-2 px-2 py-1 rounded ${
                swRegistration ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {swRegistration ? 'Registered' : 'Not Registered'}
              </span>
            </div>
            <div>
              <strong>Push Subscription:</strong> 
              <span className={`ml-2 px-2 py-1 rounded ${
                subscription ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {subscription ? 'Active' : 'None'}
              </span>
            </div>
            <div>
              <strong>Browser:</strong> 
              <span className="ml-2 text-gray-600">
                {navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other'}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <button
              onClick={requestPermission}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              1. Request Permission & Setup
            </button>

            <button
              onClick={testBrowserNotification}
              className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
              disabled={permission !== 'granted'}
            >
              2. Test Browser Notification
            </button>

            <button
              onClick={testPushNotification}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
              disabled={!subscription}
            >
              3. Test Push Notification
            </button>

            <button
              onClick={checkNotificationStatus}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700"
            >
              🔄 Refresh Status
            </button>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-800 mb-2">Troubleshooting Steps:</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Click "Request Permission & Setup" and allow notifications</li>
              <li>Test "Browser Notification" - this should show immediately</li>
              <li>Test "Push Notification" - this goes through the server</li>
              <li>Check browser settings: chrome://settings/content/notifications</li>
              <li>Ensure localhost:3000 is allowed in notification settings</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
