"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Search,
  Filter,
  ChevronDown,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Truck,
  MoreHorizontal,
  User,
  Bike,
  Star,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog"

// Mock data for orders
const orders = [
  {
    id: "JE-5289",
    customer: {
      name: "<PERSON>",
      address: "15 Beachfront, St Helier, JE2 3NG",
      avatar: "/thoughtful-brunette.png",
    },
    items: [
      { name: "Jersey Crab Cakes", quantity: 2, price: 9.95 },
      { name: "Grilled Sea Bass", quantity: 1, price: 22.95 },
    ],
    total: 42.85,
    status: "new",
    time: "2 mins ago",
    paymentMethod: "Card",
    notes: "Please include extra sauce",
    driver: null,
  },
  {
    id: "JE-5288",
    customer: {
      name: "Michael Brown",
      address: "42 Roseville Street, St Helier, JE2 4PL",
      avatar: "/thoughtful-spectacled-man.png",
    },
    items: [
      { name: "Jersey Beef Burger", quantity: 1, price: 16.95 },
      { name: "Apple Crumble", quantity: 1, price: 6.95 },
    ],
    total: 23.9,
    status: "preparing",
    time: "15 mins ago",
    paymentMethod: "PayPal",
    notes: "",
    driver: {
      id: "D001",
      name: "John Driver",
      avatar: "/driver-avatar.png",
      status: "assigned",
    },
  },
  {
    id: "JE-5287",
    customer: {
      name: "Emma Wilson",
      address: "8 La Route de St Aubin, St Helier, JE2 3SF",
      avatar: "/sunlit-blonde.png",
    },
    items: [
      { name: "Seared Jersey Scallops", quantity: 2, price: 11.5 },
      { name: "Jersey Lobster", quantity: 1, price: 34.95 },
    ],
    total: 57.95,
    status: "out_for_delivery",
    time: "32 mins ago",
    paymentMethod: "Card",
    notes: "Doorbell doesn't work, please knock",
    driver: {
      id: "D002",
      name: "Jane Smith",
      avatar: "/cheerful-delivery-on-the-go.png",
      status: "picked_up",
    },
  },
  {
    id: "JE-5286",
    customer: {
      name: "James Taylor",
      address: "23 Colomberie, St Helier, JE2 4QA",
      avatar: "/thoughtful-bearded-man.png",
    },
    items: [
      { name: "Jersey Cream Brûlée", quantity: 1, price: 7.95 },
      { name: "Classic Prawn Cocktail", quantity: 1, price: 8.95 },
    ],
    total: 16.9,
    status: "delivered",
    time: "1 hour ago",
    paymentMethod: "Cash",
    notes: "",
    driver: {
      id: "D003",
      name: "Mike Johnson",
      avatar: "/friendly-delivery.png",
      status: "delivered",
    },
  },
  {
    id: "JE-5285",
    customer: {
      name: "Olivia Davis",
      address: "5 Le Mont Felard, St Lawrence, JE3 1JA",
      avatar: "/fiery-portrait.png",
    },
    items: [
      { name: "Jersey Beef Burger", quantity: 2, price: 16.95 },
      { name: "French Onion Soup", quantity: 1, price: 7.95 },
    ],
    total: 41.85,
    status: "delivered",
    time: "2 hours ago",
    paymentMethod: "Card",
    notes: "",
    driver: {
      id: "D001",
      name: "John Driver",
      avatar: "/driver-avatar.png",
      status: "delivered",
    },
  },
  {
    id: "JE-5284",
    customer: {
      name: "Daniel Smith",
      address: "12 Gorey Village, Grouville, JE3 9EP",
      avatar: "/thoughtful-portrait.png",
    },
    items: [
      { name: "Grilled Sea Bass", quantity: 1, price: 22.95 },
      { name: "Apple Crumble", quantity: 1, price: 6.95 },
    ],
    total: 29.9,
    status: "cancelled",
    time: "3 hours ago",
    paymentMethod: "Card",
    notes: "Customer called to cancel",
    driver: null,
  },
]

// Mock data for available drivers
const availableDrivers = [
  {
    id: "D001",
    name: "John Driver",
    avatar: "/driver-avatar.png",
    status: "available",
    rating: 4.9,
    deliveries: 245,
    vehicle: "Scooter",
  },
  {
    id: "D002",
    name: "Jane Smith",
    avatar: "/cheerful-delivery-on-the-go.png",
    status: "busy",
    rating: 4.8,
    deliveries: 189,
    vehicle: "Bicycle",
  },
  {
    id: "D003",
    name: "Mike Johnson",
    avatar: "/friendly-delivery.png",
    status: "available",
    rating: 4.7,
    deliveries: 312,
    vehicle: "Car",
  },
  {
    id: "D004",
    name: "Sarah Williams",
    avatar: "/placeholder.svg?height=40&width=40&query=female with helmet",
    status: "available",
    rating: 4.9,
    deliveries: 156,
    vehicle: "Scooter",
  },
]

// Status badge component
function OrderStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "new":
      return (
        <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
          <Clock className="mr-1 h-3 w-3" />
          New Order
        </Badge>
      )
    case "preparing":
      return (
        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
          <Clock className="mr-1 h-3 w-3" />
          Preparing
        </Badge>
      )
    case "out_for_delivery":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Truck className="mr-1 h-3 w-3" />
          Out for Delivery
        </Badge>
      )
    case "delivered":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle2 className="mr-1 h-3 w-3" />
          Delivered
        </Badge>
      )
    case "cancelled":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <XCircle className="mr-1 h-3 w-3" />
          Cancelled
        </Badge>
      )
    default:
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
          <AlertCircle className="mr-1 h-3 w-3" />
          Unknown
        </Badge>
      )
  }
}

// Driver status badge component
function DriverStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "available":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Available
        </Badge>
      )
    case "busy":
      return (
        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
          Busy
        </Badge>
      )
    case "assigned":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          Assigned
        </Badge>
      )
    case "picked_up":
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          Picked Up
        </Badge>
      )
    case "delivered":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Delivered
        </Badge>
      )
    default:
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
          Unknown
        </Badge>
      )
  }
}

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [isAssignDriverDialogOpen, setIsAssignDriverDialogOpen] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<any>(null)

  // Filter orders based on active tab and search query
  const filteredOrders = orders.filter((order) => {
    // Filter by tab
    if (activeTab !== "all" && order.status !== activeTab) {
      return false
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        order.id.toLowerCase().includes(query) ||
        order.customer.name.toLowerCase().includes(query) ||
        order.items.some((item) => item.name.toLowerCase().includes(query))
      )
    }

    return true
  })

  const handleAssignDriver = (order: any) => {
    setSelectedOrder(order)
    setIsAssignDriverDialogOpen(true)
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Orders Management</h1>
          <p className="text-gray-500">Manage and track all your restaurant orders</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline">Export Orders</Button>
          <Button className="bg-emerald-600 hover:bg-emerald-700">Create Manual Order</Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search orders, customers..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-3">
          <Select defaultValue="today">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="flex items-center">
            <Filter size={16} className="mr-2" />
            Filters
            <ChevronDown size={16} className="ml-2" />
          </Button>
        </div>
      </div>

      {/* Order Tabs */}
      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-6 mb-4">
          <TabsTrigger value="all">All Orders</TabsTrigger>
          <TabsTrigger value="new">New</TabsTrigger>
          <TabsTrigger value="preparing">Preparing</TabsTrigger>
          <TabsTrigger value="out_for_delivery">Out for Delivery</TabsTrigger>
          <TabsTrigger value="delivered">Delivered</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No orders found</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>

        <TabsContent value="new" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No new orders</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>

        <TabsContent value="preparing" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No orders in preparation</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>

        <TabsContent value="out_for_delivery" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No orders out for delivery</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>

        <TabsContent value="delivered" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No delivered orders</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>

        <TabsContent value="cancelled" className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No cancelled orders</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} onAssignDriver={handleAssignDriver} />
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Assign Driver Dialog */}
      <Dialog open={isAssignDriverDialogOpen} onOpenChange={setIsAssignDriverDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Assign Driver to Order #{selectedOrder?.id}</DialogTitle>
            <DialogDescription>Select a driver to deliver this order</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
              {availableDrivers.map((driver) => (
                <div
                  key={driver.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    // In a real app, you would update the order with the assigned driver
                    setIsAssignDriverDialogOpen(false)
                  }}
                >
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      <AvatarImage src={driver.avatar || "/placeholder.svg"} alt={driver.name} />
                      <AvatarFallback>{driver.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{driver.name}</p>
                      <div className="flex items-center text-sm text-gray-500">
                        <Bike className="h-3 w-3 mr-1" />
                        {driver.vehicle}
                        <span className="mx-1">•</span>
                        <span>{driver.deliveries} deliveries</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 text-yellow-500 mr-1" />
                      <span className="font-medium">{driver.rating}</span>
                    </div>
                    <DriverStatusBadge status={driver.status} />
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDriverDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Order Card Component
function OrderCard({ order, onAssignDriver }: { order: any; onAssignDriver: (order: any) => void }) {
  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <div className="flex items-center mb-2 md:mb-0">
              <div className="font-medium mr-3">Order #{order.id}</div>
              <OrderStatusBadge status={order.status} />
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="h-4 w-4 mr-1" />
              {order.time}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Customer Info */}
            <div className="flex items-start">
              <Avatar className="h-10 w-10 mr-3">
                <AvatarImage src={order.customer.avatar || "/placeholder.svg"} alt={order.customer.name} />
                <AvatarFallback>{order.customer.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{order.customer.name}</p>
                <p className="text-sm text-gray-500">{order.customer.address}</p>
                <p className="text-sm text-gray-500">Payment: {order.paymentMethod}</p>
              </div>
            </div>

            {/* Order Items */}
            <div>
              <p className="font-medium mb-2">Order Items</p>
              <ul className="text-sm text-gray-600 space-y-1">
                {order.items.map((item: any, index: number) => (
                  <li key={index}>
                    {item.quantity}× {item.name} (£{item.price.toFixed(2)})
                  </li>
                ))}
              </ul>
              {order.notes && (
                <div className="mt-2">
                  <p className="text-xs font-medium text-gray-500">Notes:</p>
                  <p className="text-sm text-gray-600">{order.notes}</p>
                </div>
              )}
            </div>

            {/* Order Actions */}
            <div className="flex flex-col items-end justify-between">
              <div className="text-right">
                <p className="text-sm text-gray-500">Total Amount</p>
                <p className="text-xl font-bold">£{order.total.toFixed(2)}</p>
              </div>

              {/* Driver Information */}
              {order.driver && (
                <div className="flex items-center mt-2 mb-2 bg-gray-50 p-2 rounded-md w-full">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src={order.driver.avatar || "/placeholder.svg"} alt={order.driver.name} />
                    <AvatarFallback>{order.driver.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{order.driver.name}</p>
                    <div className="flex items-center">
                      <DriverStatusBadge status={order.driver.status} />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex mt-2 space-x-2">
                {order.status === "new" && (
                  <>
                    <Button size="sm" variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                      Reject
                    </Button>
                    <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700">
                      Accept
                    </Button>
                  </>
                )}

                {order.status === "preparing" && !order.driver && (
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => onAssignDriver(order)}>
                    <User className="mr-2 h-4 w-4" />
                    Assign Driver
                  </Button>
                )}

                {order.status === "preparing" && order.driver && (
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Mark Ready for Pickup
                  </Button>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Link href={`/restaurant-admin/orders/${order.id}`} className="w-full">
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>Print Receipt</DropdownMenuItem>
                    <DropdownMenuItem>Contact Customer</DropdownMenuItem>
                    {order.driver && <DropdownMenuItem>Contact Driver</DropdownMenuItem>}
                    {(order.status === "preparing" || order.status === "new") && !order.driver && (
                      <DropdownMenuItem onClick={() => onAssignDriver(order)}>Assign Driver</DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
