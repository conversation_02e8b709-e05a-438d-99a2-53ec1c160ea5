import { Clock, Truck, Store, ShoppingBag, Calendar } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getBusinessTypeIconWithStyle } from "@/utils/business-type-icons"
import { Business, OrderDetails } from "../types"

// Helper function to format variant and customization details
const formatItemDetails = (item: any): string[] => {
  const details: string[] = [];

  // Add variant information
  if (item.variantName) {
    details.push(`Size: ${item.variantName}`);
  }

  // Add customization information
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
};

interface BusinessCardProps {
  business: Business
  businessIndex: number
  orderDetails: OrderDetails
}

export function BusinessCard({ business, businessIndex, orderDetails }: BusinessCardProps) {
  return (
    <Card key={`main-business-${business.id}-${businessIndex}`} className="border-0 shadow-lg bg-white/90 backdrop-blur-sm overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="pb-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-4 shadow-md">
              {getBusinessTypeIconWithStyle(business.type, "h-6 w-6 text-white")}
            </div>
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:gap-3 mb-1">
                <h3 className="font-bold text-gray-900 text-lg">{business.name}</h3>
                {business.orderNumber && (
                  <div className="bg-emerald-50 border border-emerald-200 px-2 py-1 rounded-md">
                    <p className="text-sm text-emerald-700 font-bold">
                      {business.orderNumber}
                    </p>
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-600 capitalize font-medium">{business.type}</p>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            <div className="text-right">
              <Badge
                variant={business.deliveryMethod === 'delivery' ? 'default' : 'outline'}
                className={business.deliveryMethod === 'delivery'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md mb-1'
                  : 'border-orange-400 text-orange-600 bg-orange-50 font-medium mb-1'
                }
              >
                {business.deliveryMethod === 'delivery' ? (
                  <><Truck className="h-3 w-3 mr-1" />Delivery</>
                ) : (
                  <><Store className="h-3 w-3 mr-1" />Pickup</>
                )}
              </Badge>

              {/* Timing information next to badge */}
              <div className="text-xs text-gray-600 flex items-center justify-end">
                <Clock className="h-3 w-3 mr-1" />
                {(() => {
                  // Show scheduled time if available
                  if (business.deliveryType === 'scheduled' && business.scheduledTime) {
                    return new Date(business.scheduledTime).toLocaleString('en-GB', {
                      weekday: 'short',
                      day: 'numeric',
                      month: 'short',
                      hour: '2-digit',
                      minute: '2-digit'
                    });
                  }

                  // Show estimated time or default
                  const timeText = String(business.estimatedTime || orderDetails.estimatedDeliveryTime || 'As soon as possible');
                  const formattedTime = business.deliveryMethod === 'delivery'
                    ? timeText
                    : timeText.replace(/delivery/gi, 'pickup');

                  // Add "minutes" if the text is just a number
                  if (/^\d+$/.test(formattedTime)) {
                    return `${formattedTime} minutes`;
                  }
                  return formattedTime;
                })()}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        {/* Business Items */}
        <div className="space-y-3 mb-4">
          {business.items.map((item, itemIndex) => {
            const itemDetails = formatItemDetails(item);
            return (
              <div key={`main-business-${business.id}-item-${item.id}-${itemIndex}-${item.variantName || 'no-variant'}-${item.customizations?.map(c => c.groupName).join('-') || 'no-custom'}`} className="flex justify-between py-2 border-b border-gray-100 last:border-0">
                <div className="flex items-start">
                  <span className="font-medium text-blue-700 mr-2">{item.quantity}×</span>
                  <div>
                    <span className="text-gray-800">{item.name}</span>
                    {itemDetails.length > 0 && (
                      <p className="text-xs text-gray-500 mt-1">{itemDetails.join(", ")}</p>
                    )}
                  </div>
                </div>
                <span className="font-medium text-gray-800">£{(item.price * item.quantity).toFixed(2)}</span>
              </div>
            );
          })}
        </div>

        {/* Business Delivery Info */}
        <div className="flex flex-wrap gap-4 mb-4 text-sm">
          {business.preparationTime && (
            <div className="flex items-center text-gray-600">
              <Clock className="h-4 w-4 mr-1 text-emerald-600" />
              <span>Prep time: {business.preparationTime} min</span>
            </div>
          )}

          {business.deliveryTime && business.deliveryMethod === 'delivery' && (
            <div className="flex items-center text-gray-600">
              <Truck className="h-4 w-4 mr-1 text-blue-600" />
              <span>Delivery time: {business.deliveryTime} minutes</span>
            </div>
          )}

          {business.deliveryType === 'scheduled' && business.scheduledTime && (
            <div className="flex items-center text-gray-600">
              <Calendar className="h-4 w-4 mr-1 text-purple-600" />
              <span>Scheduled for: {new Date(business.scheduledTime).toLocaleString('en-GB', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}</span>
            </div>
          )}
        </div>

        {/* Business Totals */}
        <div className="space-y-2 pt-2 border-t border-gray-200">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Subtotal</span>
            <span className="text-gray-800">£{business.subtotal.toFixed(2)}</span>
          </div>

          {business.deliveryMethod === 'delivery' && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Delivery Fee</span>
              <span className="text-gray-800">
                {business.deliveryFee === 0 ? 'Free' : `£${business.deliveryFee.toFixed(2)}`}
              </span>
            </div>
          )}

          <div className="flex justify-between font-medium text-sm pt-1">
            <span>Business Total</span>
            <span className="text-blue-700">
              £{(business.subtotal + (business.deliveryMethod === 'delivery' ? business.deliveryFee : 0)).toFixed(2)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
