import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const orderId = params.id

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }





    // Get order details with business information
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        business_name,
        customer_name,
        customer_phone,
        delivery_address,
        postcode,
        parish,
        delivery_type,
        total,
        delivery_fee,
        status,
        driver_id,
        preparation_time,
        estimated_delivery_time,
        delivery_distance_km,
        ready_time,
        created_at,
        cart_id,
        customer_coordinates,
        driver_pickup_confirmed,
        business_pickup_confirmed,
        businesses!inner (
          id,
          name,
          address,
          location,
          phone,
          latitude,
          coordinates
        )
      `)
      .or(`order_number.eq.${orderId},id.eq.${parseInt(orderId, 10) || 0}`)
      .single()



    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    // Verify this order is assigned to this driver (if it has a driver assigned)
    if (order.driver_id && order.driver_id !== driverProfile.id) {
      return NextResponse.json(
        { error: "Order is not assigned to this driver" },
        { status: 403 }
      )
    }

    // Get order status history to find when it was assigned
    const { data: statusHistory, error: historyError } = await supabase
      .from('order_status_history')
      .select('status, created_at, notes')
      .eq('order_id', order.id)
      .order('created_at', { ascending: true })

    if (historyError) {
      console.error('Error fetching status history:', historyError)
    }

    // Find the assigned timestamp
    const assignedHistory = statusHistory?.find(h => h.status === 'assigned')
    const assignedAt = assignedHistory?.created_at

    // Get cart items for this order
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select(`
        id,
        name,
        quantity,
        price,
        business_id,
        product_id,
        image_url
      `)
      .eq('cart_id', order.cart_id)

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError)
    }

    // Use the stored distance from the database (calculated at checkout)
    const calculatedDistance = order.delivery_distance_km

    // Format the response
    const orderData = {
      ...order,
      delivery_distance_km: calculatedDistance,
      assigned_at: assignedAt,
      cart_items: cartItems || [],
      status_history: statusHistory || []
    }

    return NextResponse.json({
      success: true,
      order: orderData
    })

  } catch (error) {
    console.error('Error in driver order details API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
