/**
 * Test script to set a business to aisle layout for testing
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAisleLayout() {
  try {
    console.log('🧪 Testing aisle layout setup...\n');

    // Find a business to test with (let's use Jersey Wings)
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-wings')
      .single();

    if (businessError) {
      console.error('❌ Error finding business:', businessError);
      return;
    }

    if (!business) {
      console.error('❌ Business not found');
      return;
    }

    console.log('📋 Current business info:');
    console.log(`   Name: ${business.name}`);
    console.log(`   Slug: ${business.slug}`);
    console.log(`   Current layout: ${business.page_layout || 'standard'}\n`);

    // Update to aisle layout
    console.log('🔄 Setting business to aisle layout...');
    const { data: updatedBusiness, error: updateError } = await supabase
      .from('businesses')
      .update({ page_layout: 'aisle' })
      .eq('id', business.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating business:', updateError);
      return;
    }

    console.log('✅ Business updated successfully!');
    console.log(`   New layout: ${updatedBusiness.page_layout}\n`);

    // Check if business has categories
    const { data: categories, error: categoriesError } = await supabase
      .from('business_custom_categories')
      .select('id, name, level, parent_category')
      .eq('business_id', business.id)
      .order('level', { ascending: true })
      .order('display_order', { ascending: true });

    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
      return;
    }

    console.log('📂 Business categories:');
    if (categories && categories.length > 0) {
      categories.forEach(cat => {
        const indent = '  '.repeat(cat.level);
        console.log(`   ${indent}Level ${cat.level}: ${cat.name} (ID: ${cat.id})`);
      });
    } else {
      console.log('   No custom categories found');
    }

    console.log('\n🎉 Test complete!');
    console.log(`\n🌐 You can now test the aisle layout at:`);
    console.log(`   http://localhost:3000/business/${business.slug}/categories`);
    console.log(`\n📝 To revert back to standard layout, run:`);
    console.log(`   UPDATE businesses SET page_layout = 'standard' WHERE slug = '${business.slug}';`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testAisleLayout();
