"use client"

import {
  Clock,
  CheckCircle2,
  Truck,
  Package,
  XCircle,
  AlertCircle
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface BusinessStatusBadgeProps {
  status: string
  className?: string
  showIcon?: boolean
  size?: "sm" | "md" | "lg"
}

export function BusinessStatusBadge({
  status,
  className,
  showIcon = true,
  size = "md"
}: BusinessStatusBadgeProps) {
  const iconSize = size === "sm" ? "h-3 w-3" : size === "lg" ? "h-4 w-4" : "h-3.5 w-3.5"
  const iconMargin = showIcon ? "mr-1" : ""

  switch (status) {
    case "pending":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 hover:text-amber-800 transition-colors",
            className
          )}
        >
          {showIcon && <Clock className={`${iconSize} ${iconMargin}`} />}
          Pending
        </Badge>
      )
    case "confirmed":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100 hover:text-yellow-800 transition-colors",
            className
          )}
        >
          {showIcon && <AlertCircle className={`${iconSize} ${iconMargin}`} />}
          Confirmed
        </Badge>
      )
    case "preparing":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:text-blue-800 transition-colors",
            className
          )}
        >
          {showIcon && <Clock className={`${iconSize} ${iconMargin}`} />}
          Preparing
        </Badge>
      )
    case "ready":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 hover:text-indigo-800 transition-colors",
            className
          )}
        >
          {showIcon && <Package className={`${iconSize} ${iconMargin}`} />}
          Ready
        </Badge>
      )
    case "out_for_delivery":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:text-purple-800 transition-colors",
            className
          )}
        >
          {showIcon && <Truck className={`${iconSize} ${iconMargin}`} />}
          Out for Delivery
        </Badge>
      )
    case "delivered":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100 hover:text-emerald-800 transition-colors",
            className
          )}
        >
          {showIcon && <CheckCircle2 className={`${iconSize} ${iconMargin}`} />}
          Delivered
        </Badge>
      )
    case "cancelled":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800 transition-colors",
            className
          )}
        >
          {showIcon && <XCircle className={`${iconSize} ${iconMargin}`} />}
          Cancelled
        </Badge>
      )
    case "partially_delivered":
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-teal-50 text-teal-700 border-teal-200 hover:bg-teal-100 hover:text-teal-800 transition-colors",
            className
          )}
        >
          {showIcon && <Truck className={`${iconSize} ${iconMargin}`} />}
          Partially Delivered
        </Badge>
      )
    default:
      return (
        <Badge
          variant="outline"
          className={cn(
            "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:text-slate-800 transition-colors",
            className
          )}
        >
          {status}
        </Badge>
      )
  }
}
