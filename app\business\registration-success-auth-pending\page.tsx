"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, Mail, ArrowRight, Building2 } from "lucide-react"

export default function RegistrationSuccessAuthPending() {
  const router = useRouter()
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [userEmail, setUserEmail] = useState<string>("")

  useEffect(() => {
    // Get registration data from localStorage
    const data = localStorage.getItem('business_registration_success_auth_pending')
    if (data) {
      const parsed = JSON.parse(data)
      setRegistrationData(parsed)
      setUserEmail(parsed.userEmail || "")
    } else {
      // If no data, redirect to registration
      router.push("/partners/register-business")
    }
  }, [router])

  const handleCreateAccount = () => {
    // Store the business registration data for linking after account creation
    if (registrationData) {
      localStorage.setItem('pending_business_link', JSON.stringify({
        businessId: registrationData.registrationId,
        businessName: registrationData.businessName,
        userEmail: userEmail
      }))
    }

    // Redirect to sign-up with return URL and pre-filled name
    const userName = registrationData?.userName || '';
    router.push(`/register?email=${encodeURIComponent(userEmail)}&name=${encodeURIComponent(userName)}&returnUrl=/business-admin/dashboard&linkBusiness=true`)
  }

  const handleSignIn = () => {
    // Store the business registration data for linking after sign-in
    if (registrationData) {
      localStorage.setItem('pending_business_link', JSON.stringify({
        businessId: registrationData.registrationId,
        businessName: registrationData.businessName,
        userEmail: userEmail
      }))
    }

    // Redirect to sign-in with return URL
    router.push(`/login?email=${encodeURIComponent(userEmail)}&returnUrl=/business-admin/dashboard&linkBusiness=true`)
  }

  if (!registrationData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-emerald-600" />
          </div>
          <CardTitle className="text-2xl text-emerald-600">
            🎉 Business Registration Successful!
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Business Details */}
          <Alert className="bg-emerald-50 border-emerald-200">
            <Building2 className="h-4 w-4 text-emerald-600" />
            <AlertDescription className="text-emerald-800">
              <strong>{registrationData.businessName}</strong> has been successfully registered with Loop Jersey!
              <br />
              <span className="text-sm text-emerald-600">Registration ID: #{registrationData.registrationId}</span>
            </AlertDescription>
          </Alert>

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Mail className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">
                  One More Step: Create Your Account
                </h3>
                <p className="text-blue-800 text-sm mb-4">
                  To access your business dashboard, you'll need to create your personal account using the email: <strong>{userEmail}</strong>
                  <br />
                  <span className="text-blue-700 font-medium">Note: You'll need to verify your email address after creating your account.</span>
                </p>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2 text-sm text-blue-700">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Your business is already registered and waiting for you</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-blue-700">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>We'll automatically link your account to your business</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-blue-700">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>You'll be able to access your dashboard immediately</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleCreateAccount}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
              size="lg"
            >
              Create My Account
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>

            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">
                Already have an account with this email?
              </p>
              <Button
                onClick={handleSignIn}
                variant="outline"
                className="w-full"
              >
                Sign In Instead
              </Button>
            </div>
          </div>

          {/* Help Text */}
          <div className="text-center text-sm text-gray-500 border-t pt-4">
            <p>
              Need help? Contact us via the Connections Hub
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
