"use client"

import { useState } from "react"
import { HelpCircle, X, ChevronRight, Clock, MapPin, DollarSign, Store, Image, Users } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"

interface SettingsHelpProps {
  activeTab?: string
  className?: string
}

interface HelpSection {
  id: string
  title: string
  icon: any
  description: string
  tips: string[]
  requirements?: string[]
  examples?: string[]
}

export default function SettingsHelp({ activeTab = "basic", className = "" }: SettingsHelpProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedSection, setExpandedSection] = useState<string | null>(null)

  const helpSections: Record<string, HelpSection> = {
    basic: {
      id: 'basic',
      title: 'Business Information',
      icon: Store,
      description: 'Essential details that customers will see when browsing your business.',
      tips: [
        'Use a clear, descriptive business name that customers will recognize',
        'Write a compelling description highlighting what makes your business special',
        'Ensure contact details are accurate for customer communication',
        'Include any specialties or unique selling points in your description'
      ],
      requirements: [
        'Business name (required)',
        'Description (required)',
        'Phone number (required)',
        'Email address (required)'
      ],
      examples: [
        'Name: "Jersey Grill & Steakhouse"',
        'Description: "Premium steaks and local Jersey produce in the heart of St. Helier"'
      ]
    },
    location: {
      id: 'location',
      title: 'Location & Delivery',
      icon: MapPin,
      description: 'Set your location and delivery coverage to reach the right customers.',
      tips: [
        'Use your full business address including postcode for accurate location',
        'Set a realistic delivery radius based on your capacity',
        'Jersey postcodes (JE1-JE5) are automatically geocoded for accuracy',
        'Consider traffic and distance when setting delivery areas'
      ],
      requirements: [
        'Full business address (required)',
        'Jersey postcode (required)',
        'Delivery radius in kilometers (required)'
      ],
      examples: [
        'Address: "15 King Street, St. Helier"',
        'Postcode: "JE2 4WF"',
        'Delivery radius: "5 km" (covers most of St. Helier)'
      ]
    },
    hours: {
      id: 'hours',
      title: 'Opening Hours',
      icon: Clock,
      description: 'Set when customers can place orders and when you\'re available.',
      tips: [
        'Set realistic hours that match your actual operating times',
        'Consider prep time - stop taking orders 30-60 minutes before closing',
        'Different hours for different days are perfectly normal',
        'Customers can only place orders during your open hours'
      ],
      requirements: [
        'Opening and closing times for each day',
        'Use 24-hour format (e.g., 09:00, 17:30)',
        'Set to "Closed" for days you don\'t operate'
      ],
      examples: [
        'Monday-Friday: 11:00 - 21:00',
        'Saturday: 12:00 - 22:00',
        'Sunday: 12:00 - 20:00'
      ]
    },
    pricing: {
      id: 'pricing',
      title: 'Pricing & Fees',
      icon: DollarSign,
      description: 'Configure your minimum order amounts and delivery fees.',
      tips: [
        'Set minimum order amounts that make delivery worthwhile',
        'Consider your costs when setting delivery fees',
        'Distance-based pricing can be fairer for customers',
        'Review competitor pricing in your area'
      ],
      requirements: [
        'Minimum order amount (£)',
        'Delivery fee structure',
        'Base delivery fee (£)'
      ],
      examples: [
        'Minimum order: £15.00',
        'Fixed delivery fee: £2.50',
        'Distance-based: £1.00 + £0.50/km'
      ]
    },
    branding: {
      id: 'branding',
      title: 'Logo & Branding',
      icon: Image,
      description: 'Upload your logo and branding materials to stand out.',
      tips: [
        'Use high-quality images (minimum 300x300px for logos)',
        'Square logos work best for profile pictures',
        'Banner images should be landscape format (16:9 ratio)',
        'Keep file sizes under 2MB for faster loading'
      ],
      requirements: [
        'Logo image (PNG, JPG, or WebP)',
        'Recommended size: 500x500px or larger',
        'Banner image (optional): 1200x675px'
      ],
      examples: [
        'Logo: Square format, clear on white background',
        'Banner: Showcase your best dishes or restaurant interior'
      ]
    },
    team: {
      id: 'team',
      title: 'Team Management',
      icon: Users,
      description: 'Add team members and manage their access to your business admin.',
      tips: [
        'Only add trusted team members who need admin access',
        'Staff members can help manage orders and settings',
        'Each team member needs their own Loop Jersey account',
        'You can remove team members at any time'
      ],
      requirements: [
        'Team member email address',
        'They must have a Loop Jersey account',
        'Choose appropriate access level'
      ],
      examples: [
        'Manager: Full access to all settings and orders',
        'Staff: Access to orders and basic settings only'
      ]
    }
  }

  const currentSection = helpSections[activeTab] || helpSections.basic

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 z-50 bg-blue-600 text-white border-blue-600 hover:bg-blue-700 shadow-lg ${className}`}
      >
        <HelpCircle className="mr-2 h-4 w-4" />
        Need Help?
      </Button>
    )
  }

  return (
    <Card className={`fixed bottom-6 right-6 z-50 w-96 max-h-[80vh] overflow-hidden shadow-xl border-blue-200 ${className}`}>
      <CardHeader className="pb-3 bg-blue-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="bg-blue-100 rounded-full p-1.5">
              <currentSection.icon className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-sm text-blue-900">{currentSection.title}</CardTitle>
              <CardDescription className="text-xs text-blue-700">
                Setup guidance
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-4 space-y-4 overflow-y-auto max-h-[60vh]">
        {/* Description */}
        <p className="text-sm text-gray-700">{currentSection.description}</p>

        {/* Requirements */}
        {currentSection.requirements && (
          <Collapsible 
            open={expandedSection === 'requirements'} 
            onOpenChange={() => setExpandedSection(expandedSection === 'requirements' ? null : 'requirements')}
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-2 h-auto">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    Required
                  </Badge>
                  <span className="text-sm font-medium">What you need</span>
                </div>
                <ChevronRight className={`h-4 w-4 transition-transform ${
                  expandedSection === 'requirements' ? 'rotate-90' : ''
                }`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 pt-2">
              {currentSection.requirements.map((req, index) => (
                <div key={index} className="text-sm text-gray-600 pl-4 border-l-2 border-red-100">
                  • {req}
                </div>
              ))}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Tips */}
        <Collapsible 
          open={expandedSection === 'tips'} 
          onOpenChange={() => setExpandedSection(expandedSection === 'tips' ? null : 'tips')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-2 h-auto">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  Tips
                </Badge>
                <span className="text-sm font-medium">Best practices</span>
              </div>
              <ChevronRight className={`h-4 w-4 transition-transform ${
                expandedSection === 'tips' ? 'rotate-90' : ''
              }`} />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1 pt-2">
            {currentSection.tips.map((tip, index) => (
              <div key={index} className="text-sm text-gray-600 pl-4 border-l-2 border-blue-100">
                💡 {tip}
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* Examples */}
        {currentSection.examples && (
          <Collapsible 
            open={expandedSection === 'examples'} 
            onOpenChange={() => setExpandedSection(expandedSection === 'examples' ? null : 'examples')}
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-2 h-auto">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Examples
                  </Badge>
                  <span className="text-sm font-medium">See examples</span>
                </div>
                <ChevronRight className={`h-4 w-4 transition-transform ${
                  expandedSection === 'examples' ? 'rotate-90' : ''
                }`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 pt-2">
              {currentSection.examples.map((example, index) => (
                <div key={index} className="text-sm text-gray-600 pl-4 border-l-2 border-green-100 bg-green-50 p-2 rounded">
                  {example}
                </div>
              ))}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Quick Actions */}
        <div className="pt-2 border-t border-gray-200">
          <p className="text-xs text-gray-500 mb-2">Need more help?</p>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" className="text-xs h-7 flex-1">
              Contact Support
            </Button>
            <Button size="sm" variant="outline" className="text-xs h-7 flex-1">
              View Guide
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
