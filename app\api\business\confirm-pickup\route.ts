import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { orderId, businessId } = await request.json()

    if (!orderId || !businessId) {
      return NextResponse.json(
        { error: "Order ID and Business ID are required" },
        { status: 400 }
      )
    }

    // Call the dual confirmation function for business confirmation
    const { data: result, error: confirmError } = await supabase
      .rpc('confirm_pickup', {
        order_id: orderId,
        confirming_party: 'business',
        business_id: businessId
      })

    if (confirmError) {
      console.error('Error confirming pickup:', confirmError)
      return NextResponse.json(
        { error: "Failed to confirm pickup" },
        { status: 500 }
      )
    }

    if (!result || !result.success) {
      return NextResponse.json(
        { error: result?.error || "Failed to confirm pickup" },
        { status: 400 }
      )
    }

    // Trigger enhanced notifications for pickup confirmation
    try {
      const { updateOrderStatusWithNotifications } = await import('../../orders/enhanced-status-update')
      await updateOrderStatusWithNotifications({
        orderId: orderId,
        newStatus: 'out_for_delivery',
        notes: `Pickup confirmed by business - order is now out for delivery`,
        updatedBy: businessId, // Use business ID as updater
        businessId: businessId
      })
    } catch (notificationError) {
      console.warn('Failed to send pickup confirmation notifications:', notificationError)
      // Don't fail the request if notifications fail
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      order_id: orderId,
      business_confirmed: true,
      driver_confirmed: true,
      both_confirmed: true,
      new_status: result.new_status
    })

  } catch (error) {
    console.error('Error in business confirm pickup API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
