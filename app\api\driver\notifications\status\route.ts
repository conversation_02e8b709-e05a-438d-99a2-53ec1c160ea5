import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Check if driver has active push notifications
export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Use the auth_id for push subscriptions
    const authUserId = user.auth_id

    // Check for active subscriptions
    const { data: subscriptions, error: subError } = await supabase
      .from('push_subscriptions')
      .select('id, device_type, is_active, created_at')
      .eq('user_id', authUserId)
      .eq('is_active', true)

    if (subError) {
      console.error('Error checking subscriptions:', subError)
      return NextResponse.json(
        { error: 'Failed to check subscription status' },
        { status: 500 }
      )
    }

    const hasActiveSubscription = subscriptions && subscriptions.length > 0

    return NextResponse.json({
      hasActiveSubscription,
      subscriptionCount: subscriptions?.length || 0,
      subscriptions: subscriptions || []
    })

  } catch (error: any) {
    console.error('Error checking driver notification status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
