import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filter = searchParams.get('filter') || 'all'

    // Get driver applications with user details
    let query = supabase
      .from('driver_profiles')
      .select(`
        id,
        user_id,
        vehicle_type,
        license_number,
        insurance_number,
        vehicle_registration,
        profile_image_url,
        is_verified,
        is_active,
        notes,
        created_at,
        updated_at,
        users!driver_profiles_user_id_fkey (
          id,
          name,
          first_name,
          last_name,
          email,
          phone
        )
      `)
      .order('created_at', { ascending: false })

    // Apply filters
    switch (filter) {
      case 'pending':
        query = query.eq('is_verified', false).eq('is_active', true)
        break
      case 'approved':
        query = query.eq('is_verified', true).eq('is_active', true)
        break
      case 'rejected':
        query = query.eq('is_active', false)
        break
      // 'all' - no additional filters
    }

    const { data: applications, error } = await query

    if (error) {
      console.error('Error fetching driver applications:', error)
      return NextResponse.json(
        { error: "Failed to fetch driver applications" },
        { status: 500 }
      )
    }

    // Transform the data to flatten user details
    const transformedApplications = applications?.map(app => ({
      id: app.id,
      user_id: app.user_id,
      vehicle_type: app.vehicle_type,
      license_number: app.license_number,
      insurance_number: app.insurance_number,
      vehicle_registration: app.vehicle_registration,
      profile_image_url: app.profile_image_url,
      is_verified: app.is_verified,
      is_active: app.is_active,
      notes: app.notes,
      created_at: app.created_at,
      updated_at: app.updated_at,
      user_name: app.users.name,
      user_first_name: app.users.first_name,
      user_last_name: app.users.last_name,
      user_email: app.users.email,
      user_phone: app.users.phone
    })) || []

    return NextResponse.json({
      success: true,
      applications: transformedApplications,
      count: transformedApplications.length
    })

  } catch (error) {
    console.error('Error in driver applications API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
