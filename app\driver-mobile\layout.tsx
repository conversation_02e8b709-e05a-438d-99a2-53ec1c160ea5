import type { ReactNode } from "react"
import { Inter } from "next/font/google"
import Link from "next/link"
import { Home, Package, Clock, Wallet, User, Bell, LogOut } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import type { Metadata, Viewport } from "next"
import RegisterServiceWorker from "./register-sw"
import InstallPrompt from "./install-prompt"
import { NotificationProvider } from "@/context/notification-context"
import { Toaster } from "sonner"
import { DriverInterfaceDetector } from "@/components/driver-interface-detector"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

interface DriverMobileLayoutProps {
  children: ReactNode
}

export const metadata: Metadata = {
  title: "Loop Jersey Driver",
  description: "Loop Jersey delivery driver mobile app",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Loop Jersey Driver",
  },
  formatDetection: {
    telephone: true,
  },
}

export const viewport: Viewport = {
  themeColor: "#10b981",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
}

export default function DriverMobileLayout({ children }: DriverMobileLayoutProps) {
  return (
    <NotificationProvider>
      <DriverInterfaceDetector>
        <div className={`${inter.variable} font-sans min-h-screen bg-gray-50`}>
          <RegisterServiceWorker />
          <InstallPrompt />

          {/* Main Content - No header, handled by individual pages */}
          <main className="min-h-screen">{children}</main>

          {/* Toast notifications */}
          <Toaster
            position="top-center"
            toastOptions={{
              style: {
                background: 'white',
                border: '1px solid #e5e7eb',
                color: '#374151',
              },
            }}
          />
        </div>
      </DriverInterfaceDetector>
    </NotificationProvider>
  )
}
