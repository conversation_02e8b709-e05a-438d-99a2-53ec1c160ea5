"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function DriverRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the driver dashboard
    router.push("/driver/dashboard")
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to driver dashboard...</p>
      </div>
    </div>
  )
}
