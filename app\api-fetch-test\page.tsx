'use client'

import { useState, useEffect } from 'react'

export default function ApiFetchTestPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      
      try {
        // Fetch data from our direct Supabase API
        const response = await fetch('/api/direct-supabase-admin')
        
        if (!response.ok) {
          throw new Error(`API returned ${response.status}`)
        }
        
        const apiData = await response.json()
        setData(apiData)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Direct Supabase API Fetch Test</h1>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      
      {data && (
        <div className="grid grid-cols-1 gap-4">
          <div className="border p-4 rounded">
            <h2 className="text-xl font-semibold mb-2">Business Types</h2>
            {data.businessTypes && data.businessTypes.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.businessTypes.map((type: any) => (
                  <div key={type.id} className="border p-4 rounded">
                    <h3 className="font-bold">{type.name}</h3>
                    <p className="text-sm text-gray-600">ID: {type.id}</p>
                    <p className="text-sm">Slug: {type.slug}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p>No business types found</p>
            )}
          </div>
          
          <div className="border p-4 rounded">
            <h2 className="text-xl font-semibold mb-2">Businesses</h2>
            {data.businesses && data.businesses.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.businesses.map((business: any) => (
                  <div key={business.id} className="border p-4 rounded">
                    <h3 className="font-bold">{business.name}</h3>
                    <p className="text-sm text-gray-600">{business.location}</p>
                    <p className="text-sm">Rating: {business.rating || 'N/A'}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p>No businesses found</p>
            )}
            
            {data.businessesError && (
              <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
                <p>Error fetching businesses: {data.businessesError.status} {data.businessesError.statusText}</p>
                <pre className="text-xs mt-1 overflow-auto">{data.businessesError.text}</pre>
              </div>
            )}
          </div>
          
          <div className="border p-4 rounded">
            <h2 className="text-xl font-semibold mb-2">Tables</h2>
            {data.tables ? (
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96 text-xs">
                {JSON.stringify(data.tables, null, 2)}
              </pre>
            ) : (
              <p>No tables information available</p>
            )}
            
            {data.tablesError && (
              <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
                <p>Error fetching tables: {data.tablesError.status} {data.tablesError.statusText}</p>
                <pre className="text-xs mt-1 overflow-auto">{data.tablesError.text}</pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
