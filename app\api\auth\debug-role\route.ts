import { NextResponse } from "next/server"
import { createServerSupabase } from "@/lib/supabase-server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    // Get the user's session using the server client
    const supabase = await createServerSupabase()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return NextResponse.json(
        {
          authenticated: false,
          error: "Not authenticated"
        },
        { status: 401 }
      )
    }

    const email = session.user.email

    if (!email) {
      return NextResponse.json(
        {
          authenticated: true,
          error: "User email not found"
        },
        { status: 400 }
      )
    }

    // Get user data from the database
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("id, email, role")
      .eq("email", email)
      .single()

    if (userError) {
      return NextResponse.json(
        {
          authenticated: true,
          error: `Error fetching user data: ${userError.message}`
        },
        { status: 500 }
      )
    }

    // Return user role information
    return NextResponse.json({
      authenticated: true,
      user: {
        id: userData.id,
        email: userData.email,
        role: userData.role
      },
      session: {
        id: session.user.id,
        email: session.user.email,
        metadata: session.user.user_metadata
      },
      access: {
        isAdmin: userData.role === 'admin' || userData.role === 'super_admin',
        isSuperAdmin: userData.role === 'super_admin',
        canAccessAdmin: userData.role === 'admin' || userData.role === 'super_admin',
        canAccessSuperAdmin: userData.role === 'super_admin'
      }
    })
  } catch (error: any) {
    console.error("Unexpected error in GET /api/auth/debug-role:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: error.message || "An unexpected error occurred"
      },
      { status: 500 }
    )
  }
}
