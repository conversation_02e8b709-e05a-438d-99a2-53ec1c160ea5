"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"

import { Loader2, CheckCircle, XCircle, User, Calendar, Clock, MapPin } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"

// Define driver request status types
type RequestStatus = "pending" | "approved" | "rejected"

// Define driver request type
interface DriverRequest {
  id: number
  driver_id: string
  business_id: number
  status: RequestStatus
  application_date: string
  created_at: string
  driver_profiles: {
    id: string
    user_id: number
    users: {
      id: number
      name: string
      email: string
    }
  }
}

export default function DriverRequestsPage() {
  console.log("🚀 DriverRequestsPage component is mounting")

  const { toast } = useToast()
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin, isLoading: authLoading } = useAuth()

  console.log("🔍 DriverRequestsPage auth state:", { user: !!user, userProfile: !!userProfile, authLoading })

  // Helper function to get driver name
  const getDriverName = (driver_profiles: DriverRequest['driver_profiles']) => {
    return driver_profiles.users.name || driver_profiles.users.email.split('@')[0];
  }

  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<number | null>(null)
  const [businessId, setBusinessId] = useState<number | null>(null)
  const [driverRequests, setDriverRequests] = useState<DriverRequest[]>([])
  const [userBusinessId, setUserBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // Check if user is admin or super admin
  useEffect(() => {
    console.log("🔍 Admin check:", { isAdmin, isSuperAdmin })
    if (isAdmin || isSuperAdmin) {
      console.log("✅ User is admin, setting isAdminUser to true")
      setIsAdminUser(true)
    } else {
      console.log("✅ User is not admin, setting isAdminUser to false")
      setIsAdminUser(false)
    }
  }, [isAdmin, isSuperAdmin])

  // Fetch the user's business ID from business_managers table
  const fetchUserBusinessId = async () => {
    console.log("🔍 fetchUserBusinessId called with:", { user: !!user, isAdminUser })
    if (!user || isAdminUser) {
      console.log("❌ Skipping business ID fetch - no user or is admin user")
      return
    }

    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || '';
      const response = await fetch(`/api/business-admin/manager-data`, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()

      if (data.businessData?.id) {
        setUserBusinessId(data.businessData.id)
        console.log("✅ Found user business ID:", data.businessData.id)
      }
    } catch (err) {
      console.error("Error fetching user business ID:", err)
    }
  }

  // Fetch user's business ID when user is available
  useEffect(() => {
    console.log("🔍 Business ID fetch useEffect triggered:", { user: !!user, isAdminUser })
    if (user && !isAdminUser) {
      console.log("✅ Calling fetchUserBusinessId")
      fetchUserBusinessId()
    } else {
      console.log("❌ Not calling fetchUserBusinessId:", { hasUser: !!user, isAdminUser })
    }
  }, [user, isAdminUser])

  // Determine the correct business ID to use
  const effectiveBusinessId = (isAdminUser && businessId) ? businessId : userBusinessId;

  // Fetch driver requests
  useEffect(() => {
    console.log("🔄 useEffect triggered with:", { user: !!user, authLoading, effectiveBusinessId })

    async function fetchDriverRequests() {
      console.log("📞 fetchDriverRequests called")

      if (authLoading) {
        console.log("⏳ Auth still loading, skipping fetch")
        return
      }

      if (!user) {
        console.log("❌ No user, skipping fetch")
        return
      }

      if (!effectiveBusinessId) {
        console.log("❌ No business ID available, skipping fetch")
        return
      }

      console.log("✅ Proceeding with user:", user?.email, "businessId:", effectiveBusinessId)

      try {
        setLoading(true)

        // Build URL for the API endpoint (following the same pattern as other business-admin pages)
        let url = '/api/business-admin/driver-requests'

        // Always add the business ID parameter
        url += `?businessId=${effectiveBusinessId}`
        console.log(`Fetching driver requests for business ID: ${effectiveBusinessId}`)

        console.log('Fetching driver requests from:', url)

        // Get the authentication token from localStorage (same pattern as other business-admin pages)
        const token = localStorage.getItem('loop_jersey_auth_token') || '';
        console.log('🔑 Using token:', token ? 'Token found' : 'No token')

        console.log('🌐 Making fetch request...')
        const response = await fetch(url, {
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
        console.log('📡 Fetch response received:', response.status, response.statusText)

        if (!response.ok) {
          console.error("API error response:", response.status, response.statusText)
          throw new Error(`API error: ${response.status}`)
        }

        const result = await response.json()
        console.log("Driver requests API result:", result)

        if (result.error) {
          console.error("API returned error:", result.error)
          toast({
            variant: "destructive",
            title: "Error",
            description: result.error
          })
          return
        }

        console.log("✅ Successfully loaded driver requests:", result.data?.length || 0)
        setDriverRequests(result.data || [])

      } catch (error) {
        console.error("Error fetching driver requests:", error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load driver requests. Please try again."
        })
      } finally {
        console.log("🏁 Setting loading to false")
        setLoading(false)
      }
    }

    console.log("🚀 About to call fetchDriverRequests")
    fetchDriverRequests()
  }, [user, authLoading, effectiveBusinessId])

  // Handle approve driver request
  const handleApprove = async (requestId: number) => {
    if (!effectiveBusinessId) return

    setProcessing(requestId)

    try {
      const request = driverRequests.find(r => r.id === requestId)
      if (!request) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Driver request not found."
        })
        return
      }

      const response = await fetch('/api/business/approve-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          application_id: requestId,
          action: 'approve',
          business_id: effectiveBusinessId
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'approved' } : req
          )
        )

        toast({
          title: "Driver Approved",
          description: data.message
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to approve driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleApprove:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  // Handle reject driver request
  const handleReject = async (requestId: number) => {
    if (!effectiveBusinessId) return

    setProcessing(requestId)

    try {
      const request = driverRequests.find(r => r.id === requestId)
      if (!request) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Driver request not found."
        })
        return
      }

      const response = await fetch('/api/business/approve-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          application_id: requestId,
          action: 'reject',
          reason: 'Not suitable for our business requirements',
          business_id: effectiveBusinessId
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'rejected' } : req
          )
        )

        toast({
          title: "Driver Rejected",
          description: data.message
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to reject driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleReject:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  // Filter requests by status
  const pendingRequests = driverRequests.filter(req => req.status === 'pending')
  const approvedRequests = driverRequests.filter(req => req.status === 'approved')
  const rejectedRequests = driverRequests.filter(req => req.status === 'rejected')

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Driver Requests</h1>
        <p className="text-gray-500">Manage driver requests to deliver for your business</p>
      </div>

      <Tabs defaultValue="pending">
        <TabsList className="mb-4">
          <TabsTrigger value="pending">
            Pending
            {pendingRequests.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {pendingRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {pendingRequests.length > 0 ? (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src=""
                            alt={getDriverName(request.driver_profiles)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver_profiles).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver_profiles)}</h3>
                          <div className="flex items-center space-x-2 mt-2 md:mt-0">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                              onClick={() => handleReject(request.id)}
                              disabled={processing === request.id}
                            >
                              {processing === request.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <XCircle className="h-4 w-4 mr-1" />
                              )}
                              Reject
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
                              onClick={() => handleApprove(request.id)}
                              disabled={processing === request.id}
                            >
                              {processing === request.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <CheckCircle className="h-4 w-4 mr-1" />
                              )}
                              Approve
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver_profiles.users.email}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.application_date), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No pending driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="approved">
          {approvedRequests.length > 0 ? (
            <div className="space-y-4">
              {approvedRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src=""
                            alt={getDriverName(request.driver_profiles)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver_profiles).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver_profiles)}</h3>
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-100 w-fit">
                            Approved
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver_profiles.users.email}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.application_date), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No approved driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rejected">
          {rejectedRequests.length > 0 ? (
            <div className="space-y-4">
              {rejectedRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src=""
                            alt={getDriverName(request.driver_profiles)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver_profiles).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver_profiles)}</h3>
                          <Badge variant="outline" className="border-red-200 text-red-800 w-fit">
                            Rejected
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver_profiles.users.email}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.application_date), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No rejected driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
