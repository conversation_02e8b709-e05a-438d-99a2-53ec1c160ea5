"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Star,
  Users,
  Truck,
  Eye,
  EyeOff,
  MessageSquare,
  TrendingDown,
  TrendingUp,
  Activity,
  Wifi,
  WifiOff
} from "lucide-react"
import Link from "next/link"

interface UnavailableProduct {
  id: string
  name: string
  category: string
  reason: 'out_of_stock' | 'temporarily_disabled' | 'ingredient_shortage'
  disabledAt: Date
  estimatedReturn?: Date
}

interface HealthIssue {
  id: string
  type: 'complaint' | 'rating' | 'system' | 'delivery'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  occurredAt: Date
  resolved: boolean
}

interface BusinessHealthData {
  unavailableProducts: UnavailableProduct[]
  recentIssues: HealthIssue[]
  averageRating: number
  ratingTrend: number // percentage change
  activeDrivers: number
  totalDrivers: number
  systemStatus: 'online' | 'degraded' | 'offline'
  lastUpdated: Date
}

interface BusinessHealthStatusProps {
  healthData: BusinessHealthData
  onToggleProduct?: (productId: string, enabled: boolean) => void
  onResolveIssue?: (issueId: string) => void
  className?: string
}

function UnavailableProductCard({ 
  product, 
  onToggle 
}: { 
  product: UnavailableProduct
  onToggle?: (productId: string, enabled: boolean) => void
}) {
  const getReasonText = (reason: string) => {
    switch (reason) {
      case 'out_of_stock': return 'Out of Stock'
      case 'temporarily_disabled': return 'Temporarily Disabled'
      case 'ingredient_shortage': return 'Ingredient Shortage'
      default: return 'Unavailable'
    }
  }

  const getReasonColor = (reason: string) => {
    switch (reason) {
      case 'out_of_stock': return 'bg-red-100 text-red-800'
      case 'temporarily_disabled': return 'bg-yellow-100 text-yellow-800'
      case 'ingredient_shortage': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTimeSince = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / 3600000)
    const diffMins = Math.floor((diffMs % 3600000) / 60000)
    
    if (diffHours < 1) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${Math.floor(diffHours / 24)}d ago`
  }

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <h5 className="font-medium text-gray-900">{product.name}</h5>
          <Badge className={getReasonColor(product.reason)}>
            {getReasonText(product.reason)}
          </Badge>
        </div>
        <p className="text-xs text-gray-600">
          {product.category} • Disabled {getTimeSince(product.disabledAt)}
        </p>
        {product.estimatedReturn && (
          <p className="text-xs text-green-600">
            Expected back: {product.estimatedReturn.toLocaleDateString()}
          </p>
        )}
      </div>
      
      {onToggle && (
        <div className="flex items-center space-x-2">
          <Switch
            checked={false}
            onCheckedChange={(checked) => onToggle(product.id, checked)}
          />
          <span className="text-xs text-gray-500">Enable</span>
        </div>
      )}
    </div>
  )
}

function HealthIssueCard({ 
  issue, 
  onResolve 
}: { 
  issue: HealthIssue
  onResolve?: (issueId: string) => void
}) {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'complaint': return MessageSquare
      case 'rating': return Star
      case 'system': return Activity
      case 'delivery': return Truck
      default: return AlertTriangle
    }
  }

  const getTimeSince = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return `${Math.floor(diffMins / 1440)}d ago`
  }

  const Icon = getTypeIcon(issue.type)

  return (
    <div className={`p-3 rounded-lg border ${issue.resolved ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <Icon className="h-4 w-4 text-gray-500" />
            <h5 className="font-medium text-gray-900">{issue.title}</h5>
            {!issue.resolved && (
              <Badge className={getSeverityColor(issue.severity)}>
                {issue.severity}
              </Badge>
            )}
            {issue.resolved && (
              <Badge className="bg-green-100 text-green-800">
                Resolved
              </Badge>
            )}
          </div>
          <p className="text-sm text-gray-600 mb-1">{issue.description}</p>
          <p className="text-xs text-gray-500">{getTimeSince(issue.occurredAt)}</p>
        </div>
        
        {!issue.resolved && onResolve && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onResolve(issue.id)}
            className="text-xs"
          >
            <CheckCircle className="h-3 w-3 mr-1" />
            Resolve
          </Button>
        )}
      </div>
    </div>
  )
}

export function BusinessHealthStatus({
  healthData,
  onToggleProduct,
  onResolveIssue,
  className = ""
}: BusinessHealthStatusProps) {
  const [showResolved, setShowResolved] = useState(false)

  const unresolvedIssues = healthData.recentIssues.filter(issue => !issue.resolved)
  const resolvedIssues = healthData.recentIssues.filter(issue => issue.resolved)
  const displayIssues = showResolved ? healthData.recentIssues : unresolvedIssues

  const getSystemStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600'
      case 'degraded': return 'text-yellow-600'
      case 'offline': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getSystemStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return Wifi
      case 'degraded': return Activity
      case 'offline': return WifiOff
      default: return Activity
    }
  }

  const SystemIcon = getSystemStatusIcon(healthData.systemStatus)

  const formatLastUpdated = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    return `${Math.floor(diffMins / 60)}h ago`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* System Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Activity className="h-5 w-5 mr-2 text-gray-600" />
              Business Health Overview
            </span>
            <div className="flex items-center space-x-2 text-sm">
              <SystemIcon className={`h-4 w-4 ${getSystemStatusColor(healthData.systemStatus)}`} />
              <span className={getSystemStatusColor(healthData.systemStatus)}>
                {healthData.systemStatus.charAt(0).toUpperCase() + healthData.systemStatus.slice(1)}
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Unavailable Products */}
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {healthData.unavailableProducts.length}
              </div>
              <p className="text-sm text-gray-600">Unavailable Products</p>
              {healthData.unavailableProducts.length > 0 && (
                <Badge variant="destructive" className="mt-1">
                  Needs Attention
                </Badge>
              )}
            </div>

            {/* Average Rating */}
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1">
                <span className="text-2xl font-bold text-gray-900">
                  {healthData.averageRating.toFixed(1)}
                </span>
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
              </div>
              <p className="text-sm text-gray-600">Average Rating</p>
              <div className="flex items-center justify-center space-x-1 mt-1">
                {healthData.ratingTrend >= 0 ? (
                  <TrendingUp className="h-3 w-3 text-green-600" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600" />
                )}
                <span className={`text-xs ${healthData.ratingTrend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(healthData.ratingTrend)}%
                </span>
              </div>
            </div>

            {/* Active Drivers */}
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {healthData.activeDrivers}/{healthData.totalDrivers}
              </div>
              <p className="text-sm text-gray-600">Active Drivers</p>
              {healthData.activeDrivers < healthData.totalDrivers * 0.5 && (
                <Badge variant="outline" className="mt-1 bg-yellow-50 text-yellow-700">
                  Low Coverage
                </Badge>
              )}
            </div>

            {/* Unresolved Issues */}
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {unresolvedIssues.length}
              </div>
              <p className="text-sm text-gray-600">Open Issues</p>
              {unresolvedIssues.length > 0 && (
                <Badge variant="destructive" className="mt-1">
                  Action Required
                </Badge>
              )}
            </div>
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            Last updated: {formatLastUpdated(healthData.lastUpdated)}
          </div>
        </CardContent>
      </Card>

      {/* Unavailable Products */}
      {healthData.unavailableProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2 text-orange-600" />
              Unavailable Products
              <Badge className="ml-2 bg-orange-100 text-orange-800">
                {healthData.unavailableProducts.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {healthData.unavailableProducts.map(product => (
              <UnavailableProductCard
                key={product.id}
                product={product}
                onToggle={onToggleProduct}
              />
            ))}
            <div className="pt-2">
              <Link href="/business-admin/products">
                <Button variant="outline" size="sm" className="w-full">
                  <Package className="h-4 w-4 mr-2" />
                  Manage All Products
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Issues */}
      {healthData.recentIssues.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                Recent Issues
                {unresolvedIssues.length > 0 && (
                  <Badge className="ml-2 bg-red-100 text-red-800">
                    {unresolvedIssues.length} Open
                  </Badge>
                )}
              </CardTitle>
              
              {resolvedIssues.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showResolved}
                    onCheckedChange={setShowResolved}
                  />
                  <span className="text-sm text-gray-600">Show resolved</span>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {displayIssues.map(issue => (
              <HealthIssueCard
                key={issue.id}
                issue={issue}
                onResolve={onResolveIssue}
              />
            ))}
            
            {displayIssues.length === 0 && (
              <div className="text-center py-4">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-gray-600">No {showResolved ? '' : 'unresolved '}issues found.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
