
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Check, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useProductUpload } from '@/context/ProductUploadContext';
import { ColumnMappingType } from '@/context/ProductUploadContext';

export const MappingStep: React.FC = () => {
  const { 
    headers, 
    csvData, 
    setColumnMapping, 
    columnMapping, 
    setCurrentStep 
  } = useProductUpload();
  
  const [localMapping, setLocalMapping] = useState<ColumnMappingType>({
    productId: { sourceColumn: '', useAI: false }, // Added product ID field
    name: { sourceColumn: '', useAI: false },
    description: { sourceColumn: '', useAI: true },
    categories: { sourceColumn: '', useAI: true },
    attributes: { sourceColumn: '', useAI: true },
    price: { sourceColumn: '', useAI: false },
    images: { sourceColumn: '', useAI: false },
  });

  // Prepare sample data (first few rows) for preview
  const sampleData = csvData.slice(1, 5); // Skip header row, take next 4 rows

  // Auto-detect columns on mount
  useEffect(() => {
    if (headers.length === 0) return;
    
    const newMapping: ColumnMappingType = { ...localMapping };
    
    // Try to auto-detect column mappings based on header names
    headers.forEach((header) => {
      const lowerHeader = header.toLowerCase();
      
      // Add product ID detection
      if (lowerHeader.includes('id') || lowerHeader.includes('sku') || lowerHeader.includes('code')) {
        newMapping.productId.sourceColumn = header;
      }
      else if (lowerHeader.includes('name') || lowerHeader.includes('title') || lowerHeader.includes('product')) {
        newMapping.name.sourceColumn = header;
      }
      else if (lowerHeader.includes('desc')) {
        newMapping.description.sourceColumn = header;
        newMapping.description.useAI = false;
      }
      else if (lowerHeader.includes('categ')) {
        newMapping.categories.sourceColumn = header;
        newMapping.categories.useAI = false;
      }
      else if (lowerHeader.includes('attr') || lowerHeader.includes('spec') || lowerHeader.includes('feature')) {
        newMapping.attributes.sourceColumn = header;
        newMapping.attributes.useAI = false;
      }
      else if (lowerHeader.includes('price') || lowerHeader.includes('cost')) {
        newMapping.price.sourceColumn = header;
      }
      else if (lowerHeader.includes('image') || lowerHeader.includes('photo') || lowerHeader.includes('picture')) {
        newMapping.images.sourceColumn = header;
      }
    });
    
    setLocalMapping(newMapping);
  }, [headers]);

  const handleColumnSelect = (field: string, value: string) => {
    setLocalMapping(prev => ({
      ...prev,
      [field]: {
        ...prev[field as keyof ColumnMappingType],
        sourceColumn: value
      }
    }));
  };

  const handleAIToggle = (field: string, checked: boolean) => {
    setLocalMapping(prev => ({
      ...prev,
      [field]: {
        ...prev[field as keyof ColumnMappingType],
        useAI: checked
      }
    }));
  };

  const handleBack = () => {
    setCurrentStep(1);
  };

  const handleContinue = () => {
    setColumnMapping(localMapping);
    setCurrentStep(3);
  };

  const isConfigValid = () => {
    // Name is required and cannot use AI
    if (!localMapping.name.sourceColumn) {
      return false;
    }
    
    // For description, categories, and attributes, either have a source column or use AI
    const requiredFields = ['description', 'categories', 'attributes'];
    for (const field of requiredFields) {
      const config = localMapping[field as keyof ColumnMappingType];
      if (!config.sourceColumn && !config.useAI) {
        return false;
      }
    }
    
    return true;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Map Your CSV Columns</CardTitle>
        <CardDescription>
          Choose which columns from your CSV file correspond to product fields, or let AI generate the content.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="bg-app-blue-light bg-opacity-30 border border-app-blue border-opacity-20 p-4 rounded-lg">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-app-blue flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-app-blue">How Column Mapping Works</h4>
                <p className="text-sm text-gray-600 mt-1">
                  For each product field, you can either:
                </p>
                <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                  <li>Select a column from your CSV file as the source</li>
                  <li>Enable AI to automatically generate content based on the product name</li>
                  <li>Do both - AI will enhance your existing content</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="font-medium text-lg">Field Mapping</h3>
            
            <div className="grid gap-6">
              {/* Product ID (New) */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="id-mapping" className="col-span-3 font-medium">Product ID</Label>
                <div className="col-span-7">
                  <Select 
                    value={localMapping.productId.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('productId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="not-specified" value="none">Not specified</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-default">
                        <div className="bg-gray-100 px-3 py-1 rounded text-gray-500 text-sm">Optional</div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-60 text-sm">Your own product IDs or SKUs can be imported from the CSV.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              
              {/* Product Name (Required field) */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="name-mapping" className="col-span-3 font-medium">Product Name <span className="text-red-500">*</span></Label>
                <div className="col-span-7">
                  <Select 
                    value={localMapping.name.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('name', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2 flex items-center justify-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-default">
                        <div className="bg-gray-100 px-3 py-1 rounded text-gray-500 text-sm">Required</div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-60 text-sm">Product name is required and must come from your CSV.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              
              {/* Description */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="desc-mapping" className="col-span-3 font-medium">Description</Label>
                <div className="col-span-5">
                  <Select 
                    value={localMapping.description.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('description', value)}
                    disabled={localMapping.description.useAI && !localMapping.description.sourceColumn}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="ai-only" value="none">No column (use AI only)</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-4 flex items-center space-x-2">
                  <Switch
                    id="desc-ai"
                    checked={localMapping.description.useAI}
                    onCheckedChange={(checked) => handleAIToggle('description', checked)}
                  />
                  <Label htmlFor="desc-ai">Generate with AI</Label>
                </div>
              </div>
              
              {/* Categories */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="cat-mapping" className="col-span-3 font-medium">Categories</Label>
                <div className="col-span-5">
                  <Select 
                    value={localMapping.categories.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('categories', value)}
                    disabled={localMapping.categories.useAI && !localMapping.categories.sourceColumn}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="ai-only" value="none">No column (use AI only)</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-4 flex items-center space-x-2">
                  <Switch
                    id="cat-ai"
                    checked={localMapping.categories.useAI}
                    onCheckedChange={(checked) => handleAIToggle('categories', checked)}
                  />
                  <Label htmlFor="cat-ai">Generate with AI</Label>
                </div>
              </div>
              
              {/* Attributes */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="attr-mapping" className="col-span-3 font-medium">Attributes</Label>
                <div className="col-span-5">
                  <Select 
                    value={localMapping.attributes.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('attributes', value)}
                    disabled={localMapping.attributes.useAI && !localMapping.attributes.sourceColumn}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="ai-only" value="none">No column (use AI only)</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-4 flex items-center space-x-2">
                  <Switch
                    id="attr-ai"
                    checked={localMapping.attributes.useAI}
                    onCheckedChange={(checked) => handleAIToggle('attributes', checked)}
                  />
                  <Label htmlFor="attr-ai">Generate with AI</Label>
                </div>
              </div>
              
              {/* Price */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="price-mapping" className="col-span-3 font-medium">Price</Label>
                <div className="col-span-7">
                  <Select 
                    value={localMapping.price.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('price', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="not-specified" value="none">Not specified</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2"></div>
              </div>
              
              {/* Images */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <Label htmlFor="img-mapping" className="col-span-3 font-medium">Images</Label>
                <div className="col-span-7">
                  <Select 
                    value={localMapping.images.sourceColumn} 
                    onValueChange={(value) => handleColumnSelect('images', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="not-specified" value="none">Not specified</SelectItem>
                      {headers.map((header) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="cursor-default">
                        <div className="bg-gray-100 px-3 py-1 rounded text-gray-500 text-sm">Info</div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-60 text-sm">Image URLs can be provided in your CSV. You'll be able to generate additional images with AI in the next step.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
          </div>
          
          {/* CSV Preview */}
          <div className="mt-8 space-y-4">
            <h3 className="font-medium text-lg">CSV Preview</h3>
            <div className="border rounded overflow-auto max-h-60">
              <Table>
                <TableHeader>
                  <TableRow>
                    {headers.map((header) => (
                      <TableHead key={header} className="whitespace-nowrap">
                        {header}
                        {Object.entries(localMapping).map(([field, config]) => 
                          config.sourceColumn === header ? (
                            <span key={field} className="ml-2 px-1.5 py-0.5 bg-app-blue bg-opacity-10 text-app-blue rounded text-xs">
                              {field}
                            </span>
                          ) : null
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sampleData.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <TableCell key={cellIndex} className="truncate max-w-xs">
                          {cell || '-'}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleBack}>Back</Button>
        <Button onClick={handleContinue} disabled={!isConfigValid()}>
          Continue
        </Button>
      </CardFooter>
    </Card>
  );
};
