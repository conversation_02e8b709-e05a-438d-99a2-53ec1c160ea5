"use client"
import { Plus, Minus, ShoppingBasket } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import type { CartItem } from "@/types/cart"

interface AddToCartButtonProps {
  item: CartItem
}

export default function AddToCartButton({ item }: AddToCartButtonProps) {
  const { addToCart, removeFromCart, getItemQuantity } = useRealtimeCart()
  const quantity = getItemQuantity(item.id)

  return (
    <div>
      {quantity === 0 ? (
        <Button onClick={() => addToCart(item)} className="bg-emerald-600 hover:bg-emerald-700">
          <ShoppingBasket className="mr-2 h-4 w-4" />
          Add to Basket
        </Button>
      ) : (
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => removeFromCart(item.id)}
          >
            <Minus className="h-4 w-4" />
          </Button>
          <span className="mx-3 font-medium">{quantity}</span>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => addToCart({ ...item, quantity: 1 })}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
