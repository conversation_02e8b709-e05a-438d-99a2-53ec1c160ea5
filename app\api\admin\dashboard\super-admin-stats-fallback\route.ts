import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in fallback, using direct admin access")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check")
    } else {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }
  } else {
    console.log("Found authorization header in fallback, attempting to verify")

    // Extract the token
    const token = authorization.replace('Bearer ', '');

    try {
      // Verify the token
      const { data: { user }, error } = await adminClient.auth.getUser(token);

      if (error || !user) {
        console.error("Invalid token in fallback:", error)
        return NextResponse.json(
          { error: "Invalid authentication token" },
          { status: 401 }
        )
      }

      console.log("Token verified for user in fallback:", user.email)

      // Check if the user has super admin permissions
      const { data: userProfile, error: profileError } = await adminClient
        .from("users")
        .select("role")
        .eq("email", user.email)
        .single()

      if (profileError || !userProfile) {
        console.error("Error fetching user profile in fallback:", profileError)
        return NextResponse.json(
          { error: "User profile not found" },
          { status: 403 }
        )
      }

      // Check if the user has super_admin role
      if (userProfile.role !== "super_admin") {
        console.error("Unauthorized access attempt in fallback by:", user.email, "with role:", userProfile.role)
        return NextResponse.json(
          { error: "Super admin access required" },
          { status: 403 }
        )
      }

      console.log("Super admin access verified for user in fallback:", user.email, "with role:", userProfile.role)
    } catch (authError) {
      console.error("Error verifying token in fallback:", authError)
      // Continue anyway in development mode
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json(
          { error: "Authentication error" },
          { status: 401 }
        )
      } else {
        console.log("Development mode: Continuing despite auth error in fallback")
      }
    }
  }
  try {
    console.log("Using fallback stats endpoint for super admin dashboard")

    // Initialize default stats
    let stats = {
      totalUsers: 0,
      totalBusinesses: 0,
      totalOrders: 0,
      usersByRole: {
        customer: 0,
        business_staff: 0,
        business_manager: 0,
        admin: 0,
        super_admin: 0
      },
      businessesByType: {
        restaurant: 0,
        shop: 0,
        pharmacy: 0,
        cafe: 0,
        errand: 0
      }
    };

    try {
      // Get total users
      const { count: totalUsers } = await adminClient
        .from("users")
        .select("*", { count: "exact", head: true });

      if (totalUsers) {
        stats.totalUsers = totalUsers;
        console.log("Fallback: Got total users:", totalUsers);
      }

      // Get users by role
      const { data: userRoles } = await adminClient
        .from("users")
        .select("role");

      if (userRoles && userRoles.length > 0) {
        // Reset counts
        stats.usersByRole = {
          customer: 0,
          business_staff: 0,
          business_manager: 0,
          admin: 0,
          super_admin: 0
        };

        // Count users by role
        userRoles.forEach(user => {
          const role = user.role || "customer";
          if (stats.usersByRole[role] !== undefined) {
            stats.usersByRole[role]++;
          }
        });

        console.log("Fallback: Processed user roles");
      }

      // Get total businesses
      const { count: totalBusinesses } = await adminClient
        .from("businesses")
        .select("*", { count: "exact", head: true });

      if (totalBusinesses) {
        stats.totalBusinesses = totalBusinesses;
        console.log("Fallback: Got total businesses:", totalBusinesses);
      }

      // Get business types
      const { data: businessTypes } = await adminClient
        .from("business_types")
        .select("id, slug");

      if (businessTypes && businessTypes.length > 0) {
        // Create a map of business_type_id to slug
        const typeIdToSlug = {};
        businessTypes.forEach(type => {
          if (type.id && type.slug) {
            typeIdToSlug[type.id] = type.slug;
          }
        });

        // Get businesses with their type IDs
        const { data: businesses } = await adminClient
          .from("businesses")
          .select("business_type_id");

        if (businesses && businesses.length > 0) {
          // Reset counts
          stats.businessesByType = {
            restaurant: 0,
            shop: 0,
            pharmacy: 0,
            cafe: 0,
            errand: 0
          };

          // Count businesses by type
          businesses.forEach(business => {
            if (business.business_type_id) {
              const slug = typeIdToSlug[business.business_type_id];
              if (slug && stats.businessesByType[slug] !== undefined) {
                stats.businessesByType[slug]++;
              }
            }
          });

          console.log("Fallback: Processed business types");
        }
      }
    } catch (dbError) {
      console.error("Error fetching data in fallback endpoint:", dbError);
      // Continue with default values
    }

    return NextResponse.json(stats);
  } catch (error: any) {
    console.error("Error in fallback stats endpoint:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
