export interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  businessId: string
  businessName?: string
  businessType?: string
  variantName?: string
  customizations?: any[]
  options?: string[]
}

export interface Business {
  id: string
  name: string
  type: string
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  deliveryTime?: number
  preparationTime?: number
  deliveryMethod?: 'delivery' | 'pickup'
  deliveryType?: 'asap' | 'scheduled'
  scheduledTime?: string
  orderNumber?: string
  estimatedTime?: string
  deliveryFulfillment?: 'loop' | 'business' // PHASE 2 STEP 3: Add delivery fulfillment
}

export interface OrderDetails {
  orderId: string
  businesses: Business[]
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  serviceFee: number
  total: number
  customerName: string
  customerPhone: string
  customerAddress: string
  paymentMethod: string
  deliveryType: string
  scheduledTime?: string
  estimatedDeliveryTime: string
  sessionId?: string // PHASE 2 STEP 3: Add session ID for multi-business orders
}
