import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'

export async function GET() {
  try {
    // Create a Supabase client for authentication
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Create an admin client for database operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if the user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'You must be logged in to perform this action' },
        { status: 401 }
      )
    }

    // Check if the user is an admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (userError || !userData || (userData.role !== 'admin' && userData.role !== 'super_admin')) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'You must be an admin to perform this action' },
        { status: 403 }
      )
    }

    // Update the rating column in the businesses table
    try {
      // First check if we can access the businesses table
      const { error: accessError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .limit(1);

      if (accessError) {
        console.error('Error accessing businesses table:', accessError);
        return NextResponse.json(
          { error: 'Database Error', message: 'Cannot access businesses table: ' + accessError.message },
          { status: 500 }
        );
      }

      // Check if the exec_sql RPC function exists
      let useRawSQL = false;
      try {
        // Try to call the exec_sql function with a simple query
        const { error: rpcTestError } = await supabaseAdmin.rpc('exec_sql', {
          query_text: 'SELECT 1'
        });

        if (rpcTestError) {
          console.warn('exec_sql RPC function not available:', rpcTestError);
          useRawSQL = true;
        }
      } catch (rpcError) {
        console.warn('exec_sql RPC function not available, will use raw SQL:', rpcError);
        useRawSQL = true;
      }

      // Helper function to execute SQL based on availability of exec_sql RPC
      const execSQL = async (sql: string) => {
        try {
          if (useRawSQL) {
            // Use direct SQL execution via the REST API
            console.log('Executing SQL via REST API:', sql.substring(0, 50) + (sql.length > 50 ? '...' : ''));

            try {
              // Create a direct SQL query using the REST API
              const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || '',
                  'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}`,
                  'Prefer': 'return=minimal'
                },
                body: JSON.stringify({ query: sql })
              });

              if (!response.ok) {
                let errorMessage = 'SQL execution failed';
                try {
                  const errorData = await response.json();
                  errorMessage = errorData.message || errorData.error || JSON.stringify(errorData);
                } catch (parseErr) {
                  errorMessage = `SQL execution failed with status ${response.status}`;
                }

                console.error('SQL execution error:', errorMessage);
                return { error: { message: errorMessage } };
              }

              return { data: {}, error: null };
            } catch (fetchErr) {
              console.error('Fetch error executing SQL:', fetchErr);
              return { error: { message: fetchErr instanceof Error ? fetchErr.message : 'Unknown fetch error' } };
            }
          } else {
            // Use the RPC function
            console.log('Executing SQL via RPC:', sql.substring(0, 50) + (sql.length > 50 ? '...' : ''));
            return await supabaseAdmin.rpc('exec_sql', { query_text: sql });
          }
        } catch (execErr) {
          console.error('Exception in execSQL:', execErr);
          return { error: { message: execErr instanceof Error ? execErr.message : 'Unknown SQL execution error' } };
        }
      };

      // Check if the rating column exists using a simpler approach
      let columnExists = false;
      let columnDataType = null;

      try {
        // Try to select the rating column directly from businesses table
        const { data: testData, error: testError } = await supabaseAdmin
          .from('businesses')
          .select('rating')
          .limit(1);

        if (!testError) {
          console.log('Rating column exists in businesses table');
          columnExists = true;

          // Try to determine the data type by checking if the value can be parsed as a number
          if (testData && testData.length > 0) {
            const rating = testData[0]?.rating;
            if (rating !== null && rating !== undefined) {
              columnDataType = typeof rating === 'number' ? 'numeric' : typeof rating;
              console.log(`Rating column appears to be of type: ${columnDataType}`);
            }
          }
        } else {
          console.log('Error testing rating column:', testError);
          // The column likely doesn't exist if we get an error here
          columnExists = false;
        }
      } catch (testErr) {
        console.error('Exception testing rating column:', testErr);
        columnExists = false;
      }

      // We'll skip the information_schema query since it's causing issues
      const columnInfo = columnExists ? [{ data_type: columnDataType || 'unknown' }] : [];
      const columnError = null;

      if (columnError) {
        console.error('Error checking column info:', columnError);
        return NextResponse.json(
          { error: 'Database Error', message: 'Error checking column info: ' + columnError.message },
          { status: 500 }
        );
      }

      // Try a more direct approach to ensure the rating column exists with the correct type
      console.log('Ensuring rating column exists with correct type');

      // First try to add the column if it doesn't exist
      const { error: addColumnError } = await execSQL(
        `ALTER TABLE businesses ADD COLUMN IF NOT EXISTS rating NUMERIC(3,1) DEFAULT 0.0`
      );

      if (addColumnError) {
        console.log('Error adding column (may already exist):', addColumnError);
        // Column might already exist, which would cause an error, so we continue
      }

      // Now try to update the column type if it exists but has the wrong type
      // This is a more direct approach that doesn't rely on checking the type first
      try {
        const { error: typeError } = await execSQL(
          `ALTER TABLE businesses ALTER COLUMN rating TYPE NUMERIC(3,1) USING rating::NUMERIC(3,1)`
        );

        if (typeError) {
          console.log('Error updating column type (may already be correct):', typeError);
          // Not a critical error, the column might already have the correct type
        } else {
          console.log('Successfully updated column type to NUMERIC(3,1)');
        }
      } catch (typeErr) {
        console.error('Exception updating column type:', typeErr);
        // Continue with the process
      }

      // Ensure it has a default value
      try {
        const { error: defaultError } = await execSQL(
          `ALTER TABLE businesses ALTER COLUMN rating SET DEFAULT 0.0`
        );

        if (defaultError) {
          console.log('Error setting default value:', defaultError);
          // Not a critical error
        } else {
          console.log('Successfully set default value to 0.0');
        }
      } catch (defaultErr) {
        console.error('Exception setting default value:', defaultErr);
        // Continue with the process
      }

      // Add a comment to the column
      try {
        const { error: commentError } = await execSQL(
          `COMMENT ON COLUMN businesses.rating IS 'Rating of the business (0.0 to 5.0)'`
        );

        if (commentError) {
          console.log('Error adding column comment (non-critical):', commentError);
        } else {
          console.log('Successfully added column comment');
        }
      } catch (commentErr) {
        console.error('Exception adding column comment:', commentErr);
        // Continue with the process
      }

      // Create an index if it doesn't exist
      try {
        const { error: indexError } = await execSQL(
          `CREATE INDEX IF NOT EXISTS idx_businesses_rating ON businesses(rating)`
        );

        if (indexError) {
          console.log('Error creating index (non-critical):', indexError);
        } else {
          console.log('Successfully created or confirmed index');
        }
      } catch (indexErr) {
        console.error('Exception creating index:', indexErr);
        // Continue with the process
      }

      // Update existing ratings to ensure they're within the valid range (0.0 to 5.0)
      try {
        const { error: updateRatingsError } = await execSQL(`
          UPDATE businesses
          SET rating = CASE
            WHEN rating IS NULL THEN 0.0
            WHEN rating < 0 THEN 0.0
            WHEN rating > 5 THEN 5.0
            ELSE rating
          END
        `);

        if (updateRatingsError) {
          console.log('Error updating ratings (non-critical):', updateRatingsError);
        } else {
          console.log('Successfully updated ratings to ensure valid range');
        }
      } catch (updateErr) {
        console.error('Exception updating ratings:', updateErr);
        // Continue with the process
      }

      // Return success even if some non-critical operations failed
      console.log('Rating column update process completed');
      return NextResponse.json({
        success: true,
        message: 'Rating column in businesses table has been updated successfully'
      });
    } catch (sqlError) {
      console.error('Exception updating rating column:', sqlError)
      return NextResponse.json(
        { error: 'Database Error', message: sqlError instanceof Error ? sqlError.message : 'Unknown database error' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Rating column in businesses table has been updated successfully'
    })
  } catch (error) {
    console.error('Error in update-rating-column API route:', error)
    return NextResponse.json(
      { error: 'Server Error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
