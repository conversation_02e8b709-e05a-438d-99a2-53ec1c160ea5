-- Migration: Create business_custom_categories table if it doesn't exist
-- This migration creates the business_custom_categories table for business-specific categories

-- Step 1: Create the business_custom_categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS business_custom_categories (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  name VA<PERSON>HAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  level INTEGER NOT NULL DEFAULT 0,
  parent_category VARCHAR(255) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  UNIQUE(business_id, slug),
  UNIQUE(business_id, name),
  CHECK (level IN (0, 1)),
  CHECK (
    (level = 0 AND parent_category IS NULL) OR 
    (level = 1 AND parent_category IS NOT NULL)
  )
);

-- Step 2: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_business_id ON business_custom_categories(business_id);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_level ON business_custom_categories(level);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_parent_category ON business_custom_categories(parent_category);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_business_level ON business_custom_categories(business_id, level);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_display_order ON business_custom_categories(business_id, display_order);

-- Step 3: Add comments to explain the table structure
COMMENT ON TABLE business_custom_categories IS 'Business-specific custom categories with hierarchical support for aisle layout';
COMMENT ON COLUMN business_custom_categories.level IS 'Category hierarchy level: 0 = top level (aisle), 1 = subcategory';
COMMENT ON COLUMN business_custom_categories.parent_category IS 'Name of parent category for level 1 categories (references name field of level 0 category)';

-- Step 4: Verify the table structure
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM 
  information_schema.columns
WHERE 
  table_name = 'business_custom_categories'
ORDER BY 
  ordinal_position;
