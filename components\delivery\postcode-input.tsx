'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { StandardizedPostcode } from '@/lib/postcode-standardizer';

interface PostcodeInputProps {
  initialPostcode?: string;
  onPostcodeChange?: (standardizedPostcode: StandardizedPostcode) => void;
  onValidationError?: (error: string) => void;
  buttonText?: string;
  placeholder?: string;
  autoSubmit?: boolean;
}

export function PostcodeInput({
  initialPostcode = '',
  onPostcodeChange,
  onValidationError,
  buttonText = 'Submit',
  placeholder = 'Enter postcode (e.g., JE2 4UE)',
  autoSubmit = false
}: PostcodeInputProps) {
  const [postcode, setPostcode] = useState(initialPostcode);
  const [isProcessing, setIsProcessing] = useState(false);
  const [standardizedPostcode, setStandardizedPostcode] = useState<StandardizedPostcode | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAutoProcessing, setIsAutoProcessing] = useState(false);

  // Update postcode when initialPostcode changes
  useEffect(() => {
    if (initialPostcode && initialPostcode !== postcode) {
      setPostcode(initialPostcode);
      // Reset previous results when initialPostcode changes
      setStandardizedPostcode(null);
      setError(null);
    }
  }, [initialPostcode]);

  // Auto-standardize when postcode changes (with debounce)
  useEffect(() => {
    if (!postcode || postcode.length < 3 || !autoSubmit) return;

    const timer = setTimeout(() => {
      setIsAutoProcessing(true);
      standardizePostcode(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [postcode, autoSubmit]);

  const standardizePostcode = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsProcessing(true);
      }
      setError(null);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();

      const response = await fetch('/api/postcode/standardize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
        },
        body: JSON.stringify({
          postcode,
          geocode: true,
          timestamp, // Add timestamp to ensure unique requests
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error || 'Postcode standardization failed';
        setError(errorMessage);
        if (onValidationError) {
          onValidationError(errorMessage);
        }
        return;
      }

      setStandardizedPostcode(data.result);

      // Update input field with standardized postcode
      if (data.result.is_valid) {
        setPostcode(data.result.standardized);
      }

      if (onPostcodeChange) {
        onPostcodeChange(data.result);
      }

      if (!data.result.is_valid && onValidationError) {
        onValidationError(data.result.validation_message || 'Invalid postcode');
      }
    } catch (err) {
      console.error('Error standardizing postcode:', err);
      setError('An error occurred while standardizing the postcode');
      if (onValidationError) {
        onValidationError('An error occurred while standardizing the postcode');
      }
    } finally {
      setIsProcessing(false);
      setIsAutoProcessing(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    standardizePostcode();
  };

  return (
    <div className="space-y-2">
      <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <Input
            value={postcode}
            onChange={(e) => setPostcode(e.target.value)}
            placeholder={placeholder}
            className={`pl-12 pr-4 py-2 w-full border-2 border-emerald-100 rounded-full h-14 text-base bg-white shadow-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-600/80 focus-visible:ring-offset-0 focus-visible:border-emerald-600 ${
              standardizedPostcode?.is_valid ? 'border-emerald-300' :
              error ? 'border-red-300 focus-visible:ring-red-500 focus-visible:border-red-500' : ''
            }`}
          />
          {isAutoProcessing && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Spinner className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
        <Button
          type="submit"
          size="lg"
          className="bg-emerald-500 hover:bg-emerald-600 h-14 px-8 min-w-[120px] rounded-full shadow-md hover:shadow-lg transition-all duration-300"
          disabled={isProcessing || !postcode}
        >
          {isProcessing ? (
            <>
              <Spinner className="mr-2 h-4 w-4" />
              <span>Processing...</span>
            </>
          ) : (
            buttonText
          )}
        </Button>
      </form>

      {error && (
        <Alert variant="destructive" className="mt-2">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {standardizedPostcode && !standardizedPostcode.is_valid && standardizedPostcode.validation_message && (
        <Alert variant="destructive" className="mt-2">
          <AlertDescription>{standardizedPostcode.validation_message}</AlertDescription>
        </Alert>
      )}

      {standardizedPostcode && standardizedPostcode.is_valid && standardizedPostcode.validation_message && (
        <Alert variant={standardizedPostcode.coordinates ? "default" : "warning"} className="mt-2 bg-amber-50 border-amber-200 text-amber-700">
          <AlertDescription>{standardizedPostcode.validation_message}</AlertDescription>
        </Alert>
      )}

      {standardizedPostcode && standardizedPostcode.is_valid && standardizedPostcode.coordinates && !standardizedPostcode.validation_message && (
        <div className="text-xs text-green-600 mt-1">
          Postcode validated and geocoded successfully
        </div>
      )}
    </div>
  );
}
