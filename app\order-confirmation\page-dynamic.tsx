"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON>Circle, ArrowRight, Clock, MapPin, Receipt, Home, User, Truck, Store } from "lucide-react"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  businessId: string
  businessName?: string
  businessType?: string
  variantName?: string
  customizations?: any[]
  options?: string[]
}

// Helper function to format variant and customization details
const formatItemDetails = (item: OrderItem): string[] => {
  const details: string[] = [];

  // Add variant information
  if (item.variantName) {
    details.push(`Size: ${item.variantName}`);
  }

  // Add customization information
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
};

interface Business {
  id: string
  name: string
  type: string
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
}

interface OrderDetails {
  orderId: string
  businesses: Business[]
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  serviceFee: number
  total: number
  customerName: string
  customerPhone: string
  customerAddress: string
  paymentMethod: string
  deliveryType: string
  estimatedDeliveryTime: string
}

export default function OrderConfirmationPage() {
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    // Load order details from sessionStorage
    const loadOrderDetails = () => {
      try {
        const savedOrderDetails = sessionStorage.getItem('orderDetails')
        if (savedOrderDetails) {
          const parsedDetails = JSON.parse(savedOrderDetails)
          console.log('Loaded order details from sessionStorage:', parsedDetails)
          setOrderDetails(parsedDetails)
        } else {
          console.error('No order details found in sessionStorage')
          setError('Order details not found')
        }
      } catch (err) {
        console.error('Error loading order details:', err)
        setError('Error loading order details')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadOrderDetails()
  }, [])
  
  // If loading, show a loading state
  if (isLoading) {
    return (
      <div className="container max-w-5xl py-8 mx-auto text-center">
        <div className="h-20 w-20 rounded-full bg-blue-100 flex items-center justify-center mb-5 mx-auto">
          <Clock className="h-10 w-10 text-blue-600 animate-pulse" />
        </div>
        <h1 className="text-3xl font-bold text-gray-800">Loading Order Details...</h1>
      </div>
    )
  }
  
  // If error, show error state
  if (error || !orderDetails) {
    return (
      <div className="container max-w-5xl py-8 mx-auto text-center">
        <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center mb-5 mx-auto">
          <div className="animate-wheel-celebration">
            <WheelLogoIcon size={40} color="#dc2626" className="text-red-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-800">Order Confirmed</h1>
        <p className="text-gray-600 mt-3 max-w-lg mx-auto">
          Your order has been received, but we couldn't load the details. Please check your email for confirmation.
        </p>
        <div className="mt-8">
          <Link href="/businesses">
            <Button className="bg-blue-600 hover:bg-blue-700">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container max-w-5xl py-8 mx-auto">
      <div className="flex flex-col items-center text-center mb-8">
        <div className="h-20 w-20 rounded-full bg-emerald-100 flex items-center justify-center mb-5 shadow-sm">
          <div className="animate-wheel-celebration">
            <WheelLogoIcon size={40} color="#059669" className="text-emerald-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-800">Order Confirmed!</h1>
        <p className="text-gray-600 mt-3 max-w-lg">
          Your order <span className="font-medium">{orderDetails.orderId}</span> has been received and is being prepared.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mx-auto">
        <div className="md:col-span-2 space-y-6">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-800">Order Details</CardTitle>
                  <CardDescription>Order ID: <span className="font-medium">{orderDetails.orderId}</span></CardDescription>
                </div>
                {orderDetails.businesses.length > 1 && (
                  <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                    {orderDetails.businesses.length} businesses
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-6">
                {/* Business Cards */}
                {orderDetails.businesses.map((business) => (
                  <div key={business.id} className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                    <div className="bg-gray-50 px-4 py-3 border-b">
                      <div className="flex items-center">
                        {business.type === 'restaurant' ? (
                          <Store className="h-5 w-5 text-blue-600 mr-3" />
                        ) : (
                          <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                        )}
                        <div>
                          <h3 className="font-medium text-gray-800">{business.name}</h3>
                          <p className="text-sm text-gray-600 capitalize">{business.type}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <div className="space-y-2 mb-4">
                        {business.items.map((item) => {
                          const itemDetails = formatItemDetails(item);
                          return (
                            <div key={`${item.id}-${item.variantName || 'no-variant'}-${item.customizations?.map(c => c.groupName).join('-') || 'no-custom'}`} className="flex justify-between text-sm">
                              <div className="flex items-start">
                                <span className="font-medium text-emerald-700 mr-1.5">{item.quantity}×</span>
                                <div>
                                  <span className="text-gray-800">{item.name}</span>
                                  {itemDetails.length > 0 && (
                                    <p className="text-xs text-gray-500 mt-1">{itemDetails.join(", ")}</p>
                                  )}
                                </div>
                              </div>
                              <span className="font-medium text-gray-800">£{(item.price * item.quantity).toFixed(2)}</span>
                            </div>
                          );
                        })}
                      </div>
                      
                      <div className="flex justify-between pt-3 text-sm font-medium text-gray-700 border-t">
                        <div>Business Subtotal:</div>
                        <div>£{business.subtotal.toFixed(2)}</div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Order Summary */}
                <div className="pt-2 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="text-gray-800">£{orderDetails.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Delivery Fee</span>
                    <span className="text-gray-800">£{orderDetails.deliveryFee.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Service Fee</span>
                    <span className="text-gray-800">£{orderDetails.serviceFee.toFixed(2)}</span>
                  </div>
                  <Separator className="my-1 bg-gray-200" />
                  <div className="flex justify-between font-bold text-base pt-1">
                    <span>Total</span>
                    <span className="text-blue-700">£{orderDetails.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="text-lg font-semibold text-gray-800">Delivery Information</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-5">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Customer Name</p>
                  <div className="flex items-start">
                    <User className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800 font-medium">{orderDetails.customerName || 'Guest User'}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Delivery Address</p>
                  <div className="flex items-start">
                    <Home className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800">{orderDetails.customerAddress || 'Address not available'}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Estimated Delivery</p>
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800">{orderDetails.estimatedDeliveryTime || 'As soon as possible'}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Delivery Method</p>
                  <div className="flex items-start">
                    <Truck className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800 capitalize">{orderDetails.deliveryType || 'Standard delivery'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="text-lg font-semibold text-gray-800">Need Help?</CardTitle>
            </CardHeader>
            <CardContent className="pt-4 pb-2">
              <div className="grid grid-cols-1 gap-3">
                <Button variant="outline" className="w-full justify-start border-gray-300 hover:bg-gray-50 transition-colors">
                  <Receipt className="mr-2 h-5 w-5 text-blue-600" />
                  <span>View Receipt</span>
                </Button>
              </div>
            </CardContent>
            <CardFooter className="pt-3 pb-4">
              <div className="w-full space-y-4">
                <Separator className="bg-gray-200" />
                <div className="flex flex-wrap gap-3 justify-between items-center w-full">
                  <Link href="/" className="flex-shrink-0">
                    <Button variant="outline" className="border-gray-300 hover:bg-gray-50 transition-colors">
                      <Home className="mr-2 h-4 w-4 text-gray-600" />
                      Return Home
                    </Button>
                  </Link>
                  <Link href="/businesses" className="flex-shrink-0">
                    <Button className="bg-emerald-600 hover:bg-emerald-700 transition-colors">
                      Order More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
