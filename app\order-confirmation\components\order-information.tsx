import { User, MapPin, CreditCard } from "lucide-react"
import { OrderDetails } from "../types"

interface OrderInformationProps {
  orderDetails: OrderDetails
}

export function OrderInformation({ orderDetails }: OrderInformationProps) {
  // Check if any business has delivery method
  const hasDelivery = orderDetails.businesses.some(business => business.deliveryMethod === 'delivery');

  // Check if we have a valid delivery address (not the fallback text)
  const hasValidAddress = orderDetails.customerAddress &&
    orderDetails.customerAddress !== 'Address not available' &&
    orderDetails.customerAddress.trim() !== '';

  // Show delivery address if we have delivery orders OR if the overall order type is delivery and we have a valid address
  const shouldShowAddress = (hasDelivery || orderDetails.deliveryType === 'delivery') && hasValidAddress;



  return (
    <div className="space-y-6">
      {/* Customer Information */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
        <p className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Customer Name</p>
        <div className="flex items-start">
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mr-3 shadow-md">
            <User className="h-5 w-5 text-white" />
          </div>
          <div className="mt-1">
            <p className="text-gray-900 font-medium">{orderDetails.customerName}</p>
          </div>
        </div>
      </div>

      {/* Delivery Address - Show if delivery order and valid address */}
      {shouldShowAddress && (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
          <p className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Delivery Address</p>
          <div className="flex items-start">
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-3 shadow-md">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div className="mt-1">
              <p className="text-gray-900 font-medium">{orderDetails.customerAddress}</p>
            </div>
          </div>
        </div>
      )}

      {/* Payment Method */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
        <p className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Payment Method</p>
        <div className="flex items-start">
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mr-3 shadow-md">
            <CreditCard className="h-5 w-5 text-white" />
          </div>
          <div className="mt-1">
            <p className="text-gray-900 font-medium capitalize">{orderDetails.paymentMethod}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
