// PHASE 6 STEP 13: Delivery Metrics API
// API endpoint to provide delivery metrics to customers, businesses, and drivers

import { NextRequest, NextResponse } from 'next/server';
import { 
  getCustomerDeliveryInfo,
  getBusinessDeliveryInfo,
  getDriverOrderInfo,
  getGeneralDeliveryMetrics
} from '@/services/delivery-metrics-service';

// GET endpoint to fetch delivery metrics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    switch (type) {
      case 'customer':
        const parish = searchParams.get('parish');
        const customerInfo = await getCustomerDeliveryInfo(parish || undefined);
        
        return NextResponse.json({
          success: true,
          data: customerInfo
        });

      case 'business':
        const businessIdStr = searchParams.get('businessId');
        if (!businessIdStr) {
          return NextResponse.json(
            { error: 'Business ID is required for business metrics' },
            { status: 400 }
          );
        }

        const businessId = parseInt(businessIdStr);
        if (isNaN(businessId)) {
          return NextResponse.json(
            { error: 'Invalid business ID' },
            { status: 400 }
          );
        }

        const businessInfo = await getBusinessDeliveryInfo(businessId);
        
        return NextResponse.json({
          success: true,
          data: businessInfo
        });

      case 'driver':
        const orderIdStr = searchParams.get('orderId');
        if (!orderIdStr) {
          return NextResponse.json(
            { error: 'Order ID is required for driver metrics' },
            { status: 400 }
          );
        }

        const orderId = parseInt(orderIdStr);
        if (isNaN(orderId)) {
          return NextResponse.json(
            { error: 'Invalid order ID' },
            { status: 400 }
          );
        }

        const driverInfo = await getDriverOrderInfo(orderId);
        
        if (!driverInfo) {
          return NextResponse.json(
            { error: 'Order information not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: driverInfo
        });

      case 'general':
        const generalMetrics = await getGeneralDeliveryMetrics();
        
        return NextResponse.json({
          success: true,
          data: generalMetrics
        });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Must be one of: customer, business, driver, general' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Error in delivery metrics API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
