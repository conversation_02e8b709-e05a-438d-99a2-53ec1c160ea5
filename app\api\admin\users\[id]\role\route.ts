import { NextRequest, NextResponse } from "next/server"
import { withAdmin } from "@/lib/api-guards"
import { adminClient } from "@/lib/supabase-admin"
import { USER_ROLES } from "@/lib/auth-utils"

// Update user role - Admin only
export const PATCH = withAdmin(async (request: NextRequest, { profile, params }) => {
  try {
    const userId = parseInt(params?.id as string)
    const { role } = await request.json()

    console.log(`Admin ${profile.email} attempting to update user ${userId} role to ${role}`)

    // Validate the new role
    if (!Object.values(USER_ROLES).includes(role)) {
      return NextResponse.json(
        { error: `Invalid role: ${role}` },
        { status: 400 }
      )
    }

    // Get the target user
    const { data: targetUser, error: fetchError } = await adminClient
      .from('users')
      .select('id, email, role')
      .eq('id', userId)
      .single()

    if (fetchError || !targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Security checks
    const isPromotingToAdmin = ['admin', 'super_admin'].includes(role)
    const isDemotingFromAdmin = ['admin', 'super_admin'].includes(targetUser.role)
    const isSelfUpdate = targetUser.id === profile.id

    // Prevent self-demotion from admin roles (safety check)
    if (isSelfUpdate && isDemotingFromAdmin && !['admin', 'super_admin'].includes(role)) {
      return NextResponse.json(
        { error: 'Cannot demote yourself from admin role. Have another admin change your role.' },
        { status: 403 }
      )
    }

    // Only super_admins can promote to super_admin
    if (role === 'super_admin' && profile.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Only super admins can promote users to super admin role' },
        { status: 403 }
      )
    }

    // Only super_admins can demote other super_admins
    if (targetUser.role === 'super_admin' && profile.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Only super admins can modify super admin roles' },
        { status: 403 }
      )
    }

    // Update the user's role
    const { data: updatedUser, error: updateError } = await adminClient
      .from('users')
      .update({ 
        role,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('id, email, name, role, updated_at')
      .single()

    if (updateError) {
      console.error('Error updating user role:', updateError)
      return NextResponse.json(
        { error: 'Failed to update user role' },
        { status: 500 }
      )
    }

    // Log the role change for audit purposes
    console.log(`Role change successful: ${targetUser.email} (${targetUser.role} → ${role}) by ${profile.email}`)

    // TODO: Add audit log entry to database
    // await adminClient.from('audit_logs').insert({
    //   action: 'role_change',
    //   actor_id: profile.id,
    //   target_user_id: userId,
    //   old_value: targetUser.role,
    //   new_value: role,
    //   timestamp: new Date().toISOString()
    // })

    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: `User role updated from ${targetUser.role} to ${role}`
    })

  } catch (error: any) {
    console.error('Unexpected error updating user role:', error)
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    )
  }
})

// Get user role information - Admin only
export const GET = withAdmin(async (request: NextRequest, { profile, params }) => {
  try {
    const userId = parseInt(params?.id as string)

    const { data: user, error } = await adminClient
      .from('users')
      .select('id, email, name, role, created_at, updated_at')
      .eq('id', userId)
      .single()

    if (error || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      user,
      available_roles: Object.values(USER_ROLES).map(role => ({
        value: role,
        label: role.split('_').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' '),
        can_assign: canAssignRole(profile.role, role)
      }))
    })

  } catch (error: any) {
    console.error('Error fetching user role info:', error)
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    )
  }
})

/**
 * Check if the current user can assign a specific role
 */
function canAssignRole(currentUserRole: string, targetRole: string): boolean {
  // Super admins can assign any role
  if (currentUserRole === 'super_admin') {
    return true
  }

  // Regular admins cannot assign super_admin role
  if (currentUserRole === 'admin' && targetRole === 'super_admin') {
    return false
  }

  // Regular admins can assign other roles
  if (currentUserRole === 'admin') {
    return true
  }

  // Non-admins cannot assign any roles
  return false
}
