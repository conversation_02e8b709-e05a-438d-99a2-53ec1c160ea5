"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface SalesMetricsProps {
  metrics?: {
    conversionRate: number
    averageOrderValue: number
    repeatPurchaseRate: number
    customerLifetimeValue: number
  }
  isLoading?: boolean
}

export function SalesMetrics({ 
  metrics = {
    conversionRate: 3.2,
    averageOrderValue: 24.5,
    repeatPurchaseRate: 42.8,
    customerLifetimeValue: 187.5
  }, 
  isLoading = false 
}: SalesMetricsProps) {
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2,
    }).format(value)
  }
  
  // Helper function to determine color based on value
  const getColorClass = (value: number, type: 'percentage' | 'currency') => {
    if (type === 'percentage') {
      if (value >= 40) return 'text-green-600'
      if (value >= 20) return 'text-yellow-600'
      return 'text-red-600'
    }
    
    if (type === 'currency') {
      if (value >= 150) return 'text-green-600'
      if (value >= 75) return 'text-yellow-600'
      return 'text-red-600'
    }
    
    return ''
  }
  
  // Helper function to determine progress color
  const getProgressColor = (value: number, type: 'percentage' | 'currency') => {
    if (type === 'percentage') {
      if (value >= 40) return 'bg-green-600'
      if (value >= 20) return 'bg-yellow-600'
      return 'bg-red-600'
    }
    
    if (type === 'currency') {
      if (value >= 150) return 'bg-green-600'
      if (value >= 75) return 'bg-yellow-600'
      return 'bg-red-600'
    }
    
    return ''
  }
  
  if (isLoading) {
    return (
      <div className="space-y-6 animate-pulse">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="flex justify-between">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
              <div className="h-4 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-2 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Conversion Rate</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.conversionRate, 'percentage')}`}>
            {metrics.conversionRate}%
          </span>
        </div>
        <Progress 
          value={metrics.conversionRate * 5} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.conversionRate, 'percentage')}
        />
        <p className="text-xs text-muted-foreground">Percentage of visitors who make a purchase</p>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Average Order Value</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.averageOrderValue, 'currency')}`}>
            {formatCurrency(metrics.averageOrderValue)}
          </span>
        </div>
        <Progress 
          value={(metrics.averageOrderValue / 50) * 100} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.averageOrderValue, 'currency')}
        />
        <p className="text-xs text-muted-foreground">Average amount spent per order</p>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Repeat Purchase Rate</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.repeatPurchaseRate, 'percentage')}`}>
            {metrics.repeatPurchaseRate}%
          </span>
        </div>
        <Progress 
          value={metrics.repeatPurchaseRate} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.repeatPurchaseRate, 'percentage')}
        />
        <p className="text-xs text-muted-foreground">Percentage of customers who make multiple purchases</p>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Customer Lifetime Value</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.customerLifetimeValue, 'currency')}`}>
            {formatCurrency(metrics.customerLifetimeValue)}
          </span>
        </div>
        <Progress 
          value={(metrics.customerLifetimeValue / 300) * 100} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.customerLifetimeValue, 'currency')}
        />
        <p className="text-xs text-muted-foreground">Average revenue generated per customer over their lifetime</p>
      </div>
    </div>
  )
}
