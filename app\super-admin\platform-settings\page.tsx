"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/unified-auth-context"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Settings,
  Save,
  RefreshCw,
  Globe,
  Mail,
  Phone,
  MapPin,
  Clock,
  DollarSign,
  Shield,
  Bell,
  Database,
  AlertTriangle,
  CheckCircle
} from "lucide-react"

interface PlatformSettings {
  general: {
    platform_name: string
    platform_description: string
    support_email: string
    support_phone: string
    timezone: string
    currency: string
    language: string
  }
  business: {
    registration_enabled: boolean
    auto_approval: boolean
    require_verification: boolean
    min_delivery_fee: number
    max_delivery_distance: number
    commission_rate: number
  }
  notifications: {
    email_enabled: boolean
    sms_enabled: boolean
    push_enabled: boolean
    admin_notifications: boolean
    business_notifications: boolean
    customer_notifications: boolean
  }
  security: {
    password_min_length: number
    require_email_verification: boolean
    session_timeout: number
    max_login_attempts: number
    two_factor_enabled: boolean
  }
  maintenance: {
    maintenance_mode: boolean
    maintenance_message: string
    allowed_ips: string[]
  }
}

export default function PlatformSettingsPage() {
  const { user, userProfile } = useAuth()
  const [settings, setSettings] = useState<PlatformSettings>({
    general: {
      platform_name: "Loop Jersey",
      platform_description: "Jersey's premier delivery platform",
      support_email: "<EMAIL>",
      support_phone: "+44 1534 123456",
      timezone: "Europe/London",
      currency: "GBP",
      language: "en-GB"
    },
    business: {
      registration_enabled: true,
      auto_approval: false,
      require_verification: true,
      min_delivery_fee: 2.50,
      max_delivery_distance: 15,
      commission_rate: 15
    },
    notifications: {
      email_enabled: true,
      sms_enabled: false,
      push_enabled: true,
      admin_notifications: true,
      business_notifications: true,
      customer_notifications: true
    },
    security: {
      password_min_length: 8,
      require_email_verification: true,
      session_timeout: 24,
      max_login_attempts: 5,
      two_factor_enabled: false
    },
    maintenance: {
      maintenance_mode: false,
      maintenance_message: "We're currently performing scheduled maintenance. Please check back soon.",
      allowed_ips: ["127.0.0.1", "::1"]
    }
  })
  const [isLoading, setIsLoading] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')

  const updateSetting = (section: keyof PlatformSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  const saveSettings = async () => {
    setSaveStatus('saving')
    setIsLoading(true)

    try {
      // In real implementation, this would call the API
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      console.log("Saving platform settings:", settings)
      setSaveStatus('saved')
      
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      console.error("Error saving settings:", error)
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const getSaveButtonContent = () => {
    switch (saveStatus) {
      case 'saving':
        return (
          <>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Saving...
          </>
        )
      case 'saved':
        return (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            Saved
          </>
        )
      case 'error':
        return (
          <>
            <AlertTriangle className="h-4 w-4 mr-2" />
            Error
          </>
        )
      default:
        return (
          <>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </>
        )
    }
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6 text-purple-600" />
            Platform Settings
          </h1>
          <p className="text-gray-500">Configure system-wide platform settings</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button 
            onClick={saveSettings} 
            disabled={isLoading}
            className={`${
              saveStatus === 'saved' ? 'bg-green-600 hover:bg-green-700' :
              saveStatus === 'error' ? 'bg-red-600 hover:bg-red-700' :
              'bg-purple-600 hover:bg-purple-700'
            }`}
          >
            {getSaveButtonContent()}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                General Settings
              </CardTitle>
              <CardDescription>
                Basic platform configuration and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="platform_name">Platform Name</Label>
                  <Input
                    id="platform_name"
                    value={settings.general.platform_name}
                    onChange={(e) => updateSetting('general', 'platform_name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select 
                    value={settings.general.currency} 
                    onValueChange={(value) => updateSetting('general', 'currency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GBP">GBP (£)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="USD">USD ($)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="platform_description">Platform Description</Label>
                <Textarea
                  id="platform_description"
                  value={settings.general.platform_description}
                  onChange={(e) => updateSetting('general', 'platform_description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="support_email">Support Email</Label>
                  <Input
                    id="support_email"
                    type="email"
                    value={settings.general.support_email}
                    onChange={(e) => updateSetting('general', 'support_email', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="support_phone">Support Phone</Label>
                  <Input
                    id="support_phone"
                    value={settings.general.support_phone}
                    onChange={(e) => updateSetting('general', 'support_phone', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select 
                    value={settings.general.timezone} 
                    onValueChange={(value) => updateSetting('general', 'timezone', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Europe/London">Europe/London (GMT/BST)</SelectItem>
                      <SelectItem value="Europe/Paris">Europe/Paris (CET/CEST)</SelectItem>
                      <SelectItem value="America/New_York">America/New_York (EST/EDT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select 
                    value={settings.general.language} 
                    onValueChange={(value) => updateSetting('general', 'language', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en-GB">English (UK)</SelectItem>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="fr-FR">Français</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Business Settings */}
        <TabsContent value="business">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Business Settings
              </CardTitle>
              <CardDescription>
                Configure business registration and operational parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="registration_enabled">Business Registration</Label>
                    <p className="text-sm text-gray-500">Allow new businesses to register</p>
                  </div>
                  <Switch
                    id="registration_enabled"
                    checked={settings.business.registration_enabled}
                    onCheckedChange={(checked) => updateSetting('business', 'registration_enabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto_approval">Auto Approval</Label>
                    <p className="text-sm text-gray-500">Automatically approve new business registrations</p>
                  </div>
                  <Switch
                    id="auto_approval"
                    checked={settings.business.auto_approval}
                    onCheckedChange={(checked) => updateSetting('business', 'auto_approval', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="require_verification">Require Verification</Label>
                    <p className="text-sm text-gray-500">Require document verification for businesses</p>
                  </div>
                  <Switch
                    id="require_verification"
                    checked={settings.business.require_verification}
                    onCheckedChange={(checked) => updateSetting('business', 'require_verification', checked)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="min_delivery_fee">Min Delivery Fee (£)</Label>
                  <Input
                    id="min_delivery_fee"
                    type="number"
                    step="0.01"
                    value={settings.business.min_delivery_fee}
                    onChange={(e) => updateSetting('business', 'min_delivery_fee', parseFloat(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="max_delivery_distance">Max Delivery Distance (km)</Label>
                  <Input
                    id="max_delivery_distance"
                    type="number"
                    value={settings.business.max_delivery_distance}
                    onChange={(e) => updateSetting('business', 'max_delivery_distance', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="commission_rate">Commission Rate (%)</Label>
                  <Input
                    id="commission_rate"
                    type="number"
                    step="0.1"
                    value={settings.business.commission_rate}
                    onChange={(e) => updateSetting('business', 'commission_rate', parseFloat(e.target.value))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Configure notification channels and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_enabled">Email Notifications</Label>
                    <p className="text-sm text-gray-500">Enable email notifications system-wide</p>
                  </div>
                  <Switch
                    id="email_enabled"
                    checked={settings.notifications.email_enabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'email_enabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sms_enabled">SMS Notifications</Label>
                    <p className="text-sm text-gray-500">Enable SMS notifications system-wide</p>
                  </div>
                  <Switch
                    id="sms_enabled"
                    checked={settings.notifications.sms_enabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'sms_enabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="push_enabled">Push Notifications</Label>
                    <p className="text-sm text-gray-500">Enable push notifications system-wide</p>
                  </div>
                  <Switch
                    id="push_enabled"
                    checked={settings.notifications.push_enabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'push_enabled', checked)}
                  />
                </div>
              </div>

              <div className="border-t pt-6">
                <h4 className="font-medium mb-4">User Type Notifications</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="admin_notifications">Admin Notifications</Label>
                      <p className="text-sm text-gray-500">Send notifications to admin users</p>
                    </div>
                    <Switch
                      id="admin_notifications"
                      checked={settings.notifications.admin_notifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'admin_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="business_notifications">Business Notifications</Label>
                      <p className="text-sm text-gray-500">Send notifications to business users</p>
                    </div>
                    <Switch
                      id="business_notifications"
                      checked={settings.notifications.business_notifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'business_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="customer_notifications">Customer Notifications</Label>
                      <p className="text-sm text-gray-500">Send notifications to customer users</p>
                    </div>
                    <Switch
                      id="customer_notifications"
                      checked={settings.notifications.customer_notifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'customer_notifications', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Configure security policies and authentication requirements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="password_min_length">Minimum Password Length</Label>
                  <Input
                    id="password_min_length"
                    type="number"
                    min="6"
                    max="20"
                    value={settings.security.password_min_length}
                    onChange={(e) => updateSetting('security', 'password_min_length', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="session_timeout">Session Timeout (hours)</Label>
                  <Input
                    id="session_timeout"
                    type="number"
                    min="1"
                    max="168"
                    value={settings.security.session_timeout}
                    onChange={(e) => updateSetting('security', 'session_timeout', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
                <Input
                  id="max_login_attempts"
                  type="number"
                  min="3"
                  max="10"
                  value={settings.security.max_login_attempts}
                  onChange={(e) => updateSetting('security', 'max_login_attempts', parseInt(e.target.value))}
                  className="max-w-xs"
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="require_email_verification">Email Verification</Label>
                    <p className="text-sm text-gray-500">Require email verification for new accounts</p>
                  </div>
                  <Switch
                    id="require_email_verification"
                    checked={settings.security.require_email_verification}
                    onCheckedChange={(checked) => updateSetting('security', 'require_email_verification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="two_factor_enabled">Two-Factor Authentication</Label>
                    <p className="text-sm text-gray-500">Enable 2FA for admin accounts</p>
                  </div>
                  <Switch
                    id="two_factor_enabled"
                    checked={settings.security.two_factor_enabled}
                    onCheckedChange={(checked) => updateSetting('security', 'two_factor_enabled', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Settings */}
        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Maintenance Settings
              </CardTitle>
              <CardDescription>
                Configure maintenance mode and system access
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Label htmlFor="maintenance_mode">Maintenance Mode</Label>
                  <p className="text-sm text-gray-500">
                    Enable maintenance mode to restrict access to the platform
                  </p>
                  {settings.maintenance.maintenance_mode && (
                    <Badge variant="destructive" className="mt-2">
                      Maintenance Mode Active
                    </Badge>
                  )}
                </div>
                <Switch
                  id="maintenance_mode"
                  checked={settings.maintenance.maintenance_mode}
                  onCheckedChange={(checked) => updateSetting('maintenance', 'maintenance_mode', checked)}
                />
              </div>

              <div>
                <Label htmlFor="maintenance_message">Maintenance Message</Label>
                <Textarea
                  id="maintenance_message"
                  value={settings.maintenance.maintenance_message}
                  onChange={(e) => updateSetting('maintenance', 'maintenance_message', e.target.value)}
                  rows={3}
                  placeholder="Message to display to users during maintenance"
                />
              </div>

              <div>
                <Label htmlFor="allowed_ips">Allowed IP Addresses</Label>
                <p className="text-sm text-gray-500 mb-2">
                  IP addresses that can access the platform during maintenance (one per line)
                </p>
                <Textarea
                  id="allowed_ips"
                  value={settings.maintenance.allowed_ips.join('\n')}
                  onChange={(e) => updateSetting('maintenance', 'allowed_ips', e.target.value.split('\n').filter(ip => ip.trim()))}
                  rows={4}
                  placeholder="127.0.0.1&#10;::1&#10;*************"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
