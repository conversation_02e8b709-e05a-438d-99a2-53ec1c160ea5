import { OrderDetails, Business, OrderItem } from './types'

// Utility function to format business name from slug
export const formatBusinessName = (name: string): string => {
  console.log('🔧 formatBusinessName called with:', name);
  if (!name) return '';

  // Special handling for known business IDs (numeric) - CHECK THIS FIRST!
  if (name === '1') {
    console.log('🔧 formatBusinessName: matched business ID 1, returning Jersey Wings');
    return 'Jersey Wings';
  } else if (name === '2') {
    console.log('🔧 formatBusinessName: matched business ID 2, returning Jersey Grill');
    return 'Jersey Grill';
  } else if (name === '3') {
    console.log('🔧 formatBusinessName: matched business ID 3, returning St Brelade Bistro');
    return 'St Brelade Bistro';
  }

  // For "Business jersey Wings" format, fix it to "Jersey Wings"
  if (name.toLowerCase().startsWith('business jersey')) {
    return name.replace(/^business jersey/i, 'Jersey');
  }

  // If the name doesn't contain hyphens, it's likely already formatted
  if (!name.includes('-')) {
    console.log('🔧 formatBusinessName: no hyphens, returning as-is:', name);
    return name;
  }

  // Special handling for known business IDs (slug format)
  if (name === 'jersey-wings') {
    return 'Jersey Wings';
  } else if (name === 'jersey-grill') {
    return 'Jersey Grill';
  } else if (name === 'st-brelade-bistro') {
    return 'St Brelade Bistro';
  }

  // Split by hyphens, capitalize each word, and join with spaces
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Parse sessionStorage order details (from cart context)
function parseSessionStorageOrderDetails(sessionData: any): OrderDetails {
  console.log('📊 ORDER CONFIRMATION: Processing sessionStorage data:', sessionData);

  // Map cart items to OrderItem format
  const orderItems: OrderItem[] = (sessionData.items || []).map((item: any) => ({
    id: item.id || '',
    name: item.name || 'Unknown Product',
    price: item.price || 0,
    quantity: item.quantity || 1,
    businessId: item.businessId?.toString() || '',
    options: item.options || []
  }));

  // Create a map of business_id to order number from the orders array
  const businessOrderNumbers: Record<string, string> = {};
  if (sessionData.orders && Array.isArray(sessionData.orders)) {
    sessionData.orders.forEach((order: any) => {
      if (order.businessId && order.orderNumber) {
        businessOrderNumbers[order.businessId.toString()] = order.orderNumber;
      }
    });
    console.log('📊 ORDER CONFIRMATION: Mapped business order numbers:', businessOrderNumbers);
  } else {
    console.log('📊 ORDER CONFIRMATION: No orders array found in sessionData or it is not an array');
  }

  // Map businesses from sessionStorage (these have rich data from checkout context)
  const businesses: Business[] = (sessionData.businesses || []).map((business: any) => {
    // Get items for this business
    const businessItems = orderItems.filter(item =>
      item.businessId === business.business_id?.toString()
    );

    // Get the order number for this business from the orders array
    const businessId = business.business_id?.toString() || '';
    const orderNumber = businessOrderNumbers[businessId] || null;

    return {
      id: businessId,
      name: business.business_name || formatBusinessName(businessId),
      type: business.business_type || 'restaurant',
      items: businessItems,
      subtotal: business.subtotal || 0,
      deliveryFee: business.delivery_fee || 0,
      deliveryMethod: business.delivery_method || 'delivery',
      deliveryType: business.delivery_type || 'asap',
      scheduledTime: business.scheduled_time || business.scheduledTime,
      preparationTime: business.preparation_time || null,
      deliveryTime: business.delivery_time || null,
      orderNumber: orderNumber,
      estimatedTime: business.estimated_delivery_time || business.estimatedTime || 'As soon as possible'
    };
  });

  return {
    orderId: sessionData.orderId || 'Unknown Order',
    businesses: businesses,
    items: orderItems,
    subtotal: sessionData.subtotal || 0,
    deliveryFee: sessionData.deliveryFee || 0,
    serviceFee: sessionData.serviceFee || 0,
    total: sessionData.total || 0,
    customerName: sessionData.customerName || 'Guest User',
    customerPhone: sessionData.customerPhone || '',
    customerAddress: sessionData.customerAddress || 'Address not available',
    paymentMethod: sessionData.paymentMethod || 'card',
    deliveryType: sessionData.deliveryType || 'delivery',
    scheduledTime: sessionData.scheduledTime,
    estimatedDeliveryTime: sessionData.estimatedDeliveryTime || 'As soon as possible'
  };
}

// PHASE 2 STEP 3: Main function to fetch order details (supports sessionId for multi-business orders)
export const fetchOrderDetails = async (identifier: string): Promise<OrderDetails | null> => {
  try {
    console.log('🔍 ORDER CONFIRMATION: Fetching enhanced order details from API first');

    // PHASE 2 STEP 3: Check if this is a session ID (for multi-business orders)
    if (identifier.includes('-') && identifier.length > 20) {
      console.log('🔍 ORDER CONFIRMATION: Session ID detected, fetching multi-business order details');
      const sessionOrderDetails = await fetchSessionOrderDetailsFromAPI(identifier);
      if (sessionOrderDetails) {
        console.log('✅ ORDER CONFIRMATION: Successfully loaded session-based order details from API');
        return sessionOrderDetails;
      }
    }

    // Single business order (existing logic)
    const apiOrderDetails = await fetchOrderDetailsFromAPI(identifier);
    if (apiOrderDetails) {
      console.log('✅ ORDER CONFIRMATION: Successfully loaded enhanced order details from API');
      return apiOrderDetails;
    }

    console.log('⚠️ ORDER CONFIRMATION: API failed, falling back to sessionStorage data');

    // Fallback to sessionStorage if API fails
    if (typeof window !== 'undefined') {
      const sessionOrderDetails = sessionStorage.getItem('orderDetails');
      if (sessionOrderDetails) {
        console.log('✅ ORDER CONFIRMATION: Using cart context data from sessionStorage as fallback');
        return parseSessionStorageOrderDetails(JSON.parse(sessionOrderDetails));
      }
    }

    console.log('❌ ORDER CONFIRMATION: Both API and sessionStorage failed');
    return null;
  } catch (error) {
    console.error('Error fetching order details:', error);
    return null;
  }
};

// Function to fetch order details from API (fallback)
export const fetchOrderDetailsFromAPI = async (orderId: string): Promise<OrderDetails | null> => {
  try {
    console.log('🔍 ORDER CONFIRMATION: Fetching order details from API for order ID:', orderId);

    // Get auth token if available
    const token = localStorage.getItem('loop_jersey_auth_token') || '';

    const response = await fetch(`/api/orders/${orderId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (!response.ok) {
      console.error('❌ ORDER CONFIRMATION: API error fetching order details:', response.status, response.statusText);

      // Try to get more detailed error information
      try {
        const errorText = await response.text();
        console.error('Error details:', errorText);

        try {
          const errorJson = JSON.parse(errorText);
          console.error('Error JSON:', errorJson);
        } catch (jsonError) {
          console.error('Error response is not valid JSON');
        }
      } catch (textError) {
        console.error('Could not read error response text');
      }

      return null;
    }

    const data = await response.json();

    if (!data.order) {
      console.error('❌ ORDER CONFIRMATION: No order data returned from API');
      return null;
    }

    console.log('✅ ORDER CONFIRMATION: Successfully fetched order details from API:', data.order.id);

    // Map API response to OrderDetails format
    const orderData = data.order;
    // Handle both cart_items (new approach) and order_items (fallback)
    const itemsArray = orderData.cart_items || orderData.order_items || [];

    const orderItems: OrderItem[] = itemsArray.map((item: any) => ({
      id: item.product_id?.toString() || item.item_id || '',
      name: item.name || item.product_name || item.item_name || 'Unknown Product',
      price: item.price || 0,
      quantity: item.quantity || 1,
      businessId: item.business_id?.toString() || '',
      businessName: item.business_name || null, // Add business name from cart items
      variantName: item.variantName || null,
      customizations: item.customizations || [],
      options: item.options || [] // Keep for backward compatibility
    }));

    // Group items by business
    const itemsByBusiness: Record<string, OrderItem[]> = {};
    orderItems.forEach(item => {
      if (!itemsByBusiness[item.businessId]) {
        itemsByBusiness[item.businessId] = [];
      }
      itemsByBusiness[item.businessId].push(item);
    });

    // Create businesses array
    const businesses: Business[] = Object.keys(itemsByBusiness).map(businessId => {
      const businessItems = itemsByBusiness[businessId];
      const subtotal = businessItems.reduce((total, item) => total + (item.price * item.quantity), 0);

      // Find business info from order_businesses if available
      const businessInfo = orderData.order_businesses?.find((b: any) =>
        b.business_id?.toString() === businessId
      );

      // Try multiple sources for business name:
      // 1. From order_businesses table (if available)
      // 2. From main order record (business_name field)
      // 3. From cart items (business_name field)
      // 4. Format from business ID as fallback
      let businessName = businessInfo?.business_name;

      if (!businessName && orderData.business_name && orderData.business_id?.toString() === businessId) {
        businessName = orderData.business_name;
      }

      if (!businessName && businessItems.length > 0 && businessItems[0].businessName) {
        businessName = businessItems[0].businessName;
      }

      if (!businessName) {
        businessName = formatBusinessName(businessId);
      }

      console.log(`🏢 ORDER CONFIRMATION: Business ${businessId} - businessInfo:`, businessInfo, 'order business_name:', orderData.business_name, 'final name:', businessName);

      return {
        id: businessId,
        name: businessName,
        type: businessInfo?.business_type || 'restaurant',
        items: businessItems,
        subtotal: subtotal,
        deliveryFee: businessInfo?.delivery_fee || 0,
        deliveryMethod: businessInfo?.delivery_method || orderData.delivery_method || (orderData.delivery_address ? 'delivery' : 'pickup'),
        deliveryType: businessInfo?.delivery_type || (orderData.scheduled_delivery ? 'scheduled' : 'asap'),
        scheduledTime: businessInfo?.scheduled_time || orderData.delivery_time,
        preparationTime: businessInfo?.preparation_time || null,
        deliveryTime: businessInfo?.delivery_time || null,
        orderNumber: businessInfo?.order_number || orderData.order_number || null,
        estimatedTime: businessInfo?.estimated_delivery_time || orderData.estimated_delivery_time || 'As soon as possible'
      };
    });

    // Use order_number if available, otherwise format the ID nicely
    const displayOrderId = orderData.order_number || `Order #${orderData.id}`;

    return {
      orderId: displayOrderId,
      businesses: businesses,
      items: orderItems,
      subtotal: orderData.subtotal || 0,
      deliveryFee: orderData.delivery_fee || 0,
      serviceFee: orderData.service_fee || 0,
      total: orderData.total || 0,
      customerName: orderData.customer_name || 'Guest User',
      customerPhone: orderData.customer_phone || '',
      customerAddress: orderData.delivery_address || 'Address not available',
      paymentMethod: orderData.payment_method || 'card',
      deliveryType: orderData.delivery_type || 'delivery',
      scheduledTime: orderData.scheduled_delivery ? orderData.delivery_time : null,
      estimatedDeliveryTime: orderData.estimated_delivery_time || 'As soon as possible'
    };
  } catch (error) {
    console.error('Error fetching order details from API:', error);
    return null;
  }
};

// PHASE 2 STEP 3: Function to fetch session-based multi-business order details
export const fetchSessionOrderDetailsFromAPI = async (sessionId: string): Promise<OrderDetails | null> => {
  try {
    console.log('🔍 ORDER CONFIRMATION: Fetching session-based order details from API for session ID:', sessionId);

    // Get auth token if available
    const token = localStorage.getItem('loop_jersey_auth_token') || '';

    const response = await fetch(`/api/orders/session/${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (!response.ok) {
      console.error('❌ ORDER CONFIRMATION: API error fetching session order details:', response.status, response.statusText);
      return null;
    }

    const data = await response.json();

    if (!data.orders || data.orders.length === 0) {
      console.error('❌ ORDER CONFIRMATION: No orders data returned from session API');
      return null;
    }

    console.log(`✅ ORDER CONFIRMATION: Successfully fetched ${data.orders.length} orders for session from API`);

    // Combine all orders into a single OrderDetails object
    const allOrders = data.orders;
    const allItems: OrderItem[] = [];
    const businesses: Business[] = [];
    let totalSubtotal = 0;
    let totalDeliveryFee = 0;
    let totalServiceFee = 0;
    let totalAmount = 0;

    // Process each order in the session
    for (const orderData of allOrders) {
      const itemsArray = orderData.cart_items || orderData.order_items || [];

      const orderItems: OrderItem[] = itemsArray.map((item: any) => ({
        id: item.product_id?.toString() || item.item_id || '',
        name: item.name || item.product_name || item.item_name || 'Unknown Product',
        price: item.price || 0,
        quantity: item.quantity || 1,
        businessId: item.business_id?.toString() || orderData.business_id?.toString() || '',
        businessName: item.business_name || orderData.business_name || null,
        variantName: item.variantName || null,
        customizations: item.customizations || [],
        options: item.options || []
      }));

      allItems.push(...orderItems);

      // Create business object for this order
      const business: Business = {
        id: orderData.business_id?.toString() || '',
        name: orderData.business_name || formatBusinessName(orderData.business_id?.toString() || ''),
        type: orderData.business_type || 'restaurant',
        items: orderItems,
        subtotal: orderData.subtotal || 0,
        deliveryFee: orderData.delivery_fee || 0,
        deliveryMethod: orderData.delivery_method || (orderData.delivery_address ? 'delivery' : 'pickup'),
        deliveryType: orderData.delivery_type || (orderData.scheduled_delivery ? 'scheduled' : 'asap'),
        scheduledTime: orderData.scheduled_time || orderData.delivery_time,
        preparationTime: orderData.preparation_time || null,
        deliveryTime: orderData.delivery_time || null,
        orderNumber: orderData.order_number || null,
        estimatedTime: orderData.estimated_delivery_time || 'As soon as possible',
        deliveryFulfillment: orderData.delivery_fulfillment || 'loop' // PHASE 2 STEP 3: Include delivery fulfillment
      };

      businesses.push(business);

      // Add to totals
      totalSubtotal += orderData.subtotal || 0;
      totalDeliveryFee += orderData.delivery_fee || 0;
      totalServiceFee += orderData.service_fee || 0;
      totalAmount += orderData.total || 0;
    }

    // Use the first order for customer details (should be same across all orders in session)
    const firstOrder = allOrders[0];

    return {
      orderId: `Session ${sessionId.slice(-8)}`, // Display session ID nicely
      businesses: businesses,
      items: allItems,
      subtotal: totalSubtotal,
      deliveryFee: totalDeliveryFee,
      serviceFee: totalServiceFee,
      total: totalAmount,
      customerName: firstOrder.customer_name || 'Guest User',
      customerPhone: firstOrder.customer_phone || '',
      customerAddress: firstOrder.delivery_address || 'Address not available',
      paymentMethod: firstOrder.payment_method || 'card',
      deliveryType: firstOrder.delivery_type || 'delivery',
      scheduledTime: firstOrder.scheduled_delivery ? firstOrder.delivery_time : null,
      estimatedDeliveryTime: firstOrder.estimated_delivery_time || 'As soon as possible',
      sessionId: sessionId // PHASE 2 STEP 3: Include session ID
    };
  } catch (error) {
    console.error('Error fetching session order details from API:', error);
    return null;
  }
};
