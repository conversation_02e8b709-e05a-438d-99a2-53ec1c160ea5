"use client"

import React from "react"
import { MapPin, Loader2 } from "lucide-react"
import { getStaticMapImageUrl } from "@/lib/map-utils"

interface StaticMapProps {
  name: string
  location: string
  coordinates: [number, number] // [longitude, latitude]
  deliveryRadius: number // in kilometers
}

export default function StaticMap({ name, location, coordinates, deliveryRadius }: StaticMapProps) {
  // State to track if the map is loading
  const [isLoading, setIsLoading] = React.useState(true)

  const [longitude, latitude] = coordinates
  const zoom = 13
  const width = 600
  const height = 120
  const markerColor = "10b981" // emerald-600 in hex

  // Calculate the approximate pixel radius for the delivery radius
  // For static maps, we need to convert km to pixels at the given zoom level
  // This formula provides a better approximation for the radius
  const metersPerPixel = 156543.03392 * Math.cos(latitude * Math.PI / 180) / Math.pow(2, zoom);
  const radiusInPixels = Math.round((deliveryRadius * 1000) / metersPerPixel);

  // Create a static map URL with OpenStreetMap
  const staticMapUrl = getStaticMapImageUrl(
    longitude,
    latitude,
    zoom,
    width,
    height,
    [{ longitude, latitude, color: markerColor }]
  )

  return (
    <div className="rounded-lg overflow-hidden border shadow-md">
      <div className="bg-white p-3 border-b">
        <h3 className="font-semibold">{name} Location</h3>
        <div className="flex items-center text-sm text-gray-500 mt-1">
          <MapPin className="h-4 w-4 mr-1 text-emerald-600" />
          <span>{location}</span>
        </div>
      </div>
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
            <Loader2 className="h-8 w-8 text-emerald-600 animate-spin" />
          </div>
        )}
        <img
          src={staticMapUrl}
          alt={`Map showing ${name} location`}
          className="w-full h-[120px] object-cover"
          onLoad={() => setIsLoading(false)}
          onError={(e) => {
            setIsLoading(false);
            e.currentTarget.src = "/map-placeholder.svg";
            e.currentTarget.onerror = null;
          }}
        />
        <div className="absolute bottom-4 right-4 pointer-events-none">
          <div className="bg-white/90 p-3 rounded-lg shadow-md text-center">
            <p className="text-xs text-emerald-600 font-medium">Delivery radius: {deliveryRadius} km</p>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 p-3 text-xs text-gray-500">
        <p>For detailed delivery area information, please contact the restaurant.</p>
      </div>
    </div>
  )
}
