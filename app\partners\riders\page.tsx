import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, MessageSquare, Users, Star, Clock, DollarSign } from "lucide-react"
import Link from "next/link"

export default function RidersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Become a Loop Jersey Driver/Rider</h1>
          <p className="text-lg text-muted-foreground">Join Jersey's most flexible delivery network and build lasting business relationships</p>
          <div className="flex justify-center gap-4 mt-4">
            <Badge variant="outline" className="text-sm">Car Drivers Welcome</Badge>
            <Badge variant="outline" className="text-sm">Motorcycle Riders Welcome</Badge>
            <Badge variant="outline" className="text-sm">Bicycle Riders Welcome</Badge>
            <Badge variant="outline" className="text-sm">Van Drivers Welcome</Badge>
          </div>
        </div>

        <div className="grid gap-8">
          {/* Independence & Flexibility */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-emerald-600" />
                Complete Independence
              </CardTitle>
              <CardDescription>You're an independent contractor, not an employee</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Work when you want, where you want. Set your own schedule, work for multiple platforms, and maintain complete control over your delivery business. No fixed shifts, no uniforms, no exclusivity requirements.</p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Choose Your Hours</Badge>
                <Badge variant="secondary">Multiple Platforms OK</Badge>
                <Badge variant="secondary">No Fixed Schedule</Badge>
                <Badge variant="secondary">Be Your Own Boss</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Business Approval Process */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                Business Partnership Model
              </CardTitle>
              <CardDescription>Build relationships with businesses that value your service</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>Unlike other platforms, Loop Jersey uses a unique business approval system that puts you in control of building professional relationships:</p>

                <div className="grid gap-3">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-600 font-semibold text-sm mt-0.5">1</div>
                    <div>
                      <h4 className="font-medium">Apply to Businesses</h4>
                      <p className="text-sm text-muted-foreground">Choose which restaurants, shops, and services you want to deliver for</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-600 font-semibold text-sm mt-0.5">2</div>
                    <div>
                      <h4 className="font-medium">Business Reviews Your Profile</h4>
                      <p className="text-sm text-muted-foreground">Businesses see your ratings, experience, and delivery areas before approving</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-600 font-semibold text-sm mt-0.5">3</div>
                    <div>
                      <h4 className="font-medium">Build Your Network</h4>
                      <p className="text-sm text-muted-foreground">Get approved by multiple businesses and become their trusted delivery partner</p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm"><strong>Why this matters:</strong> Businesses that approve you specifically want you representing their brand. This leads to better relationships, more consistent work, and higher earnings potential.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Loop Chat App */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-purple-600" />
                Loop Chat: Your Business Connection Tool
              </CardTitle>
              <CardDescription>Build relationships and grow your delivery business</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>The Loop Chat app is your secret weapon for building lasting business relationships and maximizing your earning potential:</p>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Users className="h-4 w-4 text-purple-600" />
                      Relationship Building
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Direct communication with business owners</li>
                      <li>• Share delivery updates and photos</li>
                      <li>• Resolve issues quickly and professionally</li>
                      <li>• Build trust through consistent service</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Star className="h-4 w-4 text-purple-600" />
                      Customer Discovery
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Help businesses find new customers</li>
                      <li>• Share local market insights</li>
                      <li>• Suggest delivery optimizations</li>
                      <li>• Become a valued business advisor</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm"><strong>Pro Tip:</strong> Riders who actively use Loop Chat to help businesses grow often become their preferred delivery partners, leading to more orders and higher tips.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Competitive Pay */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Competitive Earnings
              </CardTitle>
              <CardDescription>Transparent pay structure with multiple income streams</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>Earn competitive rates with multiple ways to increase your income:</p>

                <div className="grid gap-3">
                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span className="font-medium">Base Delivery Fee</span>
                    <span className="text-green-600 font-semibold">Competitive rates per delivery</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span className="font-medium">Customer Tips</span>
                    <span className="text-green-600 font-semibold">Keep 100% of all tips</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span className="font-medium">Performance Bonuses</span>
                    <span className="text-green-600 font-semibold">Peak hours & quality incentives</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span className="font-medium">Relationship Rewards</span>
                    <span className="text-green-600 font-semibold">Extra earnings from preferred partnerships</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Getting Started */}
          <Card>
            <CardHeader>
              <CardTitle>Ready to Get Started?</CardTitle>
              <CardDescription>Join Jersey's most innovative delivery platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>Our application process is simple and designed to get you earning quickly:</p>

                <div className="grid gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <span>Complete your rider profile with vehicle and license information</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <span>Get verified by our team (usually within 48 hours)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <span>Apply to businesses you want to work with</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <span>Start delivering and building your business network</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Link href="/partners/riders/apply" className="w-full">
                <Button className="w-full bg-emerald-600 hover:bg-emerald-700 text-lg py-6">
                  Start Your Driver/Rider Application
                </Button>
              </Link>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
