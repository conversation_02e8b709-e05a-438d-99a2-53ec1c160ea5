import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { verifyUserAccess } from '@/utils/auth-utils';
import { updateBusinessOrderStatus } from '@/app/api/orders/transaction-helper';

// Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// PATCH endpoint to update a specific business's status within an order
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string, businessId: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order ID and business ID from the URL
    const orderId = parseInt(params.id);
    const businessId = parseInt(params.businessId);

    if (isNaN(orderId) || isNaN(businessId)) {
      return NextResponse.json(
        { error: 'Invalid order ID or business ID format' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await request.json();

    if (!body.status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate the status
    const validStatuses = [
      'pending', 'confirmed', 'preparing', 'ready',
      'out_for_delivery', 'delivered', 'cancelled'
    ];

    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status', validStatuses },
        { status: 400 }
      );
    }

    try {
      // Use enhanced status update with automatic notifications
      const { updateOrderStatusWithNotifications } = await import('../../../../enhanced-status-update');

      const result = await updateOrderStatusWithNotifications({
        orderId: parseInt(orderId),
        newStatus: body.status,
        notes: body.notes || null,
        updatedBy: user.id,
        businessId: businessId
      });

      return NextResponse.json({
        success: true,
        order: result.order
      });
    } catch (transactionError: any) {
      console.error('Error in business order status update transaction:', transactionError);
      return NextResponse.json(
        { error: 'Failed to update business order status', details: transactionError.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
