import type { Metada<PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, ChevronDown, HelpCircle, Menu, Package, Save, Settings, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export const metadata: Metadata = {
  title: "Edit Product - Loop Seller Dashboard",
  description: "Edit product details on the Loop delivery platform",
}

export default function EditProductPage({ params }: { params: { id: string } }) {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold">
            <Package className="h-6 w-6 text-emerald-500" />
            <span className="text-lg">Loop</span>
          </div>
          <Button variant="outline" size="icon" className="ml-auto h-8 w-8 lg:hidden">
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
          <div className="ml-auto hidden items-center gap-4 lg:flex">
            <Button variant="outline" size="icon" className="rounded-full">
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>
            <Button variant="outline" size="icon" className="rounded-full">
              <HelpCircle className="h-4 w-4" />
              <span className="sr-only">Help</span>
            </Button>
            <Button variant="outline" size="sm" className="rounded-full">
              <img src="/placeholder-user.jpg" alt="Avatar" className="mr-2 h-5 w-5 rounded-full" />
              John Doe
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          <div className="flex items-center gap-4">
            <Link href="/products">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Edit Product</h1>
              <p className="text-muted-foreground">Edit product details and inventory information</p>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <Button variant="outline" size="sm" className="h-8">
                <Trash2 className="mr-2 h-3.5 w-3.5" />
                Delete
              </Button>
              <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
                <Save className="mr-2 h-3.5 w-3.5" />
                Save Changes
              </Button>
            </div>
          </div>

          <Tabs defaultValue="general" className="space-y-4">
            <TabsList>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
              <TabsTrigger value="images">Images</TabsTrigger>
              <TabsTrigger value="variants">Variants</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Product Information</CardTitle>
                  <CardDescription>Basic information about your product</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="product-name">Product Name</Label>
                      <Input id="product-name" defaultValue="Premium Headphones" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="product-id">Product ID</Label>
                      <Input id="product-id" defaultValue="PROD001" disabled />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="product-description">Description</Label>
                    <Textarea
                      id="product-description"
                      className="min-h-[150px]"
                      defaultValue="High-quality wireless headphones with noise cancellation and premium sound quality. Perfect for music lovers and professionals alike."
                    />
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="product-category">Category</Label>
                      <Select defaultValue="electronics">
                        <SelectTrigger id="product-category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Categories</SelectLabel>
                            <SelectItem value="electronics">Electronics</SelectItem>
                            <SelectItem value="clothing">Clothing</SelectItem>
                            <SelectItem value="home">Home & Garden</SelectItem>
                            <SelectItem value="beauty">Beauty</SelectItem>
                            <SelectItem value="sports">Sports</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="product-brand">Brand</Label>
                      <Input id="product-brand" defaultValue="SoundMaster" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="product-tags">Tags</Label>
                    <Input id="product-tags" defaultValue="headphones, wireless, audio, premium" />
                    <p className="text-xs text-muted-foreground">Separate tags with commas</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Delivery Information</CardTitle>
                  <CardDescription>Details about product delivery and handling</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="product-weight">Weight (g)</Label>
                      <Input id="product-weight" type="number" defaultValue="350" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="product-dimensions">Dimensions (cm)</Label>
                      <Input id="product-dimensions" defaultValue="20 x 15 x 8" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="delivery-time">Delivery Time (minutes)</Label>
                      <Input id="delivery-time" type="number" defaultValue="30" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="handling-instructions">Special Handling Instructions</Label>
                    <Textarea
                      id="handling-instructions"
                      className="min-h-[100px]"
                      defaultValue="Handle with care. Keep away from extreme temperatures."
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="inventory" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Inventory Management</CardTitle>
                  <CardDescription>Track and manage your product stock</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="stock-quantity">Stock Quantity</Label>
                      <Input id="stock-quantity" type="number" defaultValue="142" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="low-stock-threshold">Low Stock Threshold</Label>
                      <Input id="low-stock-threshold" type="number" defaultValue="10" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="sku">SKU</Label>
                      <Input id="sku" defaultValue="HP-PREM-001" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="barcode">Barcode (UPC/EAN)</Label>
                      <Input id="barcode" defaultValue="8901234567890" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="inventory-location">Inventory Location</Label>
                    <Select defaultValue="warehouse-1">
                      <SelectTrigger id="inventory-location">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Locations</SelectLabel>
                          <SelectItem value="warehouse-1">Main Warehouse</SelectItem>
                          <SelectItem value="warehouse-2">Secondary Warehouse</SelectItem>
                          <SelectItem value="store-1">Retail Store #1</SelectItem>
                          <SelectItem value="store-2">Retail Store #2</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pricing Information</CardTitle>
                  <CardDescription>Set your product pricing and discounts</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="regular-price">Regular Price (£)</Label>
                      <Input id="regular-price" type="number" step="0.01" defaultValue="89.99" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="sale-price">Sale Price (£)</Label>
                      <Input id="sale-price" type="number" step="0.01" defaultValue="79.99" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cost-price">Cost Price (£)</Label>
                      <Input id="cost-price" type="number" step="0.01" defaultValue="45.00" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="tax-class">Tax Class</Label>
                      <Select defaultValue="standard">
                        <SelectTrigger id="tax-class">
                          <SelectValue placeholder="Select tax class" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Tax Classes</SelectLabel>
                            <SelectItem value="standard">Standard Rate (20%)</SelectItem>
                            <SelectItem value="reduced">Reduced Rate (5%)</SelectItem>
                            <SelectItem value="zero">Zero Rate (0%)</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profit-margin">Profit Margin (%)</Label>
                      <Input id="profit-margin" type="number" step="0.01" defaultValue="43.75" disabled />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="images" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Product Images</CardTitle>
                  <CardDescription>Upload and manage product images</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="flex flex-col items-center gap-2">
                      <div className="relative h-40 w-40 overflow-hidden rounded-lg border">
                        <Image
                          src="/placeholder.svg?height=160&width=160"
                          alt="Product image"
                          width={160}
                          height={160}
                          className="h-full w-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                          <Button variant="secondary" size="sm">
                            Replace
                          </Button>
                        </div>
                      </div>
                      <span className="text-xs text-muted-foreground">Main Image</span>
                    </div>

                    <div className="flex flex-col items-center gap-2">
                      <div className="relative h-40 w-40 overflow-hidden rounded-lg border">
                        <Image
                          src="/placeholder.svg?height=160&width=160"
                          alt="Product image"
                          width={160}
                          height={160}
                          className="h-full w-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                          <Button variant="secondary" size="sm">
                            Replace
                          </Button>
                        </div>
                      </div>
                      <span className="text-xs text-muted-foreground">Gallery Image 1</span>
                    </div>

                    <div className="flex flex-col items-center gap-2">
                      <div className="flex h-40 w-40 items-center justify-center rounded-lg border border-dashed">
                        <Button variant="outline">Add Image</Button>
                      </div>
                      <span className="text-xs text-muted-foreground">Gallery Image 2</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="variants" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Product Variants</CardTitle>
                  <CardDescription>Manage different versions of your product</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    This product currently has no variants.
                    <div className="mt-4">
                      <Button>Add Variant</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
