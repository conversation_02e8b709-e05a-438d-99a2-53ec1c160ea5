"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, XCircle, RefreshCw, ArrowLeft } from "lucide-react"
import { useAuthDirect } from "@/context/auth-context-direct"
import { supabase } from "@/lib/supabase"
import Link from "next/link"

export default function BusinessApprovalDebugPage() {
  const { user, userProfile } = useAuthDirect()
  const [isLoading, setIsLoading] = useState(true)
  const [debugData, setDebugData] = useState<any>(null)
  const [fixResult, setFixResult] = useState<string | null>(null)
  const [isFixing, setIsFixing] = useState(false)

  useEffect(() => {
    const fetchDebugData = async () => {
      if (!user || !userProfile) return

      try {
        const data: any = {
          user: userProfile,
          timestamp: new Date().toISOString()
        }

        // Check business_managers table
        const { data: managerData, error: managerError } = await supabase
          .from("business_managers")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle()

        data.businessManager = { data: managerData, error: managerError }

        // If we found a business manager entry, check the business
        if (managerData) {
          const { data: businessData, error: businessError } = await supabase
            .from("businesses")
            .select("*, business_types(name)")
            .eq("id", managerData.business_id)
            .single()

          data.business = { data: businessData, error: businessError }
        }

        // Try to find businesses directly
        const { data: businesses, error: businessesError } = await supabase
          .from("businesses")
          .select("*, business_types(name)")
          .limit(10)

        data.allBusinesses = { data: businesses, error: businessesError }

        // Check for business registrations
        const { data: registrationData, error: registrationError } = await supabase
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle()

        data.registration = { data: registrationData, error: registrationError }

        setDebugData(data)
      } catch (err) {
        console.error("Error fetching debug data:", err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDebugData()
  }, [user, userProfile])

  const handleFixApproval = async () => {
    if (!user || !userProfile || !debugData) return

    setIsFixing(true)
    setFixResult(null)

    try {
      // Check if we have a business manager entry
      if (debugData.businessManager?.data) {
        const businessId = debugData.businessManager.data.business_id

        // Update the business to be approved
        const { error } = await supabase
          .from("businesses")
          .update({ is_approved: true })
          .eq("id", businessId)

        if (error) {
          setFixResult(`Error updating business: ${error.message}`)
          return
        }

        setFixResult("Business approval status updated successfully. Please refresh the page.")
      } else if (debugData.allBusinesses?.data?.length > 0) {
        // Find a business that might belong to this user
        const business = debugData.allBusinesses.data[0]

        // Create a business manager entry
        const { error } = await supabase
          .from("business_managers")
          .insert([
            {
              user_id: userProfile.id,
              business_id: business.id,
              is_primary: true
            }
          ])

        if (error) {
          setFixResult(`Error creating business manager entry: ${error.message}`)
          return
        }

        setFixResult("Business manager entry created successfully. Please refresh the page.")
      } else {
        setFixResult("No businesses found to fix. Please contact support.")
      }
    } catch (err: any) {
      setFixResult(`Error fixing approval: ${err.message}`)
    } finally {
      setIsFixing(false)
    }
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-5 w-5 animate-spin text-emerald-600" />
          <span>Loading debug information...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl py-8">
      <Link href="/business-admin/dashboard" className="flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6">
        <ArrowLeft className="h-4 w-4 mr-1" />
        Back to Dashboard
      </Link>

      <Card>
        <CardHeader>
          <CardTitle>Business Approval Debug</CardTitle>
          <CardDescription>
            This page helps diagnose and fix issues with business approval status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Summary */}
          <div className="grid gap-4">
            <h3 className="text-lg font-medium">Status Summary</h3>

            <div className="grid gap-2">
              <div className="flex items-center">
                {debugData?.businessManager?.data ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span>Business Manager Entry</span>
              </div>

              <div className="flex items-center">
                {debugData?.business?.data ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span>Business Record</span>
              </div>

              <div className="flex items-center">
                {debugData?.business?.data?.is_approved ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span>Business Approved</span>
              </div>
            </div>
          </div>

          {/* Fix Options */}
          <div className="grid gap-4">
            <h3 className="text-lg font-medium">Fix Options</h3>

            {fixResult && (
              <Alert className={fixResult.includes("Error") ? "bg-red-50" : "bg-green-50"}>
                <AlertTitle className="flex items-center">
                  {fixResult.includes("Error") ? (
                    <XCircle className="h-4 w-4 text-red-500 mr-2" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  )}
                  Result
                </AlertTitle>
                <AlertDescription>{fixResult}</AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col space-y-2">
              <Button
                onClick={handleFixApproval}
                disabled={isFixing}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isFixing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Fixing...
                  </>
                ) : (
                  "Fix Approval Status"
                )}
              </Button>

              <Button
                variant="outline"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Page
              </Button>
            </div>
          </div>

          {/* Debug Data */}
          <div className="grid gap-4">
            <h3 className="text-lg font-medium">Debug Data</h3>

            <div className="bg-gray-50 p-4 rounded-md overflow-auto max-h-96">
              <pre className="text-xs">{JSON.stringify(debugData, null, 2)}</pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
