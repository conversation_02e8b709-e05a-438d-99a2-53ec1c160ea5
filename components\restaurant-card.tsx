import Link from "next/link"
import { Clock, MapPin, Phone, Star, Bike, DollarSign } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import FallbackImage from "@/components/fallback-image"
import type { Restaurant } from "@/types/business"
import DynamicDeliveryTime from "@/components/dynamic-delivery-time"
import { ParishName } from "@/lib/parish-distances"

interface RestaurantCardProps {
  restaurant: Restaurant
}

export default function RestaurantCard({ restaurant }: RestaurantCardProps) {
  return (
    <Link href={`/restaurants/${restaurant.id}`}>
      <Card className="overflow-hidden h-full transition-all hover:shadow-lg">
        <div className="relative h-48 bg-white">
          <FallbackImage
            src={restaurant.logo_url || restaurant.image || restaurant.coverImage}
            alt={`${restaurant.name} logo`}
            fallbackSrc="/placeholder.svg"
            className="w-full h-full object-contain p-4"
          />
          {restaurant.isNew && <Badge className="absolute top-2 left-2 bg-emerald-600">New</Badge>}
          {restaurant.offer && <Badge className="absolute top-2 right-2 bg-orange-500">{restaurant.offer}</Badge>}
        </div>
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-bold text-lg">{restaurant.name}</h3>
            {restaurant.rating && typeof restaurant.rating === 'number' && (
              <div className="flex items-center bg-green-50 px-2 py-1 rounded text-sm">
                <Star className="h-3 w-3 text-yellow-500 mr-1" />
                <span>{restaurant.rating.toFixed(1)}</span>
              </div>
            )}
          </div>

          <p className="text-gray-500 text-sm mb-3">{restaurant.cuisines.join(", ")}</p>

          {/* Location and Phone Row */}
          <div className="flex items-center justify-between text-sm mb-3">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-gray-600">{restaurant.location}</span>
            </div>
            <div className="flex items-center">
              <Phone className="h-4 w-4 text-emerald-600 mr-1" />
              <span className="text-emerald-600">{restaurant.phone || "01534 123456"}</span>
            </div>
          </div>

          {/* Delivery Information Card */}
          <div className="mt-4 bg-gray-50 p-3 rounded-md">
            {/* Card Header */}
            <div className="flex items-center mb-2">
              <div className="bg-emerald-50 p-1.5 rounded-full mr-2">
                <Bike className="h-4 w-4 text-emerald-600" />
              </div>
              <span className="font-medium text-gray-700">Delivery Information</span>
            </div>

            {/* Delivery Details Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
              {/* Delivery Time */}
              <div className="flex items-center">
                <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <DynamicDeliveryTime
                    businessCoordinates={
                      restaurant.coordinates && Array.isArray(restaurant.coordinates)
                        ? restaurant.coordinates as [number, number]
                        : [-2.1053, 49.1805] // Default to St Helier
                    }
                    businessParish={restaurant.location as ParishName}
                    preparationTimeMinutes={restaurant.preparationTimeMinutes || 15}
                    defaultDeliveryTime={
                      typeof restaurant.delivery_time_minutes === 'number'
                        ? restaurant.delivery_time_minutes
                        : typeof restaurant.deliveryTime === 'number'
                          ? restaurant.deliveryTime
                          : 20
                    }
                    showTimeRange={true}
                    deliveryRadius={restaurant.delivery_radius}
                    showDeliveryRadiusWarning={true}
                    deliveryAvailable={restaurant.delivery_available !== false} // Default to true if not specified
                  />
                </div>
              </div>

              {/* Delivery Fee or Pick-up Only */}
              <div className="flex items-center">
                {restaurant.delivery_available !== false ? (
                  <>
                    <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                      <DollarSign className="h-4 w-4 text-gray-600" />
                    </div>
                    <div className={`text-sm font-medium ${restaurant.deliveryFee === 0 ? 'text-emerald-600' : 'text-gray-700'}`}>
                      {restaurant.deliveryFee === 0 ? (
                        "Free Delivery"
                      ) : (
                        `£${restaurant.deliveryFee.toFixed(2)} delivery`
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                      <span className="text-gray-600">🚶‍♂️📦</span>
                    </div>
                    <div className="text-sm font-medium text-orange-600">
                      Pick-up only
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
