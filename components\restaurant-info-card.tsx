'use client'

import { Clock, Truck, DollarSign } from 'lucide-react'

interface RestaurantInfoCardProps {
  preparationTime?: string
  totalDeliveryTime?: string
  deliveryFee?: string
  className?: string
}

export default function RestaurantInfoCard({
  preparationTime = '15 min',
  totalDeliveryTime = '30-40 min',
  deliveryFee = '£2.00',
  className
}: RestaurantInfoCardProps) {
  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-4">Delivery Information</h3>
      <div className="space-y-4">
        {/* Preparation Time */}
        <div className="flex items-center">
          <div className="bg-gray-100 p-2 rounded-full mr-3">
            <Clock className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Preparation time</p>
            <p className="font-medium text-gray-800">
              {preparationTime}
            </p>
          </div>
        </div>

        {/* Total Delivery Time */}
        <div className="flex items-center">
          <div className="bg-gray-100 p-2 rounded-full mr-3">
            <Truck className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Total delivery time</p>
            <p className="font-medium text-emerald-600">
              {totalDeliveryTime}
            </p>
            <p className="text-xs text-gray-500">
              (includes preparation time)
            </p>
          </div>
        </div>

        {/* Delivery Fee */}
        <div className="flex items-center">
          <div className="bg-gray-100 p-2 rounded-full mr-3">
            <DollarSign className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Delivery fee</p>
            <p className="font-medium text-gray-800">
              {deliveryFee}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
