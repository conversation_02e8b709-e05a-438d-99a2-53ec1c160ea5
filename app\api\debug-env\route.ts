import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Check if the environment variables are set
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    // Try to create a Supabase client
    let clientCreationError = null;
    try {
      // Create a test client with the service role key
      const testClient = createClient(
        supabaseUrl || '',
        supabaseServiceRoleKey || '',
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );

      // Test the client with a simple query
      await testClient.from('businesses').select('count(*)').limit(1);
    } catch (err: any) {
      clientCreationError = err.message || 'Unknown error creating Supabase client';
    }

    // Get all environment variables (only public ones for security)
    const allEnvVars: Record<string, string> = {};
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('NEXT_PUBLIC_')) {
        allEnvVars[key] = process.env[key] || '';
      } else {
        allEnvVars[key] = process.env[key] ? 'Set (value hidden)' : 'Not set';
      }
    });

    return NextResponse.json({
      supabaseUrl: supabaseUrl ? "Set" : "Not set",
      supabaseUrlValue: supabaseUrl,
      supabaseAnonKey: supabaseAnonKey ? "Set" : "Not set",
      supabaseAnonKeyLength: supabaseAnonKey?.length || 0,
      supabaseServiceRoleKey: supabaseServiceRoleKey ? "Set" : "Not set",
      supabaseServiceRoleKeyLength: supabaseServiceRoleKey?.length || 0,
      nodeEnv: process.env.NODE_ENV,
      clientCreationError,
      allEnvVars,
      runtimeEnv: typeof window === 'undefined' ? 'server' : 'client'
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
