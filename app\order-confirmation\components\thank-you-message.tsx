import { Heart } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function ThankYouMessage() {
  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-emerald-50 to-blue-50 backdrop-blur-sm">
      <CardContent className="p-6 text-center">
        <div className="flex justify-center mb-4">
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-pink-500 to-red-500 flex items-center justify-center shadow-lg">
            <Heart className="h-6 w-6 text-white" />
          </div>
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Thank you for your order!</h3>
        <p className="text-gray-600 leading-relaxed">
          We'll send you updates about your order via the app. If you have any questions, please don't hesitate to contact us.
        </p>
      </CardContent>
    </Card>
  )
}
