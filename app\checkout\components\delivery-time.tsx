"use client";

import React, { useCallback } from "react";
import { Clock, AlertTriangle, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useCheckout } from "../checkout-context";
import { BusinessDeliverySettings } from "./business-delivery-settings";
import { useRealtimeCart } from "@/context/realtime-cart-context";

export const DeliveryTime: React.FC = () => {
  const {
    stepsCompleted,
    goToNextStep,
    goToPrevStep,
    itemsByBusiness,
    businessNames,
    businessPreparationTimes,
    businessDetails
  } = useCheckout();

  const { getDeliveryMethod, getDeliveryType, getScheduledTime } = useRealtimeCart();

  // Function to check if delivery settings are valid for a business
  const isBusinessDeliveryValid = useCallback((businessId: string) => {
    const deliveryMethod = getDeliveryMethod(businessId);
    const deliveryType = getDeliveryType(businessId);
    const scheduledTime = getScheduledTime(businessId);

    // If delivery type is scheduled, we need a scheduled time
    if (deliveryType === 'scheduled' && !scheduledTime) {
      return false;
    }

    return true;
  }, [getDeliveryMethod, getDeliveryType, getScheduledTime]);

  // Check if all businesses have valid delivery settings
  const allBusinessesValid = useCallback(() => {
    return Object.keys(itemsByBusiness).every(businessId => isBusinessDeliveryValid(businessId));
  }, [itemsByBusiness, isBusinessDeliveryValid]);

  return (
    <>
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-emerald-100 p-2 rounded-full mr-3">
            <Clock className="h-5 w-5 text-emerald-600" />
          </div>
          <h3 className="font-medium text-lg text-gray-800">Delivery & Pickup Options</h3>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-5 shadow-sm mb-4">
          <div className="flex items-center mb-4 text-sm text-gray-700 bg-blue-50 p-3 rounded-lg">
            <Info className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
            <p>Please select delivery or pickup options for each business in your order. Some businesses may not offer delivery.</p>
          </div>

          {/* Business-specific delivery settings */}
          {Object.keys(itemsByBusiness).map((businessId, index) => {
            const prepTime = businessPreparationTimes[businessId] || 30; // Default to 30 minutes
            const businessName = businessNames(businessId);

            // Check if delivery is available for this business
            // Default to true unless explicitly set to false
            const deliveryAvailable = businessDetails[businessId]
              ? businessDetails[businessId].delivery_available !== false
              : true;

            // Get business type from the first item in this business group
            const businessType = itemsByBusiness[businessId]?.[0]?.businessType || 'restaurant';

            // Set the first business as open by default
            const isFirstBusiness = index === 0;

            return (
              <BusinessDeliverySettings
                key={businessId}
                businessId={businessId}
                businessName={businessName}
                preparationTime={prepTime}
                deliveryAvailable={deliveryAvailable}
                defaultOpen={isFirstBusiness}
                businessType={businessType}
                businessDetails={businessDetails[businessId]}
              />
            );
          })}
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={goToPrevStep}
        >
          Back
        </Button>
        <Button
          type="button"
          onClick={goToNextStep}
          className={`${
            allBusinessesValid()
              ? 'bg-emerald-600 hover:bg-emerald-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          disabled={!allBusinessesValid()}
        >
          {allBusinessesValid() ? (
            "Continue to Address"
          ) : (
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Complete delivery options
            </div>
          )}
        </Button>
      </div>
    </>
  );
};
