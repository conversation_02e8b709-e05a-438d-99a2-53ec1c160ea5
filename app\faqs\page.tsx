"use client"

import { useState } from "react"
import Link from "next/link"
import { ChevronDown, ChevronUp, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"

// FAQ section type
type FAQSection = {
  title: string;
  faqs: {
    question: string;
    answer: React.ReactNode;
  }[];
};

export default function FAQsPage() {
  // Track which sections are expanded
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    "about": true,
    "using": false,
    "orders": false,
    "fees": false,
    "friends": false,
    "age": false,
    "other": false,
  });

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // FAQ data organized by sections
  const faqSections: FAQSection[] = [
    {
      title: "About Loop Jersey",
      faqs: [
        {
          question: "What is Loop Jersey?",
          answer: (
            <>
              <p className="mb-2">
                Loop Jersey is on a mission to transform the way you order food, drinks, and essentials across Jersey.
                We partner with the best local restaurants, cafes, and shops – from neighbourhood favourites to popular
                island brands – and bring what you love, straight to your door.
              </p>
              <p>
                With a growing choice of partners and a team of dedicated delivery riders, we'll have your order with
                you quickly and reliably.
              </p>
            </>
          )
        },
        {
          question: "What is the story behind Loop Jersey?",
          answer: (
            <>
              <p className="mb-2">
                Loop Jersey was launched to provide a better, faster, and more local delivery service for Jersey residents.
                Founded by a team of local entrepreneurs, Loop was created to support island businesses and deliver a
                premium experience to customers.
              </p>
              <p>
                We're proud to be based entirely in Jersey and passionate about keeping it local!
              </p>
            </>
          )
        }
      ]
    },
    {
      title: "Using Loop Jersey",
      faqs: [
        {
          question: "How does it work?",
          answer: (
            <>
              <p className="mb-2">
                You can order either on the website or by using the Loop Jersey app, available on iOS and Android.
                Enter your postcode (or use your location) to find local restaurants, cafes, and stores delivering
                in your area. Choose your items, place your order, and relax while we deliver to your door.
              </p>
              <p>
                You can even schedule an order in advance to suit your plans.
              </p>
            </>
          )
        },
        {
          question: "What kind of restaurants and stores are listed on Loop Jersey?",
          answer: (
            <p>
              We carefully select a high-quality and diverse range of restaurants, cafes, and retailers across Jersey.
              Whether you're craving a gourmet burger, fresh seafood, artisan coffee, or essentials from a local shop,
              you'll find it on Loop Jersey.
            </p>
          )
        },
        {
          question: "What times can I order for?",
          answer: (
            <p>
              Loop Jersey delivers every day from early morning until late evening. Opening times depend on individual
              partners – you can check available options live on our app or website.
            </p>
          )
        },
        {
          question: "How is my order delivered?",
          answer: (
            <p>
              Once you place an order, it's sent directly to the restaurant, cafe, or store to prepare. As soon as it's
              ready, a Loop Jersey rider will collect it and deliver it straight to your door.
            </p>
          )
        },
        {
          question: "Why doesn't Loop Jersey accept cash?",
          answer: (
            <p>
              We accept card payments only to ensure a smooth, secure experience for everyone. It's also safer for our
              riders. You can tip your rider directly through the app after your delivery.
            </p>
          )
        },
        {
          question: "Do I have to tip?",
          answer: (
            <p>
              Tipping is optional but always appreciated! Riders receive 100% of all tips made through the app.
            </p>
          )
        },
        {
          question: "Is there a minimum spend?",
          answer: (
            <p>
              The minimum order amount depends on the partner you're ordering from. If there's a minimum, you'll see it
              clearly at checkout before placing your order.
            </p>
          )
        },
        {
          question: "How do I redeem a voucher code?",
          answer: (
            <p>
              If you have a voucher code, redeem it during checkout on the app or website by entering it in the 'Promo Code'
              field. Voucher terms and conditions will apply.
            </p>
          )
        },
        {
          question: "What do I do if I see an error when adding a voucher?",
          answer: (
            <p>
              Please contact our Customer Service team via the Help section in the app or website for quick assistance.
            </p>
          )
        },
        {
          question: "Do you charge the same prices as in-store?",
          answer: (
            <p>
              We encourage our partners to keep their delivery prices consistent with their in-store menus, though there may
              occasionally be slight differences. Prices are clearly displayed on the app. If you have any questions about
              menu prices, please contact the restaurant or store directly.
            </p>
          )
        },
        {
          question: "Can I place orders in advance?",
          answer: (
            <p>
              Yes! You can schedule an order up to 24 hours ahead and select your preferred delivery time.
            </p>
          )
        },
        {
          question: "Can I collect my order?",
          answer: (
            <p>
              Pickup/collection options are available from selected partners. Check the app to see which partners near you
              offer Pickup.
            </p>
          )
        }
      ]
    },
    {
      title: "Questions About My Order",
      faqs: [
        {
          question: "What if I want to cancel my order?",
          answer: (
            <p>
              You can cancel your order easily through the app, provided the restaurant or store hasn't started preparing it yet.
              If preparation has already started, contact us through the Help section and we'll assist you.
            </p>
          )
        },
        {
          question: "How is the food or product packaged?",
          answer: (
            <p>
              Our partners use packaging designed to maintain the right temperature and protect your order in transit.
              If you have feedback on packaging, please get in touch with us via the Contact section.
            </p>
          )
        },
        {
          question: "What if something is wrong with my order?",
          answer: (
            <p>
              If anything's not right, please contact us through the Help function in the app and our support team will
              assist you quickly.
            </p>
          )
        },
        {
          question: "What if I want to add something to my order?",
          answer: (
            <p>
              Contact our support team via Help as soon as possible — we'll do our best to assist if the order hasn't been
              prepared yet.
            </p>
          )
        },
        {
          question: "What if my order is late?",
          answer: (
            <p>
              Sometimes unexpected delays happen. If your order is running late, we'll keep you updated through the app or by phone.
            </p>
          )
        },
        {
          question: "What if I'm not around when the rider arrives?",
          answer: (
            <p>
              If you won't be available, let us know via Help. Otherwise, the rider may try calling you, or leave your order
              safely at your address if possible. Please ensure your contact details are correct to avoid any issues.
            </p>
          )
        },
        {
          question: "I got a call from +44 (0) 1534 xxxxxx — who is this?",
          answer: (
            <p>
              If your rider needs help finding you, they might call from a local Jersey number. Please keep your phone handy
              during your delivery window.
            </p>
          )
        }
      ]
    },
    {
      title: "Fees on Loop Jersey",
      faqs: [
        {
          question: "How do fees work?",
          answer: (
            <div className="space-y-3">
              <div>
                <p className="font-medium">Delivery fee</p>
                <p>The delivery fee is based on your location and will be shown clearly before you place your order.</p>
              </div>

              <div>
                <p className="font-medium">Service fee</p>
                <p>This helps us power the Loop Jersey experience, maintain the app, and provide customer support. It's calculated based on your order total.</p>
              </div>

              <div>
                <p className="font-medium">Small order fee</p>
                <p>If your basket total is below a certain minimum, we may apply a small order fee. You can avoid this by adding more items to your basket.</p>
              </div>

              <div>
                <p className="font-medium">Extended delivery fee</p>
                <p>For deliveries from partners located further from you, a small extended delivery fee may apply. This will be shown before you confirm your order.</p>
              </div>
            </div>
          )
        }
      ]
    },
    {
      title: "Inviting Friends",
      faqs: [
        {
          question: "Can I get free credit by inviting friends?",
          answer: (
            <p>
              Our referral program will be launching soon! Stay tuned for more details.
            </p>
          )
        },
        {
          question: "Where can I find my invite link?",
          answer: (
            <p>
              Once available, your invite link will appear in the app under your profile section after you've completed your first few orders.
            </p>
          )
        }
      ]
    },
    {
      title: "Age Restricted Items",
      faqs: [
        {
          question: "Do I need ID to order age restricted items?",
          answer: (
            <p>
              Yes. If your order contains alcohol or tobacco, our riders will ask to verify your age by checking valid ID
              (passport, driving licence, or identity card). We use a "challenge all" policy to ensure that deliveries of
              restricted items comply with legal requirements.
            </p>
          )
        },
        {
          question: "Are CBD products available on Loop Jersey?",
          answer: (
            <div>
              <p className="mb-2">Some partners may offer CBD products for sale. Please note that:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>CBD products are not suitable for under-18s.</li>
                <li>Proof of age may be required on delivery.</li>
                <li>CBD products should not be used by pregnant/breastfeeding women or those taking medication.</li>
                <li>Daily intake should not exceed 70mg according to FSA guidance.</li>
                <li>Always consult a healthcare professional if you have concerns.</li>
              </ul>
            </div>
          )
        }
      ]
    },
    {
      title: "Anything Else?",
      faqs: [
        {
          question: "What if I have allergies?",
          answer: (
            <p>
              If you have allergies, check the allergen information provided by our partners before placing an order.
              Where allergen information isn't available, please contact the restaurant or store directly.
            </p>
          )
        },
        {
          question: "When will you deliver to my area?",
          answer: (
            <p>
              We're growing fast! If we're not yet in your area, we hope to be soon. Check our app for the latest updates.
            </p>
          )
        },
        {
          question: "Does Loop Jersey offer company accounts?",
          answer: (
            <p>
              We are working on corporate delivery solutions — <NAME_EMAIL> if you're interested.
            </p>
          )
        },
        {
          question: "Is there a Loop Jersey app?",
          answer: (
            <p>
              Yes! Our app is available free on the App Store and Google Play — search for Loop Jersey.
            </p>
          )
        },
        {
          question: "What is the Customer Champion badge?",
          answer: (
            <div>
              <p className="mb-2">Our Customer Champion badge highlights local partners who consistently deliver great service, quality, and customer satisfaction.</p>
              <p className="mb-2"><strong>How are partners selected?</strong></p>
              <p className="mb-2">Partners automatically qualify based on customer feedback, delivery reliability, food quality, and hygiene standards.</p>
              <p><strong>Can partners pay to appear on the list?</strong></p>
              <p>No. Customer Champion status is awarded based purely on service quality and customer feedback — not by payment.</p>
            </div>
          )
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <div className="bg-emerald-800 text-white py-4">
        <div className="container-fluid">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-xl font-bold">Loop Jersey</Link>
          </div>
        </div>
      </div>

      <main className="flex-1 container-fluid py-8">
        <Link href="/" className="inline-flex items-center text-emerald-600 mb-6">
          <ArrowLeft size={16} className="mr-2" />
          Back to home
        </Link>

        <h1 className="text-3xl font-bold mb-8">Frequently Asked Questions</h1>

        <div className="max-w-3xl mx-auto space-y-6">
          {faqSections.map((section, sectionIndex) => {
            const sectionKey = section.title.toLowerCase().split(' ')[0];
            const isExpanded = expandedSections[sectionKey];

            return (
              <div key={sectionIndex} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                <button
                  className="w-full flex justify-between items-center p-4 text-left font-semibold text-lg"
                  onClick={() => toggleSection(sectionKey)}
                >
                  {section.title}
                  {isExpanded ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>

                {isExpanded && (
                  <div className="px-4 pb-4 divide-y">
                    {section.faqs.map((faq, faqIndex) => (
                      <div key={faqIndex} className="py-3">
                        <h3 className="font-medium text-emerald-700 mb-2">{faq.question}</h3>
                        <div className="text-gray-600 text-sm">{faq.answer}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </main>

      <footer className="bg-emerald-800 text-white py-6">
        <div className="container-fluid">
          <div className="text-center">
            <p className="text-sm">© {new Date().getFullYear()} Loop Jersey. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
