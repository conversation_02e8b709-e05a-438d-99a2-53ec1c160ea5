'use client'

import { useState } from 'react'
import PortraitBusinessCard from '@/components/portrait-business-card'

export default function PortraitGridDemo() {
  // Sample business data for demonstration
  const sampleBusinesses = [
    {
      id: '1',
      name: '<PERSON>\'s Jersey',
      image: '/images/businesses/mcdonalds.jpg',
      businessType: 'restaurant',
      rating: 4.5,
      deliveryTime: '25',
      deliveryTimeRange: '20-30 min',
      deliveryFee: '£2.50',
      location: 'St Helier',
      offers: [
        { text: '20% OFF', color: 'bg-red-500' },
        { text: 'Free Delivery', color: 'bg-emerald-500' }
      ]
    },
    {
      id: '2',
      name: 'Burger King',
      image: '/images/businesses/burgerking.jpg',
      businessType: 'restaurant',
      rating: 4.2,
      deliveryTime: '30',
      deliveryTimeRange: '25-35 min',
      deliveryFee: '£3.00',
      location: 'St Brelade',
      offers: [
        { text: 'Buy One Get One Free', color: 'bg-orange-500' }
      ]
    },
    {
      id: '3',
      name: 'Harbour Coffee',
      image: '/images/businesses/coffee.jpg',
      businessType: 'cafe',
      rating: 4.8,
      deliveryTime: '15',
      deliveryTimeRange: '10-20 min',
      deliveryFee: 'Free delivery',
      location: 'St Aubin',
      sponsored: true
    },
    {
      id: '4',
      name: 'Jersey Wraps',
      image: '/images/businesses/wraps.jpg',
      businessType: 'restaurant',
      rating: 4.3,
      deliveryTime: '20',
      deliveryTimeRange: '15-25 min',
      deliveryFee: '£2.00',
      location: 'St Helier'
    },
    {
      id: '5',
      name: 'Coastal Convenience',
      image: '/images/businesses/convenience.jpg',
      businessType: 'shop',
      rating: 4.1,
      deliveryTime: '35',
      deliveryTimeRange: '30-40 min',
      deliveryFee: '£3.50',
      location: 'St Clement'
    },
    {
      id: '6',
      name: 'St Brelade\'s Bistro',
      image: '/images/businesses/bistro.jpg',
      businessType: 'restaurant',
      rating: 4.7,
      deliveryTime: '40',
      deliveryTimeRange: '35-45 min',
      deliveryFee: '£4.00',
      location: 'St Brelade'
    },
    {
      id: '7',
      name: 'Burger Shack',
      image: '/images/businesses/burgershack.jpg',
      businessType: 'restaurant',
      rating: 4.4,
      deliveryTime: '30',
      deliveryTimeRange: '25-35 min',
      deliveryFee: '£3.00',
      location: 'St Helier'
    },
    {
      id: '8',
      name: 'Jersey Grill',
      image: '/images/businesses/grill.jpg',
      businessType: 'restaurant',
      rating: 4.6,
      deliveryTime: '35',
      deliveryTimeRange: '30-40 min',
      deliveryFee: '£3.50',
      location: 'St Saviour'
    }
  ]

  return (
    <div className="container-fluid py-8">
      <h1 className="text-2xl font-bold mb-6">Portrait Business Cards Grid</h1>

      <div className="space-y-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">2-Column Grid (Mobile)</h2>
          <div className="grid grid-cols-2 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.slice(0, 4).map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">3-Column Grid (Tablet)</h2>
          <div className="grid grid-cols-3 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.slice(0, 6).map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">4-Column Grid (Desktop)</h2>
          <div className="grid grid-cols-4 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">5-Column Grid (Large Desktop)</h2>
          <div className="grid grid-cols-5 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Responsive Grid (2-5 columns based on screen size)</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
