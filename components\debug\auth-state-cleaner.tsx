"use client"

import { useState } from "react"
import { clearAuthToken } from "@/utils/auth-token"

export function AuthStateCleaner() {
  const [isClearing, setIsClearing] = useState(false)
  const [message, setMessage] = useState("")

  const clearAuthState = async () => {
    setIsClearing(true)
    setMessage("")

    try {
      console.log("🔧 Clearing authentication state...")

      // Clear all authentication-related data from localStorage
      const authKeys = [
        'loop_jersey_auth_token',
        'loop_jersey_session_active',
        'loop_jersey_session_timestamp',
        'loop_jersey_user',
        'loop_jersey_selected_business_id',
        'business_registration_success'
      ]

      console.log("🧹 Clearing localStorage authentication data...")
      authKeys.forEach(key => {
        try {
          const value = localStorage.getItem(key)
          if (value) {
            console.log(`  - Removing ${key}`)
            localStorage.removeItem(key)
          }
        } catch (e) {
          console.error(`Error removing ${key}:`, e)
        }
      })

      // Clear Supabase-specific tokens
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('sb-') && key.includes('-auth-token')) {
          console.log(`  - Removing Supabase token: ${key}`)
          localStorage.removeItem(key)
        }
      })

      // Use the utility function to clear the main auth token
      clearAuthToken()

      // Clear any authentication cookies
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/")
      })

      console.log("✅ Authentication state cleared!")
      setMessage("Authentication state cleared successfully! Refreshing page...")

      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1000)

    } catch (error) {
      console.error("Error clearing auth state:", error)
      setMessage("Error clearing authentication state. Check console for details.")
    } finally {
      setIsClearing(false)
    }
  }

  return (
    <div className="p-4 border border-red-200 rounded-lg bg-red-50">
      <h3 className="text-lg font-semibold text-red-800 mb-2">
        Authentication State Cleaner
      </h3>
      <p className="text-sm text-red-600 mb-4">
        Use this if you're experiencing authentication issues (403 errors, logged out but can access protected areas, etc.)
      </p>
      
      <button
        onClick={clearAuthState}
        disabled={isClearing}
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isClearing ? "Clearing..." : "Clear Authentication State"}
      </button>

      {message && (
        <div className="mt-3 p-2 bg-white border border-red-200 rounded text-sm text-red-700">
          {message}
        </div>
      )}
    </div>
  )
}
