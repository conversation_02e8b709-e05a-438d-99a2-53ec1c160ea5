'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  const isChunkError = error.message && (
    error.message.includes('ChunkLoadError') || 
    error.message.includes('Loading chunk') || 
    error.message.includes('Failed to fetch dynamically imported module')
  )

  const handleReset = () => {
    // Clear cache and reload if it's a chunk error
    if (isChunkError) {
      // Clear browser cache for this site
      if ('caches' in window) {
        caches.keys().then((names) => {
          names.forEach((name) => {
            caches.delete(name)
          })
        })
      }
      
      // Hard reload the page
      window.location.reload()
    } else {
      // For other errors, just use the provided reset function
      reset()
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 py-16 text-center">
      <div className="max-w-md mx-auto">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          {isChunkError ? 'Application Update Available' : 'Something went wrong!'}
        </h2>
        <p className="text-gray-600 mb-6">
          {isChunkError 
            ? 'It looks like the application has been updated. Please reload to get the latest version.'
            : 'An unexpected error occurred. Please try again or contact support if the problem persists.'}
        </p>
        <Button 
          onClick={handleReset}
          className="bg-emerald-600 hover:bg-emerald-700 text-white"
        >
          {isChunkError ? 'Reload Application' : 'Try again'}
        </Button>
      </div>
    </div>
  )
}
