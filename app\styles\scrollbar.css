/* Custom scrollbar styles */
.table-container {
  max-width: 100% !important;
  width: 100% !important;
  overflow-x: auto !important; /* Use auto instead of scroll for better behavior */
  overflow-y: auto !important; /* Enable vertical scrolling */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto !important; /* Use 'auto' instead of 'thin' for better visibility */
  scrollbar-color: #10b981 #f1f1f1 !important; /* Emerald thumb, light gray track */
  border: 1px solid #e5e7eb;
  border-top: none;
  position: relative;
  display: block !important;
  max-height: 600px !important; /* Set a maximum height to enable vertical scrolling */
  flex: 1 1 auto !important; /* Allow the container to grow and shrink */
}

/* Ensure table inside container has proper width */
.table-container table {
  width: auto !important;
  min-width: 100% !important;
  table-layout: auto !important; /* Allow columns to size based on content */
}

/* Ensure table cells size properly */
.table-container table th,
.table-container table td {
  white-space: nowrap !important; /* Prevent text wrapping */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Custom scrollbar for Webkit browsers (Chrome, Safari, Edge) */
.table-container::-webkit-scrollbar {
  height: 16px !important; /* Taller scrollbar for easier targeting */
  width: 16px !important;
  display: block !important;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1 !important; /* Light gray background */
  border-radius: 0;
  border-bottom: 1px solid #e5e7eb;
  display: block !important;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: #10b981 !important; /* Emerald green to match brand */
  border-radius: 8px;
  border: 4px solid #f1f1f1 !important; /* Creates padding effect */
  min-width: 40px !important; /* Ensure thumb has minimum size */
  display: block !important;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background-color: #059669 !important; /* Darker emerald on hover */
}

/* Scrollbar corner */
.table-container::-webkit-scrollbar-corner {
  background-color: #f1f1f1 !important;
}

/* Scrollbar buttons (arrows) */
.table-container::-webkit-scrollbar-button {
  display: block !important;
  background-color: #f1f1f1 !important;
  height: 16px !important;
  width: 16px !important;
  border: 1px solid #e5e7eb !important;
}

.table-container::-webkit-scrollbar-button:hover {
  background-color: #e5e7eb !important;
}

/* Animation to highlight scrollbar on page load */
@keyframes pulseScrollbar {
  0% { background-color: #10b981; }
  50% { background-color: #059669; }
  100% { background-color: #10b981; }
}

/* Apply animation to scrollbar thumb */
.table-container::-webkit-scrollbar-thumb {
  animation: pulseScrollbar 2s ease-in-out 3;
}

/* Add a subtle shadow to indicate there's more content to scroll horizontally */
.table-container.has-horizontal-scrollbar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 30px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7));
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  animation: fadeInShadow 1s ease-in-out 1s forwards;
}

/* Add a subtle shadow to indicate there's more content to scroll vertically */
.table-container.has-vertical-scrollbar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.7));
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  animation: fadeInShadow 1s ease-in-out 1s forwards;
}

/* Make the first column sticky for better usability with horizontal scrolling */
.table-container table th:first-child,
.table-container table td:first-child {
  position: sticky !important;
  left: 0 !important;
  z-index: 20 !important;
  background-color: white !important;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05) !important;
}

/* Style for the sticky header when scrolling vertically */
.table-container table thead {
  position: sticky !important;
  top: 0 !important;
  z-index: 30 !important;
  background-color: #f9fafb !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

/* Style for the intersection of sticky header and sticky first column */
.table-container table th:first-child {
  z-index: 40 !important; /* Higher z-index to appear above both sticky header and column */
}

/* Tablet-specific styles */
@media (max-width: 1023px) {
  /* Adjust table cell padding for tablets */
  .table-container table th,
  .table-container table td {
    padding: 0.5rem 0.5rem !important;
  }

  /* Make scrollbars more visible on touch devices */
  .table-container::-webkit-scrollbar {
    height: 18px !important;
    width: 18px !important;
  }

  /* Add visual feedback for touch interactions */
  .table-container.touch-active {
    border: 2px solid #10b981 !important;
  }

  /* Ensure tooltip content is properly sized for tablets */
  .tooltip-content {
    max-width: 150px !important;
    font-size: 0.7rem !important;
  }

  /* Ensure the sticky first column works well on tablets */
  .table-container table th:first-child,
  .table-container table td:first-child {
    box-shadow: 3px 0 8px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Ensure proper scrolling on iOS devices */
@supports (-webkit-touch-callout: none) {
  .table-container {
    -webkit-overflow-scrolling: touch !important;
  }
}

@keyframes fadeInShadow {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
