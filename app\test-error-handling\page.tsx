'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function TestErrorHandling() {
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testUnhandledPromiseRejection = () => {
    addResult('Testing unhandled promise rejection...');
    
    // This should be caught by our global error handler
    Promise.reject(new Error('Test unhandled promise rejection'));
    
    addResult('Promise rejection triggered (should be caught by global handler)');
  };

  const testEventObjectRejection = () => {
    addResult('Testing Event object rejection...');
    
    // Create a fake event object and reject with it
    const fakeEvent = new Event('test-event');
    Promise.reject(fakeEvent);
    
    addResult('Event object rejection triggered (should be caught by global handler)');
  };

  const testAsyncError = async () => {
    addResult('Testing async error...');
    
    try {
      // This should throw an error
      await new Promise((resolve, reject) => {
        setTimeout(() => reject(new Error('Async test error')), 100);
      });
    } catch (error) {
      addResult('Async error caught properly');
    }
  };

  const testCustomEvent = () => {
    addResult('Testing custom event...');
    
    // Dispatch a custom event that might cause issues
    const customEvent = new CustomEvent('test-auth-event', {
      detail: { test: true }
    });
    
    window.dispatchEvent(customEvent);
    addResult('Custom event dispatched');
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Error Handling Test Page</h1>
      
      <div className="space-y-4 mb-6">
        <p className="text-gray-600">
          This page tests various error scenarios to ensure our global error handlers are working properly.
          Check the browser console for error messages.
        </p>
        
        <div className="flex flex-wrap gap-3">
          <Button onClick={testUnhandledPromiseRejection} variant="outline">
            Test Promise Rejection
          </Button>
          
          <Button onClick={testEventObjectRejection} variant="outline">
            Test Event Object Rejection
          </Button>
          
          <Button onClick={testAsyncError} variant="outline">
            Test Async Error
          </Button>
          
          <Button onClick={testCustomEvent} variant="outline">
            Test Custom Event
          </Button>
          
          <Button onClick={clearResults} variant="secondary">
            Clear Results
          </Button>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Test Results</h2>
        
        {results.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Click the buttons above to test error handling.</p>
        ) : (
          <div className="space-y-1">
            {results.map((result, index) => (
              <div key={index} className="text-sm font-mono bg-white p-2 rounded border">
                {result}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">What to Look For:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Check browser console for error messages</li>
          <li>• Errors should be logged but not cause the app to crash</li>
          <li>• No "Uncaught (in promise)" errors should appear</li>
          <li>• Global error handlers should catch and log all errors</li>
        </ul>
      </div>
    </div>
  );
}
