'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { standardizeJerseyPostcodeFormat, isValidJerseyPostcodeFormat } from '@/lib/jersey-postcodes';
import { UserLocationData } from '@/lib/session-utils';

interface UserPostcodeInputProps {
  initialPostcode?: string;
  onPostcodeSubmit: (postcode: string) => Promise<void>;
  className?: string;
  placeholder?: string;
  buttonText?: string;
}

export function UserPostcodeInput({
  initialPostcode = '',
  onPostcodeSubmit,
  className = '',
  placeholder = 'Enter your postcode',
  buttonText = 'Submit'
}: UserPostcodeInputProps) {
  const [postcode, setPostcode] = useState(initialPostcode);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionPostcode, setSessionPostcode] = useState<string | null>(null);

  // Load postcode from session on mount
  useEffect(() => {
    const loadSessionPostcode = async () => {
      try {
        const response = await fetch('/api/user/location');
        if (response.ok) {
          const data = await response.json() as { location: UserLocationData | null };
          if (data.location?.postcode) {
            setSessionPostcode(data.location.postcode);
            if (!initialPostcode) {
              setPostcode(data.location.postcode);
            }
          }
        }
      } catch (error) {
        console.error('Error loading session postcode:', error);
      }
    };

    loadSessionPostcode();
  }, [initialPostcode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Basic validation
    if (!postcode.trim()) {
      setError('Please enter a postcode');
      return;
    }

    // Format validation
    const standardized = standardizeJerseyPostcodeFormat(postcode);
    if (!standardized || !isValidJerseyPostcodeFormat(standardized)) {
      setError('Please enter a valid Jersey postcode');
      return;
    }

    setIsLoading(true);
    try {
      await onPostcodeSubmit(standardized);
    } catch (error) {
      console.error('Error submitting postcode:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={`flex flex-col space-y-2 ${className}`}>
      <div className="flex space-x-2">
        <Input
          type="text"
          value={postcode}
          onChange={(e) => setPostcode(e.target.value)}
          placeholder={placeholder}
          className="flex-1"
          disabled={isLoading}
        />
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-emerald-600 hover:bg-emerald-700 text-white"
        >
          {isLoading ? <Spinner className="mr-2 h-4 w-4" /> : null}
          {buttonText}
        </Button>
      </div>
      {error && <p className="text-red-500 text-sm">{error}</p>}
      {sessionPostcode && sessionPostcode !== postcode && (
        <p className="text-sm text-gray-500">
          Your last used postcode:
          <Button
            variant="link"
            className="p-0 h-auto ml-1 text-sm"
            onClick={() => setPostcode(sessionPostcode)}
          >
            {sessionPostcode}
          </Button>
        </p>
      )}
    </form>
  );
}
