import { NextResponse } from "next/server"

// GET - API documentation and health check
export async function GET() {
  return NextResponse.json({
    name: "Connections Hub API",
    version: "1.0.0",
    description: "API for managing connections and communications in the Loop Jersey ecosystem",
    endpoints: {
      connections: {
        "GET /api/connections-hub/connections": "Get user's connections",
        "POST /api/connections-hub/connections": "Create new connection",
        "GET /api/connections-hub/connections/[id]": "Get specific connection",
        "PUT /api/connections-hub/connections/[id]": "Update connection",
        "DELETE /api/connections-hub/connections/[id]": "Delete connection"
      },
      profiles: {
        "GET /api/connections-hub/profile": "Get user's connection profiles",
        "POST /api/connections-hub/profile": "Create new connection profile",
        "PUT /api/connections-hub/profile": "Update connection profile"
      },
      users: {
        "GET /api/connections-hub/users/search": "Search for users to connect with"
      }
    },
    authentication: "Required for all endpoints. Use Authorization header with Bear<PERSON> token or authenticated session.",
    status: "operational"
  })
}
