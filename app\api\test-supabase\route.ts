import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('businesses')
      .select('count(*)', { count: 'exact' });

    if (connectionError) {
      return NextResponse.json({
        success: false,
        error: connectionError.message,
        details: connectionError,
        env: {
          hasUrl: <PERSON><PERSON><PERSON>(process.env.NEXT_PUBLIC_SUPABASE_URL),
          hasAnonKey: <PERSON><PERSON><PERSON>(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
          hasServiceRole: <PERSON><PERSON><PERSON>(process.env.SUPABASE_SERVICE_ROLE_KEY),
        }
      }, { status: 500 });
    }

    // Test auth configuration
    const { data: authConfig, error: authError } = await supabase.auth.getSession();

    return NextResponse.json({
      success: true,
      connection: {
        status: 'connected',
        count: connectionTest,
      },
      auth: {
        status: authError ? 'error' : 'configured',
        hasSession: <PERSON><PERSON><PERSON>(authConfig?.session),
        error: authError,
      },
      env: {
        hasUrl: Boolean(process.env.NEXT_PUBLIC_SUPABASE_URL),
        hasAnonKey: Boolean(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
        hasServiceRole: Boolean(process.env.SUPABASE_SERVICE_ROLE_KEY),
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      env: {
        hasUrl: Boolean(process.env.NEXT_PUBLIC_SUPABASE_URL),
        hasAnonKey: Boolean(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
        hasServiceRole: Boolean(process.env.SUPABASE_SERVICE_ROLE_KEY),
      }
    }, { status: 500 });
  }
}
