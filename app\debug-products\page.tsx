"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/unified-auth-context"

export default function DebugProductsPage() {
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [debugData, setDebugData] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAPI = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/products?businessId=4')
      const data = await response.json()
      setDebugData(data)
    } catch (error) {
      setDebugData({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (user) {
      testAPI()
    }
  }, [user])

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Products API</h1>

      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold mb-2">Auth State</h2>
          <pre className="text-sm">
            {JSON.stringify({
              user: user?.email,
              userProfile: userProfile,
              isAdmin,
              isSuperAdmin
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold mb-2">API Response</h2>
          {loading ? (
            <p>Loading...</p>
          ) : (
            <pre className="text-sm overflow-auto max-h-96">
              {JSON.stringify(debugData, null, 2)}
            </pre>
          )}
        </div>

        <button
          onClick={testAPI}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test API'}
        </button>
      </div>
    </div>
  )
}
