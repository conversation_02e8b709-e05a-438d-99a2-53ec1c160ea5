import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get order items for a specific order (for drivers)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Get order data including cart_id in one query
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, order_number, status, driver_id, cart_id, delivery_instructions')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      console.error('Order lookup error:', orderError)
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    // Check if order is available (no driver assigned) or assigned to this driver
    if (order.driver_id && order.driver_id !== driverProfile.id) {
      return NextResponse.json(
        { error: "Order not accessible" },
        { status: 403 }
      )
    }

    if (!order.cart_id) {
      return NextResponse.json(
        { error: 'Order has no cart associated' },
        { status: 404 }
      )
    }

    // Get cart items for this order
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select(`
        id,
        name,
        quantity,
        image_url,
        product_id,
        variant_id,
        business_id
      `)
      .eq('cart_id', order.cart_id)

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError)
      return NextResponse.json(
        { error: 'Failed to fetch order items' },
        { status: 500 }
      )
    }

    // Get customizations for all cart items
    let customizations: any[] = []
    if (cartItems && cartItems.length > 0) {
      const cartItemIds = cartItems.map(item => item.id)
      const { data: customizationData, error: customizationError } = await supabase
        .from('cart_item_customizations')
        .select(`
          cart_item_id,
          customization_group_name,
          customization_option_name
        `)
        .in('cart_item_id', cartItemIds)

      if (!customizationError && customizationData) {
        customizations = customizationData
      }
    }

    // Combine cart items with their customizations
    const itemsWithCustomizations = cartItems?.map(item => {
      const itemCustomizations = customizations.filter(c => c.cart_item_id === item.id)
      return {
        ...item,
        customizations: itemCustomizations,
        special_instructions: order.delivery_instructions // Order-level instructions
      }
    }) || []

    // Calculate totals
    const totalItems = cartItems?.reduce((sum, item) => sum + item.quantity, 0) || 0

    return NextResponse.json({
      orderId: order.id,
      orderNumber: order.order_number,
      status: order.status,
      items: itemsWithCustomizations,
      summary: {
        totalItems,
        itemCount: itemsWithCustomizations.length
      }
    })

  } catch (error: any) {
    console.error('Error fetching driver order items:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
