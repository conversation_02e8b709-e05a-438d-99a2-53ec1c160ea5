import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

// Create a Supabase client with service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);

// GET endpoint to fetch trigger performance metrics
export async function GET(request: NextRequest) {
  try {
    // Verify user access (admin only)
    const accessCheck = await verifyUserAccess(request, { requiredRole: 'admin' })
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Use the supabase client we created

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const days = parseInt(searchParams.get('days') || '7')
    const limit = parseInt(searchParams.get('limit') || '100')

    // Fetch trigger performance metrics
    const { data, error } = await supabaseClient
      .from('trigger_performance_logs')
      .select('*')
      .order('execution_time_ms', { ascending: false })
      .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .limit(limit)

    if (error) {
      console.error('Error fetching trigger performance metrics:', error)
      return NextResponse.json(
        { error: 'Failed to fetch trigger performance metrics' },
        { status: 500 }
      )
    }

    // Calculate summary statistics
    const triggerStats: Record<string, {
      count: number,
      avg_time: number,
      max_time: number,
      min_time: number
    }> = {}

    data.forEach(log => {
      if (!triggerStats[log.trigger_name]) {
        triggerStats[log.trigger_name] = {
          count: 0,
          avg_time: 0,
          max_time: 0,
          min_time: Number.MAX_VALUE
        }
      }

      const stats = triggerStats[log.trigger_name]
      stats.count++
      stats.avg_time = (stats.avg_time * (stats.count - 1) + log.execution_time_ms) / stats.count
      stats.max_time = Math.max(stats.max_time, log.execution_time_ms)
      stats.min_time = Math.min(stats.min_time, log.execution_time_ms)
    })

    return NextResponse.json({
      metrics: data,
      summary: triggerStats,
      period: `Last ${days} days`
    })
  } catch (error: any) {
    console.error('Unexpected error in trigger performance endpoint:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message },
      { status: 500 }
    )
  }
}
