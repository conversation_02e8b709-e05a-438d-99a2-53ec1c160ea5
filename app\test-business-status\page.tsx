"use client"

import { useState } from "react"
import { BusinessStatusToggle } from "@/components/business/business-status-toggle"
import { BusinessClosedDialog } from "@/components/business/business-closed-dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestBusinessStatusPage() {
  const [showClosedDialog, setShowClosedDialog] = useState(false)
  const [closureMessage, setClosureMessage] = useState("Be back online by 8pm, we are very busy")

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Business Status Test Page</h1>
          <p className="text-muted-foreground mt-2">
            Test the business closure functionality before integrating into the dashboard.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Business Status Toggle Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This tests the business status toggle component. Business ID 4 (Jersey Grill) is used for testing.
            </p>
            
            <BusinessStatusToggle
              businessId={4}
              onStatusChange={(isOpen, message) => {
                console.log('Status changed:', { isOpen, message })
                setClosureMessage(message || "")
              }}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer-Facing Closed Dialog Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This tests the dialog that customers see when a business is closed.
            </p>
            
            <Button 
              onClick={() => setShowClosedDialog(true)}
              variant="outline"
            >
              Show Closed Business Dialog
            </Button>

            <BusinessClosedDialog
              isOpen={showClosedDialog}
              onClose={() => setShowClosedDialog(false)}
              businessName="Jersey Grill"
              closureMessage={closureMessage}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>API Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Test the business status API directly.
            </p>
            
            <div className="space-y-2">
              <Button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/business-admin/status?businessId=4')
                    const data = await response.json()
                    console.log('GET Status API Response:', data)

                    if (data.success) {
                      alert(`✅ GET API Success!\n\nBusiness: ${data.data.name}\nClosed: ${data.data.isTemporarilyClosed}\nMessage: "${data.data.closureMessage || 'none'}"`)
                    } else {
                      alert(`❌ GET API Error!\n\n${data.error}\n\nFull response:\n${JSON.stringify(data, null, 2)}`)
                    }
                  } catch (error) {
                    console.error('Error:', error)
                    alert('❌ Network Error: ' + error)
                  }
                }}
                variant="outline"
                size="sm"
              >
                Test GET Status API
              </Button>

              <Button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/business-admin/status', {
                      method: 'PATCH',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        businessId: 4,
                        isTemporarilyClosed: true,
                        closureMessage: "Test closure message from API test"
                      })
                    })
                    const data = await response.json()
                    console.log('PATCH Status API Response:', data)

                    if (data.success) {
                      alert(`✅ PATCH API Success!\n\nBusiness: ${data.data.name}\nClosed: ${data.data.isTemporarilyClosed}\nMessage: "${data.data.closureMessage || 'none'}"`)
                    } else {
                      alert(`❌ PATCH API Error!\n\n${data.error}\n\nFull response:\n${JSON.stringify(data, null, 2)}`)
                    }
                  } catch (error) {
                    console.error('Error:', error)
                    alert('❌ Network Error: ' + error)
                  }
                }}
                variant="outline"
                size="sm"
              >
                Test PATCH Status API (Close)
              </Button>

              <Button 
                onClick={async () => {
                  try {
                    const response = await fetch('/api/business-admin/status', {
                      method: 'PATCH',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        businessId: 4,
                        isTemporarilyClosed: false,
                        closureMessage: null
                      })
                    })
                    const data = await response.json()
                    console.log('PATCH Status API Response:', data)
                    alert(`Status: ${data.success ? 'Success' : 'Error'}\n${JSON.stringify(data, null, 2)}`)
                  } catch (error) {
                    console.error('Error:', error)
                    alert('Error: ' + error)
                  }
                }}
                variant="outline"
                size="sm"
              >
                Test PATCH Status API (Open)
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="text-sm text-muted-foreground">
          <p><strong>Note:</strong> This test page will be removed after testing is complete.</p>
          <p>Check the browser console and network tab for detailed API responses.</p>
        </div>
      </div>
    </div>
  )
}
