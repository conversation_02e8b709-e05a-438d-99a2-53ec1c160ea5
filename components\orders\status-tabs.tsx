"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

interface StatusTabsProps {
  activeTab: string
  onTabChange: (value: string) => void
}

export function StatusTabs({ activeTab, onTabChange }: StatusTabsProps) {
  return (
    <TabsList>
      <TabsTrigger value="all" onClick={() => onTabChange("all")}>
        All Orders
      </TabsTrigger>
      <TabsTrigger value="pending" onClick={() => onTabChange("pending")}>
        Pending
      </TabsTrigger>
      <TabsTrigger value="processing" onClick={() => onTabChange("processing")}>
        Processing
      </TabsTrigger>
      <TabsTrigger value="completed" onClick={() => onTabChange("completed")}>
        Completed
      </TabsTrigger>
      <TabsTrigger value="cancelled" onClick={() => onTabChange("cancelled")}>
        Cancelled
      </TabsTrigger>
    </TabsList>
  )
}
