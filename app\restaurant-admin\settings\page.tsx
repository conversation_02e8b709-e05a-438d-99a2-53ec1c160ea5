"use client"

import React, { useState, useEffect } from "react"
import {
  Save,
  MapPin,
  Info,
  Clock,
  Phone,
  Mail,
  AlertCircle,
  CheckCircle,
  Loader2,
  Image as ImageIcon
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import { geocodeAndUpdateBusinessCoordinates } from "@/lib/address-utils"
import { useToast } from "@/components/ui/use-toast"
import FileUpload from "@/components/ui/file-upload"

export default function RestaurantSettings() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [geocoding, setGeocoding] = useState(false)
  const [restaurant, setRestaurant] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: "",
    postcode: "",
    location: "",
    phone: "",
    email: "",
    delivery_radius: 5,
    preparation_time_minutes: 15,
    minimum_order_amount: 15.00,
    delivery_fee: 2.50,
    coordinates: "",
    logo_url: "",
    banner_url: ""
  })

  // Fetch restaurant data
  useEffect(() => {
    async function fetchRestaurantData() {
      try {
        // In a real app, you would get the restaurant ID from the authenticated user
        // For now, we'll use a hardcoded ID (jersey-grill)
        const restaurantSlug = "jersey-grill"

        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .eq('slug', restaurantSlug)
          .single()

        if (error) {
          console.error("Error fetching restaurant data:", error)
          return
        }

        if (data) {
          setRestaurant(data)

          // Format coordinates for display
          let coordinatesStr = ""
          if (data.coordinates) {
            const coordsMatch = data.coordinates.match(/\(([^,]+),([^)]+)\)/)
            if (coordsMatch && coordsMatch.length === 3) {
              const lng = parseFloat(coordsMatch[1])
              const lat = parseFloat(coordsMatch[2])
              coordinatesStr = `${lat}, ${lng}`
            }
          }

          setFormData({
            name: data.name || "",
            description: data.description || "",
            address: data.address || "",
            postcode: data.postcode || "",
            location: data.location || "",
            phone: data.phone || "",
            email: data.email || "",
            delivery_radius: data.delivery_radius || 5,
            preparation_time_minutes: data.preparation_time_minutes || 15,
            minimum_order_amount: data.minimum_order_amount || 15.00,
            delivery_fee: data.delivery_fee || 2.50,
            coordinates: coordinatesStr,
            logo_url: data.logo_url || "",
            banner_url: data.banner_url || ""
          })
        }
      } catch (error) {
        console.error("Error in fetchRestaurantData:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchRestaurantData()
  }, [])

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setFormData(prev => ({ ...prev, [name]: numValue }))
    }
  }

  // Save restaurant settings
  const handleSave = async () => {
    if (!restaurant) {
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: "Restaurant data not found. Please refresh the page and try again."
      })
      return
    }

    setSaving(true)
    try {
      // Log the data being sent for debugging
      console.log("Updating restaurant with ID:", restaurant.id)
      console.log("Update data:", {
        name: formData.name,
        description: formData.description,
        address: formData.address,
        postcode: formData.postcode,
        location: formData.location,
        phone: formData.phone,
        email: formData.email,
        delivery_radius: formData.delivery_radius,
        preparation_time_minutes: formData.preparation_time_minutes,
        minimum_order_amount: formData.minimum_order_amount,
        delivery_fee: formData.delivery_fee,
        logo_url: formData.logo_url,
        banner_url: formData.banner_url
      })

      // Perform the update
      const { data, error, status } = await supabase
        .from('businesses')
        .update({
          name: formData.name,
          description: formData.description,
          address: formData.address,
          postcode: formData.postcode,
          location: formData.location,
          phone: formData.phone,

          delivery_radius: formData.delivery_radius,
          preparation_time_minutes: formData.preparation_time_minutes,
          minimum_order_amount: formData.minimum_order_amount,
          delivery_fee: formData.delivery_fee,
          logo_url: formData.logo_url,
          banner_url: formData.banner_url
        })
        .eq('id', restaurant.id)
        .select()

      // Log the response for debugging
      console.log("Supabase update response:", { data, error, status })

      if (error) {
        console.error("Error updating restaurant:", error)
        toast({
          variant: "destructive",
          title: "Error saving settings",
          description: error.message || "An unknown error occurred while saving settings"
        })
      } else {
        toast({
          title: "Settings saved",
          description: "Your restaurant settings have been updated successfully."
        })
      }
    } catch (error: any) {
      console.error("Error in handleSave:", error)
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: error.message || "An unexpected error occurred"
      })
    } finally {
      setSaving(false)
    }
  }

  // Update coordinates based on address and postcode
  const handleUpdateCoordinates = async () => {
    if (!restaurant) {
      toast({
        variant: "destructive",
        title: "Error updating coordinates",
        description: "Restaurant data not found. Please refresh the page and try again."
      })
      return
    }

    if (!formData.address) {
      toast({
        variant: "destructive",
        title: "Address required",
        description: "Please enter a valid address before updating coordinates."
      })
      return
    }

    if (!formData.postcode) {
      toast({
        variant: "destructive",
        title: "Postcode required",
        description: "Please enter a valid Jersey postcode (e.g., JE2 4UE) for accurate geocoding."
      })
      return
    }

    setGeocoding(true)
    try {
      console.log("Geocoding address for restaurant ID:", restaurant.id)
      console.log("Address to geocode:", formData.address)
      console.log("Postcode:", formData.postcode)

      // Combine address and postcode for geocoding
      const fullAddress = `${formData.address}, ${formData.postcode}`

      const success = await geocodeAndUpdateBusinessCoordinates(
        restaurant.id,
        fullAddress,
        formData.postcode
      )

      console.log("Geocoding result:", success)

      if (success) {
        toast({
          title: "Coordinates updated",
          description: "Your restaurant coordinates have been updated based on the address."
        })

        // Refresh the page to show updated coordinates
        window.location.reload()
      } else {
        toast({
          variant: "destructive",
          title: "Error updating coordinates",
          description: "Could not geocode the address. Please check the address and try again."
        })
      }
    } catch (error: any) {
      console.error("Error in handleUpdateCoordinates:", error)
      toast({
        variant: "destructive",
        title: "Error updating coordinates",
        description: error.message || "An unexpected error occurred while updating coordinates"
      })
    } finally {
      setGeocoding(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Restaurant Settings</h1>
          <p className="text-gray-500">Manage your restaurant profile and settings</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button
            className="bg-emerald-600 hover:bg-emerald-700"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="location">Location & Delivery</TabsTrigger>
          <TabsTrigger value="brand-assets">Brand Assets</TabsTrigger>
          <TabsTrigger value="business-hours">Business Hours</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Information</CardTitle>
              <CardDescription>
                Update your restaurant's basic information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Restaurant Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Restaurant Name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Phone Number"
                  />
                </div>
              </div>



              <div className="space-y-2">
                <Label htmlFor="description">Restaurant Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Describe your restaurant"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="location">
          <Card>
            <CardHeader>
              <CardTitle>Location & Delivery Settings</CardTitle>
              <CardDescription>
                Manage your restaurant's address, delivery area, and preparation time
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Address Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Street address (e.g. 1 Castle St, St Helier)"
                    rows={2}
                  />
                  <p className="text-sm text-gray-500">
                    Enter your street address without the postcode
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="postcode">Postcode</Label>
                    <Input
                      id="postcode"
                      name="postcode"
                      value={formData.postcode}
                      onChange={handleChange}
                      placeholder="e.g. JE2 4UE"
                    />
                    <p className="text-sm text-gray-500">
                      Jersey postcode (required for accurate mapping)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location/Area</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleChange}
                      placeholder="e.g. St Helier"
                    />
                    <p className="text-sm text-gray-500">
                      General area or neighborhood (e.g. St Helier, St Brelade)
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="coordinates">Coordinates</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleUpdateCoordinates}
                      disabled={geocoding || !formData.address}
                    >
                      {geocoding ? (
                        <>
                          <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <MapPin className="mr-2 h-3 w-3" />
                          Update from Address
                        </>
                      )}
                    </Button>
                  </div>
                  <Input
                    id="coordinates"
                    name="coordinates"
                    value={formData.coordinates}
                    readOnly
                    className="bg-gray-50"
                  />
                  <p className="text-sm text-gray-500">
                    Latitude, Longitude - Updated automatically from your address
                  </p>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>About Coordinates</AlertTitle>
                  <AlertDescription>
                    <p className="mb-1">
                      Coordinates are used to calculate delivery distances and display your restaurant on the map.
                    </p>
                    <p className="mb-1">
                      <strong>Important:</strong> Both street address and postcode are required for accurate geocoding in Jersey.
                    </p>
                    <p>
                      Click "Update from Address" to automatically generate coordinates using your address and postcode.
                    </p>
                  </AlertDescription>
                </Alert>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Delivery Settings</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="delivery_radius">Delivery Radius (km)</Label>
                    <Input
                      id="delivery_radius"
                      name="delivery_radius"
                      type="number"
                      min="0.5"
                      step="0.5"
                      value={formData.delivery_radius}
                      onChange={handleNumberChange}
                    />
                    <p className="text-sm text-gray-500">
                      Maximum distance you'll deliver from your location
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preparation_time_minutes">Preparation Time (minutes)</Label>
                    <Input
                      id="preparation_time_minutes"
                      name="preparation_time_minutes"
                      type="number"
                      min="5"
                      step="5"
                      value={formData.preparation_time_minutes}
                      onChange={handleNumberChange}
                    />
                    <p className="text-sm text-gray-500">
                      Average time to prepare orders before delivery
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="delivery_fee">Delivery Fee (£)</Label>
                    <Input
                      id="delivery_fee"
                      name="delivery_fee"
                      type="number"
                      min="0"
                      step="0.50"
                      value={formData.delivery_fee}
                      onChange={handleNumberChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="minimum_order_amount">Minimum Order Amount (£)</Label>
                    <Input
                      id="minimum_order_amount"
                      name="minimum_order_amount"
                      type="number"
                      min="0"
                      step="0.50"
                      value={formData.minimum_order_amount}
                      onChange={handleNumberChange}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button
                className="bg-emerald-600 hover:bg-emerald-700"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="brand-assets">
          <Card>
            <CardHeader>
              <CardTitle>Brand Assets</CardTitle>
              <CardDescription>
                Upload and manage your restaurant's logo and banner images
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Logo</h3>
                <p className="text-sm text-gray-500">
                  Your logo will be displayed in search results and throughout the platform.
                  A square image with transparent background is recommended.
                </p>

                {restaurant && (
                  <FileUpload
                    type="logo"
                    businessId={restaurant.slug}
                    currentImageUrl={restaurant.logo_url}
                    onUploadComplete={(url) => {
                      setFormData(prev => ({ ...prev, logo_url: url }))
                    }}
                  />
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Banner Image</h3>
                <p className="text-sm text-gray-500">
                  Your banner image will be displayed at the top of your restaurant page.
                  Recommended size is 1200×400 pixels.
                </p>

                {restaurant && (
                  <FileUpload
                    type="banner"
                    businessId={restaurant.slug}
                    currentImageUrl={restaurant.banner_url}
                    onUploadComplete={(url) => {
                      setFormData(prev => ({ ...prev, banner_url: url }))
                    }}
                  />
                )}
              </div>

              <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Images will be saved when you click the "Save Changes" button at the top of the page.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business-hours">
          <Card>
            <CardHeader>
              <CardTitle>Business Hours</CardTitle>
              <CardDescription>
                Set your restaurant's opening and closing times
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">
                Business hours functionality will be implemented in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive order and customer notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">
                Notification settings will be implemented in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
