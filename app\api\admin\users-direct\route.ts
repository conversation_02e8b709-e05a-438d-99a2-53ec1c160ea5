import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting admin users-direct API request")

    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      console.log("No authorization header found, using direct admin access")
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check")
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        )
      }
    } else {
      console.log("Found authorization header, attempting to verify")

      // Extract the token
      const token = authorization.replace('Bearer ', '');

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          console.error("Invalid token:", error)
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        console.log("Token verified for user:", user.email)

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          console.error("Error fetching user profile:", profileError)
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          console.error("Unauthorized access attempt by:", user.email, "with role:", userProfile.role)
          return NextResponse.json(
            { error: "You do not have permission to access this resource" },
            { status: 403 }
          )
        }

        console.log("Admin access verified for user:", user.email, "with role:", userProfile.role)
      } catch (authError) {
        console.error("Error verifying token:", authError)
        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          )
        } else {
          console.log("Development mode: Continuing despite auth error")
        }
      }
    }

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch users with the service role key and join with business information
    console.log("Fetching users with service role key and business information")

    // First get all users
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("*")
      .order("created_at", { ascending: false })

    if (userError) {
      console.error("Error fetching users:", userError)
      return NextResponse.json(
        { error: userError.message },
        { status: 500 }
      )
    }

    // Then get business manager relationships
    const { data: managerData, error: managerError } = await supabase
      .from("business_managers")
      .select("user_id, business_id, businesses(name)")

    if (managerError) {
      console.log("Error fetching business managers, continuing with users only:", managerError)
    }

    // Create a map of user_id to business name
    const userBusinessMap = new Map()

    if (managerData && managerData.length > 0) {
      managerData.forEach(manager => {
        if (manager.user_id && manager.businesses) {
          userBusinessMap.set(manager.user_id, manager.businesses.name)
        }
      })
    }

    // Combine the data
    const data = userData.map(user => ({
      ...user,
      business_name: userBusinessMap.get(user.id) || null
    }))

    console.log("Users query result:", { count: data?.length || 0, userError, managerError })

    // Error handling already done above for userError

    return NextResponse.json({ users: data })
  } catch (error: any) {
    console.error("Unexpected error in users API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
