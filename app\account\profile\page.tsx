"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import UserProfile from "@/components/auth/user-profile"
import LogoutButton from "@/components/auth/logout-button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Building2, ShieldCheck, Store, Truck, Edit } from "lucide-react"
import Link from "next/link"

export default function ProfilePage() {
  const { user, userProfile, isLoading } = useAuth()
  const router = useRouter()
  const [userRole, setUserRole] = useState<string | null>(null)

  // Simple auth check - redirect if not authenticated after loading completes
  useEffect(() => {
    if (!isLoading && !user) {
      console.log("No user found after loading complete, redirecting to login")
      router.push("/login")
    }
  }, [isLoading, user, router])

  // Set user role from userProfile when available
  useEffect(() => {
    if (userProfile?.role) {
      console.log("Setting user role from profile:", userProfile.role)
      setUserRole(userProfile.role)
    }
  }, [userProfile])

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="bg-white rounded-xl shadow-sm p-8 flex flex-col items-center">
          <div className="h-12 w-12 border-2 border-emerald-600 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-[#2e3333] font-medium">Loading your profile...</p>
        </div>
      </div>
    )
  }

  // If no user after loading, show redirecting message
  if (!user) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 flex flex-col items-center">
        <div className="h-12 w-12 border-2 border-emerald-600 border-t-transparent rounded-full animate-spin mb-4"></div>
        <p className="text-[#2e3333] font-medium">Redirecting to login...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Profile</h1>
          <p className="text-gray-600">
            Manage your personal information and account settings
          </p>
        </div>

        <LogoutButton variant="outline" className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50" />
      </div>

      {/* Profile hero section */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
          <div className="relative">
            <div className="h-24 w-24 rounded-full bg-emerald-600 flex items-center justify-center text-white text-2xl font-medium">
              {user?.email?.substring(0, 1).toUpperCase() || 'U'}
            </div>
            <div className="absolute -bottom-1 -right-1 bg-white p-1 rounded-full shadow-sm">
              <div className="bg-emerald-600 text-white p-1 rounded-full">
                <Edit className="h-3 w-3" />
              </div>
            </div>
          </div>
          <div className="flex-1 text-center md:text-left">
            <h2 className="text-2xl font-bold text-[#2e3333] mb-1">
              {user?.email?.split('@')[0]?.charAt(0).toUpperCase() + user?.email?.split('@')[0]?.slice(1) || 'User'}
            </h2>
            <p className="text-[#585c5c] mb-3">{user?.email}</p>
            <div className="flex flex-wrap justify-center md:justify-start gap-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-600">
                {userRole?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Customer'}
              </span>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Member since {new Date().getFullYear()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <UserProfile />

      {/* Role-based navigation section */}
      {userRole && (
        <Card className="border-none shadow-sm overflow-hidden">
          <CardHeader className="bg-white border-b border-gray-100 pb-4">
            <CardTitle className="text-xl text-[#2e3333]">Your Access</CardTitle>
            <CardDescription className="text-[#585c5c]">
              Based on your account type, you have access to the following areas
            </CardDescription>
          </CardHeader>
          <CardContent className="bg-white pt-6">
            <div className="grid gap-4">
              {/* Business Manager or Staff */}
              {(userRole === 'business_manager' || userRole === 'business_staff') && (
                <Link
                  href="/business-admin/dashboard"
                  className="flex items-center p-4 rounded-lg border border-gray-100 hover:border-emerald-600 hover:shadow-md transition-all duration-200"
                >
                  <div className="h-12 w-12 rounded-lg bg-emerald-50 flex items-center justify-center mr-4">
                    <Store className="h-6 w-6 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-[#2e3333]">Business Admin</h3>
                    <p className="text-sm text-[#585c5c]">
                      Manage your business settings, products, and orders
                    </p>
                  </div>
                  <div className="ml-2 text-emerald-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </div>
                </Link>
              )}

              {/* Driver */}
              {userRole === 'driver' && (
                <>
                  <Link
                    href="/driver/dashboard"
                    className="flex items-center p-4 rounded-lg border border-gray-100 hover:border-emerald-600 hover:shadow-md transition-all duration-200"
                  >
                    <div className="h-12 w-12 rounded-lg bg-emerald-50 flex items-center justify-center mr-4">
                      <Truck className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-[#2e3333]">Driver Dashboard</h3>
                      <p className="text-sm text-[#585c5c]">
                        View your delivery schedule and manage your deliveries
                      </p>
                    </div>
                    <div className="ml-2 text-emerald-600">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                        <path d="m9 18 6-6-6-6"/>
                      </svg>
                    </div>
                  </Link>

                  <Link
                    href="/driver-mobile/dashboard"
                    className="flex items-center p-4 rounded-lg border border-gray-100 hover:border-emerald-600 hover:shadow-md transition-all duration-200"
                  >
                    <div className="h-12 w-12 rounded-lg bg-emerald-50 flex items-center justify-center mr-4">
                      <Truck className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-[#2e3333]">Driver Mobile App</h3>
                      <p className="text-sm text-[#585c5c]">
                        Mobile-optimized interface for on-the-go deliveries
                      </p>
                    </div>
                    <div className="ml-2 text-emerald-600">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                        <path d="m9 18 6-6-6-6"/>
                      </svg>
                    </div>
                  </Link>
                </>
              )}

              {/* Admin */}
              {(userRole === 'admin' || userRole === 'super_admin') && (
                <Link
                  href="/admin"
                  className="flex items-center p-4 rounded-lg border border-gray-100 hover:border-emerald-600 hover:shadow-md transition-all duration-200"
                >
                  <div className="h-12 w-12 rounded-lg bg-emerald-50 flex items-center justify-center mr-4">
                    <Building2 className="h-6 w-6 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-[#2e3333]">Admin Dashboard</h3>
                    <p className="text-sm text-[#585c5c]">
                      Manage businesses, users, and platform settings
                    </p>
                  </div>
                  <div className="ml-2 text-emerald-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </div>
                </Link>
              )}

              {/* Super Admin */}
              {userRole === 'super_admin' && (
                <Link
                  href="/super-admin"
                  className="flex items-center p-4 rounded-lg border border-gray-100 hover:border-emerald-600 hover:shadow-md transition-all duration-200"
                >
                  <div className="h-12 w-12 rounded-lg bg-emerald-50 flex items-center justify-center mr-4">
                    <ShieldCheck className="h-6 w-6 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-[#2e3333]">Super Admin</h3>
                    <p className="text-sm text-[#585c5c]">
                      Advanced platform management and system settings
                    </p>
                  </div>
                  <div className="ml-2 text-emerald-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </div>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
