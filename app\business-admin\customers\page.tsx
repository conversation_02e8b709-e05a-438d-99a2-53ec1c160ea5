"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { useAuth } from "@/context/unified-auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Calendar,
  ChevronDown,
  Download,
  Edit,
  ExternalLink,
  Filter,
  HelpCircle,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  ShoppingBag,
  SlidersHorizontal,
  UserCog,
  Users,
} from "lucide-react"

// Define types for our data
interface Customer {
  id: string // User ID for authenticated customers, or formatted ID for guests
  name: string
  email: string
  phone: string
  address?: string
  avatar?: string
  customerSince: string
  totalOrders: number
  totalSpent: number
  lastOrder: string
  status: "active" | "inactive" | "new" | "at-risk"
  favoriteItems?: string[]
  notes?: string
  _internalId?: string // Internal identifier for grouping
  _isAuthenticated?: boolean // Whether this is an authenticated customer
  _userId?: number | null // Real user ID from users table
  _authId?: string | null // Auth ID from auth.users
}

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export default function BusinessAdminCustomersNew() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // Customers state
  const [customers, setCustomers] = useState<Customer[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recent")
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    activeCustomers: true,
    inactiveCustomers: true,
    newCustomers: true,
    oneOrMoreOrders: true,
    fiveOrMoreOrders: false,
    tenOrMoreOrders: false
  })

  // Customer stats
  const [customerStats, setCustomerStats] = useState({
    total: 0,
    active: 0,
    averageOrderValue: 0,
    retentionRate: 0
  })

  // Help dialog state
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)

  // Check if user is admin or super admin
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setIsAdminUser(true)
    }
  }, [isAdmin, isSuperAdmin])

  // Define fetchBusinessData outside of useEffect so it can be called from multiple places
  const fetchBusinessData = async () => {
    try {
      console.log("Fetching business data...")
      setIsLoading(true)

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/business-data'

      // If admin user and a business is selected, add the business ID as a query parameter
      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/business-data?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching data for business ID: ${selectedBusinessId}`)
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/customers-new")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Business data received:", data)

      if (data.business) {
        setBusiness({
          id: data.business.id,
          name: data.business.name,
          business_type_id: data.business.business_type_id,
          business_type: data.business.business_type
        })

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        setError("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // For admin users, fetch available businesses
  const fetchAvailableBusinesses = async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        setAvailableBusinesses(data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        })))
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }

  // Handle business selection change for admin users
  const handleBusinessChange = (businessId: number) => {
    console.log("Selected business changed to:", businessId)
    setSelectedBusinessId(businessId)

    // Refetch data with the new business ID
    fetchBusinessData()
    fetchCustomers()
  }

  // Fetch customers
  const fetchCustomers = async () => {
    try {
      console.log("🔄 Starting fetchCustomers...")
      setError(null)

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/customers'

      // If admin user and a business is selected, add the business ID as a query parameter
      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/customers?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching customers for business ID: ${selectedBusinessId}`)
      }

      console.log("Fetching customers from:", url)

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Customers API response:", data)

      if (data.success && Array.isArray(data.customers)) {
        // Convert API response to match our Customer interface
        const apiCustomers: Customer[] = data.customers.map((customer: any) => ({
          id: customer.id,
          name: customer.name,
          email: customer.email || '',
          phone: customer.phone,
          address: customer.address,
          avatar: customer.avatar || "/placeholder.svg",
          customerSince: customer.customerSince,
          totalOrders: customer.totalOrders,
          totalSpent: customer.totalSpent,
          lastOrder: customer.lastOrder,
          status: customer.status,
          favoriteItems: customer.favoriteItems || [],
          notes: customer.notes || ""
        }))

        setCustomers(apiCustomers)

        // Calculate customer stats from real data
        const activeCustomers = apiCustomers.filter(c => c.status === "active").length
        const totalSpent = apiCustomers.reduce((sum, c) => sum + c.totalSpent, 0)
        const totalOrders = apiCustomers.reduce((sum, c) => sum + c.totalOrders, 0)
        const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

        // Calculate retention rate: percentage of customers who have made repeat orders (2+ orders)
        const repeatCustomers = apiCustomers.filter(c => c.totalOrders >= 2).length
        const retentionRate = apiCustomers.length > 0 ? Math.round((repeatCustomers / apiCustomers.length) * 100) : 0

        setCustomerStats({
          total: apiCustomers.length,
          active: activeCustomers,
          averageOrderValue: averageOrderValue,
          retentionRate: retentionRate
        })

        // Apply initial filtering
        applyFilters(apiCustomers, searchQuery, statusFilter, sortBy)

        // Log summary for debugging
        if (data.summary) {
          console.log("Customer summary:", data.summary)
        }
      } else {
        console.warn("No customers found or invalid response format")
        setCustomers([])
        setCustomerStats({
          total: 0,
          active: 0,
          averageOrderValue: 0,
          retentionRate: 0
        })
        applyFilters([], searchQuery, statusFilter, sortBy)
      }
    } catch (err) {
      console.error("Error fetching customers:", err)
      setError(err instanceof Error ? err.message : "Failed to load customers")
      setCustomers([])
      setCustomerStats({
        total: 0,
        active: 0,
        averageOrderValue: 0,
        retentionRate: 0
      })
      applyFilters([], searchQuery, statusFilter, sortBy)
    } finally {
      console.log("🔄 fetchCustomers completed")
    }
  }

  // Apply filters to customers
  const applyFilters = (customers: Customer[], search: string, status: string, sort: string) => {
    let filtered = [...customers]

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filtered = filtered.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchLower) ||
          customer.email.toLowerCase().includes(searchLower) ||
          customer.phone.includes(searchLower) ||
          (customer.address && customer.address.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter based on tab selection
    if (status !== "all") {
      filtered = filtered.filter((customer) => customer.status === status)
    }

    // Apply advanced filter options
    if (!filterOptions.activeCustomers && !filterOptions.inactiveCustomers && !filterOptions.newCustomers) {
      // If no status filters are selected, show nothing
      filtered = []
    } else {
      // Apply status filters
      const allowedStatuses = []
      if (filterOptions.activeCustomers) allowedStatuses.push("active")
      if (filterOptions.inactiveCustomers) allowedStatuses.push("inactive")
      if (filterOptions.newCustomers) allowedStatuses.push("new")

      if (status === "all") {
        filtered = filtered.filter((customer) => allowedStatuses.includes(customer.status))
      }
    }

    // Apply order count filters
    if (filterOptions.tenOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 10)
    } else if (filterOptions.fiveOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 5)
    } else if (filterOptions.oneOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 1)
    }

    // Apply sorting
    if (sort === "name-asc") {
      filtered.sort((a, b) => a.name.localeCompare(b.name))
    } else if (sort === "name-desc") {
      filtered.sort((a, b) => b.name.localeCompare(a.name))
    } else if (sort === "recent") {
      filtered.sort((a, b) => new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime())
    } else if (sort === "oldest") {
      filtered.sort((a, b) => new Date(a.lastOrder).getTime() - new Date(b.lastOrder).getTime())
    } else if (sort === "spent-high") {
      filtered.sort((a, b) => b.totalSpent - a.totalSpent)
    } else if (sort === "spent-low") {
      filtered.sort((a, b) => a.totalSpent - b.totalSpent)
    } else if (sort === "orders-high") {
      filtered.sort((a, b) => b.totalOrders - a.totalOrders)
    } else if (sort === "orders-low") {
      filtered.sort((a, b) => a.totalOrders - b.totalOrders)
    }

    setFilteredCustomers(filtered)
  }

  // Handle filter option changes
  const handleFilterChange = (filterKey: string, checked: boolean) => {
    const newFilterOptions = { ...filterOptions, [filterKey]: checked }
    setFilterOptions(newFilterOptions)
    // Use setTimeout to ensure state is updated before applying filters
    setTimeout(() => {
      applyFiltersWithOptions(customers, searchQuery, statusFilter, sortBy, newFilterOptions)
    }, 0)
  }

  // Apply filters with specific filter options (used when filter options change)
  const applyFiltersWithOptions = (customers: Customer[], search: string, status: string, sort: string, options: typeof filterOptions) => {
    let filtered = [...customers]

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filtered = filtered.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchLower) ||
          customer.email.toLowerCase().includes(searchLower) ||
          customer.phone.includes(searchLower) ||
          (customer.address && customer.address.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter based on tab selection
    if (status !== "all") {
      filtered = filtered.filter((customer) => customer.status === status)
    }

    // Apply advanced filter options
    if (!options.activeCustomers && !options.inactiveCustomers && !options.newCustomers) {
      // If no status filters are selected, show nothing
      filtered = []
    } else {
      // Apply status filters
      const allowedStatuses = []
      if (options.activeCustomers) allowedStatuses.push("active")
      if (options.inactiveCustomers) allowedStatuses.push("inactive")
      if (options.newCustomers) allowedStatuses.push("new")

      if (status === "all") {
        filtered = filtered.filter((customer) => allowedStatuses.includes(customer.status))
      }
    }

    // Apply order count filters
    if (options.tenOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 10)
    } else if (options.fiveOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 5)
    } else if (options.oneOrMoreOrders) {
      filtered = filtered.filter((customer) => customer.totalOrders >= 1)
    }

    // Apply sorting
    if (sort === "name-asc") {
      filtered.sort((a, b) => a.name.localeCompare(b.name))
    } else if (sort === "name-desc") {
      filtered.sort((a, b) => b.name.localeCompare(a.name))
    } else if (sort === "recent") {
      filtered.sort((a, b) => new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime())
    } else if (sort === "oldest") {
      filtered.sort((a, b) => new Date(a.lastOrder).getTime() - new Date(b.lastOrder).getTime())
    } else if (sort === "spent-high") {
      filtered.sort((a, b) => b.totalSpent - a.totalSpent)
    } else if (sort === "spent-low") {
      filtered.sort((a, b) => a.totalSpent - b.totalSpent)
    } else if (sort === "orders-high") {
      filtered.sort((a, b) => b.totalOrders - a.totalOrders)
    } else if (sort === "orders-low") {
      filtered.sort((a, b) => a.totalOrders - b.totalOrders)
    }

    setFilteredCustomers(filtered)
  }

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setStatusFilter(value)
    applyFilters(customers, searchQuery, value, sortBy)
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    applyFilters(customers, query, statusFilter, sortBy)
  }

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSortBy(value)
    applyFilters(customers, searchQuery, statusFilter, value)
  }

  // Initial data loading
  useEffect(() => {
    if (user) {
      fetchBusinessData()

      if (isAdminUser) {
        fetchAvailableBusinesses()
      }
    }
  }, [user, isAdminUser])

  // Fetch customers when business data is loaded
  useEffect(() => {
    console.log("Business data useEffect triggered:", {
      user: !!user,
      business: !!business,
      isLoading,
      isPendingApproval
    })
    if (user && business && !isLoading && !isPendingApproval) {
      console.log("Business data loaded, fetching customers...")
      fetchCustomers()
    }
  }, [business, isLoading, isPendingApproval])

  // Fetch customers when business selection changes (for admin users)
  useEffect(() => {
    console.log("Business selection useEffect triggered:", { user: !!user, selectedBusinessId, isAdminUser })
    if (user && isAdminUser && selectedBusinessId && !isLoading) {
      console.log("Admin user business selection changed, fetching customers...")
      fetchCustomers()
    }
  }, [selectedBusinessId])

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, 'dd MMM yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Format time helper - displays time in Jersey timezone (BST/GMT)
  const formatTime = (dateString: string) => {
    try {
      const utcDate = new Date(dateString)
      return new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZone: 'Europe/London'
      }).format(utcDate)
    } catch (error) {
      return ""
    }
  }

  // Get status badge helper
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge
            variant="outline"
            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
          >
            Active
          </Badge>
        )
      case "inactive":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100 hover:text-yellow-800"
          >
            Inactive
          </Badge>
        )
      case "new":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800"
          >
            New
          </Badge>
        )
      case "at-risk":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800"
          >
            At Risk
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        )
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading customers...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Customers</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => fetchCustomers()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Customers</h1>
            <p className="text-muted-foreground">
              Manage your customer relationships and data
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-8">
              <Download className="mr-2 h-3.5 w-3.5" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <Mail className="mr-2 h-3.5 w-3.5" />
              Email All
            </Button>
            <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
              <Plus className="mr-2 h-3.5 w-3.5" />
              Add Customer
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsHelpDialogOpen(true)}
              className="h-8 flex items-center gap-2"
            >
              <HelpCircle className="h-3.5 w-3.5" />
              Help
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Total Customers</CardTitle>
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Users className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{customerStats.total}</div>
              <p className="text-xs text-emerald-600 font-medium">
                {customerStats.total > 0 ? `+${Math.floor(customerStats.total * 0.05)} new this month` : "No customers yet"}
              </p>
            </CardContent>
          </Card>
          <Card className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-blue-50/50 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <CardTitle className="text-sm font-medium text-gray-700">Active Customers</CardTitle>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help hover:text-blue-500 transition-colors" />
                  </TooltipTrigger>
                  <TooltipContent className="shadow-lg border-0 bg-gray-900 text-white">
                    <p className="w-[200px] text-xs">Customers who have placed an order in the last 30 days</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-blue-600"
                >
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{customerStats.active}</div>
              <p className="text-xs text-blue-600 font-medium">
                {customerStats.total > 0 ? `${Math.round((customerStats.active / customerStats.total) * 100)}% of total customers` : "No active customers"}
              </p>
            </CardContent>
          </Card>
          <Card className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-purple-50/50 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Average Order Value</CardTitle>
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-purple-600"
                >
                  <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                </svg>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{formatCurrency(customerStats.averageOrderValue)}</div>
              <p className="text-xs text-purple-600 font-medium">+£2.50 from last month</p>
            </CardContent>
          </Card>
          <Card className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-orange-50/50 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <CardTitle className="text-sm font-medium text-gray-700">Retention Rate</CardTitle>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help hover:text-orange-500 transition-colors" />
                  </TooltipTrigger>
                  <TooltipContent className="shadow-lg border-0 bg-gray-900 text-white">
                    <p className="w-[250px] text-xs">Percentage of customers who have made repeat orders (2 or more orders). This indicates customer loyalty and satisfaction with your business.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="p-2 bg-orange-100 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-orange-600"
                >
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  <circle cx="9" cy="7" r="1" />
                </svg>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{customerStats.retentionRate}%</div>
              <p className="text-xs text-orange-600 font-medium">
                {customerStats.total > 0
                  ? `${Math.floor(customerStats.retentionRate * customerStats.total / 100)} repeat customers`
                  : "No repeat customers yet"
                }
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="all" className="space-y-6 mt-8" onValueChange={handleTabChange}>
          <div className="flex items-center justify-between">
            <TabsList className="bg-gray-100 p-1 rounded-lg shadow-sm">
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-gray-900 data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
              >
                All Customers
              </TabsTrigger>
              <TabsTrigger
                value="active"
                className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
              >
                Active
              </TabsTrigger>
              <TabsTrigger
                value="inactive"
                className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
              >
                Inactive
              </TabsTrigger>
              <TabsTrigger
                value="new"
                className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
              >
                New
              </TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-3">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300">
                    <Filter className="mr-2 h-3.5 w-3.5" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px] shadow-lg border-0 bg-white">
                  <DropdownMenuLabel className="text-gray-700 font-semibold">Filter by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.activeCustomers}
                    onCheckedChange={(checked) => handleFilterChange('activeCustomers', checked)}
                    className="hover:bg-gray-50"
                  >
                    Active Customers
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.inactiveCustomers}
                    onCheckedChange={(checked) => handleFilterChange('inactiveCustomers', checked)}
                    className="hover:bg-gray-50"
                  >
                    Inactive Customers
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.newCustomers}
                    onCheckedChange={(checked) => handleFilterChange('newCustomers', checked)}
                    className="hover:bg-gray-50"
                  >
                    New Customers
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel className="text-gray-700 font-semibold">Order Count</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.oneOrMoreOrders}
                    onCheckedChange={(checked) => handleFilterChange('oneOrMoreOrders', checked)}
                    className="hover:bg-gray-50"
                  >
                    1+ Orders
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.fiveOrMoreOrders}
                    onCheckedChange={(checked) => handleFilterChange('fiveOrMoreOrders', checked)}
                    className="hover:bg-gray-50"
                  >
                    5+ Orders
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.tenOrMoreOrders}
                    onCheckedChange={(checked) => handleFilterChange('tenOrMoreOrders', checked)}
                    className="hover:bg-gray-50"
                  >
                    10+ Orders
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300">
                    <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px] shadow-lg border-0 bg-white">
                  <DropdownMenuLabel className="text-gray-700 font-semibold">Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleSortChange("name-asc")} className="hover:bg-gray-50">Name (A-Z)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("name-desc")} className="hover:bg-gray-50">Name (Z-A)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("recent")} className="hover:bg-gray-50">Last Order (Recent)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("oldest")} className="hover:bg-gray-50">Last Order (Oldest)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("spent-high")} className="hover:bg-gray-50">Total Spent (High to Low)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("spent-low")} className="hover:bg-gray-50">Total Spent (Low to High)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("orders-high")} className="hover:bg-gray-50">Order Count (High to Low)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("orders-low")} className="hover:bg-gray-50">Order Count (Low to High)</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search customers..."
                  className="w-64 rounded-lg bg-white pl-10 h-9 shadow-sm border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>

          <TabsContent value="all" className="space-y-4">
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-semibold text-gray-900">Customer Directory</CardTitle>
                  <CardDescription className="text-sm text-gray-600 font-medium">
                    Showing {filteredCustomers.length} of {customerStats.total} customers
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="relative overflow-x-auto">
                  <TooltipProvider>
                    <Table>
                    <TableHeader className="bg-gray-50">
                      <TableRow className="border-b border-gray-200 hover:bg-gray-100 transition-colors">
                        <TableHead className="w-[40px]">
                          <Checkbox />
                        </TableHead>
                        <TableHead className="min-w-[50px]">
                          <div className="flex items-center gap-1">
                            ID
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Unique identifier for the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[150px]">
                          <div className="flex items-center gap-1">
                            Name
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's full name</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[180px]">
                          <div className="flex items-center gap-1">
                            Email
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's email address</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[120px]">
                          <div className="flex items-center gap-1">
                            Phone
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's phone number</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Status
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Current status of the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Orders
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Total number of orders placed</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Total Spent
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Total amount spent by the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[150px]">
                          <div className="flex items-center gap-1">
                            Last Order
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Date of the customer's most recent order</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCustomers.length > 0 ? (
                        filteredCustomers.map((customer, index) => (
                          <TableRow
                            key={customer.id}
                            className={`border-b border-gray-100 hover:bg-blue-50 transition-all duration-200 cursor-pointer ${
                              index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                            }`}
                          >
                            <TableCell>
                              <Checkbox />
                            </TableCell>
                            <TableCell className="font-medium">{customer.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={customer.avatar || "/placeholder.svg"} alt={customer.name} />
                                  <AvatarFallback>
                                    {customer.name
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="font-medium">{customer.name}</div>
                              </div>
                            </TableCell>
                            <TableCell>{customer.email}</TableCell>
                            <TableCell>{customer.phone}</TableCell>
                            <TableCell>{getStatusBadge(customer.status)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <ShoppingBag className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {customer.totalOrders}
                              </div>
                            </TableCell>
                            <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {formatDate(customer.lastOrder)}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="hover:bg-gray-100 transition-colors duration-200">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="shadow-lg border-0 bg-white min-w-[160px]">
                                  <DropdownMenuLabel className="text-gray-700 font-semibold">Actions</DropdownMenuLabel>
                                  <DropdownMenuItem className="hover:bg-blue-50 focus:bg-blue-50">
                                    <Link href={`/business-admin/customers/${customer.id}`} className="flex items-center w-full">
                                      <ExternalLink className="mr-2 h-4 w-4 text-blue-500" />
                                      View Details
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="hover:bg-emerald-50 focus:bg-emerald-50">
                                    <UserCog className="mr-2 h-4 w-4 text-emerald-500" />
                                    Edit Customer
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="hover:bg-purple-50 focus:bg-purple-50">
                                    <Mail className="mr-2 h-4 w-4 text-purple-500" />
                                    Send Email
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="hover:bg-orange-50 focus:bg-orange-50">
                                    <ShoppingBag className="mr-2 h-4 w-4 text-orange-500" />
                                    View Orders
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600 hover:bg-red-50 focus:bg-red-50">
                                    Delete Customer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={10} className="h-24 text-center">
                            No customers found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                  </TooltipProvider>
                </div>
              </CardContent>
              <CardFooter className="flex items-center justify-between p-6 border-t border-gray-100 bg-gray-50/50">
                <div className="text-sm text-gray-600 font-medium">
                  Showing {filteredCustomers.length} of {customerStats.total} customers
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={filteredCustomers.length === 0}
                    className="shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300 disabled:opacity-50"
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0 shadow-sm hover:shadow-md transition-all duration-200 bg-emerald-500 text-white border-emerald-500 hover:bg-emerald-600 hover:border-emerald-600"
                    disabled={filteredCustomers.length === 0}
                  >
                    1
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={filteredCustomers.length === 0}
                    className="shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300 disabled:opacity-50"
                  >
                    Next
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-semibold text-gray-900">Active Customers</CardTitle>
                  <CardDescription className="text-sm text-gray-600 font-medium">
                    Showing {filteredCustomers.length} active customers
                  </CardDescription>
                </div>
                <CardDescription className="text-sm text-gray-600">Customers who have placed an order in the last 30 days</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="relative overflow-x-auto">
                  {filteredCustomers.length > 0 ? (
                    <TooltipProvider>
                      <Table>
                      <TableHeader className="bg-gray-50">
                        <TableRow className="border-b border-gray-200 hover:bg-gray-100 transition-colors">
                          <TableHead className="w-[40px]"><Checkbox /></TableHead>
                          <TableHead className="min-w-[50px]">ID</TableHead>
                          <TableHead className="min-w-[150px]">Name</TableHead>
                          <TableHead className="min-w-[180px]">Email</TableHead>
                          <TableHead className="min-w-[120px]">Phone</TableHead>
                          <TableHead className="min-w-[100px]">Orders</TableHead>
                          <TableHead className="min-w-[100px]">Total Spent</TableHead>
                          <TableHead className="min-w-[150px]">Last Order</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredCustomers.map((customer, index) => (
                          <TableRow
                            key={customer.id}
                            className={`border-b border-gray-100 hover:bg-blue-50 transition-all duration-200 cursor-pointer ${
                              index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                            }`}
                          >
                            <TableCell><Checkbox /></TableCell>
                            <TableCell className="font-medium">{customer.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={customer.avatar || "/placeholder.svg"} alt={customer.name} />
                                  <AvatarFallback>
                                    {customer.name.split(" ").map((n) => n[0]).join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="font-medium">{customer.name}</div>
                              </div>
                            </TableCell>
                            <TableCell>{customer.email}</TableCell>
                            <TableCell>{customer.phone}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <ShoppingBag className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {customer.totalOrders}
                              </div>
                            </TableCell>
                            <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {formatDate(customer.lastOrder)}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="hover:bg-gray-100 transition-colors duration-200">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="shadow-lg border-0 bg-white min-w-[160px]">
                                  <DropdownMenuLabel className="text-gray-700 font-semibold">Actions</DropdownMenuLabel>
                                  <DropdownMenuItem className="hover:bg-blue-50 focus:bg-blue-50">
                                    <ExternalLink className="mr-2 h-4 w-4 text-blue-500" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="hover:bg-emerald-50 focus:bg-emerald-50">
                                    <UserCog className="mr-2 h-4 w-4 text-emerald-500" />
                                    Edit Customer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    </TooltipProvider>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <p className="text-lg">No active customers found.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inactive" className="space-y-4">
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-semibold text-gray-900">Inactive Customers</CardTitle>
                  <CardDescription className="text-sm text-gray-600 font-medium">
                    Showing {filteredCustomers.length} inactive customers
                  </CardDescription>
                </div>
                <CardDescription className="text-sm text-gray-600">Customers who haven't placed an order in the last 30 days</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center py-12 text-gray-500">
                  {filteredCustomers.length > 0 ? (
                    <p className="text-lg">Showing {filteredCustomers.length} inactive customers. Consider reaching out to re-engage them.</p>
                  ) : (
                    <p className="text-lg">No inactive customers found. Great job keeping customers engaged!</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="new" className="space-y-4">
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-semibold text-gray-900">New Customers</CardTitle>
                  <CardDescription className="text-sm text-gray-600 font-medium">
                    Showing {filteredCustomers.length} new customers
                  </CardDescription>
                </div>
                <CardDescription className="text-sm text-gray-600">Customers who registered in the last 7 days</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center py-12 text-gray-500">
                  {filteredCustomers.length > 0 ? (
                    <p className="text-lg">Welcome {filteredCustomers.length} new customers! Consider sending them a welcome message.</p>
                  ) : (
                    <p className="text-lg">No new customers found. Focus on marketing to attract new customers.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Help Dialog */}
        <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-blue-600" />
                Customer Management Help
              </DialogTitle>
              <DialogDescription>
                Learn how to effectively manage your customer relationships and data
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 max-h-[60vh] overflow-y-auto">
              {/* Customer Overview */}
              <div>
                <h4 className="font-medium text-sm mb-3 text-blue-900">Understanding Your Customers</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-start gap-2">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></span>
                    <span><strong>Customer Profiles:</strong> View detailed information about each customer including order history and preferences</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></span>
                    <span><strong>Search & Filter:</strong> Use the search bar and filters to quickly find specific customers</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></span>
                    <span><strong>Customer Stats:</strong> Monitor key metrics like total customers, retention rate, and average order value</span>
                  </div>
                </div>
              </div>

              {/* Customer Status Types */}
              <div>
                <h4 className="font-medium text-sm mb-3 text-blue-900">Customer Status Types</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span><strong>Active:</strong> Customers who have placed orders recently and engage regularly</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-yellow-500"></span>
                    <span><strong>New:</strong> Recently registered customers who may need special attention</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-gray-500"></span>
                    <span><strong>Inactive:</strong> Customers who haven't ordered recently and may need re-engagement</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-red-500"></span>
                    <span><strong>At Risk:</strong> Previously active customers showing signs of disengagement</span>
                  </div>
                </div>
              </div>

              {/* Customer Management Actions */}
              <div>
                <h4 className="font-medium text-sm mb-3 text-blue-900">Managing Customer Relationships</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• <strong>Export Data:</strong> Download customer information for marketing campaigns or analysis</li>
                  <li>• <strong>Email Communications:</strong> Send targeted messages to customer segments</li>
                  <li>• <strong>Add New Customers:</strong> Manually register customers who order by phone or in-person</li>
                  <li>• <strong>Track Order History:</strong> Review customer purchase patterns to identify preferences</li>
                  <li>• <strong>Monitor Spending:</strong> Identify high-value customers for special treatment</li>
                </ul>
              </div>

              {/* Best Practices */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-sm mb-2 text-blue-900">Best Practices</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Reach out to new customers within 24 hours of their first order</li>
                  <li>• Monitor inactive customers and send re-engagement offers</li>
                  <li>• Reward loyal customers with special discounts or early access</li>
                  <li>• Use customer data to personalize marketing messages</li>
                  <li>• Regularly review customer feedback to improve service</li>
                  <li>• Keep customer information up-to-date and secure</li>
                </ul>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setIsHelpDialogOpen(false)}>
                Got it, thanks!
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  )
}
