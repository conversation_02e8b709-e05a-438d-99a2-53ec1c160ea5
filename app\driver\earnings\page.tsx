"use client"

import { useState } from "react"
import { DollarSign, TrendingUp, Clock, Download, ArrowUpRight, ArrowDownRight, Package } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock earnings data
const weeklyEarnings = [
  { day: "Monday", deliveries: 8, earnings: 42.5, tips: 5.75, total: 48.25 },
  { day: "Tuesday", deliveries: 10, earnings: 53.25, tips: 7.5, total: 60.75 },
  { day: "Wednesday", deliveries: 7, earnings: 38.75, tips: 4.25, total: 43.0 },
  { day: "Thursday", deliveries: 9, earnings: 48.5, tips: 6.25, total: 54.75 },
  { day: "Friday", deliveries: 12, earnings: 65.75, tips: 10.5, total: 76.25 },
  { day: "Saturday", deliveries: 14, earnings: 78.25, tips: 15.75, total: 94.0 },
  { day: "Sunday", deliveries: 11, earnings: 62.5, tips: 12.25, total: 74.75 },
]

// Mock transactions data
const transactions = [
  {
    id: "TRX-001",
    date: "Today",
    time: "14:32",
    type: "delivery",
    description: "Delivery #JE-5280",
    amount: 6.5,
    status: "completed",
  },
  {
    id: "TRX-002",
    date: "Today",
    time: "12:15",
    type: "delivery",
    description: "Delivery #JE-5275",
    amount: 7.25,
    status: "completed",
  },
  {
    id: "TRX-003",
    date: "Today",
    time: "10:45",
    type: "tip",
    description: "Tip from Delivery #JE-5275",
    amount: 2.5,
    status: "completed",
  },
  {
    id: "TRX-004",
    date: "Yesterday",
    time: "19:45",
    type: "delivery",
    description: "Delivery #JE-5268",
    amount: 5.75,
    status: "completed",
  },
  {
    id: "TRX-005",
    date: "Yesterday",
    time: "18:20",
    type: "adjustment",
    description: "Cancellation Fee #JE-5262",
    amount: 2.0,
    status: "completed",
  },
  {
    id: "TRX-006",
    date: "2 days ago",
    time: "13:10",
    type: "payout",
    description: "Weekly Payout",
    amount: -152.75,
    status: "completed",
  },
]

export default function EarningsPage() {
  const [period, setPeriod] = useState("this_week")

  // Calculate total earnings
  const totalDeliveryEarnings = weeklyEarnings.reduce((total, day) => total + day.earnings, 0)
  const totalTips = weeklyEarnings.reduce((total, day) => total + day.tips, 0)
  const totalEarnings = weeklyEarnings.reduce((total, day) => total + day.total, 0)
  const totalDeliveries = weeklyEarnings.reduce((total, day) => total + day.deliveries, 0)

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Earnings</h1>
          <p className="text-gray-500">Track your delivery earnings and payouts</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="this_week">This Week</SelectItem>
              <SelectItem value="last_week">Last Week</SelectItem>
              <SelectItem value="this_month">This Month</SelectItem>
              <SelectItem value="last_month">Last Month</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Earnings Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                8%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <h3 className="text-2xl font-bold">£{totalEarnings.toFixed(2)}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£24.50 from last week</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                12%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Delivery Earnings</p>
              <h3 className="text-2xl font-bold">£{totalDeliveryEarnings.toFixed(2)}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">From {totalDeliveries} deliveries</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                15%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Tips</p>
              <h3 className="text-2xl font-bold">£{totalTips.toFixed(2)}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£3.25 from last week</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-orange-100 p-2 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
                <ArrowDownRight className="mr-1 h-3 w-3" />
                5%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Avg. Per Hour</p>
              <h3 className="text-2xl font-bold">£14.25</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Based on 32 active hours</div>
          </CardContent>
        </Card>
      </div>

      {/* Earnings Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Earnings Breakdown</CardTitle>
            <CardDescription>Your earnings for this week</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="chart">
              <TabsList className="mb-4">
                <TabsTrigger value="chart">Chart</TabsTrigger>
                <TabsTrigger value="table">Table</TabsTrigger>
              </TabsList>

              <TabsContent value="chart">
                <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-md">
                  <p className="text-gray-500">Earnings chart would be displayed here</p>
                </div>
              </TabsContent>

              <TabsContent value="table">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Day</TableHead>
                        <TableHead className="text-right">Deliveries</TableHead>
                        <TableHead className="text-right">Delivery Earnings</TableHead>
                        <TableHead className="text-right">Tips</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {weeklyEarnings.map((day) => (
                        <TableRow key={day.day}>
                          <TableCell className="font-medium">{day.day}</TableCell>
                          <TableCell className="text-right">{day.deliveries}</TableCell>
                          <TableCell className="text-right">£{day.earnings.toFixed(2)}</TableCell>
                          <TableCell className="text-right">£{day.tips.toFixed(2)}</TableCell>
                          <TableCell className="text-right font-medium">£{day.total.toFixed(2)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Earnings Stats</CardTitle>
            <CardDescription>Your performance metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Busiest Day</p>
                  <p className="text-sm font-medium">Saturday</p>
                </div>
                <p className="text-xs text-gray-500">14 deliveries, £94.00 earned</p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Highest Tip</p>
                  <p className="text-sm font-medium">£5.25</p>
                </div>
                <p className="text-xs text-gray-500">Saturday, Delivery #JE-5268</p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Longest Distance</p>
                  <p className="text-sm font-medium">7.8 km</p>
                </div>
                <p className="text-xs text-gray-500">Friday, Delivery #JE-5255</p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Most Profitable Area</p>
                  <p className="text-sm font-medium">St Helier</p>
                </div>
                <p className="text-xs text-gray-500">85% of your earnings</p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Next Payout</p>
                  <p className="text-sm font-medium">£451.75</p>
                </div>
                <p className="text-xs text-gray-500">Scheduled for Monday, May 6</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transactions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Your recent earnings and payouts</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            View All
          </Button>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">{transaction.id}</TableCell>
                    <TableCell>
                      {transaction.date} at {transaction.time}
                    </TableCell>
                    <TableCell>{transaction.description}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          transaction.type === "delivery"
                            ? "bg-blue-50 text-blue-700 border-blue-200"
                            : transaction.type === "tip"
                              ? "bg-purple-50 text-purple-700 border-purple-200"
                              : transaction.type === "payout"
                                ? "bg-amber-50 text-amber-700 border-amber-200"
                                : "bg-gray-50 text-gray-700 border-gray-200"
                        }
                      >
                        {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell
                      className={`text-right font-medium ${
                        transaction.amount < 0 ? "text-red-600" : "text-emerald-600"
                      }`}
                    >
                      {transaction.amount < 0 ? "-" : "+"}£{Math.abs(transaction.amount).toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
