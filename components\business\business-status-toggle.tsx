"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { HelpCircle, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useBusinessStatus } from "@/hooks/use-business-status"

interface BusinessStatusToggleProps {
  businessId: number
  initialStatus?: boolean
  initialMessage?: string
  onStatusChange?: (isOpen: boolean, message?: string) => void
}

export function BusinessStatusToggle({
  businessId,
  initialStatus,
  initialMessage = "",
  onStatusChange
}: BusinessStatusToggleProps) {
  console.log("🏢 BusinessStatusToggle: Rendered with business ID:", businessId)
  const { status, loading: statusLoading, error: statusError, refetch } = useBusinessStatus(businessId)
  const [isOpen, setIsOpen] = useState(!initialStatus) // Note: inverted logic - isOpen = !is_temporarily_closed
  const [closureMessage, setClosureMessage] = useState(initialMessage)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // Update local state when status is fetched
  useEffect(() => {
    if (status) {
      setIsOpen(!status.isTemporarilyClosed)
      setClosureMessage(status.closureMessage || "")
    }
  }, [status])

  const handleToggle = async () => {
    setIsLoading(true)

    try {
      const newIsOpen = !isOpen
      const requestData = {
        businessId,
        isTemporarilyClosed: !newIsOpen, // Convert to database format
        closureMessage: newIsOpen ? null : closureMessage // Clear message when opening
      }

      console.log('🔄 Toggling business status:', requestData)

      const response = await fetch(`/api/business-admin/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      const responseData = await response.json()
      console.log('📝 Toggle response:', responseData)

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to update business status')
      }

      setIsOpen(newIsOpen)

      // Clear message when opening
      if (newIsOpen) {
        setClosureMessage("")
      }

      toast({
        title: "Status Updated",
        description: `Your business is now ${newIsOpen ? 'open' : 'closed'} for new orders.`,
        variant: "default"
      })

      // Refresh status from server
      await refetch()

      // Notify parent component
      onStatusChange?.(newIsOpen, newIsOpen ? "" : closureMessage)

    } catch (error) {
      console.error('❌ Error updating business status:', error)
      toast({
        title: "Error",
        description: "Failed to update business status. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleMessageChange = async () => {
    if (isOpen) return // Don't update message when business is open

    setIsLoading(true)

    try {
      console.log('🔄 Updating closure message:', {
        businessId,
        isTemporarilyClosed: true,
        closureMessage
      })

      const response = await fetch(`/api/business-admin/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId,
          isTemporarilyClosed: true,
          closureMessage
        })
      })

      const responseData = await response.json()
      console.log('📝 Message update response:', responseData)

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to update closure message')
      }

      toast({
        title: "Message Updated",
        description: "Your closure message has been updated.",
        variant: "default"
      })

      // Refresh status from server
      await refetch()

      onStatusChange?.(false, closureMessage)

    } catch (error) {
      console.error('❌ Error updating closure message:', error)
      toast({
        title: "Error",
        description: "Failed to update closure message. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while fetching initial status
  if (statusLoading && !status) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading business status...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error state if there's an error fetching status
  if (statusError) {
    return (
      <Card className="mb-6 border-red-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <HelpCircle className="h-4 w-4" />
            <span className="text-sm">Failed to load business status: {statusError}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">We are</span>
              <Button
                onClick={handleToggle}
                disabled={isLoading}
                className={`min-w-[80px] ${
                  isOpen 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  isOpen ? 'Open' : 'Closed'
                )}
              </Button>
            </div>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    When closed, your business will stop accepting new orders but remain visible on Loop. 
                    You can reopen at any time by clicking the button again.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {!isOpen && (
          <div className="mt-4 space-y-3">
            <Label htmlFor="closure-message" className="text-sm font-medium">
              Message for customers (optional)
            </Label>
            <Textarea
              id="closure-message"
              placeholder="e.g., Be back online by 8pm, we are very busy"
              value={closureMessage}
              onChange={(e) => setClosureMessage(e.target.value)}
              className="min-h-[80px]"
              maxLength={200}
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">
                {closureMessage.length}/200 characters
              </span>
              <div className="flex gap-2">
                <Button
                  onClick={handleMessageChange}
                  disabled={isLoading}
                  variant="outline"
                  size="sm"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : null}
                  Save Message
                </Button>
                <Button
                  onClick={async () => {
                    setClosureMessage("")
                    // Update the message to empty string
                    setIsLoading(true)
                    try {
                      const response = await fetch(`/api/business-admin/status`, {
                        method: 'PATCH',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          businessId,
                          isTemporarilyClosed: true,
                          closureMessage: ""
                        })
                      })

                      const responseData = await response.json()
                      console.log('🗑️ Clear message response:', responseData)

                      if (response.ok) {
                        toast({
                          title: "Message Cleared",
                          description: "Your closure message has been cleared.",
                          variant: "default"
                        })
                        await refetch()
                      }
                    } catch (error) {
                      console.error('❌ Error clearing message:', error)
                    } finally {
                      setIsLoading(false)
                    }
                  }}
                  disabled={isLoading || !closureMessage}
                  variant="ghost"
                  size="sm"
                >
                  Clear
                </Button>
              </div>
            </div>
            <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
              💡 <strong>Tip:</strong> Type your message and click "Save Message" to update it.
              The message will be shown to customers when they try to order from your business.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
