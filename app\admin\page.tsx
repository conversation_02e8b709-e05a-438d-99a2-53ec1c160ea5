"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  Store,
  Users,
  MapPin,
  ShoppingBag,
  TrendingUp,
  AlertTriangle,
  Activity,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Truck,
  Settings
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuthDirect } from "@/context/auth-context-direct"
import { useAdminDashboardStats } from "@/hooks/use-admin-dashboard-stats"
import { usePendingDriverApplications } from "@/hooks/use-pending-driver-applications"

export default function AdminDashboard() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isLoading } = useAuthDirect()
  const [authChecked, setAuthChecked] = useState(false)

  // Use our real-time stats hook with fallback
  const {
    stats,
    pendingBusinesses,
    loading: statsLoading,
    error,
    refetch: refetchStats
  } = useAdminDashboardStats()

  // Get pending driver applications count
  const { pendingCount } = usePendingDriverApplications()

  // Fallback stats in case of error
  const fallbackStats = {
    totalBusinesses: 5,
    pendingBusinesses: 2,
    totalUsers: 150,
    totalOrders: 45,
    activeUsers: 120,
    newUsersThisMonth: 18,
    totalRevenue: 2500,
    averageOrderValue: 55.56,
    averageRating: 4.2
  }

  // Use fallback stats if there's an error or still loading
  const displayStats = error || statsLoading ? fallbackStats : stats

  // First, check authentication before fetching any data
  useEffect(() => {
    // Skip if still loading auth state
    if (isLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!isLoading && (!user || !isAdmin)) {
      console.log("AdminDashboard: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin")
      return
    }

    // If user is admin, mark auth as checked and allow data fetching
    if (!isLoading && user && isAdmin) {
      console.log("AdminDashboard: User is authorized, proceeding to fetch data")
      setAuthChecked(true)
    }
  }, [user, isAdmin, isLoading, router])

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Get business type name
  const getBusinessTypeName = (typeId: number) => {
    const businessTypes: Record<number, string> = {
      1: "Restaurant",
      2: "Cafe",
      3: "Shop",
      4: "Pharmacy",
      5: "Grocery",
      6: "Bakery",
      7: "Errands"
    }
    return businessTypes[typeId] || "Business"
  }

  // Show loading state only if auth is not checked (don't wait for stats)
  if (!authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
          {!isLoading && !user && (
            <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md max-w-md">
              <p className="font-semibold">Authentication Error</p>
              <p className="text-sm mt-1">You need to be logged in as an admin to access this page.</p>
              <Button
                className="mt-3 bg-red-600 hover:bg-red-700 text-white"
                onClick={() => router.push("/login?redirectTo=/admin")}
              >
                Go to Login
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Error Banner */}
      {error && (
        <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-amber-400" />
            </div>
            <div className="ml-3 flex-1">
              <div className="flex justify-between items-start">
                <p className="text-sm text-amber-700 font-medium">
                  {error?.name === 'TimeoutError'
                    ? 'Data fetch timed out'
                    : 'Real-time data connection issue'}
                </p>
                <button
                  className="ml-2 text-xs bg-amber-100 hover:bg-amber-200 text-amber-800 px-2 py-1 rounded"
                  onClick={() => refetchStats()}
                >
                  Retry Connection
                </button>
              </div>
              <p className="text-sm text-amber-700 mt-1">
                {error?.name === 'TimeoutError'
                  ? 'The dashboard data took too long to load. Showing fallback data instead.'
                  : 'There was an error loading real-time data. Showing fallback data instead.'}
              </p>
            </div>
          </div>
        </div>
      )}

      <div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className="text-gray-500">Welcome to the Loop Jersey admin panel</p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <Button variant="outline">Download Report</Button>
            <Button className="bg-emerald-600 hover:bg-emerald-700">View System Status</Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Businesses</p>
                  <h3 className="text-2xl font-bold mt-1">{displayStats.totalBusinesses}</h3>
                </div>
                <div className="bg-emerald-100 p-3 rounded-full">
                  <Store className="h-6 w-6 text-emerald-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Link href="/admin/businesses" className="text-emerald-600 font-medium hover:underline">View all businesses</Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Pending Approvals</p>
                  <h3 className="text-2xl font-bold mt-1">{displayStats.pendingBusinesses}</h3>
                </div>
                <div className="bg-amber-100 p-3 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-amber-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                {displayStats.pendingBusinesses > 0 ? (
                  <Link href="/admin/businesses?filter=pending" className="text-amber-600 font-medium hover:underline">
                    Review pending businesses
                  </Link>
                ) : (
                  <span className="text-gray-500">No pending approvals</span>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Users</p>
                  <h3 className="text-2xl font-bold mt-1">{displayStats.totalUsers}</h3>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Link href="/admin/users" className="text-blue-600 font-medium hover:underline">Manage users</Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Orders</p>
                  <h3 className="text-2xl font-bold mt-1">{displayStats.totalOrders}</h3>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <ShoppingBag className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Link href="/admin/orders" className="text-orange-600 font-medium hover:underline">View all orders</Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pending Approvals Section */}
        {pendingBusinesses.length > 0 && (
          <>
            <h2 className="text-xl font-semibold mb-4">Pending Business Approvals</h2>
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Businesses Awaiting Approval</CardTitle>
                <CardDescription>
                  These businesses need your review before they can be listed on the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pendingBusinesses.map((business) => (
                    <div key={business.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                      <div>
                        <p className="font-medium">{business.name}</p>
                        <div className="flex items-center text-sm text-gray-500">
                          <span className="mr-2">{getBusinessTypeName(business.business_type_id)}</span>
                          <span>•</span>
                          <span className="ml-2">{formatDate(business.created_at)}</span>
                        </div>
                      </div>
                      <Link
                        href={`/admin/businesses/${business.id}`}
                        className="px-3 py-1 text-sm bg-emerald-100 text-emerald-700 rounded-md hover:bg-emerald-200 transition-colors"
                      >
                        Review
                      </Link>
                    </div>
                  ))}

                  {displayStats.pendingBusinesses > 5 && (
                    <div className="text-center mt-4">
                      <Link
                        href="/admin/businesses?filter=pending"
                        className="text-sm text-emerald-600 hover:text-emerald-800"
                      >
                        View all {displayStats.pendingBusinesses} pending approvals
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Quick Actions */}
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Store className="h-5 w-5 mr-2 text-emerald-600" />
                Manage Businesses
              </CardTitle>
              <CardDescription>
                Add, edit, or remove businesses from the platform
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/businesses" className="w-full">
                <Button className="w-full">View Businesses</Button>
              </Link>
              <span className="text-xs text-gray-500">manage_businesses</span>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-blue-600" />
                Manage Users
              </CardTitle>
              <CardDescription>
                View, edit, and manage all platform users
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/users" className="w-full">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  View Users
                </Button>
              </Link>
              <span className="text-xs text-gray-500">manage_users</span>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                View Analytics
              </CardTitle>
              <CardDescription>
                Access platform analytics and reporting
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/analytics" className="w-full">
                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  View Analytics
                </Button>
              </Link>
              <span className="text-xs text-gray-500">view_analytics</span>
            </CardFooter>
          </Card>
        </div>

        {/* Additional Admin Functions */}
        <h2 className="text-xl font-semibold mb-4">Additional Admin Functions</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="h-5 w-5 mr-2 text-green-600" />
                Driver Applications
              </CardTitle>
              <CardDescription>
                Approve or reject driver applications
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/driver-applications" className="w-full">
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Review Applications
                  {pendingCount > 0 && (
                    <Badge variant="destructive" className="ml-2">
                      {pendingCount}
                    </Badge>
                  )}
                </Button>
              </Link>
              <span className="text-xs text-gray-500">approve_drivers</span>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-red-600" />
                Update Coordinates
              </CardTitle>
              <CardDescription>
                Batch update business coordinates
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/update-coordinates" className="w-full">
                <Button className="w-full bg-red-600 hover:bg-red-700">
                  Update Coordinates
                </Button>
              </Link>
              <span className="text-xs text-gray-500">manage_businesses</span>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-orange-600" />
                Admin Settings
              </CardTitle>
              <CardDescription>
                Configure admin panel settings
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col gap-2">
              <Link href="/admin/settings" className="w-full">
                <Button className="w-full bg-orange-600 hover:bg-orange-700">
                  Settings
                </Button>
              </Link>
              <span className="text-xs text-gray-500">access_admin_panel</span>
            </CardFooter>
          </Card>
        </div>

        {/* System Status */}
        <h2 className="text-xl font-semibold mb-4">System Status</h2>
        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>
              Current status of system components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "Database", status: "Operational" },
                { name: "API Services", status: "Operational" },
                { name: "Payment Processing", status: "Operational" },
                { name: "Geocoding Services", status: "Operational" },
              ].map((service, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>{service.name}</span>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    {service.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { action: "New business registered", business: "Island Grocers", time: "10 minutes ago" },
                  { action: "Business approved", business: "Jersey Bakery", time: "1 hour ago" },
                  { action: "User registered", business: "Sarah Johnson", time: "3 hours ago" },
                  { action: "Business rejected", business: "Quick Mart", time: "5 hours ago" },
                  { action: "System update completed", business: "v2.3.1", time: "Yesterday" },
                ].map((activity, index) => (
                  <div key={index} className="flex items-start gap-4 rounded-lg border p-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                      <Activity className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">{activity.business}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}
