import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      areaName,
      parish,
      postcode,
      customerName,
      customerEmail,
      notes
    } = body

    if (!areaName || !customerName || !customerEmail) {
      return NextResponse.json(
        { error: 'Area name, customer name, and email are required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get user session if available
    const { data: { session } } = await supabase.auth.getSession()

    // Check if user has already voted for this area
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('user_votes')
      .select('id')
      .eq('user_email', customerEmail)
      .eq('request_type', 'area')
      .eq('request_name', areaName)
      .single()

    if (voteCheckError && voteCheckError.code !== 'PGRST116') {
      console.error('Error checking existing vote:', voteCheckError)
      return NextResponse.json(
        { error: 'Failed to check existing votes' },
        { status: 500 }
      )
    }

    if (existingVote) {
      return NextResponse.json(
        { error: 'You have already voted for this area' },
        { status: 400 }
      )
    }

    // Check if this area already exists
    const { data: existingArea, error: areaError } = await supabase
      .from('area_requests')
      .select('id, vote_count')
      .eq('area_name', areaName)
      .eq('status', 'pending')
      .single()

    if (areaError && areaError.code !== 'PGRST116') {
      console.error('Error checking existing area:', areaError)
      return NextResponse.json(
        { error: 'Failed to check existing areas' },
        { status: 500 }
      )
    }

    if (existingArea) {
      // Area exists, increment vote count
      const { data: updatedRequest, error: updateError } = await supabase
        .from('area_requests')
        .update({
          vote_count: existingArea.vote_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingArea.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating area request:', updateError)
        return NextResponse.json(
          { error: 'Failed to update area request' },
          { status: 500 }
        )
      }

      // Record this user's vote
      await supabase
        .from('user_votes')
        .insert({
          user_id: session?.user?.id || null,
          user_email: customerEmail,
          request_type: 'area',
          request_name: areaName
        })

      return NextResponse.json({
        message: 'Vote added successfully',
        request: updatedRequest
      })
    }

    // Create new area request
    const { data: newRequest, error: insertError } = await supabase
      .from('area_requests')
      .insert({
        area_name: areaName,
        parish: parish,
        postcode: postcode,
        customer_name: customerName,
        customer_email: customerEmail,
        user_id: session?.user?.id || null,
        notes: notes || null,
        vote_count: 1,
        status: 'pending'
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating area request:', insertError)
      return NextResponse.json(
        { error: 'Failed to create area request' },
        { status: 500 }
      )
    }

    // Record this user's vote for the new request
    await supabase
      .from('user_votes')
      .insert({
        user_id: session?.user?.id || null,
        user_email: customerEmail,
        request_type: 'area',
        request_name: areaName
      })

    return NextResponse.json({
      message: 'Area request created successfully',
      request: newRequest
    })

  } catch (error) {
    console.error('Error in area requests API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'vote_count'
    const order = searchParams.get('order') || 'desc'

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get area requests sorted by vote count
    const { data: requests, error, count } = await supabase
      .from('area_requests')
      .select('*', { count: 'exact' })
      .eq('status', 'pending')
      .order(sortBy, { ascending: order === 'asc' })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching area requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch area requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      requests: requests || [],
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in area requests GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
