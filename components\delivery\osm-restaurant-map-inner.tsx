"use client"

import { useState, useEffect, useRef } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface OSMRestaurantMapInnerProps {
  restaurantName: string
  restaurantLocation: string
  restaurantCoordinates: [number, number] // [longitude, latitude]
  userLocation: [number, number] | null
  userAddress: string
  deliveryRadius: number // in kilometers
  estimatedDeliveryTime: number | null
  estimatedDistance: number | null
  preparationTimeMinutes: number
}

export default function OSMRestaurantMapInner({
  restaurantName,
  restaurantLocation,
  restaurantCoordinates,
  userLocation,
  userAddress,
  deliveryRadius,
  estimatedDeliveryTime,
  estimatedDistance,
  preparationTimeMinutes
}: OSMRestaurantMapInnerProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const circleRef = useRef<L.Circle | null>(null)
  const userMarkerRef = useRef<L.Marker | null>(null)
  const routeRef = useRef<L.Polyline | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    // Ensure coordinates are valid
    if (!restaurantCoordinates || !Array.isArray(restaurantCoordinates) || restaurantCoordinates.length !== 2) {
      console.error("Invalid restaurant coordinates:", restaurantCoordinates);
      return;
    }

    // Make sure coordinates are numbers
    const validCoordinates: [number, number] = [
      typeof restaurantCoordinates[0] === 'number' ? restaurantCoordinates[0] : -2.1053,
      typeof restaurantCoordinates[1] === 'number' ? restaurantCoordinates[1] : 49.1805
    ];

    // Create map instance - note that Leaflet uses [lat, lng] order, opposite of our [lng, lat]
    const map = L.map(mapContainer.current, {
      center: [validCoordinates[1], validCoordinates[0]], // [lat, lng]
      zoom: 13,
      zoomControl: true,
    })

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    // Create custom restaurant icon
    const restaurantIcon = L.divIcon({
      html: `<div style="background-color: #f97316; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                <path d="M12 21v-9"></path>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Add restaurant marker
    const restaurantMarker = L.marker([validCoordinates[1], validCoordinates[0]], { 
      icon: restaurantIcon 
    }).addTo(map)

    // Add popup to restaurant marker
    restaurantMarker.bindPopup(`
      <div class="p-2">
        <h3 class="font-bold">${restaurantName}</h3>
        <p class="text-sm">${restaurantLocation}</p>
        <div class="flex items-center mt-1 text-sm">
          <span class="font-medium">Prep time: ${preparationTimeMinutes} min</span>
        </div>
      </div>
    `)

    // Add delivery radius circle
    const circle = L.circle([validCoordinates[1], validCoordinates[0]], {
      radius: deliveryRadius * 1000, // Convert km to meters
      color: '#10b981',
      fillColor: '#10b981',
      fillOpacity: 0.2,
      weight: 2
    }).addTo(map)
    
    circleRef.current = circle

    // Add a label for the delivery radius
    const radiusLabelPoint = [
      validCoordinates[1] + (deliveryRadius / 111), // Approximate conversion from km to degrees latitude
      validCoordinates[0]
    ]
    
    const radiusLabel = L.marker(radiusLabelPoint as [number, number], {
      icon: L.divIcon({
        html: `<div class="bg-white px-2 py-1 rounded-md shadow-md text-sm font-medium border border-emerald-200">${deliveryRadius} km delivery radius</div>`,
        className: '',
        iconSize: [120, 30],
        iconAnchor: [60, 15]
      })
    }).addTo(map)

    mapRef.current = map
    setMapLoaded(true)

    return () => {
      map.remove()
      mapRef.current = null
      circleRef.current = null
      userMarkerRef.current = null
      routeRef.current = null
    }
  }, [restaurantCoordinates, restaurantName, restaurantLocation, deliveryRadius, preparationTimeMinutes])

  // Update map when user location changes
  useEffect(() => {
    if (!mapRef.current || !mapLoaded || !userLocation) return

    const map = mapRef.current

    // Remove previous user marker and route
    if (userMarkerRef.current) {
      map.removeLayer(userMarkerRef.current)
      userMarkerRef.current = null
    }

    if (routeRef.current) {
      map.removeLayer(routeRef.current)
      routeRef.current = null
    }

    // Ensure coordinates are valid
    const validRestCoords: [number, number] = [
      typeof restaurantCoordinates[0] === 'number' ? restaurantCoordinates[0] : -2.1053,
      typeof restaurantCoordinates[1] === 'number' ? restaurantCoordinates[1] : 49.1805
    ]

    // Ensure user coordinates are valid
    const validUserCoords: [number, number] = [
      typeof userLocation[0] === 'number' ? userLocation[0] : 0,
      typeof userLocation[1] === 'number' ? userLocation[1] : 0
    ]

    // Check if user is within delivery radius
    const [userLng, userLat] = validUserCoords
    const [restLng, restLat] = validRestCoords

    const R = 6371 // Earth's radius in km
    const dLat = (userLat - restLat) * Math.PI / 180
    const dLon = (userLng - restLng) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(restLat * Math.PI / 180) * Math.cos(userLat * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    const distance = R * c

    const isWithinDeliveryRadius = distance <= deliveryRadius
    const markerColor = isWithinDeliveryRadius ? "#10b981" : "#f97316"

    // Create custom user icon
    const userIcon = L.divIcon({
      html: `<div style="background-color: ${markerColor}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Add user marker - note that Leaflet uses [lat, lng] order
    const userMarker = L.marker([userLat, userLng], { 
      icon: userIcon 
    }).addTo(map)
    
    userMarkerRef.current = userMarker

    // Add popup to user marker
    userMarker.bindPopup(`
      <div class="p-2">
        <h3 class="font-bold">Your Location</h3>
        ${userAddress ? `<p class="text-sm">${userAddress}</p>` : ''}
        ${estimatedDeliveryTime ?
          `<div class="flex items-center mt-1 text-sm">
            <span class="font-medium">Estimated delivery: ${estimatedDeliveryTime} min</span>
          </div>` : ''
        }
        <div class="flex items-center mt-1 text-sm">
          <span class="font-medium ${isWithinDeliveryRadius ? 'text-emerald-600' : 'text-orange-500'}">
            ${isWithinDeliveryRadius
              ? 'Within delivery radius'
              : 'Outside standard delivery radius'}
          </span>
        </div>
      </div>
    `)

    // Add route line between restaurant and user
    const route = L.polyline(
      [
        [restLat, restLng], // [lat, lng]
        [userLat, userLng]  // [lat, lng]
      ],
      {
        color: '#10b981',
        weight: 3,
        dashArray: '5, 5',
        opacity: 0.7
      }
    ).addTo(map)
    
    routeRef.current = route

    // Fit map to show both points
    const bounds = L.latLngBounds(
      [restLat, restLng],
      [userLat, userLng]
    )
    
    map.fitBounds(bounds, {
      padding: [50, 50],
      maxZoom: 15
    })

  }, [mapLoaded, userLocation, restaurantCoordinates, userAddress, estimatedDeliveryTime, deliveryRadius])

  return (
    <div ref={mapContainer} className="h-[400px]" />
  )
}
