'use client'

import { useState } from "react"
import { Receipt, Home, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"

interface QuickActionsProps {
  orderId: string
}

export function QuickActions({ orderId }: QuickActionsProps) {
  const router = useRouter()
  const [isNavigating, setIsNavigating] = useState(false)

  const handleViewReceipt = () => {
    // For now, just scroll to top to show the order details
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleReturnHome = async () => {
    setIsNavigating(true)
    try {
      router.push('/')
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
    }
  }

  const handleOrderMore = async () => {
    setIsNavigating(true)
    try {
      router.push('/')
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
    }
  }

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-emerald-50/30 backdrop-blur-sm">
      <CardHeader className="pb-4 border-b border-gray-100">
        <CardTitle className="text-xl font-bold text-gray-900">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <Button
            onClick={handleViewReceipt}
            variant="outline"
            className="w-full justify-start text-left h-auto p-4 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
          >
            <Receipt className="h-5 w-5 mr-3 text-blue-600" />
            <div>
              <div className="font-medium text-gray-900">View Receipt</div>
              <div className="text-sm text-gray-500">See full order details</div>
            </div>
          </Button>

          <Button
            onClick={handleReturnHome}
            variant="outline"
            disabled={isNavigating}
            className="w-full justify-start text-left h-auto p-4 border-gray-200 hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200"
          >
            <Home className="h-5 w-5 mr-3 text-emerald-600" />
            <div>
              <div className="font-medium text-gray-900">Return Home</div>
              <div className="text-sm text-gray-500">Back to main page</div>
            </div>
          </Button>

          <Button
            onClick={handleOrderMore}
            disabled={isNavigating}
            className="w-full justify-between h-auto p-4 bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <div className="flex items-center">
              <div className="text-left">
                <div className="font-medium">Order More</div>
                <div className="text-sm text-blue-100">Continue shopping</div>
              </div>
            </div>
            <ArrowRight className="h-5 w-5 ml-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
