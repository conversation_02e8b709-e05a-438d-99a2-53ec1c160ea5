import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

// Only allow this endpoint in development mode
const isDevelopment = process.env.NODE_ENV === 'development'

export async function GET(request: NextRequest) {
  // Only allow this endpoint in development mode
  if (!isDevelopment) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 403 }
    )
  }

  // Get the order ID from the query parameters
  const { searchParams } = new URL(request.url)
  const orderId = searchParams.get('orderId')

  if (!orderId) {
    return NextResponse.json(
      { error: 'Order ID is required' },
      { status: 400 }
    )
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Log the request details
    console.log(`Debug API: Fetching order data for order ID: ${orderId}`)

    // Try to determine if the order ID is a UUID or a number
    let orderIdForQuery = orderId
    let isUuid = false

    // Check if it looks like a UUID
    if (orderId.includes('-') && orderId.length > 30) {
      console.log(`Debug API: Order ID appears to be a UUID: ${orderId}`)
      isUuid = true
    } else {
      // Try to convert to a number
      const numericId = Number(orderId)
      if (!isNaN(numericId)) {
        console.log(`Debug API: Order ID converted to number: ${numericId}`)
        orderIdForQuery = numericId
      } else {
        console.log(`Debug API: Order ID is not a number or UUID: ${orderId}`)
      }
    }

    // Fetch the order data
    let order = null
    let orderError = null

    try {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderIdForQuery)
        .single()

      order = data
      orderError = error

      if (error) {
        console.error('Error fetching order by ID:', error)

        // If we couldn't find the order by ID, try looking it up by order_number
        console.log(`Debug API: Trying to find order by order_number: ${orderId}`)
        const { data: orderByNumber, error: orderByNumberError } = await supabase
          .from('orders')
          .select('*')
          .eq('order_number', orderId)
          .single()

        if (!orderByNumberError && orderByNumber) {
          console.log(`Debug API: Found order by order_number: ${orderId}`)
          order = orderByNumber
          orderError = null
        } else {
          console.error('Error fetching order by order_number:', orderByNumberError)
        }
      }
    } catch (error) {
      console.error('Exception fetching order:', error)
      return NextResponse.json(
        { error: `Exception fetching order: ${error.message}` },
        { status: 500 }
      )
    }

    if (orderError) {
      console.error('Error fetching order:', orderError)
      return NextResponse.json(
        {
          error: `Error fetching order: ${orderError.message}`,
          details: {
            orderId,
            orderIdForQuery,
            isUuid,
            errorCode: orderError.code,
            errorMessage: orderError.message,
            errorDetails: orderError.details
          }
        },
        { status: 500 }
      )
    }

    if (!order) {
      console.error('No order found with ID:', orderId)
      return NextResponse.json(
        {
          error: `No order found with ID: ${orderId}`,
          details: {
            orderId,
            orderIdForQuery,
            isUuid
          }
        },
        { status: 404 }
      )
    }

    // Use the actual order ID for related tables
    const actualOrderId = order.id
    console.log(`Debug API: Using actual order ID for related tables: ${actualOrderId}`)

    // Fetch the order businesses
    let orderBusinesses = []
    try {
      const { data, error } = await supabase
        .from('order_businesses')
        .select('*')
        .eq('order_id', actualOrderId)

      if (error) {
        console.error('Error fetching order businesses:', error)
      } else {
        orderBusinesses = data || []
        console.log(`Debug API: Found ${orderBusinesses.length} order businesses`)
      }
    } catch (error) {
      console.error('Exception fetching order businesses:', error)
    }

    // Fetch the order items
    let orderItems = []
    try {
      const { data, error } = await supabase
        .from('order_items')
        .select('*')
        .eq('order_id', actualOrderId)

      if (error) {
        console.error('Error fetching order items:', error)
      } else {
        orderItems = data || []
        console.log(`Debug API: Found ${orderItems.length} order items`)
      }
    } catch (error) {
      console.error('Exception fetching order items:', error)
    }

    // Fetch the order status history
    let statusHistory = []
    try {
      const { data, error } = await supabase
        .from('order_status_history')
        .select('*')
        .eq('order_id', actualOrderId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching status history:', error)
      } else {
        statusHistory = data || []
        console.log(`Debug API: Found ${statusHistory.length} status history entries`)
      }
    } catch (error) {
      console.error('Exception fetching status history:', error)
    }

    // Fetch the cart items for this order if available
    let cartItems = []
    try {
      const { data, error } = await supabase
        .from('cart_items')
        .select('*')
        .eq('order_id', actualOrderId)

      if (error) {
        console.error('Error fetching cart items:', error)
      } else {
        cartItems = data || []
        console.log(`Debug API: Found ${cartItems.length} cart items`)
      }
    } catch (error) {
      console.error('Exception fetching cart items:', error)
    }

    // Return all the data with additional debugging information
    return NextResponse.json({
      order,
      orderBusinesses,
      orderItems,
      statusHistory,
      cartItems,
      debug: {
        requestedOrderId: orderId,
        orderIdForQuery: orderIdForQuery,
        actualOrderId: actualOrderId,
        isUuid: isUuid,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        counts: {
          orderBusinesses: orderBusinesses.length,
          orderItems: orderItems.length,
          statusHistory: statusHistory.length,
          cartItems: cartItems.length
        }
      }
    })
  } catch (error: any) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: `Unexpected error: ${error.message}` },
      { status: 500 }
    )
  }
}
