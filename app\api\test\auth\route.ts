import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== TEST AUTH API ===")
    
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()
    
    console.log("Session check:", {
      hasSession: !!session,
      sessionError,
      userEmail: session?.user?.email,
      userId: session?.user?.id
    })

    if (!session) {
      return NextResponse.json({
        authenticated: false,
        sessionError,
        message: "No session found"
      })
    }

    // Try to get user profile
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, email")
      .eq("auth_id", session.user.id)
      .single()

    console.log("User profile check:", {
      userProfile,
      userError,
      authId: session.user.id
    })

    // Try alternative lookup
    const { data: userProfileAlt, error: userErrorAlt } = await adminClient
      .from("users")
      .select("id, role, email, auth_id")
      .eq("email", session.user.email)
      .single()

    console.log("User profile alt check:", {
      userProfileAlt,
      userErrorAlt
    })

    return NextResponse.json({
      authenticated: true,
      session: {
        userId: session.user.id,
        email: session.user.email
      },
      userProfile,
      userError,
      userProfileAlt,
      userErrorAlt
    })

  } catch (error: any) {
    console.error("Error in test auth API:", error)
    return NextResponse.json({
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
