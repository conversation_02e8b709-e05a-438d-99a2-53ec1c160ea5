"use client"

import { Building2 } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

interface BusinessSelectorProps {
  businesses: BusinessOption[]
  selectedBusinessId: number | null
  onBusinessChange: (businessId: number) => void
}

export function BusinessSelector({
  businesses,
  selectedBusinessId,
  onBusinessChange
}: BusinessSelectorProps) {
  if (businesses.length === 0) {
    return null
  }

  return (
    <div className="flex items-center bg-gray-50 rounded-md border px-3 py-1 mr-2">
      <Building2 className="h-4 w-4 text-gray-500 mr-2" />
      <Select
        value={selectedBusinessId?.toString() || ""}
        onValueChange={(value) => onBusinessChange(parseInt(value))}
      >
        <SelectTrigger className="border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 w-[180px]">
          <SelectValue placeholder="Select a business" />
        </SelectTrigger>
        <SelectContent>
          {businesses.map((business) => (
            <SelectItem key={business.id} value={business.id.toString()}>
              {business.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
