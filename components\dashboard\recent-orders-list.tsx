"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatDistanceToNow } from "date-fns"
import { ArrowRight, Clock, Package } from "lucide-react"
import Link from "next/link"

// Sample data - in a real implementation, this would come from the API
const data = [
  {
    id: 12345,
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/john-smith.jpg"
    },
    status: "completed",
    items: 3,
    total: 32.97,
    date: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    deliveryMethod: "delivery"
  },
  {
    id: 12344,
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/sarah-johnson.jpg"
    },
    status: "preparing",
    items: 2,
    total: 24.98,
    date: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
    deliveryMethod: "pickup"
  },
  {
    id: 12343,
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/michael-brown.jpg"
    },
    status: "pending",
    items: 5,
    total: 56.95,
    date: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    deliveryMethod: "delivery"
  },
  {
    id: 12342,
    customer: {
      name: "Emily Davis",
      email: "<EMAIL>",
      avatar: "/avatars/emily-davis.jpg"
    },
    status: "completed",
    items: 1,
    total: 12.99,
    date: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
    deliveryMethod: "pickup"
  },
  {
    id: 12341,
    customer: {
      name: "David Wilson",
      email: "<EMAIL>",
      avatar: "/avatars/david-wilson.jpg"
    },
    status: "completed",
    items: 4,
    total: 43.96,
    date: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
    deliveryMethod: "delivery"
  }
]

interface RecentOrdersListProps {
  data?: any[]
  isLoading?: boolean
}

export function RecentOrdersList({ data: propData, isLoading = false }: RecentOrdersListProps) {
  const orders = propData || data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2,
    }).format(value)
  }

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Completed</Badge>
      case "preparing":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Preparing</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Cancelled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center animate-pulse">
            <div className="h-10 w-10 rounded-full bg-gray-200 mr-4"></div>
            <div className="flex-1">
              <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-5">
      {orders.map((order) => (
        <div key={order.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
          <div className="flex items-center mb-3 sm:mb-0">
            <Avatar className="h-10 w-10 mr-4">
              <AvatarImage src={order.customer.avatar} alt={order.customer.name} />
              <AvatarFallback>{order.customer.name.substring(0, 2)}</AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{order.customer.name}</p>
              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="mr-1 h-3 w-3" />
                <span>{formatDistanceToNow(order.date, { addSuffix: true })}</span>
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap items-center gap-2 sm:gap-4">
            <div className="flex items-center">
              <Package className="mr-1 h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{order.items} {order.items === 1 ? 'item' : 'items'}</span>
            </div>
            
            <div className="flex items-center">
              <Badge variant="outline" className="mr-2">
                {order.deliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'}
              </Badge>
              {getStatusBadge(order.status)}
            </div>
            
            <div className="text-sm font-medium">
              {formatCurrency(order.total)}
            </div>
            
            <Link href={`/business-admin/orders-new/${order.id}`}>
              <Button variant="ghost" size="sm" className="ml-2">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      ))}
    </div>
  )
}
