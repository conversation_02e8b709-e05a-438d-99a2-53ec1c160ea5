"use client"

import type { ReactNode } from "react"
import { Inter } from "next/font/google"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { BarChart3, Home, Settings, Package, Clock, Wallet, User, FileText, Building2, Smartphone, LogOut, Network, TrendingUp, Truck } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import UserMenu from "@/components/auth/user-menu"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { DriverInterfaceDetector } from "@/components/driver-interface-detector"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

interface DriverLayoutProps {
  children: ReactNode
}

export default function DriverLayout({ children }: DriverLayoutProps) {
  const pathname = usePathname()

  // Helper function to determine if a link is active
  const isActive = (href: string) => {
    if (href === '/driver/dashboard') {
      return pathname === '/driver/dashboard' || pathname === '/driver'
    }
    return pathname === href
  }

  // Helper function to get navigation link classes
  const getNavLinkClasses = (href: string) => {
    if (isActive(href)) {
      return "flex items-center px-3 py-2 text-emerald-600 bg-emerald-50 rounded-md font-medium"
    }
    return "flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100"
  }

  // Helper function to get icon classes
  const getIconClasses = (href: string) => {
    if (isActive(href)) {
      return "w-5 h-5 mr-3 text-emerald-600"
    }
    return "w-5 h-5 mr-3 text-gray-500"
  }

  return (
    <DriverInterfaceDetector>
      <div className={`${inter.variable} font-sans flex min-h-screen bg-gray-100 overflow-hidden`}>
      {/* Sidebar - Desktop Only (hidden on mobile/tablet) */}
      <aside className="hidden lg:flex flex-col w-64 bg-white border-r">
        <div className="p-4 border-b">
          <Link href="/driver/dashboard" className="flex items-center group">
            <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
              <div className="wheel-logo mr-2 group-hover:animate-spin">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop</span>
            </div>
            <span className="ml-3 text-xs bg-emerald-600 text-white px-2 py-1 rounded-full font-medium">Driver</span>
          </Link>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link
            href="/driver/dashboard"
            className={getNavLinkClasses('/driver/dashboard')}
          >
            <Home className={getIconClasses('/driver/dashboard')} />
            Dashboard
          </Link>
          <Link
            href="/driver/orders"
            className={getNavLinkClasses('/driver/orders')}
          >
            <Package className={getIconClasses('/driver/orders')} />
            Orders
          </Link>
          <Link
            href="/driver/delivery-history"
            className={getNavLinkClasses('/driver/delivery-history')}
          >
            <Truck className={getIconClasses('/driver/delivery-history')} />
            Delivery History
          </Link>
          <Link
            href="/driver/businesses"
            className={getNavLinkClasses('/driver/businesses')}
          >
            <Building2 className={getIconClasses('/driver/businesses')} />
            Businesses
          </Link>
          <Link
            href="/driver/connections"
            className={getNavLinkClasses('/driver/connections')}
          >
            <Network className={getIconClasses('/driver/connections')} />
            Connections Hub
          </Link>
          <Link
            href="/driver/applications"
            className={getNavLinkClasses('/driver/applications')}
          >
            <FileText className={getIconClasses('/driver/applications')} />
            Applications
          </Link>
          <Link
            href="/driver/profile"
            className={getNavLinkClasses('/driver/profile')}
          >
            <User className={getIconClasses('/driver/profile')} />
            Profile
          </Link>
          <Link
            href="/driver/history"
            className={getNavLinkClasses('/driver/history')}
          >
            <Clock className={getIconClasses('/driver/history')} />
            History
          </Link>
          <Link
            href="/driver/earnings"
            className={getNavLinkClasses('/driver/earnings')}
          >
            <Wallet className={getIconClasses('/driver/earnings')} />
            Earnings
          </Link>
          <Link
            href="/driver/analytics"
            className={getNavLinkClasses('/driver/analytics')}
          >
            <BarChart3 className={getIconClasses('/driver/analytics')} />
            Analytics
          </Link>
          <Link
            href="/driver/settings"
            className={getNavLinkClasses('/driver/settings')}
          >
            <Settings className={getIconClasses('/driver/settings')} />
            Settings
          </Link>

          {/* Mobile App Link */}
          <div className="pt-2 mt-2 border-t border-gray-200">
            <Link
              href="/driver-mobile"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-3 py-2 text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100 font-medium"
            >
              <Smartphone className="w-5 h-5 mr-3 text-blue-600" />
              Mobile App
            </Link>
            <Link
              href="/driver/analytics-aggregation"
              className={`${getNavLinkClasses('/driver/analytics-aggregation')} mt-1`}
            >
              <TrendingUp className={getIconClasses('/driver/analytics-aggregation')} />
              Analytics Aggregation
            </Link>
          </div>
        </nav>

        {/* Sidebar footer removed - user info is in header UserMenu */}
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200 shadow-sm">
          <div className="flex items-center justify-between px-4 py-3">
            {/* Mobile/Tablet Header - Show on smaller screens */}
            <div className="lg:hidden flex items-center space-x-3">
              <Link href="/driver/dashboard" className="flex items-center group">
                <div className="flex items-center bg-emerald-600 rounded-lg px-3 py-2 border border-emerald-500 shadow-sm h-9">
                  <div className="wheel-logo mr-1.5 group-hover:animate-spin">
                    <WheelLogoIcon
                      size={20}
                      color="white"
                      className="text-white w-5 h-5"
                    />
                  </div>
                  <span className="text-base font-bold text-white">Loop</span>
                </div>
              </Link>
            </div>

            {/* Mobile Interface Suggestion - Show on smaller screens */}
            <div className="lg:hidden">
              <Button variant="outline" className="h-10 px-4 font-medium" asChild>
                <Link href="/driver-mobile/dashboard">
                  <Smartphone className="h-4 w-4 mr-2" />
                  Mobile View
                </Link>
              </Button>
            </div>

            {/* Desktop Header Content */}
            <div className="hidden lg:flex items-center justify-end w-full">
              {/* Right: Account Menu */}
              <div className="flex items-center space-x-3">
                <Button variant="outline" className="h-10 px-4 font-medium" asChild>
                  <Link href="/driver-mobile/dashboard" target="_blank" rel="noopener noreferrer">
                    <Smartphone className="h-4 w-4 mr-2" />
                    Mobile App
                  </Link>
                </Button>
                <UserMenu />
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 min-w-0">{children}</main>
      </div>
    </div>
    </DriverInterfaceDetector>
  )
}
