import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET() {
  try {
    console.log('Create Test Data API called');
    
    // Check if the business_types table exists
    const { data: businessTypesData, error: businessTypesError } = await adminClient
      .from('business_types')
      .select('count(*)', { count: 'exact', head: true });
    
    // If the business_types table doesn't exist or is empty, create it
    let businessTypesCreated = false;
    if (businessTypesError || (businessTypesData && businessTypesData.count === 0)) {
      // Create the business_types table
      const { error: createError } = await adminClient
        .from('business_types')
        .insert([
          { name: 'Restaurant', slug: 'restaurant' },
          { name: 'Shop', slug: 'shop' },
          { name: 'Pharmacy', slug: 'pharmacy' },
          { name: 'Cafe', slug: 'cafe' },
          { name: 'Errand', slug: 'errand' }
        ]);
      
      businessTypesCreated = !createError;
    }
    
    // Check if the businesses table exists
    const { data: businessesData, error: businessesError } = await adminClient
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });
    
    // If the businesses table doesn't exist or is empty, create it
    let businessesCreated = false;
    if (businessesError || (businessesData && businessesData.count === 0)) {
      // Get the restaurant type ID
      const { data: restaurantType, error: restaurantTypeError } = await adminClient
        .from('business_types')
        .select('id')
        .eq('slug', 'restaurant')
        .single();
      
      if (restaurantTypeError) {
        return NextResponse.json({
          error: 'Failed to get restaurant type ID',
          details: restaurantTypeError.message
        }, { status: 500 });
      }
      
      // Create the businesses table
      const { error: createError } = await adminClient
        .from('businesses')
        .insert([
          {
            name: 'Test Restaurant 1',
            slug: 'test-restaurant-1',
            business_type_id: restaurantType.id,
            location: 'St Helier',
            rating: 4.5,
            review_count: 100,
            delivery_time_minutes: 30,
            preparation_time_minutes: 15,
            delivery_fee: 2.0,
            minimum_order_amount: 15.0,
            is_approved: true
          },
          {
            name: 'Test Restaurant 2',
            slug: 'test-restaurant-2',
            business_type_id: restaurantType.id,
            location: 'St Brelade',
            rating: 4.2,
            review_count: 80,
            delivery_time_minutes: 35,
            preparation_time_minutes: 20,
            delivery_fee: 2.5,
            minimum_order_amount: 20.0,
            is_approved: true
          }
        ]);
      
      businessesCreated = !createError;
    }
    
    return NextResponse.json({
      success: true,
      businessTypes: {
        exists: !businessTypesError,
        count: businessTypesData?.count || 0,
        created: businessTypesCreated
      },
      businesses: {
        exists: !businessesError,
        count: businessesData?.count || 0,
        created: businessesCreated
      }
    });
  } catch (error) {
    console.error('Error in create-test-data API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
