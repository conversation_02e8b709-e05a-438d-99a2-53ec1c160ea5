"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import PortraitBusinessCard from "@/components/portrait-business-card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

interface Business {
  id: string | number;
  slug: string;
  name: string;
  logo_url?: string;
  banner_url?: string;
  rating?: number;
  review_count?: number;
  delivery_time_minutes?: number;
  preparation_time_minutes?: number;
  delivery_fee?: number;
  delivery_fee_model?: string;
  delivery_fee_per_km?: number;
  coordinates?: [number, number];
  delivery_radius?: number;
  minimum_order_amount?: number;
  location?: string;
  business_type: string;
  business_type_slug: string;
  offer?: string;
  dealType?: string;
}

interface DealsSectionProps {
  businesses: Business[];
  userLocation?: [number, number] | null;
}

export default function DealsSection({ businesses, userLocation }: DealsSectionProps) {
  if (!businesses || businesses.length === 0) {
    return null;
  }

  return (
    <section className="py-6 bg-gray-50 rounded-lg relative overflow-hidden">
      <div className="absolute inset-0 opacity-5 pointer-events-none"
           style={{
             backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%2300875a\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'1\'/%3E%3Ccircle cx=\'13\' cy=\'13\' r=\'1\'/%3E%3C/g%3E%3C/svg%3E")',
             backgroundSize: '20px 20px'
           }}>
      </div>
      <div className="px-4 relative">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-800">Deals</h2>
            <span className="ml-3 px-2 py-1 bg-red-100 text-red-600 text-xs font-semibold rounded-full">Limited Time</span>
          </div>
          <Link
            href="/deals"
            className="hidden md:flex items-center text-emerald-600 hover:text-emerald-700 transition-colors bg-white px-3 py-1.5 rounded-full shadow-sm hover:shadow border border-emerald-100"
          >
            <span className="text-sm font-medium mr-2">View all deals</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>

        <div>
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent>
              {businesses.map((business) => (
                  <CarouselItem key={business.id} className="md:basis-1/2 lg:basis-1/2 xl:basis-1/3 pl-4">
                    <PortraitBusinessCard
                      key={business.id}
                      business={business}
                      userLocation={userLocation}
                    />
                  </CarouselItem>
              ))}
            </CarouselContent>
            <div className="hidden md:flex justify-end mt-4 gap-2">
              <CarouselPrevious className="relative h-9 w-9 rounded-full border-0 bg-white shadow-md hover:bg-gray-50 hover:text-emerald-600 transition-colors" style={{ position: 'static', left: 'auto', transform: 'none' }} />
              <CarouselNext className="relative h-9 w-9 rounded-full border-0 bg-white shadow-md hover:bg-gray-50 hover:text-emerald-600 transition-colors" style={{ position: 'static', right: 'auto', transform: 'none' }} />
            </div>
          </Carousel>
        </div>
      </div>
    </section>
  );
}
