'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/context/unified-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { getAuthHeaders } from '@/utils/auth-utils'
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Home,
  Building,
  Star,
  Briefcase,
  X,
  Save,
  AlertTriangle
} from 'lucide-react'

// Define the UserAddress type based on the database schema
interface UserAddress {
  id: number
  user_id: number
  address_name: string
  address_line1: string
  address_line2?: string | null
  parish: string
  postcode: string
  coordinates?: string | null
  is_default: boolean
  created_at: string
  updated_at: string
}

export default function AddressesPage() {
  const { user, isLoading } = useAuth()
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(true)
  const [isAddingAddress, setIsAddingAddress] = useState(false)
  const [isEditingAddress, setIsEditingAddress] = useState(false)
  const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Form state for new/edit address
  const [formData, setFormData] = useState({
    address_name: '',
    address_line1: '',
    address_line2: '',
    parish: '',
    postcode: '',
    is_default: false
  })

  // Fetch addresses from API
  const fetchAddresses = async () => {
    if (!user) return

    try {
      setIsLoadingAddresses(true)
      setError(null)

      // Use a simpler approach with fallback headers
      let headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      try {
        const authHeaders = await getAuthHeaders()
        headers = authHeaders
      } catch (authError) {
        console.warn("Failed to get auth headers, using basic headers:", authError)
        // Continue with basic headers
      }

      const response = await fetch('/api/user/addresses', {
        credentials: 'include',
        headers
      })

      const result = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          setError("Please sign in to view your addresses")
        } else {
          setError(result.error || "Failed to load addresses")
        }
        setAddresses([])
      } else {
        setAddresses(result.addresses || [])
      }
    } catch (err: any) {
      console.error("Error fetching addresses:", err)
      setError("Failed to load addresses. Please try again.")
      setAddresses([])
    } finally {
      setIsLoadingAddresses(false)
    }
  }

  // Load addresses when component mounts or user changes
  useEffect(() => {
    if (user) {
      fetchAddresses()
    } else {
      setAddresses([])
      setIsLoadingAddresses(false)
    }
  }, [user])

  // Clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null)
        setSuccess(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [error, success])

  // Reset form data
  const resetForm = () => {
    setFormData({
      address_name: '',
      address_line1: '',
      address_line2: '',
      parish: '',
      postcode: '',
      is_default: false
    })
  }

  // Handle add new address
  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      setIsProcessing(true)
      setError(null)

      // Use a simpler approach with fallback headers
      let headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      try {
        const authHeaders = await getAuthHeaders()
        headers = authHeaders
      } catch (authError) {
        console.warn("Failed to get auth headers, using basic headers:", authError)
        // Continue with basic headers
      }

      const response = await fetch('/api/user/addresses', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || "Failed to add address")
      }

      const result = await response.json()

      // Add the new address to the list
      if (result.address) {
        setAddresses(prev => [...prev, result.address])
        setSuccess("Address added successfully")
        setIsAddingAddress(false)
        resetForm()
      }
    } catch (err: any) {
      console.error("Error adding address:", err)
      setError(err.message || "Failed to add address. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle edit address
  const handleEditAddress = (address: UserAddress) => {
    setEditingAddress(address)
    setFormData({
      address_name: address.address_name,
      address_line1: address.address_line1,
      address_line2: address.address_line2 || '',
      parish: address.parish,
      postcode: address.postcode,
      is_default: address.is_default
    })
    setIsEditingAddress(true)
  }

  // Handle update address
  const handleUpdateAddress = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !editingAddress) return

    try {
      setIsProcessing(true)
      setError(null)

      // Use a simpler approach with fallback headers
      let headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      try {
        const authHeaders = await getAuthHeaders()
        headers = authHeaders
      } catch (authError) {
        console.warn("Failed to get auth headers, using basic headers:", authError)
        // Continue with basic headers
      }

      const response = await fetch(`/api/user/addresses/${editingAddress.id}`, {
        method: 'PATCH',
        credentials: 'include',
        headers,
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || "Failed to update address")
      }

      const result = await response.json()

      // Update the address in the list
      if (result.address) {
        setAddresses(prev => prev.map(addr =>
          addr.id === editingAddress.id ? result.address : addr
        ))
        setSuccess("Address updated successfully")
        setIsEditingAddress(false)
        setEditingAddress(null)
        resetForm()
      }
    } catch (err: any) {
      console.error("Error updating address:", err)
      setError(err.message || "Failed to update address. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle set default address
  const handleSetDefault = async (addressId: number) => {
    if (!user) return

    try {
      setIsProcessing(true)
      setError(null)

      // Use a simpler approach with fallback headers
      let headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      try {
        const authHeaders = await getAuthHeaders()
        headers = authHeaders
      } catch (authError) {
        console.warn("Failed to get auth headers, using basic headers:", authError)
        // Continue with basic headers
      }

      const response = await fetch(`/api/user/addresses/${addressId}/default`, {
        method: 'POST',
        credentials: 'include',
        headers
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || "Failed to set default address")
      }

      const result = await response.json()

      // Update the addresses list
      setAddresses(prev => prev.map(addr => ({
        ...addr,
        is_default: addr.id === addressId
      })))
      setSuccess("Default address updated")
    } catch (err: any) {
      console.error("Error setting default address:", err)
      setError(err.message || "Failed to set default address. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle delete address
  const handleDeleteAddress = async (addressId: number) => {
    if (!user) return

    if (!confirm("Are you sure you want to delete this address?")) {
      return
    }

    try {
      setIsProcessing(true)
      setError(null)

      // Use a simpler approach with fallback headers
      let headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      try {
        const authHeaders = await getAuthHeaders()
        headers = authHeaders
      } catch (authError) {
        console.warn("Failed to get auth headers, using basic headers:", authError)
        // Continue with basic headers
      }

      const response = await fetch(`/api/user/addresses/${addressId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || "Failed to delete address")
      }

      // Remove the address from the list
      setAddresses(prev => prev.filter(addr => addr.id !== addressId))
      setSuccess("Address deleted successfully")
    } catch (err: any) {
      console.error("Error deleting address:", err)
      setError(err.message || "Failed to delete address. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  // Get address icon based on name
  const getAddressIcon = (addressName: string) => {
    const name = addressName.toLowerCase()
    if (name.includes('home')) {
      return <Home className="h-5 w-5 text-blue-600" />
    } else if (name.includes('work') || name.includes('office')) {
      return <Briefcase className="h-5 w-5 text-purple-600" />
    } else {
      return <MapPin className="h-5 w-5 text-gray-600" />
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Authentication Required</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Please sign in to manage your saved addresses.
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Error/Success Messages */}
      {error && (
        <Alert className="bg-red-50 text-red-600 border-red-200">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && (
        <Alert className="bg-emerald-50 text-emerald-600 border-emerald-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Saved Addresses</h1>
          <p className="text-gray-600">
            Manage your delivery addresses for faster checkout
          </p>
        </div>

        <Dialog open={isAddingAddress} onOpenChange={setIsAddingAddress}>
          <DialogTrigger asChild>
            <Button
              className="bg-emerald-600 hover:bg-emerald-700"
              onClick={() => {
                resetForm()
                setIsAddingAddress(true)
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Address
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Address</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleAddAddress} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="address_name">Address Name</Label>
                <Input
                  id="address_name"
                  value={formData.address_name}
                  onChange={(e) => setFormData({...formData, address_name: e.target.value})}
                  placeholder="e.g. Home, Work"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address_line1">Address Line 1</Label>
                <Input
                  id="address_line1"
                  value={formData.address_line1}
                  onChange={(e) => setFormData({...formData, address_line1: e.target.value})}
                  placeholder="Street address"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address_line2">Address Line 2 (Optional)</Label>
                <Input
                  id="address_line2"
                  value={formData.address_line2}
                  onChange={(e) => setFormData({...formData, address_line2: e.target.value})}
                  placeholder="Apartment, suite, etc."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="parish">Parish</Label>
                  <Select
                    value={formData.parish}
                    onValueChange={(value) => setFormData({...formData, parish: value})}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select parish" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="St Helier">St Helier</SelectItem>
                      <SelectItem value="St Saviour">St Saviour</SelectItem>
                      <SelectItem value="St Clement">St Clement</SelectItem>
                      <SelectItem value="St Brelade">St Brelade</SelectItem>
                      <SelectItem value="St Peter">St Peter</SelectItem>
                      <SelectItem value="St Ouen">St Ouen</SelectItem>
                      <SelectItem value="St Mary">St Mary</SelectItem>
                      <SelectItem value="St John">St John</SelectItem>
                      <SelectItem value="St Lawrence">St Lawrence</SelectItem>
                      <SelectItem value="Trinity">Trinity</SelectItem>
                      <SelectItem value="St Martin">St Martin</SelectItem>
                      <SelectItem value="Grouville">Grouville</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="postcode">Postcode</Label>
                  <Input
                    id="postcode"
                    value={formData.postcode}
                    onChange={(e) => setFormData({...formData, postcode: e.target.value})}
                    placeholder="JE2 3NN"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_default"
                  checked={formData.is_default}
                  onCheckedChange={(checked) => setFormData({...formData, is_default: checked})}
                />
                <Label htmlFor="is_default">Set as default address</Label>
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  type="submit"
                  className="flex-1 bg-emerald-600 hover:bg-emerald-700"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Add Address
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddingAddress(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Loading State */}
      {isLoadingAddresses ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {/* Addresses Grid */}
          {addresses.length === 0 ? (
            <Card>
              <CardContent className="py-16 text-center">
                <div className="p-6 bg-gray-100 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                  <MapPin className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                  No saved addresses
                </h3>
                <p className="text-gray-600 mb-8 text-lg max-w-md mx-auto">
                  Add your first address to make ordering faster and easier.
                </p>
                <Button
                  className="bg-emerald-600 hover:bg-emerald-700"
                  onClick={() => {
                    resetForm()
                    setIsAddingAddress(true)
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Address
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {addresses.map((address) => (
                <Card key={address.id} className="relative overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-gray-100 rounded-full">
                          {getAddressIcon(address.address_name)}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{address.address_name}</h3>
                          {address.is_default && (
                            <Badge variant="secondary" className="mt-1 bg-emerald-100 text-emerald-700">
                              <Star className="h-3 w-3 mr-1" />
                              Default
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <div className="space-y-2 mb-4">
                      <p className="text-gray-900 font-medium">{address.address_line1}</p>
                      {address.address_line2 && (
                        <p className="text-gray-600">{address.address_line2}</p>
                      )}
                      <p className="text-gray-600">{address.parish}, {address.postcode}</p>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {!address.is_default && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSetDefault(address.id)}
                          className="text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                          disabled={isProcessing}
                        >
                          <Star className="h-3 w-3 mr-1" />
                          Set as Default
                        </Button>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditAddress(address)}
                        className="text-gray-600 hover:text-gray-900"
                        disabled={isProcessing}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteAddress(address.id)}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        disabled={isProcessing}
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </>
      )}

      {/* Edit Address Dialog */}
      <Dialog open={isEditingAddress} onOpenChange={setIsEditingAddress}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Address</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleUpdateAddress} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_address_name">Address Name</Label>
              <Input
                id="edit_address_name"
                value={formData.address_name}
                onChange={(e) => setFormData({...formData, address_name: e.target.value})}
                placeholder="e.g. Home, Work"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_address_line1">Address Line 1</Label>
              <Input
                id="edit_address_line1"
                value={formData.address_line1}
                onChange={(e) => setFormData({...formData, address_line1: e.target.value})}
                placeholder="Street address"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_address_line2">Address Line 2 (Optional)</Label>
              <Input
                id="edit_address_line2"
                value={formData.address_line2}
                onChange={(e) => setFormData({...formData, address_line2: e.target.value})}
                placeholder="Apartment, suite, etc."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_parish">Parish</Label>
                <Select
                  value={formData.parish}
                  onValueChange={(value) => setFormData({...formData, parish: value})}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parish" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="St Helier">St Helier</SelectItem>
                    <SelectItem value="St Saviour">St Saviour</SelectItem>
                    <SelectItem value="St Clement">St Clement</SelectItem>
                    <SelectItem value="St Brelade">St Brelade</SelectItem>
                    <SelectItem value="St Peter">St Peter</SelectItem>
                    <SelectItem value="St Ouen">St Ouen</SelectItem>
                    <SelectItem value="St Mary">St Mary</SelectItem>
                    <SelectItem value="St John">St John</SelectItem>
                    <SelectItem value="St Lawrence">St Lawrence</SelectItem>
                    <SelectItem value="Trinity">Trinity</SelectItem>
                    <SelectItem value="St Martin">St Martin</SelectItem>
                    <SelectItem value="Grouville">Grouville</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_postcode">Postcode</Label>
                <Input
                  id="edit_postcode"
                  value={formData.postcode}
                  onChange={(e) => setFormData({...formData, postcode: e.target.value})}
                  placeholder="JE2 3NN"
                  required
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="edit_is_default"
                checked={formData.is_default}
                onCheckedChange={(checked) => setFormData({...formData, is_default: checked})}
              />
              <Label htmlFor="edit_is_default">Set as default address</Label>
            </div>

            <div className="flex space-x-3 pt-4">
              <Button
                type="submit"
                className="flex-1 bg-emerald-600 hover:bg-emerald-700"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Update Address
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditingAddress(false)
                  setEditingAddress(null)
                  resetForm()
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-blue-900 mb-2">💡 Tips for managing addresses</h3>
          <ul className="text-blue-800 space-y-1 text-sm">
            <li>• Set a default address for faster checkout</li>
            <li>• Include apartment/flat numbers for accurate delivery</li>
            <li>• Add both home and work addresses for convenience</li>
            <li>• Keep your addresses up to date for successful deliveries</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
