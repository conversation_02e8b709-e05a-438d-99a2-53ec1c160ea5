"use client"

import { useState, useMemo } from "react"
import { ShoppingBag, X, ChevronUp, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import CheckoutLink from "@/components/checkout-link"
import { getBusinessTypeIconWithStyle } from "@/utils/business-type-icons"

interface FloatingCartProps {
  businessId: number
  businessSlug: string
  businessName: string
  businessType?: string
  deliveryTime?: string
  deliveryFee?: string
  minimumOrder?: number
}

function formatItemDetails(item: any): string[] {
  const details: string[] = []

  if (item.weight_class_kg && item.weight_class_kg > 0) {
    details.push(`${item.weight_class_kg}kg`)
  }

  if (item.size && item.size !== 'regular') {
    details.push(item.size)
  }

  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        customization.options.forEach((option: any) => {
          details.push(option.name)
        })
      }
    })
  }

  return details
}

export default function FloatingCart({
  businessId,
  businessSlug,
  businessName,
  businessType,
  deliveryTime,
  deliveryFee,
  minimumOrder
}: FloatingCartProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const { getItemsByBusiness, getDeliveryMethod, getDeliveryFee } = useRealtimeCart()

  // Get items for this business
  const businessItems = useMemo(() => {
    const allItemsByBusiness = getItemsByBusiness()
    return allItemsByBusiness[businessId.toString()] || []
  }, [getItemsByBusiness, businessId])

  // Calculate subtotal
  const businessSubtotal = useMemo(() => {
    return businessItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  }, [businessItems])

  // Get delivery method and fee from cart context
  const deliveryMethod = getDeliveryMethod(businessId)
  const currentDeliveryFee = getDeliveryFee(businessId)

  // Use cart context fee for calculations, fallback to static prop for display only
  const deliveryFeeNumeric = useMemo(() => {
    if (deliveryMethod === 'pickup') return 0
    if (deliveryMethod === 'delivery') return currentDeliveryFee

    // Fallback to parsing static prop if no delivery method set yet
    if (!deliveryFee) return 0
    if (deliveryFee.toLowerCase().includes('free')) return 0
    const match = deliveryFee.match(/£?(\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  }, [deliveryMethod, currentDeliveryFee, deliveryFee])

  // Calculate total
  const total = businessSubtotal + (businessItems.length > 0 ? deliveryFeeNumeric + 0.5 : 0)

  // Don't show if no items
  if (businessItems.length === 0) {
    return null
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Expanded Cart */}
      {isExpanded && (
        <div className="mb-4 bg-white rounded-lg shadow-lg border border-gray-200 w-80 max-h-[calc(100vh-200px)] overflow-hidden flex flex-col">
          {/* Header - Fixed */}
          <div className="p-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {getBusinessTypeIconWithStyle(businessType, "h-5 w-5 mr-2 text-emerald-600")}
                <h3 className="font-semibold text-sm">Your Order</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Items List - Scrollable */}
          <div className="p-4 flex-1 overflow-y-auto min-h-0">
            {businessItems.map((item) => (
              <div key={item.cartItemId || `${item.id}-${Math.random()}`} className="flex justify-between mb-3">
                <div className="flex-1">
                  <p className="font-medium text-sm">
                    {item.quantity}x {item.name}
                  </p>
                  {(() => {
                    const details = formatItemDetails(item);
                    return details.length > 0 && (
                      <p className="text-xs text-gray-500">{details.join(", ")}</p>
                    );
                  })()}
                </div>
                <p className="font-medium text-sm">£{(item.price * item.quantity).toFixed(2)}</p>
              </div>
            ))}
          </div>

          {/* Summary and Checkout - Fixed at bottom */}
          <div className="p-4 border-t border-gray-200 space-y-2 flex-shrink-0 bg-white">
            <div className="flex justify-between text-sm">
              <span>Subtotal</span>
              <span>£{businessSubtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Delivery Fee</span>
              <span>
                {deliveryMethod === 'pickup'
                  ? 'Free'
                  : deliveryFeeNumeric === 0
                    ? 'Free'
                    : `£${deliveryFeeNumeric.toFixed(2)}`
                }
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Service Fee</span>
              <span>£0.50</span>
            </div>
            <div className="flex justify-between font-semibold pt-2 border-t">
              <span>Total</span>
              <span>£{total.toFixed(2)}</span>
            </div>

            <CheckoutLink
              buttonClassName={`w-full mt-3 ${
                deliveryMethod === 'pickup'
                  ? 'bg-orange-600 hover:bg-orange-700'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              Go to checkout
            </CheckoutLink>
          </div>
        </div>
      )}

      {/* Floating Cart Button */}
      <Button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`rounded-full h-14 px-6 shadow-lg ${
          deliveryMethod === 'pickup'
            ? 'bg-orange-600 hover:bg-orange-700'
            : 'bg-blue-600 hover:bg-blue-700'
        }`}
      >
        <div className="flex items-center space-x-2">
          {getBusinessTypeIconWithStyle(businessType, "h-5 w-5")}
          <div className="text-left">
            <div className="text-sm font-medium">
              {businessItems.length} item{businessItems.length !== 1 ? 's' : ''}
            </div>
            <div className="text-xs opacity-90">£{total.toFixed(2)}</div>
          </div>
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronUp className="h-4 w-4" />
          )}
        </div>
      </Button>
    </div>
  )
}
