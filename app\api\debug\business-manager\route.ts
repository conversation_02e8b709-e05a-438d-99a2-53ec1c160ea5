import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    // Get the user ID from the query parameters
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const email = url.searchParams.get('email')
    
    if (!userId && !email) {
      return NextResponse.json(
        { error: "Either userId or email parameter is required" },
        { status: 400 }
      )
    }
    
    // Get user data
    let userData
    if (email) {
      const { data: user, error: userError } = await adminClient
        .from("users")
        .select("*")
        .eq("email", email)
        .single()
        
      if (userError) {
        return NextResponse.json(
          { error: "Error fetching user data", details: userError },
          { status: 500 }
        )
      }
      
      userData = user
    } else {
      const { data: user, error: userError } = await adminClient
        .from("users")
        .select("*")
        .eq("id", userId)
        .single()
        
      if (userError) {
        return NextResponse.json(
          { error: "Error fetching user data", details: userError },
          { status: 500 }
        )
      }
      
      userData = user
    }
    
    // Get business manager data
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("*")
      .eq("user_id", userData.id)
      
    // Get business data
    let businessData = []
    if (managerData && managerData.length > 0) {
      const businessIds = managerData.map(manager => manager.business_id)
      const { data: businesses, error: businessError } = await adminClient
        .from("businesses")
        .select("*")
        .in("id", businessIds)
        
      if (!businessError) {
        businessData = businesses
      }
    }
    
    return NextResponse.json({
      user: userData,
      businessManager: managerData,
      businesses: businessData
    })
  } catch (error: any) {
    console.error("Error in debug business manager API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
