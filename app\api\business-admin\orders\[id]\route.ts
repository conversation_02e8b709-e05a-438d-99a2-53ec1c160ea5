import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Helper function to enhance cart items with customizations and variant names
async function enhanceItemsWithDetails(adminClient: any, items: any[]) {
  if (!items || items.length === 0) {
    return []
  }

  // Get customizations for all cart items
  const cartItemIds = items.map(item => item.id)
  const { data: customizations, error: customizationsError } = await adminClient
    .from("cart_item_customizations")
    .select(`
      cart_item_id,
      customization_group_name,
      customization_option_name,
      price
    `)
    .in("cart_item_id", cartItemIds)

  if (customizationsError) {
    console.error("Error fetching customizations:", customizationsError)
  }

  // Get variant names for items that have variants
  const variantIds = items.filter(item => item.variant_id).map(item => item.variant_id)
  let variants = []
  if (variantIds.length > 0) {
    const { data: variantData, error: variantError } = await adminClient
      .from("product_variants")
      .select("id, name")
      .in("id", variantIds)

    if (variantError) {
      console.error("Error fetching variants:", variantError)
    } else {
      variants = variantData || []
    }
  }

  // Combine items with their customizations and variant names
  return items.map(item => {
    // Get customizations for this item
    const itemCustomizations = (customizations || []).filter(c => c.cart_item_id === item.id)

    // Group customizations by group name
    const groupedCustomizations: Record<string, any[]> = {}
    itemCustomizations.forEach(customization => {
      if (!groupedCustomizations[customization.customization_group_name]) {
        groupedCustomizations[customization.customization_group_name] = []
      }
      groupedCustomizations[customization.customization_group_name].push({
        name: customization.customization_option_name,
        price: customization.price
      })
    })

    // Convert to array format
    const customizationGroups = Object.keys(groupedCustomizations).map(groupName => ({
      groupName,
      options: groupedCustomizations[groupName]
    }))

    // Get variant name if applicable
    const variant = variants.find(v => v.id === item.variant_id)
    const variantName = variant?.name

    return {
      ...item,
      variantName,
      customizations: customizationGroups
    }
  })
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // TEMPORARILY DISABLED: Authentication for development
    console.log("Authentication DISABLED for development - allowing all access")

    // Mock user profile for development
    const userProfile = { id: 1, role: "admin", auth_id: "dev-user" }
    const isBusinessManager = false
    const isBusinessStaff = false
    const isAdmin = true
    const isSuperAdmin = false

    const orderId = parseInt(params.id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: "Invalid order ID" },
        { status: 400 }
      )
    }

    // For admin users, allow viewing any order
    if (isAdmin || isSuperAdmin) {
      // Use orders table directly with manual joins
      const { data: order, error: orderError } = await adminClient
        .from("orders")
        .select(`
          *,
          businesses (id, name, address, phone),
          order_status_history(*),
          order_payment_allocations(*)
        `)
        .eq("id", orderId)
        .single()

      if (orderError) {
        console.error("Error fetching order:", orderError)
        return NextResponse.json(
          { error: "Order not found" },
          { status: 404 }
        )
      }

      // Get order items from cart_items table using cart_id
      const { data: items, error: itemsError } = await adminClient
        .from("cart_items")
        .select("*")
        .eq("cart_id", order.cart_id)

      if (itemsError) {
        console.error("Error fetching cart items:", itemsError)
        return NextResponse.json({
          order,
          items: []
        })
      }

      // Enhance items with customizations and variant names
      const enhancedItems = await enhanceItemsWithDetails(adminClient, items || [])

      return NextResponse.json({
        order,
        items: enhancedItems
      })
    }

    // For business users, verify they own the order
    let businessId: number | null = null

    // Try to find business association in both tables regardless of role
    // This handles data inconsistencies where role doesn't match table placement

    // First try business_managers table
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (!managerError && managerData) {
      businessId = managerData.business_id
      console.log(`Found user ${userProfile.id} in business_managers table for business ${businessId}`)
    }

    // If not found in managers, try business_staff table
    if (!businessId) {
      const { data: staffData, error: staffError } = await adminClient
        .from("business_staff")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (!staffError && staffData) {
        businessId = staffData.business_id
        console.log(`Found user ${userProfile.id} in business_staff table for business ${businessId}`)
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: "No business found for this user" },
        { status: 404 }
      )
    }

    // Verify the order belongs to this business
    const { data: order, error: orderError } = await adminClient
      .from("orders")
      .select("id, business_id")
      .eq("id", orderId)
      .single()

    if (orderError) {
      console.error("Error fetching order:", orderError)
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.business_id !== businessId) {
      return NextResponse.json(
        { error: "You do not have permission to view this order" },
        { status: 403 }
      )
    }

    // Fetch order details directly from orders table (no problematic views/joins)
    const { data: orderData, error: orderDataError } = await adminClient
      .from("orders")
      .select("*")
      .eq("id", orderId)
      .single()

    if (orderDataError) {
      console.error("Error fetching order:", orderDataError)
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    // Get cart items using cart_id from the order
    const { data: items, error: itemsError } = await adminClient
      .from("cart_items")
      .select("*")
      .eq("cart_id", orderData.cart_id)

    if (itemsError) {
      console.error("Error fetching cart items:", itemsError)
      return NextResponse.json({
        order: orderData,
        items: []
      })
    }

    // Enhance items with customizations and variant names
    const itemsWithDetails = await enhanceItemsWithDetails(adminClient, items || [])

    return NextResponse.json({
      order: orderData,
      items: itemsWithDetails
    })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/orders/[id]:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
