import { NextResponse } from "next/server"
import { createServerSupabase } from "@/lib/supabase-server"

export async function GET(request: Request) {
  try {
    // Get the user's session
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error("Error verifying session:", error)
      return NextResponse.json(
        {
          authenticated: false,
          error: error.message
        },
        { status: 401 }
      )
    }

    if (!session) {
      return NextResponse.json(
        {
          authenticated: false,
          message: "No active session found"
        },
        { status: 401 }
      )
    }

    // Session exists, return user info
    return NextResponse.json({
      authenticated: true,
      user: {
        id: session.user.id,
        email: session.user.email,
        created_at: session.user.created_at
      }
    })
  } catch (error: any) {
    console.error("Unexpected error in verify-session:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: error.message || "An unexpected error occurred"
      },
      { status: 500 }
    )
  }
}
