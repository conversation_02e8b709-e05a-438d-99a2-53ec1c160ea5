import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// POST endpoint to create or update a guest cart
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { sessionId, cart, businessNames, preparationTimes, deliveryTimes, deliveryFees, deliveryMethods, deliveryType, scheduledTimes } = body;

    console.log(`GUEST-CART-API: Received request with sessionId: ${sessionId ? 'present' : 'missing'}, cart: ${cart ? (Array.isArray(cart) ? `array with ${cart.length} items` : 'not an array') : 'missing'}`);

    if (preparationTimes) {
      console.log(`GUEST-CART-API: Preparation times: ${JSON.stringify(preparationTimes)}`);
    }

    if (deliveryTimes) {
      console.log(`GUEST-CART-API: Delivery times: ${JSON.stringify(deliveryTimes)}`);
    }

    if (deliveryFees) {
      console.log(`GUEST-CART-API: Delivery fees: ${JSON.stringify(deliveryFees)}`);
    }

    if (deliveryMethods) {
      console.log(`GUEST-CART-API: Delivery methods: ${JSON.stringify(deliveryMethods)}`);
    }

    if (deliveryType) {
      console.log(`GUEST-CART-API: Delivery types: ${JSON.stringify(deliveryType)}`);
    }

    if (scheduledTimes) {
      console.log(`GUEST-CART-API: Scheduled times: ${JSON.stringify(scheduledTimes)}`);
    }

    if (!sessionId || !cart || !Array.isArray(cart)) {
      console.log(`GUEST-CART-API: Invalid request - sessionId: ${!!sessionId}, cart: ${!!cart}, isArray: ${Array.isArray(cart)}`);
      return NextResponse.json(
        { error: 'Valid session ID and cart data are required' },
        { status: 400 }
      );
    }

    console.log(`GUEST-CART-API: Processing cart for session ${sessionId} with ${cart.length} items`);

    // Check if a cart already exists for this session
    const { data: existingCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id, preparation_times, delivery_times, delivery_fees, delivery_type, scheduled_times')
      .eq('session_id', sessionId)
      .maybeSingle();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching guest cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch guest cart', details: cartError.message },
        { status: 500 }
      );
    }

    let cartId;

    if (existingCart) {
      cartId = existingCart.id;
      console.log(`GUEST-CART-API: Found existing cart with ID ${cartId}`);

      // Update preparation times, delivery times, delivery fees, delivery methods, delivery types, and scheduled times if provided
      if (preparationTimes || deliveryTimes || deliveryFees || deliveryMethods || deliveryType || scheduledTimes) {
        const updateData: any = {};

        if (preparationTimes) {
          updateData.preparation_times = preparationTimes;
        }

        if (deliveryTimes) {
          updateData.delivery_times = deliveryTimes;
        }

        if (deliveryFees) {
          updateData.delivery_fees = deliveryFees;
        }

        if (deliveryMethods) {
          updateData.delivery_method = deliveryMethods;
        }

        if (deliveryType) {
          updateData.delivery_type = deliveryType;
        }

        if (scheduledTimes) {
          updateData.scheduled_times = scheduledTimes;
        }

        if (Object.keys(updateData).length > 0) {
          const { error: updateTimesError } = await supabase
            .from('user_carts')
            .update(updateData)
            .eq('id', cartId);

          if (updateTimesError) {
            console.error('Error updating cart metadata:', updateTimesError);
            // Continue with the rest of the operation, don't return an error
          }
        }
      }

      // Clear existing cart items
      const { error: clearError } = await supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cartId);

      if (clearError) {
        console.error('Error clearing cart items:', clearError);
        return NextResponse.json(
          { error: 'Failed to clear cart items', details: clearError.message },
          { status: 500 }
        );
      }
    } else {
      // Create a new cart
      const { data: newCart, error: createCartError } = await supabase
        .from('user_carts')
        .insert([{
          session_id: sessionId,
          user_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          meta: { businessNames },
          preparation_times: preparationTimes || {},
          delivery_times: deliveryTimes || {},
          delivery_fees: deliveryFees || {},
          delivery_method: deliveryMethods || {},
          delivery_type: deliveryType || {},
          scheduled_times: scheduledTimes || {}
        }])
        .select('id')
        .single();

      if (createCartError) {
        console.error('Error creating guest cart:', createCartError);
        return NextResponse.json(
          { error: 'Failed to create guest cart', details: createCartError.message },
          { status: 500 }
        );
      }

      cartId = newCart.id;
      console.log(`GUEST-CART-API: Created new cart with ID ${cartId}`);
    }

    // If cart is empty, just return success
    if (cart.length === 0) {
      return NextResponse.json({
        success: true,
        cartId,
        message: 'Cart updated successfully (empty cart)'
      });
    }

    // Insert cart items
    console.log(`GUEST-CART-API: Preparing to insert ${cart.length} items into cart_items table`);

    try {
      const cartItems = cart.map(item => {
        // Ensure business_id is a number (required by the updated schema)
        let businessId = item.businessId;
        let businessSlug = item.businessSlug || '';

        // If businessId is a number, use it directly
        if (typeof businessId === 'number') {
          // Keep businessId as is
          console.log(`GUEST-CART-API: Business ID is already a number: ${businessId}`);
        }
        // If businessId is a string but looks like a number, convert it
        else if (typeof businessId === 'string' && !isNaN(parseInt(businessId))) {
          console.log(`GUEST-CART-API: Converting business ID from string to number: ${businessId} -> ${parseInt(businessId)}`);
          businessId = parseInt(businessId);
        }
        // If businessId is a string and doesn't look like a number, try to find the numeric ID
        else if (typeof businessId === 'string') {
          console.error(`GUEST-CART-API: Invalid business ID: ${businessId} (type: ${typeof businessId}). Using fallback ID.`);
          businessSlug = businessId; // Store the original string as the slug

          // For now, use a default ID
          // In a production environment, we would look up the business by slug
          businessId = 1; // Default to first business
        }

        // Ensure all required fields are present and have valid values
        const cartItem = {
          cart_id: cartId,
          product_id: parseInt(item.id, 10) || 0,
          variant_id: item.variantId || null,
          quantity: item.quantity || 1,
          business_id: businessId,
          business_slug: businessSlug,
          business_type: item.businessType || 'restaurant',
          name: item.name || 'Unknown Item',
          price: typeof item.price === 'number' ? item.price : 0,
          image_url: item.imageUrl || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log(`GUEST-CART-API: Prepared cart item: ${JSON.stringify(cartItem)}`);
        return cartItem;
      });

      console.log(`GUEST-CART-API: Inserting ${cartItems.length} items into cart_items table`);

      const { error: itemsError } = await supabase
        .from('cart_items')
        .insert(cartItems);

      if (itemsError) {
        console.error('GUEST-CART-API: Error inserting cart items:', itemsError);
        throw itemsError;
      }
    } catch (insertError) {
      console.error('GUEST-CART-API: Exception inserting cart items:', insertError);
      return NextResponse.json(
        { error: 'Failed to insert cart items', details: insertError.message || 'Unknown error' },
        { status: 500 }
      );
    }

    // Get the updated cart items
    const { data: updatedCartItems, error: updatedItemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId);

    if (updatedItemsError) {
      console.error('Error fetching updated cart items:', updatedItemsError);
      return NextResponse.json({
        success: true,
        cartId,
        message: 'Cart updated successfully, but failed to fetch updated items'
      });
    }

    // Format the cart items for the response
    const formattedItems = updatedCartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id, // Now a numeric ID
      businessSlug: item.business_slug || '', // Include the slug
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url
    }));

    return NextResponse.json({
      success: true,
      cartId,
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames,
      preparationTimes: preparationTimes || {},
      deliveryTimes: deliveryTimes || {},
      deliveryFees: deliveryFees || {},
      deliveryMethods: deliveryMethods || {}
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch a guest cart
export async function GET(request: NextRequest) {
  try {
    // Get the session ID from the request headers
    const sessionId = request.headers.get('X-Session-ID');

    if (!sessionId) {
      console.log('GUEST-CART-API: No session ID provided in request headers');
      // Return empty cart instead of error for better user experience
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {}
      });
    }

    console.log(`GUEST-CART-API: Fetching cart for session ${sessionId}`);

    // Get the cart for this session
    const { data: userCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id, meta, preparation_times, delivery_times, delivery_fees, delivery_type')
      .eq('session_id', sessionId)
      .maybeSingle();

    if (cartError) {
      console.error('Error fetching guest cart:', cartError);
      // Return empty cart instead of error for better user experience
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {}
      });
    }

    // If no cart exists, return empty cart
    if (!userCart) {
      console.log(`GUEST-CART-API: No cart found for session ${sessionId}`);
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {}
      });
    }

    // Get the cart items
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', userCart.id);

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart items', details: itemsError.message },
        { status: 500 }
      );
    }

    // Format the cart items for the response
    const formattedItems = cartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id, // Now a numeric ID
      businessSlug: item.business_slug || '', // Include the slug
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url,
      // Include delivery attributes
      weight_class_kg: item.weight_class_kg,
      thermal_requirement: item.thermal_requirement,
      size_category: item.size_category
    }));

    // Get business names from cart meta
    const businessNames = userCart.meta?.businessNames || {};

    // Return the cart data
    return NextResponse.json({
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames,
      preparationTimes: userCart.preparation_times || {},
      deliveryTimes: userCart.delivery_times || {},
      deliveryFees: userCart.delivery_fees || {},
      deliveryMethods: userCart.delivery_type ? JSON.parse(userCart.delivery_type) : {}
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clear a guest cart
export async function DELETE(request: NextRequest) {
  try {
    // Get the session ID from the request headers
    const sessionId = request.headers.get('X-Session-ID');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    console.log(`GUEST-CART-API: Clearing cart for session ${sessionId}`);

    // Get the cart for this session
    const { data: userCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('session_id', sessionId)
      .maybeSingle();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching guest cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch guest cart', details: cartError.message },
        { status: 500 }
      );
    }

    // If no cart exists, return success (nothing to clear)
    if (!userCart) {
      return NextResponse.json({
        success: true,
        cleared: 0
      });
    }

    // Clear the cart items
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('cart_id', userCart.id)
      .select('id');

    if (error) {
      console.error('Error clearing cart items:', error);
      return NextResponse.json(
        { error: 'Failed to clear cart items', details: error.message },
        { status: 500 }
      );
    }

    // Also clear the cart context values in user_carts
    const { error: updateError } = await supabase
      .from('user_carts')
      .update({
        delivery_method: {},
        delivery_type: {},
        delivery_fees: {},
        preparation_times: {},
        delivery_times: {},
        scheduled_times: {},
        updated_at: new Date().toISOString()
      })
      .eq('id', userCart.id);

    if (updateError) {
      console.error('Error clearing cart context values:', updateError);
      // Don't return error, just log it since cart items were cleared successfully
    }

    return NextResponse.json({
      success: true,
      cleared: data ? data.length : 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
