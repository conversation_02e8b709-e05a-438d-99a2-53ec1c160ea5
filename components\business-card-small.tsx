import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>, Clock, DollarSign } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import FallbackImage from "@/components/fallback-image"
import type { Restaurant, Shop } from "@/types/business"
import DynamicDeliveryTime from "@/components/dynamic-delivery-time"
import { ParishName } from "@/lib/parish-distances"

interface BusinessCardSmallProps {
  business: any
}

export default function BusinessCardSmall({ business }: BusinessCardSmallProps) {
  // Get the business attributes based on type
  let attributes: string[] = [];
  let businessType = "business";
  let linkUrl = `/businesses/${business.id}`;

  if ("cuisines" in business && business.cuisines) {
    attributes = business.cuisines;
    businessType = "restaurant";
    linkUrl = `/restaurants/${business.id}`;
  } else if ("storeTypes" in business && business.storeTypes) {
    attributes = business.storeTypes;
    businessType = "shop";
    linkUrl = `/shops/${business.id}`;
  } else if ("serviceTypes" in business && business.serviceTypes) {
    attributes = business.serviceTypes;
    businessType = "lift";
    linkUrl = `/lift/${business.id}`;
  } else if ("errandTypes" in business && business.errandTypes) {
    attributes = business.errandTypes;
    businessType = "errand";
    linkUrl = `/errand/${business.id}`;
  } else if (business.business_types && business.business_types.slug) {
    // Fallback to business type from database
    businessType = business.business_types.slug;
    linkUrl = `/${businessType}s/${business.id}`;

    // Try to find any attributes
    if (business.attributes) {
      attributes = business.attributes;
    } else {
      attributes = ["General"];
    }
  }

  return (
    <Link href={linkUrl}>
      <Card className="overflow-hidden h-full transition-all hover:shadow-lg">
        <div className="relative h-36 bg-white">
          <FallbackImage
            src={business.logo_url || business.image || business.coverImage}
            alt={`${business.name} logo`}
            fallbackSrc="/placeholder.svg"
            className="w-full h-full object-contain p-2"
          />
          {business.isNew && <Badge className="absolute top-2 left-2 bg-emerald-600 text-xs py-0.5 px-2">New</Badge>}
          {business.offer && <Badge className="absolute top-2 right-2 bg-orange-500 text-xs py-0.5 px-2">{business.offer}</Badge>}
        </div>
        <CardContent className="p-3">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-bold text-base truncate max-w-[70%]">{business.name}</h3>
            <div className="flex items-center bg-green-50 px-2 py-0.5 rounded text-sm">
              <Star className="h-4 w-4 text-yellow-500 mr-1" />
              <span>{business.rating}</span>
            </div>
          </div>

          <p className="text-gray-500 text-sm mb-2">
            {Array.isArray(attributes) && attributes.length > 0 ? attributes.join(", ") : "General"}
          </p>

          {/* Delivery Information Card */}
          <div className="mt-3 bg-gray-50 p-3 rounded-md">
            {/* Card Header */}
            <div className="flex items-center mb-2">
              <div className="bg-emerald-50 p-1 rounded-full mr-2">
                <Bike className="h-3.5 w-3.5 text-emerald-600" />
              </div>
              <span className="text-xs font-medium text-gray-700">Delivery Information</span>
            </div>

            {/* Delivery Details Row */}
            <div className="flex justify-between items-center mt-1">
              {/* Delivery Time */}
              <div className="flex items-center">
                <div className="bg-gray-100 p-1 rounded-full mr-1.5">
                  <Clock className="h-3 w-3 text-gray-600" />
                </div>
                <DynamicDeliveryTime
                  businessCoordinates={
                    business.coordinates && Array.isArray(business.coordinates)
                      ? business.coordinates as [number, number]
                      : [-2.1053, 49.1805] // Default to St Helier
                  }
                  businessParish={business.location as ParishName}
                  preparationTimeMinutes={business.preparationTimeMinutes || 15}
                  defaultDeliveryTime={
                    typeof business.delivery_time_minutes === 'number'
                      ? business.delivery_time_minutes
                      : typeof business.deliveryTime === 'number'
                        ? business.deliveryTime
                        : 20
                  }
                  showTimeRange={true}
                  deliveryAvailable={business.delivery_available !== false} // Default to true if not specified
                />
              </div>

              {/* Delivery Fee or Pick-up Only */}
              <div className="flex items-center">
                {business.delivery_available !== false ? (
                  <>
                    <div className="bg-gray-100 p-1 rounded-full mr-1.5">
                      <DollarSign className="h-3 w-3 text-gray-600" />
                    </div>
                    <div className={`text-xs font-medium ${business.deliveryFee === 0 ? 'text-emerald-600' : 'text-gray-700'}`}>
                      {business.deliveryFee === 0 ? (
                        "Free"
                      ) : (
                        `£${typeof business.deliveryFee === 'number' ? business.deliveryFee.toFixed(2) : '2.00'}`
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="bg-gray-100 p-1 rounded-full mr-1.5">
                      <span className="text-gray-600 text-xs">🚶‍♂️📦</span>
                    </div>
                    <div className="text-xs font-medium text-orange-600">
                      Pick-up only
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
