"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, Clock, MapPin, Receipt, Home, User } from "lucide-react"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

export default function OrderConfirmationPage() {
  const [orderId] = useState("JE-" + Math.floor(1000 + Math.random() * 9000));
  
  return (
    <div className="container max-w-5xl py-8 mx-auto">
      <div className="flex flex-col items-center text-center mb-8">
        <div className="h-20 w-20 rounded-full bg-emerald-100 flex items-center justify-center mb-5 shadow-sm">
          <div className="animate-spin" style={{ animation: 'spin 2s ease-in-out' }}>
            <WheelLogoIcon size={40} color="#059669" className="text-emerald-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-800">Order Confirmed!</h1>
        <p className="text-gray-600 mt-3 max-w-lg">
          Your order <span className="font-medium">{orderId}</span> has been received and is being prepared.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mx-auto">
        <div className="md:col-span-2 space-y-6">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="text-lg font-semibold text-gray-800">Order Details</CardTitle>
              <CardDescription>Order ID: <span className="font-medium">{orderId}</span></CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <h3 className="font-medium text-gray-800">Jersey Wings</h3>
                      <p className="text-sm text-gray-600">Restaurant</p>
                    </div>
                  </div>
                  
                  <Separator className="my-3" />
                  
                  <div className="space-y-3">
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <div className="flex">
                        <span className="font-medium text-blue-700">1×</span>
                        <span className="ml-2 text-gray-800">Classic Wings</span>
                      </div>
                      <span className="font-medium text-gray-800">£8.99</span>
                    </div>
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <div className="flex">
                        <span className="font-medium text-blue-700">1×</span>
                        <span className="ml-2 text-gray-800">Fries</span>
                      </div>
                      <span className="font-medium text-gray-800">£3.50</span>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3 pt-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="text-gray-800">£12.49</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Delivery Fee</span>
                    <span className="text-gray-800">£2.50</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Service Fee</span>
                    <span className="text-gray-800">£0.50</span>
                  </div>
                  <Separator className="my-1 bg-gray-200" />
                  <div className="flex justify-between font-bold text-base pt-1">
                    <span>Total</span>
                    <span className="text-blue-700">£15.49</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="text-lg font-semibold text-gray-800">Delivery Information</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-5">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Customer Name</p>
                  <div className="flex items-start">
                    <User className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800 font-medium">Guest User</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Delivery Address</p>
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-800">15 Beachfront, St Helier, JE2 3NG</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Estimated Delivery Time</p>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-blue-600 mr-2" />
                    <p className="text-gray-800 font-medium">
                      Today at {new Date(Date.now() + 45 * 60000).toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })}
                    </p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Payment Method</p>
                  <p className="text-gray-800 capitalize">Credit Card</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="text-lg font-semibold text-gray-800">Need Help?</CardTitle>
            </CardHeader>
            <CardContent className="pt-4 pb-2">
              <div className="grid grid-cols-1 gap-3">
                <Button variant="outline" className="w-full justify-start border-gray-300 hover:bg-gray-50 transition-colors">
                  <Receipt className="mr-2 h-5 w-5 text-blue-600" />
                  <span>View Receipt</span>
                </Button>
              </div>
            </CardContent>
            <CardFooter className="pt-3 pb-4">
              <div className="w-full space-y-4">
                <Separator className="bg-gray-200" />
                <div className="flex flex-wrap gap-3 justify-between items-center w-full">
                  <Link href="/" className="flex-shrink-0">
                    <Button variant="outline" className="border-gray-300 hover:bg-gray-50 transition-colors">
                      <Home className="mr-2 h-4 w-4 text-gray-600" />
                      Return Home
                    </Button>
                  </Link>
                  <Link href="/businesses" className="flex-shrink-0">
                    <Button className="bg-emerald-600 hover:bg-emerald-700 transition-colors">
                      Order More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
