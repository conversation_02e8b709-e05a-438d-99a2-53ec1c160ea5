import { AlertCircle } from "lucide-react"

export default function OrderFailedLoading() {
  return (
    <div className="container max-w-5xl py-8 mx-auto text-center">
      <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center mb-5 mx-auto">
        <AlertCircle className="h-10 w-10 text-red-600" />
      </div>
      <h1 className="text-3xl font-bold text-gray-800">Processing Order Status</h1>
      <p className="text-gray-600 mt-3 max-w-lg mx-auto">
        Please wait while we check the status of your order...
      </p>
      
      <div className="mt-8 flex justify-center">
        <div className="animate-pulse flex space-x-4">
          <div className="h-3 w-3 bg-red-400 rounded-full"></div>
          <div className="h-3 w-3 bg-red-400 rounded-full"></div>
          <div className="h-3 w-3 bg-red-400 rounded-full"></div>
        </div>
      </div>
    </div>
  )
}
