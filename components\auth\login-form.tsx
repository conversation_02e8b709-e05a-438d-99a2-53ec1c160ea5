"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Lock, Mail } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/context/unified-auth-context"

export default function LoginForm() {
  const router = useRouter()
  const { signIn } = useAuth()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    try {
      console.log("Login form: Attempting to sign in with email:", email)

      // Use the unified auth context for sign in
      const { data, error } = await signIn(email, password)

      if (error) {
        console.error("Login form: Login error:", error)

        // Handle email confirmation error specifically
        if (error.message?.includes('email not confirmed') || error.message?.includes('Email not confirmed')) {
          setError("Please check your email and click the confirmation link before signing in. Check your spam folder if you don't see it.")
        } else {
          setError(error.message)
        }

        setIsLoading(false)
        return
      }

      console.log("Login form: Login successful, session established:", data?.session ? "Yes" : "No")

      if (!data?.session) {
        console.error("Login form: No session returned after successful login")
        setError("Authentication succeeded but no session was created. Please try again.")
        setIsLoading(false)
        return
      }

      // Check if we're trying to access a protected page
      const params = new URLSearchParams(window.location.search)
      const returnUrl = params.get('returnUrl') || params.get('redirect') || params.get('redirectTo')
      const redirectTo = returnUrl && returnUrl.startsWith('/') ? returnUrl : "/account/profile" // Default to account profile page

      console.log("Login form: Login successful, will redirect to:", redirectTo)

      // Dispatch an event to notify the app that a user has logged in
      window.dispatchEvent(new CustomEvent('auth-login-success', {
        detail: {
          user: data.session.user,
          session: data.session
        }
      }));

      // Add a small delay to ensure auth state is properly established
      setTimeout(() => {
        try {
          console.log("Login form: Attempting redirect to:", redirectTo);
          // Use Next.js router for better navigation
          router.push(redirectTo);
        } catch (redirectError) {
          console.error("Login form: Error during redirect:", redirectError);
          // Fallback to window.location if router fails
          window.location.href = redirectTo;
        }
      }, 200); // Reduced timeout for faster redirect

      // Set a fallback timeout in case redirect fails
      setTimeout(() => {
        if (isLoading) {
          console.warn("Login form: Redirect may have failed, clearing loading state");
          setIsLoading(false);
          setError("Login successful but redirect failed. Please navigate manually.");
        }
      }, 2000);

    } catch (err) {
      console.error("Login form: Unexpected login error:", err)
      setError("An unexpected error occurred. Please try again.")
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Login</CardTitle>
        <CardDescription>Enter your credentials to access your account</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert
            variant="destructive"
            className="mb-4 cursor-pointer"
            onClick={() => setError(null)}
          >
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading && !error && (
          <Alert className="mb-4 bg-blue-50 border-blue-200">
            <AlertDescription className="text-blue-800">
              Signing in... Please wait while we redirect you.
            </AlertDescription>
          </Alert>
        )}
        <form onSubmit={handleLogin}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="/reset-password" className="text-sm font-medium text-emerald-600 hover:text-emerald-700">
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <Button type="submit" className="w-full bg-emerald-600 hover:bg-emerald-700" disabled={isLoading}>
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-center">
        <p className="text-sm text-gray-500">
          Don&apos;t have an account?{" "}
          <Link href="/register" className="text-emerald-600 hover:text-emerald-700 font-medium">
            Sign up
          </Link>
        </p>
      </CardFooter>
    </Card>
  )
}
