import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const period = searchParams.get('period') || 'all'

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Build query for orders assigned to this driver
    let query = supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        customer_name,
        delivery_address,
        postcode,
        delivery_fee,
        total,
        status,
        created_at,
        updated_at,
        ready_time,
        estimated_delivery_time,
        delivery_distance_km,
        delivery_instructions,
        payment_method,
        payment_status
      `, { count: 'exact' })
      .eq('driver_id', driverProfile.id)
      .order('created_at', { ascending: false })

    // Apply status filter
    if (status !== 'all') {
      query = query.eq('status', status)
    }

    // Apply search filter
    if (search) {
      query = query.or(`order_number.ilike.%${search}%,customer_name.ilike.%${search}%,business_name.ilike.%${search}%`)
    }

    // Apply date filter
    if (period !== 'all') {
      const now = new Date()
      let startDate: Date

      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3
          startDate = new Date(now.getFullYear(), quarterStart, 1)
          break
        default:
          startDate = new Date(0) // Beginning of time
      }

      query = query.gte('created_at', startDate.toISOString())
    }

    // Apply pagination
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)

    const { data: deliveries, error: deliveriesError, count } = await query

    if (deliveriesError) {
      console.error('Error fetching deliveries:', deliveriesError)
      return NextResponse.json(
        { error: "Failed to fetch deliveries" },
        { status: 500 }
      )
    }

    // Get stats for all orders assigned to this driver
    const { data: statsData, error: statsError } = await supabase
      .from('orders')
      .select('status, delivery_fee, total')
      .eq('driver_id', driverProfile.id)

    let stats = {
      total: 0,
      completed: 0,
      inProgress: 0,
      totalEarnings: 0,
      averageRating: 4.8
    }

    if (statsData && !statsError) {
      stats.total = statsData.length
      stats.completed = statsData.filter(order => order.status === 'delivered').length
      stats.inProgress = statsData.filter(order =>
        ['assigned', 'picked_up', 'out_for_delivery'].includes(order.status)
      ).length
      stats.totalEarnings = statsData.reduce((sum, order) => sum + (parseFloat(order.delivery_fee) || 0), 0)
    }

    // Format deliveries data
    const formattedDeliveries = deliveries?.map(delivery => ({
      id: delivery.id,
      order_number: delivery.order_number,
      business_name: delivery.business_name || 'Unknown Business',
      customer_name: delivery.customer_name || 'Customer',
      delivery_address: `${delivery.delivery_address}${delivery.postcode ? ', ' + delivery.postcode : ''}`,
      delivery_fee: parseFloat(delivery.delivery_fee) || 0,
      total: parseFloat(delivery.total) || 0,
      status: delivery.status,
      created_at: delivery.created_at,
      updated_at: delivery.updated_at,
      ready_time: delivery.ready_time,
      estimated_delivery_time: delivery.estimated_delivery_time,
      distance: parseFloat(delivery.delivery_distance_km) || 0,
      notes: delivery.delivery_instructions,
      payment_method: delivery.payment_method,
      payment_status: delivery.payment_status
    })) || []

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      deliveries: formattedDeliveries,
      stats,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    })

  } catch (error) {
    console.error('Error in driver deliveries history API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
