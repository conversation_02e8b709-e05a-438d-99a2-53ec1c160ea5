import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get all push subscriptions for the dev user
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', '43560081-d469-4d4e-85e2-457bda286397') // Your user ID from debug
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching subscriptions:', error)
      return NextResponse.json({ error: 'Failed to fetch subscriptions' }, { status: 500 })
    }

    // Format the response for easy reading
    const formattedSubscriptions = subscriptions?.map(sub => ({
      id: sub.id,
      device_type: sub.device_type,
      browser_name: sub.browser_name,
      is_active: sub.is_active,
      created_at: sub.created_at,
      updated_at: sub.updated_at,
      hasRealEndpoint: sub.subscription_data?.endpoint && !sub.subscription_data.endpoint.includes('placeholder'),
      endpoint_preview: sub.subscription_data?.endpoint ? 
        sub.subscription_data.endpoint.substring(0, 50) + '...' : 'No endpoint',
      preferences: sub.preferences
    }))

    return NextResponse.json({
      total_subscriptions: subscriptions?.length || 0,
      active_subscriptions: subscriptions?.filter(s => s.is_active).length || 0,
      desktop_active: subscriptions?.filter(s => s.device_type === 'desktop' && s.is_active).length || 0,
      mobile_active: subscriptions?.filter(s => s.device_type === 'mobile' && s.is_active).length || 0,
      subscriptions: formattedSubscriptions
    })

  } catch (error: any) {
    console.error('Database query error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
