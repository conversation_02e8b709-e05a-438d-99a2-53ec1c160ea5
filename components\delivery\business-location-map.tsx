"use client"

import dynamic from 'next/dynamic'

// Import the OSM business location map component with dynamic import to avoid SSR issues with Leaflet
const OSMBusinessLocationMap = dynamic(
  () => import('./osm-business-location-map'),
  { ssr: false }
)

interface BusinessLocationMapProps {
  businessName: string
  businessLocation: string
  coordinates: [number, number] // [longitude, latitude]
  height?: string
  className?: string
  interactive?: boolean
}

// This is a wrapper component that dynamically imports the OSM map component
// to avoid SSR issues with Leaflet
function BusinessLocationMap(props: BusinessLocationMapProps) {
  return <OSMBusinessLocationMap {...props} />
}

export default BusinessLocationMap
