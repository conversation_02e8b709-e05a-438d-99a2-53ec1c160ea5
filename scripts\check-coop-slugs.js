/**
 * Check Jersey Co-op category slugs
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkCoopSlugs() {
  try {
    console.log('🔍 Checking Jersey Co-op category slugs...\n');

    const { data: categories, error } = await supabase
      .from('business_custom_categories')
      .select('id, name, slug, level')
      .eq('business_id', 82)
      .eq('level', 0)
      .order('display_order');

    if (error) {
      console.error('❌ Error:', error);
      return;
    }

    console.log('📂 Level 0 categories and their slugs:');
    categories.forEach(cat => {
      console.log(`   "${cat.name}" -> slug: "${cat.slug || 'NULL'}"`);
      if (!cat.slug) {
        const suggestedSlug = cat.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        console.log(`     Suggested slug: "${suggestedSlug}"`);
      }
    });

    // Check if any slugs are missing
    const missingSlugs = categories.filter(cat => !cat.slug);
    if (missingSlugs.length > 0) {
      console.log(`\n⚠️  ${missingSlugs.length} categories are missing slugs`);
      console.log('   These need to be fixed for routing to work properly');
    } else {
      console.log('\n✅ All Level 0 categories have slugs');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkCoopSlugs();
