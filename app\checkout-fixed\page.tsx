"use client"

import React, { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { FixedCheckbox } from "@/components/ui/fixed-checkbox"

export default function CheckoutFixedPage() {
  const [saveAddress, setSaveAddress] = useState(false)
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Fixed Checkout Page</h1>
      <p className="mb-6 text-gray-600">
        This is a simplified version of the checkout page that uses the fixed checkbox implementation.
      </p>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Save Address Section</h2>
        
        <div className="flex items-center p-3 bg-emerald-50 rounded-lg border border-emerald-100">
          <FixedCheckbox
            id="saveAddress"
            checked={saveAddress}
            onCheckedChange={(checked) => {
              console.log("Checkbox changed:", checked)
              setSaveAddress(!!checked)
            }}
            className="text-emerald-600"
          />
          <div className="ml-2">
            <Label htmlFor="saveAddress" className="cursor-pointer text-gray-800 font-medium">
              Save this address for future orders
            </Label>
            <p className="text-xs text-gray-600 mt-0.5">
              This will be added to your saved addresses for easy selection next time
            </p>
          </div>
        </div>
        
        <div className="mt-4">
          <p>Current value: {saveAddress ? "Checked" : "Unchecked"}</p>
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="font-medium text-blue-800">Implementation Notes</h3>
          <p className="text-sm text-blue-700 mt-2">
            This page uses the FixedCheckbox component which prevents infinite update loops by:
          </p>
          <ul className="list-disc list-inside text-sm text-blue-700 mt-2 space-y-1">
            <li>Using a local state to track the checked state</li>
            <li>Using useEffect with proper dependencies to update the local state</li>
            <li>Using useCallback to create stable event handlers</li>
            <li>Properly handling prop overrides to avoid conflicts</li>
          </ul>
        </div>
        
        <div className="mt-6">
          <Button onClick={() => window.location.href = '/checkout-test'}>
            Go to Test Suite
          </Button>
        </div>
      </div>
    </div>
  )
}
