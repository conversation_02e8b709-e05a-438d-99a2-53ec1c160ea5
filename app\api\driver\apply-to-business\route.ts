import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { requireAuth } from "@/lib/auth-utils"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { business_id, message } = body

    if (!business_id) {
      return NextResponse.json(
        { error: "Business ID is required" },
        { status: 400 }
      )
    }

    // Require authentication
    const { user: authUser, profile, error: authError } = await requireAuth(request)

    if (authError || !profile) {
      return NextResponse.json(
        { error: authError || "Authentication required" },
        { status: 401 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', profile.id)
      .single()

    if (driverError) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified) {
      return NextResponse.json(
        { error: "Driver not verified by platform" },
        { status: 403 }
      )
    }

    if (!driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver profile is inactive" },
        { status: 403 }
      )
    }

    // Verify business exists and is available for delivery
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name, delivery_available')
      .eq('id', business_id)
      .single()

    if (businessError || !business) {
      return NextResponse.json(
        { error: "Business not found" },
        { status: 404 }
      )
    }

    if (!business.delivery_available) {
      return NextResponse.json(
        { error: "Business is not currently accepting delivery applications" },
        { status: 400 }
      )
    }

    // Check if driver already has an application for this business
    const { data: existingApplication, error: existingError } = await supabase
      .from('driver_business_approvals')
      .select('id, status')
      .eq('driver_id', driverProfile.id)
      .eq('business_id', business_id)
      .single()

    if (existingError && existingError.code !== 'PGRST116') {
      console.error('Error checking existing application:', existingError)
      return NextResponse.json(
        { error: "Failed to check existing application" },
        { status: 500 }
      )
    }

    if (existingApplication) {
      if (existingApplication.status === 'pending') {
        return NextResponse.json(
          { error: "You already have a pending application for this business" },
          { status: 400 }
        )
      } else if (existingApplication.status === 'approved') {
        return NextResponse.json(
          { error: "You are already approved for this business" },
          { status: 400 }
        )
      } else if (existingApplication.status === 'banned') {
        return NextResponse.json(
          { error: "You are banned from applying to this business" },
          { status: 403 }
        )
      }
      // If status is 'rejected', allow reapplication
    }

    // Create or update the application
    const applicationData = {
      driver_id: driverProfile.id,
      business_id: business_id,
      status: 'pending',
      application_date: new Date().toISOString(),
      decision_date: null,
      decision_by: null,
      rejection_reason: null,
      ban_reason: null,
      notes: message || null,
      updated_at: new Date().toISOString()
    }

    let result
    if (existingApplication && existingApplication.status === 'rejected') {
      // Update existing rejected application
      const { data, error } = await supabase
        .from('driver_business_approvals')
        .update(applicationData)
        .eq('id', existingApplication.id)
        .select()
        .single()

      result = { data, error }
    } else {
      // Create new application
      const { data, error } = await supabase
        .from('driver_business_approvals')
        .insert(applicationData)
        .select()
        .single()

      result = { data, error }
    }

    if (result.error) {
      console.error('Error creating/updating application:', result.error)
      return NextResponse.json(
        { error: "Failed to submit application" },
        { status: 500 }
      )
    }

    // Also create/update entry in driver_business_requests for tracking
    const requestData = {
      driver_id: driverProfile.id,
      business_id: business_id,
      status: 'pending',
      updated_at: new Date().toISOString()
    }

    const { data: existingRequest } = await supabase
      .from('driver_business_requests')
      .select('id')
      .eq('driver_id', driverProfile.id)
      .eq('business_id', business_id)
      .single()

    if (existingRequest) {
      await supabase
        .from('driver_business_requests')
        .update(requestData)
        .eq('id', existingRequest.id)
    } else {
      await supabase
        .from('driver_business_requests')
        .insert(requestData)
    }

    return NextResponse.json({
      success: true,
      message: `Application submitted to ${business.name}`,
      application: result.data
    })

  } catch (error) {
    console.error('Error in apply to business POST:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
