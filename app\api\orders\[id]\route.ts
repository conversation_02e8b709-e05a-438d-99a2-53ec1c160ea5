import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to enhance cart items with customizations and variant names
async function enhanceItemsWithDetails(adminClient: any, items: any[]) {
  if (!items || items.length === 0) {
    return []
  }

  // Get customizations for all cart items
  const cartItemIds = items.map(item => item.id)
  const { data: customizations, error: customizationsError } = await adminClient
    .from("cart_item_customizations")
    .select(`
      cart_item_id,
      customization_group_name,
      customization_option_name,
      price
    `)
    .in("cart_item_id", cartItemIds)

  if (customizationsError) {
    console.error("Error fetching customizations:", customizationsError)
  }

  // Get variant names for items that have variants
  const variantIds = items.filter(item => item.variant_id).map(item => item.variant_id)
  let variants = []
  if (variantIds.length > 0) {
    const { data: variantData, error: variantError } = await adminClient
      .from("product_variants")
      .select("id, name")
      .in("id", variantIds)

    if (variantError) {
      console.error("Error fetching variants:", variantError)
    } else {
      variants = variantData || []
    }
  }

  // Combine items with their customizations and variant names
  return items.map(item => {
    // Get customizations for this item
    const itemCustomizations = (customizations || []).filter(c => c.cart_item_id === item.id)

    // Group customizations by group name
    const groupedCustomizations: Record<string, any[]> = {}
    itemCustomizations.forEach(customization => {
      if (!groupedCustomizations[customization.customization_group_name]) {
        groupedCustomizations[customization.customization_group_name] = []
      }
      groupedCustomizations[customization.customization_group_name].push({
        name: customization.customization_option_name,
        price: customization.price
      })
    })

    // Convert to array format
    const customizationGroups = Object.keys(groupedCustomizations).map(groupName => ({
      groupName,
      options: groupedCustomizations[groupName]
    }))

    // Get variant name if applicable
    const variant = variants.find(v => v.id === item.variant_id)
    const variantName = variant?.name

    return {
      ...item,
      variantName,
      customizations: customizationGroups
    }
  })
}

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in orders/[id] API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in orders/[id] API")
      return { authorized: true };
    } else {
      return {
        authorized: false,
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in orders/[id] API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in orders/[id] API:", error)
      return {
        authorized: false,
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in orders/[id] API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in orders/[id] API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return {
        authorized: false,
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in orders/[id] API")
      return { authorized: true };
    }
  }
}

// GET endpoint to fetch a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order identifier from the URL (could be ID or order number)
    const orderIdentifier = params.id;

    if (!orderIdentifier) {
      return NextResponse.json(
        { error: 'Order identifier is required' },
        { status: 400 }
      );
    }

    // PHASE 2 STEP 3: First, fetch the order to see its structure (include delivery_fulfillment)
    // Try to determine if this is an order number or order ID
    let query = supabase.from('orders').select('*, delivery_fulfillment');

    // If it's a number, try ID first, otherwise try order_number
    if (!isNaN(Number(orderIdentifier))) {
      query = query.eq('id', orderIdentifier);
    } else {
      query = query.eq('order_number', orderIdentifier);
    }

    const { data: orderData, error: orderError } = await query.single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      return NextResponse.json(
        { error: 'Failed to fetch order', details: orderError.message },
        { status: 500 }
      );
    }



    // Check if order has cart_id, if so fetch cart_items, otherwise use order_items
    let data;
    let error;

    if (orderData.cart_id) {
      // New approach: fetch cart_items via cart_id
      const { data: cartItemsData, error: cartItemsError } = await supabase
        .from('cart_items')
        .select('*')
        .eq('cart_id', orderData.cart_id);

      if (cartItemsError) {
        console.error('Error fetching cart items:', cartItemsError);
        return NextResponse.json(
          { error: 'Failed to fetch cart items', details: cartItemsError.message },
          { status: 500 }
        );
      }

      // Enhance cart items with customizations and variant names
      const enhancedCartItems = await enhanceItemsWithDetails(supabase, cartItemsData || []);

      // Also fetch order_businesses data for business names and details
      const { data: orderBusinessesData, error: orderBusinessesError } = await supabase
        .from('order_businesses')
        .select('*')
        .eq('order_id', orderData.id);

      if (orderBusinessesError) {
        console.error('Error fetching order businesses:', orderBusinessesError);
      }

      data = {
        ...orderData,
        cart_items: enhancedCartItems,
        order_businesses: orderBusinessesData || []
      };
      error = null;
    } else {
      // Fallback: use order_items if cart_id doesn't exist
      const { data: orderWithItems, error: orderWithItemsError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items(*),
          order_status_history(*),
          order_payment_allocations(*),
          order_businesses(*)
        `)
        .eq('id', orderData.id)
        .single();

      data = orderWithItems;
      error = orderWithItemsError;
    }

    if (error) {
      console.error('Error fetching order:', error);
      return NextResponse.json(
        { error: 'Failed to fetch order', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if the user is authorized to view this order
    // Skip authorization check in development mode if no user is provided
    if (user && data.user_id !== user.id) {
      // Check if the user is an admin or business manager for this order
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single();

      if (userError || !userData) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        );
      }

      const isAdmin = userData.role === 'admin' || userData.role === 'super_admin';

      if (!isAdmin) {
        // Check if user is a business manager for the business in this order
        const businessId = data.business_id;

        if (!businessId) {
          return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 403 }
          );
        }

        const { data: managerData, error: managerError } = await supabase
          .from('business_managers')
          .select('business_id')
          .eq('user_id', user.id)
          .eq('business_id', businessId);

        if (managerError || !managerData || managerData.length === 0) {
          return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 403 }
          );
        }
      }
    }

    return NextResponse.json({
      order: data
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// Import the transaction helper
import { updateOrderStatus } from '../transaction-helper';

// PATCH endpoint to update an order's status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order identifier from the URL and resolve to order ID
    const orderIdentifier = params.id;

    // First, fetch the order to get the actual order ID
    let query = supabase.from('orders').select('id');

    // If it's a number, try ID first, otherwise try order_number
    if (!isNaN(Number(orderIdentifier))) {
      query = query.eq('id', orderIdentifier);
    } else {
      query = query.eq('order_number', orderIdentifier);
    }

    const { data: orderData, error: orderError } = await query.single();

    if (orderError || !orderData) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    const orderId = orderData.id;

    // Parse the request body
    const body = await request.json();

    if (!body.status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate the status
    const validStatuses = [
      'pending', 'confirmed', 'preparing', 'ready',
      'out_for_delivery', 'delivered', 'cancelled'
    ];

    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status', validStatuses },
        { status: 400 }
      );
    }

    try {
      // Use enhanced status update with automatic notifications
      const { updateOrderStatusWithNotifications } = await import('../enhanced-status-update');

      const result = await updateOrderStatusWithNotifications({
        orderId: parseInt(orderId),
        newStatus: body.status,
        notes: body.notes || null,
        updatedBy: user?.id || null
      });

      return NextResponse.json({
        success: true,
        order: result.order
      });
    } catch (transactionError) {
      console.error('Error in order status update transaction:', transactionError);
      return NextResponse.json(
        { error: 'Failed to update order status', details: transactionError.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
