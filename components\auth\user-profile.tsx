"use client"

import { useState, useEffect } from "react"
import { Mail, Phone, Save, Edit, X, Store, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useAuth } from "@/context/unified-auth-context"
import { getAuthHeaders } from '@/utils/auth-utils'
import Link from "next/link"

interface UserData {
  id: number
  name: string
  email: string
  phone: string | null
  address: string | null
  first_name: string | null
  last_name: string | null
  role?: string
  created_at?: string
  updated_at?: string
}



export default function UserProfile() {
  const { user } = useAuth()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [phone, setPhone] = useState("")

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return

      // Set default values based on auth user
      const defaultUserData = {
        id: 0, // This will be assigned by the database
        email: user.email || "",
        name: user.email?.split("@")[0] || "User",
        phone: null,
        address: null,
        first_name: null,
        last_name: null
      }

      // Initialize with default data
      setUserData(defaultUserData as UserData)
      setFirstName("")
      setLastName("")
      setPhone("")

      try {
        console.log("Fetching user profile data from API for:", user.email)

        // Get authentication headers with timeout
        let headers: HeadersInit = {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        };

        try {
          // Try to get auth headers with a shorter timeout
          const authHeadersPromise = getAuthHeaders();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Auth headers timeout')), 3000)
          );

          headers = await Promise.race([authHeadersPromise, timeoutPromise]) as HeadersInit;
        } catch (authError) {
          // Silently fall back to basic headers - this is normal during initial load
          console.log("Using basic headers due to auth timeout")
        }

        // Use our server-side API endpoint with timeout
        const fetchPromise = fetch('/api/user/profile', {
          credentials: 'include',
          headers
        });

        const fetchTimeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('API request timeout')), 5000)
        );

        const response = await Promise.race([fetchPromise, fetchTimeoutPromise]) as Response;
        const result = await response.json()

        // If we get a 401 Not authenticated, that's expected when not logged in
        // Just use the default data in this case
        if (!response.ok) {
          if (response.status === 401 && result.error === "Not authenticated") {
            console.log("User not authenticated, using default profile data")
          } else {
            // For other errors, log them but don't throw
            console.warn("Error fetching user data:", result.error)
          }
          // Continue with default data
        } else if (result.data) {
          // Successfully got user data
          console.log("Found user profile data:", result.data)

          // Make sure we have all the required fields
          const processedData = {
            id: result.data.id,
            name: result.data.name || defaultUserData.name,
            email: result.data.email || defaultUserData.email,
            phone: result.data.phone || null,
            address: result.data.address || null,
            first_name: result.data.first_name || null,
            last_name: result.data.last_name || null,
            role: result.data.role || "customer",
            created_at: result.data.created_at,
            updated_at: result.data.updated_at
          };

          setUserData(processedData)
          setFirstName(processedData.first_name || "")
          setLastName(processedData.last_name || "")
          setPhone(processedData.phone || "")
        }
      } catch (err: any) {
        console.error("Error fetching user data:", err.message)
        // Set a user-friendly error message but don't block the UI
        if (err.message.includes('timeout')) {
          console.log("API timeout - using default profile data")
        } else {
          console.log("API error - using default profile data")
        }
        // Continue with default data - don't show error to user for profile loading
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [user])





  const handleSave = async () => {
    if (!user) return

    setError(null)
    setSuccess(null)
    setIsSaving(true)

    try {
      console.log("Saving user profile data to API")

      // Combine first and last name for the name field
      const fullName = `${firstName} ${lastName}`.trim()

      // Get authentication headers
      const headers = await getAuthHeaders({
        'Content-Type': 'application/json'
      });

      // Use our server-side API endpoint instead of direct Supabase access
      const response = await fetch('/api/user/profile', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({
          first_name: firstName,
          last_name: lastName,
          name: fullName,
          phone
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Error updating profile")
      }

      // Update local state with the returned data
      if (result.data) {
        setUserData(result.data)
      } else {
        // If no data returned, just update the local state with what we know
        setUserData({
          ...userData!,
          name: fullName,
          first_name: firstName,
          last_name: lastName,
          phone
        })
      }

      setSuccess(result.message || "Profile updated successfully")
      setIsEditing(false)
    } catch (err: any) {
      console.error("Error updating profile:", err)
      setError(err.message || "An unexpected error occurred. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }



  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  if (isLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center">
              <p>Loading profile...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="container max-w-4xl py-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center">
              <p>Unable to load profile information.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div>
      {error && (
        <Alert variant="destructive" className="mb-6 border-none shadow-sm">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && (
        <Alert className="mb-6 bg-emerald-50 text-emerald-600 border-none shadow-sm">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card className="border-none shadow-sm overflow-hidden">
            <CardHeader className="bg-white border-b border-gray-100 pb-4">
              <CardTitle className="text-xl text-[#2e3333]">Personal Information</CardTitle>
              <CardDescription className="text-[#585c5c]">Update your personal details</CardDescription>
            </CardHeader>
            <CardContent className="bg-white pt-6">
              {!isEditing ? (
                <div className="space-y-6">
                  {/* View mode */}
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-[#f9fafa] p-4 rounded-lg">
                      <p className="text-sm font-medium text-[#585c5c] mb-1">First Name</p>
                      <p className="text-[#2e3333] font-medium">
                        {userData.first_name || "Not provided"}
                      </p>
                    </div>
                    <div className="bg-[#f9fafa] p-4 rounded-lg">
                      <p className="text-sm font-medium text-[#585c5c] mb-1">Last Name</p>
                      <p className="text-[#2e3333] font-medium">
                        {userData.last_name || "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div className="bg-[#f9fafa] p-4 rounded-lg">
                    <p className="text-sm font-medium text-[#585c5c] mb-1">Email</p>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-emerald-600" />
                      <p className="text-[#2e3333] font-medium">{userData.email}</p>
                    </div>
                  </div>

                  <div className="bg-[#f9fafa] p-4 rounded-lg">
                    <p className="text-sm font-medium text-[#585c5c] mb-1">Phone</p>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-emerald-600" />
                      <p className="text-[#2e3333] font-medium">{userData.phone || "Not provided"}</p>
                    </div>
                  </div>

                  {userData.role && (
                    <div className="bg-[#f9fafa] p-4 rounded-lg">
                      <p className="text-sm font-medium text-[#585c5c] mb-1">Account Type</p>
                      <div className="flex items-center justify-between">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                          {userData.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                        {(userData.role === 'business_manager' || userData.role === 'business_staff') && (
                          <Link href="/business-admin/dashboard">
                            <Button
                              size="sm"
                              className="bg-emerald-600 hover:bg-emerald-700 text-white text-xs px-3 py-1 h-8"
                            >
                              <Store className="mr-1 h-3 w-3" />
                              Business Dashboard
                              <ExternalLink className="ml-1 h-3 w-3" />
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <form onSubmit={(e) => { e.preventDefault(); handleSave(); }} className="space-y-6">
                  <div className="grid gap-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="first_name" className="text-[#2e3333]">First Name</Label>
                        <Input
                          id="first_name"
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          placeholder="First name"
                          required
                          className="border-gray-200 focus:border-emerald-600 focus:ring-emerald-600 transition-colors"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="last_name" className="text-[#2e3333]">Last Name</Label>
                        <Input
                          id="last_name"
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          placeholder="Last name"
                          required
                          className="border-gray-200 focus:border-emerald-600 focus:ring-emerald-600 transition-colors"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-[#2e3333]">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="email"
                          value={userData.email}
                          disabled
                          className="bg-gray-50 pl-10"
                        />
                      </div>
                      <p className="text-xs text-[#585c5c]">Email cannot be changed</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-[#2e3333]">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="phone"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          placeholder="Your phone number"
                          className="pl-10 border-gray-200 focus:border-emerald-600 focus:ring-emerald-600 transition-colors"
                        />
                      </div>
                      <p className="text-xs text-[#585c5c]">Phone number is used for delivery notifications</p>
                    </div>
                  </div>
                </form>
              )}
            </CardContent>
            <CardFooter className="flex justify-end bg-white border-t border-gray-100 py-4">
              {!isEditing ? (
                <Button
                  onClick={() => setIsEditing(true)}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white transition-colors"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Profile
                </Button>
              ) : (
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                    className="border-gray-200 text-[#2e3333] hover:bg-gray-50"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white transition-colors"
                  >
                    {isSaving ? (
                      <>
                        <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardFooter>
          </Card>

    </div>
  )
}
