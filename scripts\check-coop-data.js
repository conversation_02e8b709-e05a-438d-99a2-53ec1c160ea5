/**
 * Simple script to check Jersey Co-op data
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkCoopData() {
  try {
    console.log('🔍 Checking Jersey Co-op data...\n');

    // Get a few specific categories to debug
    const { data: categories, error } = await supabase
      .from('business_custom_categories')
      .select('*')
      .eq('business_id', 82)
      .in('id', [64, 72, 73, 74]) // Fresh Produce and some of its children
      .order('id');

    if (error) {
      console.error('❌ Error:', error);
      return;
    }

    console.log('📂 Sample categories:');
    categories.forEach(cat => {
      console.log(`ID: ${cat.id}, Name: "${cat.name}", Level: ${cat.level}, Parent: ${cat.parent_category}, Business: ${cat.business_id}`);
    });

    // Now check if the relationships work
    const freshProduce = categories.find(c => c.name === 'Fresh Produce');
    const fruits = categories.find(c => c.name === 'Fruits');

    console.log('\n🔗 Relationship check:');
    console.log(`Fresh Produce ID: ${freshProduce?.id} (type: ${typeof freshProduce?.id})`);
    console.log(`Fruits parent_category: ${fruits?.parent_category} (type: ${typeof fruits?.parent_category})`);
    console.log(`Match: ${freshProduce?.id === fruits?.parent_category}`);
    console.log(`Strict equality: ${freshProduce?.id === fruits?.parent_category}`);
    console.log(`Loose equality: ${freshProduce?.id == fruits?.parent_category}`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkCoopData();
