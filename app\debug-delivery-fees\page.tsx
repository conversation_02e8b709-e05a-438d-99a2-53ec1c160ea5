"use client";

import { useState } from 'react';

export default function DebugDeliveryFeesPage() {
  const [businessId, setBusinessId] = useState('1'); // Jersey Grill
  const [postcode, setPostcode] = useState('JE2 3QG');
  const [businessPageResult, setBusinessPageResult] = useState(null);
  const [checkoutPageResult, setCheckoutPageResult] = useState(null);
  const [loading, setLoading] = useState(false);

  // Mock business coordinates (Jersey Grill)
  const businessCoordinates = [-2.109951, 49.185141];
  
  // Mock customer coordinates for the postcode
  const customerCoordinates = [-2.1333, 49.2444]; // JE2 3QG coordinates

  const testBusinessPageCalculation = async () => {
    setLoading(true);
    try {
      console.log('🚀 Testing Business Page Calculation...');
      
      const { calculateDeliveryEstimates } = await import('@/lib/delivery-calculation-service');
      
      const params = {
        businessId: businessId,
        businessName: 'Jersey Grill',
        businessCoordinates: businessCoordinates as [number, number],
        preparationTimeMinutes: 15,
        deliveryFeeModel: 'mixed' as const,
        deliveryFee: 1.00,
        deliveryFeePerKm: 1.00,
        customerPostcode: postcode,
        customerCoordinates: customerCoordinates as [number, number]
      };

      const result = await calculateDeliveryEstimates(params);
      setBusinessPageResult(result);
      console.log('✅ Business Page Result:', result);
    } catch (error) {
      console.error('❌ Business Page Error:', error);
      setBusinessPageResult({ error: error.message });
    }
    setLoading(false);
  };

  const testCheckoutPageCalculation = async () => {
    setLoading(true);
    try {
      console.log('🚀 Testing Checkout Page Calculation...');
      
      const apiParams = {
        businessId: parseInt(businessId, 10),
        businessCoordinates: businessCoordinates,
        customerCoordinates: customerCoordinates,
        postcode: postcode,
        deliveryFeeModel: 'mixed',
        deliveryFee: 1.00,
        deliveryFeePerKm: 1.00
      };

      console.log('📤 Checkout API Parameters:', apiParams);

      const response = await fetch('/api/delivery/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiParams)
      });

      if (response.ok) {
        const result = await response.json();
        setCheckoutPageResult(result);
        console.log('✅ Checkout Page Result:', result);
      } else {
        const errorText = await response.text();
        console.error('❌ Checkout Page API Error:', response.status, errorText);
        setCheckoutPageResult({ error: `API Error: ${response.status} - ${errorText}` });
      }
    } catch (error) {
      console.error('❌ Checkout Page Error:', error);
      setCheckoutPageResult({ error: error.message });
    }
    setLoading(false);
  };

  const testBothCalculations = async () => {
    await testBusinessPageCalculation();
    await testCheckoutPageCalculation();
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Debug Delivery Fee Calculations</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Parameters</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-2">Business ID</label>
            <input
              type="text"
              value={businessId}
              onChange={(e) => setBusinessId(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Customer Postcode</label>
            <input
              type="text"
              value={postcode}
              onChange={(e) => setPostcode(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600">
            <strong>Business Coordinates:</strong> [{businessCoordinates.join(', ')}]<br/>
            <strong>Customer Coordinates:</strong> [{customerCoordinates.join(', ')}]
          </p>
        </div>

        <div className="flex gap-4">
          <button
            onClick={testBusinessPageCalculation}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            Test Business Page
          </button>
          
          <button
            onClick={testCheckoutPageCalculation}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
          >
            Test Checkout Page
          </button>
          
          <button
            onClick={testBothCalculations}
            disabled={loading}
            className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50"
          >
            Test Both
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Business Page Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 text-blue-600">Business Page Calculation</h3>
          {businessPageResult ? (
            <div className="space-y-2">
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(businessPageResult, null, 2)}
              </pre>
              {businessPageResult.feeNumeric && (
                <div className="text-lg font-bold text-blue-600">
                  Fee: £{businessPageResult.feeNumeric.toFixed(2)}
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No results yet</p>
          )}
        </div>

        {/* Checkout Page Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 text-green-600">Checkout Page Calculation</h3>
          {checkoutPageResult ? (
            <div className="space-y-2">
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(checkoutPageResult, null, 2)}
              </pre>
              {checkoutPageResult.fee && (
                <div className="text-lg font-bold text-green-600">
                  Fee: £{typeof checkoutPageResult.fee === 'number' ? checkoutPageResult.fee.toFixed(2) : checkoutPageResult.fee}
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No results yet</p>
          )}
        </div>
      </div>

      {/* Comparison */}
      {businessPageResult && checkoutPageResult && (
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-semibold mb-4">Comparison</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>Business Page Fee:</strong> 
              {businessPageResult.feeNumeric ? ` £${businessPageResult.feeNumeric.toFixed(2)}` : ' N/A'}
            </div>
            <div>
              <strong>Checkout Page Fee:</strong> 
              {checkoutPageResult.fee ? ` £${typeof checkoutPageResult.fee === 'number' ? checkoutPageResult.fee.toFixed(2) : checkoutPageResult.fee}` : ' N/A'}
            </div>
          </div>

          {businessPageResult.feeNumeric && checkoutPageResult.fee && typeof checkoutPageResult.fee === 'number' && (
            <div className="mt-4">
              {Math.abs(businessPageResult.feeNumeric - checkoutPageResult.fee) < 0.01 ? (
                <div className="text-green-600 font-semibold">✅ Fees match!</div>
              ) : (
                <div className="text-red-600 font-semibold">
                  ❌ Fees differ by £{Math.abs(businessPageResult.feeNumeric - checkoutPageResult.fee).toFixed(2)}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
        <h4 className="font-semibold text-yellow-800 mb-2">Instructions:</h4>
        <ol className="list-decimal list-inside text-sm text-yellow-700 space-y-1">
          <li>Open the browser console to see detailed logging</li>
          <li>Test both calculation methods with the same parameters</li>
          <li>Compare the results to identify any differences</li>
          <li>Check the console for any errors or network issues</li>
        </ol>
      </div>
    </div>
  );
}
