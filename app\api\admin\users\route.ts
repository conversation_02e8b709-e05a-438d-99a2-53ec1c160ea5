import { NextRequest, NextResponse } from "next/server"
import { withAdmin } from "@/lib/api-guards"
import { adminClient } from "@/lib/supabase-admin"

// Protected admin endpoint using new RBAC system
export const GET = withAdmin(async (request: NextRequest, { profile }) => {
  try {
    console.log("Admin users API accessed by:", profile.email, "with role:", profile.role)

    // Fetch users with the admin client
    const { data, error } = await adminClient
      .from("users")
      .select("*")
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching users:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      users: data,
      meta: {
        total: data.length,
        accessed_by: profile.email,
        access_level: profile.role
      }
    })
  } catch (error: any) {
    console.error("Unexpected error in users API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
})
