"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  CheckCircle,
  XCircle,
  Eye,
  Clock,
  User,
  Car,
  FileText,
  AlertCircle,
  Mail,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  Phone
} from "lucide-react"

interface DriverApplication {
  id: string
  user_id: number
  vehicle_type: string
  license_number: string | null
  insurance_number: string | null
  vehicle_registration: string | null
  profile_image_url: string | null
  is_verified: boolean
  is_active: boolean
  notes: string | null
  created_at: string
  updated_at: string
  // User details
  user_name: string
  user_first_name: string | null
  user_last_name: string | null
  user_email: string
  user_phone: string | null
}

export default function DriverApplicationsPage() {
  const [applications, setApplications] = useState<DriverApplication[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedApplication, setSelectedApplication] = useState<DriverApplication | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending')
  const [searchTerm, setSearchTerm] = useState("")
  const [vehicleFilter, setVehicleFilter] = useState<string>("all")

  useEffect(() => {
    fetchApplications()
  }, [filter])

  const fetchApplications = async () => {
    try {
      const response = await fetch(`/api/admin/driver-applications?filter=${filter}`)
      const data = await response.json()
      setApplications(data.applications || [])
    } catch (error) {
      console.error('Error fetching applications:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleApproveDriver = async (driverId: string, userEmail: string) => {
    setIsProcessing(true)
    try {
      const response = await fetch('/api/admin/driver-applications/approve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ driverId, userEmail })
      })

      if (response.ok) {
        await fetchApplications()
        setSelectedApplication(null)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error approving driver:', error)
      alert('Failed to approve driver')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectDriver = async (driverId: string, reason: string) => {
    setIsProcessing(true)
    try {
      const response = await fetch('/api/admin/driver-applications/reject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ driverId, reason })
      })

      if (response.ok) {
        await fetchApplications()
        setSelectedApplication(null)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error rejecting driver:', error)
      alert('Failed to reject driver')
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusBadge = (application: DriverApplication) => {
    if (!application.is_active) {
      return <Badge variant="destructive">Rejected</Badge>
    }
    if (application.is_verified) {
      return <Badge variant="default" className="bg-green-600">Approved</Badge>
    }
    return <Badge variant="secondary">Pending Review</Badge>
  }

  const filteredApplications = applications.filter(app => {
    // Status filter
    let statusMatch = true
    switch (filter) {
      case 'pending':
        statusMatch = !app.is_verified && app.is_active
        break
      case 'approved':
        statusMatch = app.is_verified && app.is_active
        break
      case 'rejected':
        statusMatch = !app.is_active
        break
      default:
        statusMatch = true
    }

    // Search filter
    const searchMatch = searchTerm === "" ||
      app.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (app.user_phone && app.user_phone.includes(searchTerm))

    // Vehicle filter
    const vehicleMatch = vehicleFilter === "all" || app.vehicle_type === vehicleFilter

    return statusMatch && searchMatch && vehicleMatch
  })

  // Get unique vehicle types for filter dropdown
  const vehicleTypes = [...new Set(applications.map(app => app.vehicle_type))]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading driver applications...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Driver Applications</h1>
          <p className="text-gray-600">Review and approve driver applications for Loop Jersey</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchApplications}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={vehicleFilter}
            onChange={(e) => setVehicleFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
          >
            <option value="all">All Vehicles</option>
            {vehicleTypes.map(type => (
              <option key={type} value={type} className="capitalize">
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-2">
        {[
          { key: 'pending', label: 'Pending Review', count: applications.filter(a => !a.is_verified && a.is_active).length },
          { key: 'approved', label: 'Approved', count: applications.filter(a => a.is_verified && a.is_active).length },
          { key: 'rejected', label: 'Rejected', count: applications.filter(a => !a.is_active).length },
          { key: 'all', label: 'All', count: applications.length }
        ].map(tab => (
          <Button
            key={tab.key}
            variant={filter === tab.key ? "default" : "outline"}
            onClick={() => setFilter(tab.key as any)}
            className="relative"
          >
            {tab.label}
            {tab.count > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {tab.count}
              </Badge>
            )}
          </Button>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {applications.filter(a => !a.is_verified && a.is_active).length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">
                  {applications.filter(a => a.is_verified && a.is_active).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-red-600">
                  {applications.filter(a => !a.is_active).length}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {applications.length}
                </p>
              </div>
              <User className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Applications Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Driver Applications</CardTitle>
              <CardDescription>
                {filteredApplications.length} of {applications.length} application(s) shown
              </CardDescription>
            </div>
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchTerm("")}
              >
                Clear search
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
              <p className="text-gray-600">
                {searchTerm || vehicleFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "No driver applications match the selected status"}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver Information</TableHead>
                  <TableHead>Vehicle & Documentation</TableHead>
                  <TableHead>Application Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredApplications.map((application) => {
                  const requiresDocumentation = ['car', 'van', 'motorcycle'].includes(application.vehicle_type)
                  const hasAllDocuments = requiresDocumentation ?
                    (application.license_number && application.insurance_number && application.vehicle_registration) :
                    true

                  return (
                    <TableRow key={application.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            {application.user_first_name && application.user_last_name
                              ? `${application.user_first_name} ${application.user_last_name}`
                              : application.user_name}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Mail className="h-3 w-3 mr-1" />
                            {application.user_email}
                          </div>
                          {application.user_phone && (
                            <div className="flex items-center text-sm text-gray-600">
                              <Phone className="h-3 w-3 mr-1" />
                              {application.user_phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <Car className="h-4 w-4 mr-2 text-gray-500" />
                            <span className="capitalize font-medium">{application.vehicle_type}</span>
                          </div>
                          {requiresDocumentation && (
                            <div className="flex items-center">
                              <FileText className="h-3 w-3 mr-1 text-gray-400" />
                              <span className="text-xs text-gray-600">
                                {hasAllDocuments ? (
                                  <span className="text-green-600">Documents complete</span>
                                ) : (
                                  <span className="text-red-600">Documents missing</span>
                                )}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm">
                          <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                          {new Date(application.created_at).toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric'
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(application)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedApplication(application)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Review
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Driver Application Review</DialogTitle>
                              <DialogDescription>
                                Review {application.user_name}'s application details and documentation
                              </DialogDescription>
                            </DialogHeader>

                            {selectedApplication && (
                              <DriverApplicationReview
                                application={selectedApplication}
                                onApprove={handleApproveDriver}
                                onReject={handleRejectDriver}
                                isProcessing={isProcessing}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

interface DriverApplicationReviewProps {
  application: DriverApplication
  onApprove: (driverId: string, userEmail: string) => void
  onReject: (driverId: string, reason: string) => void
  isProcessing: boolean
}

function DriverApplicationReview({
  application,
  onApprove,
  onReject,
  isProcessing
}: DriverApplicationReviewProps) {
  const [rejectionReason, setRejectionReason] = useState("")
  const [showRejectForm, setShowRejectForm] = useState(false)

  const requiresDocumentation = ['car', 'van', 'motorcycle'].includes(application.vehicle_type)

  return (
    <div className="space-y-6">
      {/* Driver Information */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label className="text-sm font-medium text-gray-700">Name</Label>
          <p className="text-sm">
            {application.user_first_name && application.user_last_name
              ? `${application.user_first_name} ${application.user_last_name}`
              : application.user_name}
          </p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-700">Email</Label>
          <p className="text-sm">{application.user_email}</p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-700">Phone</Label>
          <p className="text-sm">{application.user_phone || 'Not provided'}</p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-700">Vehicle Type</Label>
          <p className="text-sm capitalize">{application.vehicle_type}</p>
        </div>
      </div>

      {/* Documentation */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Documentation</h3>

        {requiresDocumentation ? (
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">Driver's License</span>
              </div>
              <div className="flex items-center">
                {application.license_number ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm text-gray-600">{application.license_number}</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600 mr-2" />
                    <span className="text-sm text-red-600">Not provided</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">Insurance</span>
              </div>
              <div className="flex items-center">
                {application.insurance_number ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm text-gray-600">{application.insurance_number}</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600 mr-2" />
                    <span className="text-sm text-red-600">Not provided</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">Vehicle Registration</span>
              </div>
              <div className="flex items-center">
                {application.vehicle_registration ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm text-gray-600">{application.vehicle_registration}</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600 mr-2" />
                    <span className="text-sm text-red-600">Not provided</span>
                  </>
                )}
              </div>
            </div>
          </div>
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No additional documentation required for {application.vehicle_type} delivery.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Application Notes */}
      {application.notes && (
        <div>
          <Label className="text-sm font-medium text-gray-700">Application Notes</Label>
          <div className="mt-1 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{application.notes}</p>
          </div>
        </div>
      )}

      {/* Actions */}
      {!application.is_verified && application.is_active && (
        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            {!showRejectForm ? (
              <Button
                variant="outline"
                onClick={() => setShowRejectForm(true)}
                disabled={isProcessing}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            ) : (
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Rejection reason..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="w-48"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onReject(application.id, rejectionReason)}
                  disabled={!rejectionReason.trim() || isProcessing}
                >
                  Confirm Reject
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowRejectForm(false)
                    setRejectionReason("")
                  }}
                >
                  Cancel
                </Button>
              </div>
            )}
          </div>

          <Button
            onClick={() => onApprove(application.id, application.user_email)}
            disabled={isProcessing}
            className="bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            {isProcessing ? "Processing..." : "Approve Driver"}
          </Button>
        </DialogFooter>
      )}

      {application.is_verified && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            This driver has been approved and can access their dashboard.
          </AlertDescription>
        </Alert>
      )}

      {!application.is_active && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            This driver application has been rejected.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
