'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from '@/context/unified-auth-context'
import { useRealtimeCart } from '@/context/realtime-cart-context'
import { customerOrdersService } from '@/services/customer-orders-service'
import { useReorder } from '@/hooks/use-reorder'
import { useOrderUpdates } from '@/hooks/use-order-updates'
import { usePullToRefresh } from '@/hooks/use-pull-to-refresh'
import { OrderSession } from '@/types/customer-orders'
import { OrderSessionCard } from './order-session-card'
import { OrderFilters } from './order-filters'
import { OrderSearch } from './order-search'
import { EmptyOrdersState } from './empty-orders-state'
import { LoadingOrdersState } from './loading-orders-state'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RefreshCw, ShoppingBag } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

export function CustomerOrdersClient() {
  const { user, isLoading } = useAuth()
  const { cart } = useRealtimeCart()
  const { reorderSession, isReordering } = useReorder()

  // State
  const [orderSessions, setOrderSessions] = useState<OrderSession[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // Filters and search
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [businessFilter, setBusinessFilter] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState('')

  // Pagination
  const [hasMore, setHasMore] = useState(false)
  const [totalCount, setTotalCount] = useState(0)

  // Real-time updates (only if we have a user)
  const { updates } = useOrderUpdates(user?.id, undefined)

  // Pull-to-refresh for mobile
  const {
    containerRef,
    pullToRefreshStyle,
    refreshIndicatorStyle,
    isRefreshing: isPullRefreshing
  } = usePullToRefresh({
    onRefresh: async () => {
      await fetchOrders(true)
    },
    disabled: loading
  })

  // Fetch orders function
  const fetchOrders = useCallback(async (refresh = false) => {
    if (!user) return

    if (refresh) {
      setRefreshing(true)
    } else {
      setLoading(true)
    }
    setError(null)

    try {
      // Get auth headers like the addresses page does
      const getAuthHeaders = () => {
        const token = localStorage.getItem('supabase.auth.token')
        return token ? { Authorization: `Bearer ${token}` } : {}
      }

      // Build query params
      const params = new URLSearchParams()
      if (statusFilter) params.append('status', statusFilter)

      const url = `/api/user/orders${params.toString() ? `?${params.toString()}` : ''}`

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch orders: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success && data.orderSessions) {
        setOrderSessions(data.orderSessions)
        setTotalCount(data.totalCount)
        setHasMore(data.hasMore)
      } else {
        setOrderSessions([])
        setTotalCount(0)
        setHasMore(false)
      }

    } catch (err: any) {
      setError(err.message || 'Failed to load orders')
      toast({
        title: 'Error loading orders',
        description: 'Please try again in a moment.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [user, statusFilter])

  // Initial load
  useEffect(() => {
    if (user && orderSessions.length === 0) {
      fetchOrders()
    }
  }, [user, fetchOrders])

  // Refetch when filters change
  useEffect(() => {
    if (user && orderSessions.length > 0) {
      fetchOrders()
    }
  }, [statusFilter, businessFilter, user, fetchOrders])

  // Enhanced search functionality with debouncing
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')

  // Debounce search query to avoid excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300) // 300ms debounce

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Enhanced filter orders based on search query
  const filteredOrderSessions = useMemo(() => {
    if (!debouncedSearchQuery) return orderSessions

    const query = debouncedSearchQuery.toLowerCase().trim()

    return orderSessions.filter(session => {
      // Search in session data
      if (session.customer_address?.toLowerCase().includes(query)) return true
      if (session.customer_name?.toLowerCase().includes(query)) return true
      if (session.customer_phone?.toLowerCase().includes(query)) return true
      if (session.parish?.toLowerCase().includes(query)) return true
      if (session.postcode?.toLowerCase().includes(query)) return true

      // Search in business orders
      return session.business_orders.some(order => {
        // Search order details
        if (order.business_name?.toLowerCase().includes(query)) return true
        if (order.order_number?.toLowerCase().includes(query)) return true
        if (order.status?.toLowerCase().includes(query)) return true
        if (order.delivery_method?.toLowerCase().includes(query)) return true

        // Search in cart items
        if (order.cart_items) {
          return order.cart_items.some(item =>
            item.name?.toLowerCase().includes(query)
          )
        }

        return false
      })
    })
  }, [orderSessions, debouncedSearchQuery])

  // Handle refresh
  const handleRefresh = () => {
    if (!user) return
    fetchOrders(true)
  }

  // Auto-refresh when order updates are received
  useEffect(() => {
    if (updates.length > 0 && orderSessions.length > 0) {
      handleRefresh()
    }
  }, [updates.length, orderSessions.length, handleRefresh])

  // Loading state
  if (loading && !orderSessions.length) {
    return <LoadingOrdersState />
  }

  // Authentication required
  if (!isLoading && !user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Authentication Required</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Please sign in to view your order history.
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error && orderSessions.length === 0) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => fetchOrders()} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div ref={containerRef} className="space-y-6" style={pullToRefreshStyle}>
      {/* Pull-to-refresh indicator */}
      <div
        className="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 md:hidden"
        style={refreshIndicatorStyle}
      >
        <div className="bg-white rounded-full p-3 shadow-lg border">
          <RefreshCw className={`h-5 w-5 text-emerald-600 ${isPullRefreshing ? 'animate-spin' : ''}`} />
        </div>
      </div>

      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Order History</h1>
          <p className="text-gray-600">
            {debouncedSearchQuery ? (
              `${filteredOrderSessions.length} order session${filteredOrderSessions.length !== 1 ? 's' : ''} found for "${debouncedSearchQuery}"`
            ) : totalCount > 0 ? (
              `${totalCount} order session${totalCount !== 1 ? 's' : ''} found`
            ) : (
              'Your complete order history'
            )}
          </p>
        </div>

        <div className="flex items-center gap-3">
          {cart.length > 0 && (
            <Button variant="outline" asChild>
              <a href="/cart">
                <ShoppingBag className="h-4 w-4 mr-2" />
                View Basket ({cart.length})
              </a>
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <OrderSearch
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="Search by order number, business name, items, address, or phone..."
          />
        </div>

        <OrderFilters
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          businessFilter={businessFilter}
          onBusinessFilterChange={setBusinessFilter}
        />
      </div>

      {/* Orders List */}
      {filteredOrderSessions.length === 0 ? (
        <EmptyOrdersState
          hasFilters={!!(statusFilter || businessFilter || debouncedSearchQuery)}
          onClearFilters={() => {
            setStatusFilter('')
            setBusinessFilter('')
            setSearchQuery('')
          }}
        />
      ) : (
        <div className="space-y-8">
          {filteredOrderSessions.map((session, index) => (
            <OrderSessionCard
              key={`${session.session_id}-${index}`}
              session={session}
              onReorder={async (sessionId) => {
                await reorderSession(sessionId, true) // true = clear cart first
              }}
              isReordering={isReordering}
              searchQuery={debouncedSearchQuery}
            />
          ))}

          {/* Load More */}
          {hasMore && (
            <div className="text-center py-6">
              <Button
                variant="outline"
                onClick={() => {
                  // TODO: Implement pagination
                  toast({
                    title: 'Pagination coming soon',
                    description: 'Load more functionality will be available soon.'
                  })
                }}
              >
                Load More Orders
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
