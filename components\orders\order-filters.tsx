"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Filter,
  Search,
  SlidersHorizontal,
} from "lucide-react"

interface OrderFiltersProps {
  onSearch: (query: string) => void
  onSort: (field: string, direction: string) => void
  onDateFilter: (filter: string) => void
  onPaymentFilter: (filter: string) => void
  currentSortField: string
  currentSortDirection: string
}

export function OrderFilters({
  onSearch,
  onSort,
  onDateFilter,
  onPaymentFilter,
  currentSortField,
  currentSortDirection
}: OrderFiltersProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [dateFilters, setDateFilters] = useState({
    today: false,
    thisWeek: true,
    thisMonth: false
  })
  const [paymentFilters, setPaymentFilters] = useState({
    paid: true,
    unpaid: false,
    refunded: false
  })

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery)
  }

  const handleDateFilterChange = (key: keyof typeof dateFilters) => {
    const newFilters = { ...dateFilters, [key]: !dateFilters[key] }
    setDateFilters(newFilters)
    
    // Find the active filter
    const activeFilter = Object.entries(newFilters)
      .filter(([_, value]) => value)
      .map(([key]) => key)[0] || 'all'
      
    onDateFilter(activeFilter)
  }

  const handlePaymentFilterChange = (key: keyof typeof paymentFilters) => {
    const newFilters = { ...paymentFilters, [key]: !paymentFilters[key] }
    setPaymentFilters(newFilters)
    
    // Find the active filter
    const activeFilter = Object.entries(newFilters)
      .filter(([_, value]) => value)
      .map(([key]) => key)[0] || 'all'
      
    onPaymentFilter(activeFilter)
  }

  // Helper to check if a sort option is currently active
  const isSortActive = (field: string, direction: string) => {
    return currentSortField === field && currentSortDirection === direction
  }

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8">
            <Filter className="mr-2 h-3.5 w-3.5" />
            Filter
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[200px]">
          <DropdownMenuLabel>Filter by Date</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem 
            checked={dateFilters.today}
            onCheckedChange={() => handleDateFilterChange('today')}
          >
            Today
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem 
            checked={dateFilters.thisWeek}
            onCheckedChange={() => handleDateFilterChange('thisWeek')}
          >
            This Week
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem 
            checked={dateFilters.thisMonth}
            onCheckedChange={() => handleDateFilterChange('thisMonth')}
          >
            This Month
          </DropdownMenuCheckboxItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Payment Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem 
            checked={paymentFilters.paid}
            onCheckedChange={() => handlePaymentFilterChange('paid')}
          >
            Paid
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem 
            checked={paymentFilters.unpaid}
            onCheckedChange={() => handlePaymentFilterChange('unpaid')}
          >
            Unpaid
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem 
            checked={paymentFilters.refunded}
            onCheckedChange={() => handlePaymentFilterChange('refunded')}
          >
            Refunded
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8">
            <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
            Sort
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <DropdownMenuLabel>Sort by</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={() => onSort("order_id", "asc")}
            className={isSortActive("order_id", "asc") ? "bg-accent text-accent-foreground" : ""}
          >
            Order ID (Asc)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("order_id", "desc")}
            className={isSortActive("order_id", "desc") ? "bg-accent text-accent-foreground" : ""}
          >
            Order ID (Desc)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("created_at", "desc")}
            className={isSortActive("created_at", "desc") ? "bg-accent text-accent-foreground" : ""}
          >
            Date (Newest First)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("created_at", "asc")}
            className={isSortActive("created_at", "asc") ? "bg-accent text-accent-foreground" : ""}
          >
            Date (Oldest First)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("customer_name", "asc")}
            className={isSortActive("customer_name", "asc") ? "bg-accent text-accent-foreground" : ""}
          >
            Customer Name (A-Z)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("customer_name", "desc")}
            className={isSortActive("customer_name", "desc") ? "bg-accent text-accent-foreground" : ""}
          >
            Customer Name (Z-A)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("total_amount", "desc")}
            className={isSortActive("total_amount", "desc") ? "bg-accent text-accent-foreground" : ""}
          >
            Total (High to Low)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => onSort("total_amount", "asc")}
            className={isSortActive("total_amount", "asc") ? "bg-accent text-accent-foreground" : ""}
          >
            Total (Low to High)
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <form onSubmit={handleSearch} className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search orders..."
          className="w-64 rounded-lg bg-background pl-8 h-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </form>
    </div>
  )
}
