"use client"
import React from "react"
import { MapPin } from "lucide-react"
import { notFound } from "next/navigation"
import OSMStaticMap from "@/components/osm-static-map"
// Removed import of deleted delivery-time-estimator
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { getRestaurantById } from "@/services/business-service"

// Add Jersey coordinates for each restaurant
const restaurantCoordinates: Record<string, [number, number]> = {
  "jersey-grill": [-2.1053, 49.1805], // St Helier
  "st-brelade-bistro": [-2.1777, 49.1872], // St Brelade
  "spice-of-jersey": [-2.1053, 49.1805], // St Helier
  "jersey-pizza-co": [-2.0933, 49.1994], // St Saviour
  "gorey-castle-cafe": [-2.0158, 49.2003], // Gorey
  "wok-this-way": [-2.1053, 49.1805], // St Helier
  "jersey-bean": [-2.1672, 49.1876], // <PERSON> Aubin
  "coastal-bites": [-2.2236, 49.2173], // St Ouen
}

// Add delivery radius for each restaurant (in kilometers)
const restaurantDeliveryRadius: Record<string, number> = {
  "jersey-grill": 5,
  "st-brelade-bistro": 4,
  "spice-of-jersey": 5,
  "jersey-pizza-co": 6,
  "gorey-castle-cafe": 3,
  "wok-this-way": 5,
  "jersey-bean": 4,
  "coastal-bites": 3,
}

export default async function RestaurantLocationPage({ params }: { params: { id: string } }) {
  const restaurant = await getRestaurantById(params.id)

  if (!restaurant) {
    notFound()
  }

  // Get coordinates for this restaurant, default to St Helier if not found
  const coordinates = restaurantCoordinates[restaurant.id] || [-2.1053, 49.1805]
  const deliveryRadius = restaurantDeliveryRadius[restaurant.id] || 5

  return (
    <div className="container-fluid py-8">
      <Link href={`/restaurants/${restaurant.id}`} className="inline-flex items-center text-emerald-600 mb-6">
        <Button variant="outline" className="mb-6">
          <MapPin className="mr-2 h-4 w-4" /> Back to {restaurant.name}
        </Button>
      </Link>

      <h1 className="text-3xl font-bold mb-6">{restaurant.name} - Location & Delivery</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <OSMStaticMap
            name={restaurant.name}
            location={restaurant.location}
            coordinates={coordinates}
            deliveryRadius={deliveryRadius}
          />

          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h3 className="text-xl font-semibold mb-4">Delivery Information</h3>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Delivery Hours</h4>
                <p className="text-gray-600">Monday - Friday: 11:00 - 22:00</p>
                <p className="text-gray-600">Saturday - Sunday: 12:00 - 23:00</p>

                <h4 className="font-semibold mt-4 mb-2">Delivery Fee</h4>
                <p className="text-gray-600">
                  {restaurant.deliveryFee === 0 ? "Free Delivery" : `£${restaurant.deliveryFee.toFixed(2)}`}
                </p>

                <h4 className="font-semibold mt-4 mb-2">Minimum Order</h4>
                <p className="text-gray-600">£15.00</p>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Delivery Area</h4>
                <p className="text-gray-600 mb-2">We deliver within a {deliveryRadius} km radius from our location.</p>

                <h4 className="font-semibold mt-4 mb-2">Estimated Delivery Times</h4>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-emerald-800 mr-2"></div>
                    <p className="text-gray-600">
                      Inner zone: {Math.max(restaurant.deliveryTime - 10, 15)}-{restaurant.deliveryTime} minutes
                    </p>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-emerald-600 mr-2"></div>
                    <p className="text-gray-600">
                      Outer zone: {restaurant.deliveryTime}-{restaurant.deliveryTime + 5} minutes
                    </p>
                  </div>
                </div>

                <p className="text-sm text-gray-500 mt-4">
                  * Delivery times may vary based on traffic, weather conditions, and order volume.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <DeliveryTimeEstimator
            restaurantId={restaurant.id}
            restaurantName={restaurant.name}
            baseDeliveryTime={restaurant.deliveryTime}
          />

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="font-semibold mb-3">Restaurant Address</h3>
            <div className="flex items-start mt-2">
              <MapPin className="h-5 w-5 text-emerald-600 mr-2 mt-0.5" />
              <div>
                <p className="font-medium">{restaurant.name}</p>
                <p className="text-gray-600">{restaurant.location}</p>
                <p className="text-gray-600">Jersey, Channel Islands</p>
              </div>
            </div>

            <div className="mt-4">
              <a
                href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(`${restaurant.name} ${restaurant.location} Jersey`)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-emerald-600 text-sm hover:underline"
              >
                View on Google Maps
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="font-semibold mb-3">Contact Information</h3>
            <div className="space-y-3">
              <p className="text-gray-600">
                <span className="font-medium">Phone:</span> {restaurant.phone}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Email:</span> info@{restaurant.id}.com
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
