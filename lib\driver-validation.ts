import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface DriverOrderValidation {
  driverId: string
  orderId?: number
  businessId?: number
}

/**
 * Validates that a driver can be assigned to an order
 * Checks all business rules and cross-table consistency
 */
export async function validateDriverOrderAssignment(
  driverId: string, 
  orderId: number
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  }

  try {
    // 1. Check if driver exists and is Loop-approved
    const { data: driver, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active, user_id')
      .eq('id', driverId)
      .single()

    if (driverError || !driver) {
      result.errors.push('Driver not found')
      result.isValid = false
      return result
    }

    if (!driver.is_verified) {
      result.errors.push('Driver not verified by Loop')
      result.isValid = false
    }

    if (!driver.is_active) {
      result.errors.push('Driver not active')
      result.isValid = false
    }

    // 2. Check if order exists and is available
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, business_id, delivery_method')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      result.errors.push('Order not found')
      result.isValid = false
      return result
    }

    if (order.status !== 'offered') {
      result.errors.push(`Order status is '${order.status}', must be 'offered'`)
      result.isValid = false
    }

    if (order.driver_id !== null) {
      result.errors.push('Order already assigned to another driver')
      result.isValid = false
    }

    if (order.delivery_method !== 'delivery') {
      result.errors.push('Order is not a delivery order')
      result.isValid = false
    }

    // 3. Check business approval
    const { data: businessApproval, error: approvalError } = await supabase
      .from('driver_business_approvals')
      .select('status')
      .eq('driver_id', driverId)
      .eq('business_id', order.business_id)
      .eq('status', 'approved')
      .single()

    if (approvalError || !businessApproval) {
      result.errors.push(`Driver not approved by business ${order.business_id}`)
      result.isValid = false
    }

    // 4. Check if driver already has an active order
    const { data: activeOrders, error: activeOrdersError } = await supabase
      .from('orders')
      .select('id, order_number, status')
      .eq('driver_id', driverId)
      .in('status', ['assigned', 'picked_up', 'on_the_way'])

    if (activeOrdersError) {
      result.warnings.push('Could not check for active orders')
    } else if (activeOrders && activeOrders.length > 0) {
      result.errors.push(`Driver already has active order: ${activeOrders[0].order_number}`)
      result.isValid = false
    }

    // 5. Check driver status consistency
    const { data: driverStatus, error: statusError } = await supabase
      .from('driver_status')
      .select('is_on_delivery, current_order_id')
      .eq('driver_id', driverId)
      .single()

    if (!statusError && driverStatus) {
      if (driverStatus.is_on_delivery && !activeOrders?.length) {
        result.warnings.push('Driver status shows on delivery but no active orders found')
      }
      if (!driverStatus.is_on_delivery && activeOrders?.length) {
        result.warnings.push('Driver has active orders but status shows not on delivery')
      }
    }

  } catch (error) {
    result.errors.push(`Validation error: ${error}`)
    result.isValid = false
  }

  return result
}

/**
 * Validates consistency between orders table and driver_status table
 */
export async function validateDriverStatusConsistency(driverId: string): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  }

  try {
    // Get active orders from orders table
    const { data: activeOrders, error: ordersError } = await supabase
      .from('orders')
      .select('id, order_number, status')
      .eq('driver_id', driverId)
      .in('status', ['assigned', 'picked_up', 'on_the_way'])

    if (ordersError) {
      result.errors.push('Could not fetch active orders')
      result.isValid = false
      return result
    }

    // Get driver status
    const { data: driverStatus, error: statusError } = await supabase
      .from('driver_status')
      .select('is_on_delivery, current_order_id')
      .eq('driver_id', driverId)
      .single()

    if (statusError) {
      result.warnings.push('Driver status record not found')
      return result
    }

    // Validate consistency
    const hasActiveOrders = activeOrders && activeOrders.length > 0
    const statusSaysOnDelivery = driverStatus.is_on_delivery

    if (hasActiveOrders && !statusSaysOnDelivery) {
      result.errors.push('Driver has active orders but status shows not on delivery')
      result.isValid = false
    }

    if (!hasActiveOrders && statusSaysOnDelivery) {
      result.errors.push('Driver status shows on delivery but no active orders found')
      result.isValid = false
    }

    if (hasActiveOrders && driverStatus.current_order_id) {
      const currentOrderExists = activeOrders.some(order => order.id === driverStatus.current_order_id)
      if (!currentOrderExists) {
        result.errors.push('Driver status current_order_id does not match any active order')
        result.isValid = false
      }
    }

  } catch (error) {
    result.errors.push(`Consistency check error: ${error}`)
    result.isValid = false
  }

  return result
}

/**
 * Syncs driver_status table to match orders table (orders table is source of truth)
 */
export async function syncDriverStatus(driverId: string): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  }

  try {
    // Get current active order from orders table (source of truth)
    const { data: activeOrder, error: orderError } = await supabase
      .from('orders')
      .select('id, order_number, status')
      .eq('driver_id', driverId)
      .in('status', ['assigned', 'picked_up', 'on_the_way'])
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    const hasActiveOrder = !orderError && activeOrder

    // Update driver_status to match
    const { error: updateError } = await supabase
      .from('driver_status')
      .update({
        is_on_delivery: hasActiveOrder,
        current_order_id: hasActiveOrder ? activeOrder.id : null,
        last_status_change: new Date().toISOString()
      })
      .eq('driver_id', driverId)

    if (updateError) {
      result.errors.push(`Failed to sync driver status: ${updateError.message}`)
      result.isValid = false
    } else {
      result.warnings.push(`Driver status synced: ${hasActiveOrder ? `on delivery (${activeOrder.order_number})` : 'available'}`)
    }

  } catch (error) {
    result.errors.push(`Sync error: ${error}`)
    result.isValid = false
  }

  return result
}

/**
 * Validates that order status changes are consistent across the system
 */
export async function validateOrderStatusChange(
  orderId: number, 
  newStatus: string, 
  driverId?: string
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  }

  try {
    // Get current order state
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, business_id')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      result.errors.push('Order not found')
      result.isValid = false
      return result
    }

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      'pending': ['offered', 'cancelled'],
      'offered': ['assigned', 'cancelled'],
      'assigned': ['picked_up', 'cancelled'],
      'picked_up': ['on_the_way', 'cancelled'],
      'on_the_way': ['delivered', 'cancelled'],
      'delivered': [], // Terminal state
      'cancelled': [] // Terminal state
    }

    const allowedNextStatuses = validTransitions[order.status] || []
    if (!allowedNextStatuses.includes(newStatus)) {
      result.errors.push(`Invalid status transition from '${order.status}' to '${newStatus}'`)
      result.isValid = false
    }

    // Validate driver assignment consistency
    if (newStatus === 'delivered' || newStatus === 'cancelled') {
      // Order is completing - driver should be freed up
      if (order.driver_id && driverId && order.driver_id !== driverId) {
        result.errors.push('Order assigned to different driver')
        result.isValid = false
      }
    }

    // Check business approval if assigning to driver
    if (newStatus === 'assigned' && driverId) {
      const { data: approval, error: approvalError } = await supabase
        .from('driver_business_approvals')
        .select('status')
        .eq('driver_id', driverId)
        .eq('business_id', order.business_id)
        .eq('status', 'approved')
        .single()

      if (approvalError || !approval) {
        result.errors.push(`Driver ${driverId} not approved for business ${order.business_id}`)
        result.isValid = false
      }
    }

  } catch (error) {
    result.errors.push(`Status validation error: ${error}`)
    result.isValid = false
  }

  return result
}
