import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

// Check if environment variables are available
if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ CUSTOMER ORDERS API: Missing Supabase environment variables:', {
    hasUrl: !!supabaseUrl,
    hasServiceKey: !!supabaseServiceKey
  })
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export async function GET(request: NextRequest) {
  try {
    // Check environment variables first
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ CUSTOMER ORDERS API: Missing required environment variables')
      return NextResponse.json(
        {
          error: 'Server configuration error - missing Supabase credentials',
          details: {
            hasUrl: !!supabaseUrl,
            hasServiceKey: !!supabaseServiceKey
          }
        },
        { status: 500 }
      )
    }

    const { searchParams } = new URL(request.url)

    // Get query parameters
    const userId = searchParams.get('userId')
    const sessionId = searchParams.get('sessionId')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    const status = searchParams.get('status') // Filter by status
    const businessId = searchParams.get('businessId') // Filter by business

    console.log('🔍 CUSTOMER ORDERS API: Request parameters:', {
      userId,
      sessionId,
      limit,
      offset,
      status,
      businessId
    })

    // Validate that we have either userId or sessionId
    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'Either userId or sessionId is required' },
        { status: 400 }
      )
    }

    // Build the base query
    let query = supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        business_id,
        status,
        subtotal,
        delivery_fee,
        service_fee,
        total,
        delivery_method,
        delivery_type,
        estimated_delivery_time,
        delivery_address,
        customer_name,
        customer_phone,
        parish,
        postcode,
        created_at,
        updated_at,
        session_id,
        user_id,
        cart_id
      `)

    // Add user/session filter
    if (userId && sessionId) {
      // For logged-in users, get orders by both user_id and session_id
      // Use a more explicit OR syntax
      query = query.or(`user_id.eq."${userId}",session_id.eq."${sessionId}"`)
    } else if (userId) {
      // For logged-in users without session_id (legacy orders)
      query = query.eq('user_id', userId)
    } else {
      // For guest users
      query = query.eq('session_id', sessionId)
    }

    // Add optional filters
    if (status) {
      query = query.eq('status', status)
    }

    if (businessId) {
      query = query.eq('business_id', parseInt(businessId))
    }

    // Only include orders that have session_id for proper grouping
    // (legacy orders without session_id will be shown individually)
    query = query.not('session_id', 'is', null)

    // Add ordering and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    console.log('🔍 CUSTOMER ORDERS API: Executing query...')
    const { data: orders, error } = await query

    if (error) {
      console.error('❌ CUSTOMER ORDERS API: Database error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return NextResponse.json(
        {
          error: 'Failed to fetch orders',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      )
    }

    console.log(`✅ CUSTOMER ORDERS API: Found ${orders?.length || 0} orders`)

    if (!orders || orders.length === 0) {
      return NextResponse.json({
        success: true,
        orderSessions: [],
        totalCount: 0,
        hasMore: false
      })
    }

    // Fetch cart items for all orders
    const cartIds = orders.filter(order => order.cart_id).map(order => order.cart_id)
    let allCartItems: any[] = []

    if (cartIds.length > 0) {
      const { data: cartItemsData, error: cartItemsError } = await supabase
        .from('cart_items')
        .select(`
          id,
          cart_id,
          product_id,
          name,
          price,
          quantity,
          business_id,
          image_url,
          variant_id
        `)
        .in('cart_id', cartIds)

      if (cartItemsError) {
        console.warn('⚠️ CUSTOMER ORDERS API: Error fetching cart items:', cartItemsError)
      } else {
        allCartItems = cartItemsData || []
      }
    }

    // Group cart items by cart_id for easy lookup
    const cartItemsByCartId = new Map<string, any[]>()
    allCartItems.forEach(item => {
      if (!cartItemsByCartId.has(item.cart_id)) {
        cartItemsByCartId.set(item.cart_id, [])
      }
      cartItemsByCartId.get(item.cart_id)!.push(item)
    })

    // Group orders by session_id and customer_address
    const sessionGroups = new Map<string, {
      session_id: string
      session_date: Date
      customer_address: string
      customer_name: string
      customer_phone: string
      parish: string
      postcode: string
      business_count: number
      session_total: number
      business_orders: any[]
    }>()

    orders.forEach(order => {
      const sessionKey = `${order.session_id}_${order.delivery_address}`

      if (!sessionGroups.has(sessionKey)) {
        sessionGroups.set(sessionKey, {
          session_id: order.session_id,
          session_date: new Date(order.created_at),
          customer_address: order.delivery_address,
          customer_name: order.customer_name,
          customer_phone: order.customer_phone,
          parish: order.parish,
          postcode: order.postcode,
          business_count: 0,
          session_total: 0,
          business_orders: []
        })
      }

      const sessionGroup = sessionGroups.get(sessionKey)!

      // Get cart items for this order
      const orderCartItems = order.cart_id ? cartItemsByCartId.get(order.cart_id) || [] : []

      // Add business order to the session group
      sessionGroup.business_orders.push({
        order_id: order.id,
        order_number: order.order_number,
        business_name: order.business_name,
        business_id: order.business_id,
        status: order.status,
        subtotal: order.subtotal,
        delivery_fee: order.delivery_fee,
        service_fee: order.service_fee,
        total: order.total,
        delivery_method: order.delivery_method,
        delivery_type: order.delivery_type,
        estimated_delivery_time: order.estimated_delivery_time,
        created_at: order.created_at,
        updated_at: order.updated_at,
        cart_id: order.cart_id,
        cart_items: orderCartItems
      })

      // Update session totals
      sessionGroup.business_count = sessionGroup.business_orders.length
      sessionGroup.session_total = sessionGroup.business_orders.reduce(
        (total, businessOrder) => total + (businessOrder.total || 0),
        0
      )
    })

    // Convert map to array and sort by session date (newest first)
    const orderSessions = Array.from(sessionGroups.values())
      .sort((a, b) => b.session_date.getTime() - a.session_date.getTime())

    // Calculate total item count across all business orders
    const totalItemCount = orderSessions.reduce((total, session) => {
      return total + session.business_orders.length
    }, 0)

    console.log(`📦 CUSTOMER ORDERS API: Grouped into ${orderSessions.length} order sessions`)
    console.log('📦 CUSTOMER ORDERS API: Session summary:', orderSessions.map(session => ({
      session_id: session.session_id,
      business_count: session.business_count,
      session_total: session.session_total,
      date: session.session_date.toISOString()
    })))

    return NextResponse.json({
      success: true,
      orderSessions,
      totalCount: orderSessions.length,
      totalItemCount,
      hasMore: orders.length === limit, // Simple pagination check
      pagination: {
        limit,
        offset,
        returned: orderSessions.length
      }
    })

  } catch (error) {
    console.error('❌ CUSTOMER ORDERS API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST endpoint for additional operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, orderId, sessionId } = body

    console.log('🔄 CUSTOMER ORDERS API POST: Action requested:', { action, orderId, sessionId })

    switch (action) {
      case 'reorder_business':
        return await handleReorderBusiness(orderId)

      case 'reorder_session':
        return await handleReorderSession(sessionId)

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('❌ CUSTOMER ORDERS API POST: Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle reordering from a specific business order
async function handleReorderBusiness(orderId: string) {
  try {
    console.log('🔄 REORDER BUSINESS: Starting reorder for order:', orderId)

    // Get the original order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        id,
        business_id,
        business_name,
        cart_id,
        delivery_method,
        delivery_address,
        customer_name,
        customer_phone,
        parish,
        postcode
      `)
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      console.error('❌ REORDER BUSINESS: Order not found:', orderError)
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Get the cart items for this order
    const { data: cartItems, error: cartError } = await supabase
      .from('cart_items')
      .select(`
        product_id,
        variant_id,
        quantity,
        price,
        product_name,
        variant_name
      `)
      .eq('cart_id', order.cart_id)

    if (cartError) {
      console.error('❌ REORDER BUSINESS: Error fetching cart items:', cartError)
      return NextResponse.json(
        { error: 'Failed to fetch order items' },
        { status: 500 }
      )
    }

    console.log(`✅ REORDER BUSINESS: Found ${cartItems?.length || 0} items to reorder`)

    // Return the reorder data for the frontend to handle
    return NextResponse.json({
      success: true,
      message: 'Reorder data retrieved successfully',
      reorderData: {
        businessId: order.business_id,
        businessName: order.business_name,
        items: cartItems || [],
        deliveryMethod: order.delivery_method,
        deliveryAddress: order.delivery_address,
        customerInfo: {
          name: order.customer_name,
          phone: order.customer_phone,
          parish: order.parish,
          postcode: order.postcode
        }
      }
    })

  } catch (error) {
    console.error('❌ REORDER BUSINESS: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Failed to process reorder' },
      { status: 500 }
    )
  }
}

// Handle reordering an entire session (all businesses)
async function handleReorderSession(sessionId: string) {
  try {
    console.log('🔄 REORDER SESSION: Starting reorder for session:', sessionId)

    // Get all orders in this session
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        business_id,
        business_name,
        cart_id,
        delivery_method,
        delivery_address,
        customer_name,
        customer_phone,
        parish,
        postcode
      `)
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true })

    if (ordersError || !orders || orders.length === 0) {
      console.error('❌ REORDER SESSION: Orders not found:', ordersError)
      return NextResponse.json(
        { error: 'Session orders not found' },
        { status: 404 }
      )
    }

    console.log(`🔄 REORDER SESSION: Found ${orders.length} orders in session`)

    // Get cart items for all orders in the session
    const cartIds = orders.map(order => order.cart_id)
    const { data: allCartItems, error: cartError } = await supabase
      .from('cart_items')
      .select(`
        cart_id,
        product_id,
        variant_id,
        quantity,
        price,
        product_name,
        variant_name
      `)
      .in('cart_id', cartIds)

    if (cartError) {
      console.error('❌ REORDER SESSION: Error fetching cart items:', cartError)
      return NextResponse.json(
        { error: 'Failed to fetch session items' },
        { status: 500 }
      )
    }

    // Group items by business
    const businessReorders = orders.map(order => {
      const orderItems = allCartItems?.filter(item => item.cart_id === order.cart_id) || []

      return {
        businessId: order.business_id,
        businessName: order.business_name,
        items: orderItems,
        deliveryMethod: order.delivery_method,
        deliveryAddress: order.delivery_address,
        customerInfo: {
          name: order.customer_name,
          phone: order.customer_phone,
          parish: order.parish,
          postcode: order.postcode
        }
      }
    })

    console.log(`✅ REORDER SESSION: Prepared reorder data for ${businessReorders.length} businesses`)

    return NextResponse.json({
      success: true,
      message: 'Session reorder data retrieved successfully',
      reorderData: {
        sessionId,
        businesses: businessReorders,
        totalBusinesses: businessReorders.length,
        totalItems: allCartItems?.length || 0
      }
    })

  } catch (error) {
    console.error('❌ REORDER SESSION: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Failed to process session reorder' },
      { status: 500 }
    )
  }
}
