import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { orderId } = await request.json()

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Check if order exists and is available
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, business_name, order_number')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.status !== 'ready') {
      return NextResponse.json(
        { error: `Order cannot be declined. Current status: ${order.status}` },
        { status: 409 }
      )
    }

    if (order.driver_id && order.driver_id !== driverProfile.id) {
      return NextResponse.json(
        { error: "Order is already assigned to another driver" },
        { status: 409 }
      )
    }

    // Record order decline in shift tracking
    const { error: shiftTrackingError } = await supabase
      .rpc('record_shift_order_offer', {
        p_driver_id: driverProfile.id,
        p_order_id: orderId,
        p_action: 'declined'
      })

    if (shiftTrackingError) {
      console.error('Error recording shift order decline:', shiftTrackingError)
      // Don't fail the request if shift tracking fails
    }

    // Log the decline action (for analytics/tracking)
    const { error: logError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverProfile.id,
        activity_type: 'order_declined',
        related_order_id: orderId,
        notes: `Driver declined order ${order.order_number}`,
        timestamp: new Date().toISOString()
      })

    if (logError) {
      console.error('Error logging decline action:', logError)
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: `Order ${order.order_number} declined`,
      orderId: orderId,
      orderNumber: order.order_number
    })

  } catch (error) {
    console.error('Error in decline order API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
