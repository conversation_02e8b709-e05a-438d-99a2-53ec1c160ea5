'use client'

import { useState, useEffect, useMemo } from 'react'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { X, Loader2 } from 'lucide-react'
import { useAttributeFilters, FilterOption, FilterCategory } from '@/hooks/use-attribute-filters'
import { getBusinessTypeColors } from '@/utils/business-colors'
import { useDeliveryMode } from '@/context/delivery-mode-context'

type PriceRange = [number, number]

type FilterSidebarProps = {
  businessType: string
  activeFilters: string[]
  onFilterChange: (filterId: string) => void
  onClearFilters: () => void
  priceRange: PriceRange
  onPriceRangeChange: (range: PriceRange) => void
  maxDeliveryTime: number
  onMaxDeliveryTimeChange: (time: number) => void
  maxPrepTime: number
  onMaxPrepTimeChange: (time: number) => void
  onTypeChange?: (type: string) => void
}

export default function FilterSidebar({
  businessType,
  activeFilters,
  onFilterChange,
  onClearFilters,
  priceRange,
  onPriceRangeChange,
  maxDeliveryTime,
  onMaxDeliveryTimeChange,
  maxPrepTime,
  onMaxPrepTimeChange,
  onTypeChange
}: FilterSidebarProps) {
  // Get common filter categories
  const commonCategories = getCommonFilterCategories()

  // Get dynamic attribute filters based on business type
  const {
    attributeCategories,
    loading: attributesLoading,
    error: attributesError
  } = useAttributeFilters(businessType)

  // Combine common categories with attribute categories, avoiding duplicates
  const filterCategories = useMemo(() => {
    const categoryMap = new Map<string, FilterCategory>();

    // Add common categories first
    commonCategories.forEach(category => {
      categoryMap.set(category.id, category);
    });

    // Add attribute categories, merging options if category already exists
    attributeCategories.forEach(category => {
      if (categoryMap.has(category.id)) {
        // Merge options if category already exists
        const existingCategory = categoryMap.get(category.id)!;
        const mergedOptions = [...existingCategory.options];

        // Add new options that don't already exist
        category.options.forEach(option => {
          if (!mergedOptions.some(existing => existing.id === option.id)) {
            mergedOptions.push(option);
          }
        });

        categoryMap.set(category.id, {
          ...existingCategory,
          options: mergedOptions
        });
      } else {
        categoryMap.set(category.id, category);
      }
    });

    return Array.from(categoryMap.values());
  }, [commonCategories, attributeCategories]);

  return (
    <div className="w-full p-4">
      <div className="flex justify-end mb-4">
        {activeFilters.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-gray-500 hover:text-gray-700 p-0 h-auto"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Time Filters - Shows both Delivery Time and Prep Time */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <DeliveryTimeFilterInput
          maxTime={maxDeliveryTime}
          onMaxTimeChange={onMaxDeliveryTimeChange}
        />
        <PrepTimeFilterInput
          maxTime={maxPrepTime}
          onMaxTimeChange={onMaxPrepTimeChange}
        />
      </div>

      {/* Business Type */}
      {onTypeChange && (
        <Accordion type="multiple" defaultValue={[]} className="mb-6 border-b border-gray-200 pb-6">
          <AccordionItem value="business-type" className="border-none">
            <AccordionTrigger className="py-2 text-gray-800 hover:no-underline px-0">
              <span className="font-medium text-gray-700 text-sm">Business Type</span>
            </AccordionTrigger>
            <AccordionContent className="pt-3 pb-2">
              <RadioGroup
                defaultValue={businessType}
                onValueChange={onTypeChange}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="type-all" />
                  <Label htmlFor="type-all" className="text-sm cursor-pointer">All</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="restaurant" id="type-restaurant" />
                  <Label htmlFor="type-restaurant" className="text-sm cursor-pointer">Restaurants</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="shop" id="type-shop" />
                  <Label htmlFor="type-shop" className="text-sm cursor-pointer">Shops</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pharmacy" id="type-pharmacy" />
                  <Label htmlFor="type-pharmacy" className="text-sm cursor-pointer">Pharmacy</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="cafe" id="type-cafe" />
                  <Label htmlFor="type-cafe" className="text-sm cursor-pointer">Cafe</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="errand" id="type-errand" />
                  <Label htmlFor="type-errand" className="text-sm cursor-pointer">Errand</Label>
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}

      {/* Filter Categories */}
      {attributesLoading ? (
        <div className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
          <span className="ml-2 text-sm text-gray-600">Loading filters...</span>
        </div>
      ) : (
        <Accordion type="multiple" defaultValue={[]} className="space-y-0 divide-y divide-gray-200">
          {filterCategories.map((category) => (
            <AccordionItem key={category.id} value={category.id} className="border-none pt-4 pb-2">
              <AccordionTrigger className="py-2 text-gray-800 hover:no-underline">
                <span className="font-medium text-sm">{category.title}</span>
              </AccordionTrigger>
              <AccordionContent className="pt-3 pb-2">
                <div className="space-y-2">
                  {category.options.map((option) => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.id}
                        checked={activeFilters.includes(option.id)}
                        onCheckedChange={() => onFilterChange(option.id)}
                      />
                      <label
                        htmlFor={option.id}
                        className="text-sm text-gray-700 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="font-medium text-gray-700 mb-3 text-sm">Active Filters</h4>
          <div className="flex flex-wrap gap-2">
            {activeFilters.map((filterId) => {
              const filterLabel = findFilterLabel(filterCategories, filterId)
              return (
                <div
                  key={filterId}
                  className="flex items-center bg-emerald-50 text-emerald-700 px-2 py-1 rounded-full text-sm"
                >
                  {filterLabel}
                  <button
                    onClick={() => onFilterChange(filterId)}
                    className="ml-1 text-emerald-700 hover:text-emerald-900"
                  >
                    <X size={14} />
                  </button>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to find filter label
function findFilterLabel(categories: FilterCategory[], filterId: string): string {
  for (const category of categories) {
    const option = category.options.find(opt => opt.id === filterId)
    if (option) return option.label
  }
  return filterId
}

// Helper function to get common filter categories
function getCommonFilterCategories(): FilterCategory[] {
  return [
    {
      id: 'offers',
      title: 'Offers',
      options: [
        { id: 'free_delivery', label: 'Free Delivery' },
        { id: 'deals', label: 'Deals' },
        { id: 'discounts', label: 'Discounts' }
      ]
    },
    {
      id: 'rating',
      title: 'Rating',
      options: [
        { id: 'rating_4.5', label: '4.5+' },
        { id: 'rating_4', label: '4.0+' },
        { id: 'rating_3.5', label: '3.5+' }
      ]
    }
  ]
}

// Time Filter Input component for delivery time
function DeliveryTimeFilterInput({
  maxTime,
  onMaxTimeChange
}: {
  maxTime: number,
  onMaxTimeChange: (time: number) => void
}) {
  const [inputValue, setInputValue] = useState(maxTime.toString())

  // Update the input value when maxTime changes
  useEffect(() => {
    setInputValue(maxTime.toString())
  }, [maxTime])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  // Handle input blur - apply the filter
  const handleInputBlur = () => {
    const value = parseInt(inputValue)
    if (!isNaN(value) && value > 0) {
      onMaxTimeChange(value)
    } else {
      // Reset to current maxTime if invalid
      setInputValue(maxTime.toString())
    }
  }

  // Handle key press - apply on Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur()
    }
  }

  return (
    <div className="py-3">
      <div className="flex items-center justify-between">
        <span className="font-medium text-gray-700 text-sm">
          Max. Delivery Time (mins)
        </span>
        <div className="flex items-center">
          <input
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyDown={handleKeyDown}
            min="1"
            max="60"
            className="w-14 h-9 px-0 border border-gray-300 rounded-md text-sm text-center focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  )
}

// Time Filter Input component for prep time
function PrepTimeFilterInput({
  maxTime,
  onMaxTimeChange
}: {
  maxTime: number,
  onMaxTimeChange: (time: number) => void
}) {
  const [inputValue, setInputValue] = useState(maxTime.toString())

  // Update the input value when maxTime changes
  useEffect(() => {
    setInputValue(maxTime.toString())
  }, [maxTime])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  // Handle input blur - apply the filter
  const handleInputBlur = () => {
    const value = parseInt(inputValue)
    if (!isNaN(value) && value > 0) {
      onMaxTimeChange(value)
    } else {
      // Reset to current maxTime if invalid
      setInputValue(maxTime.toString())
    }
  }

  // Handle key press - apply on Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur()
    }
  }

  return (
    <div className="py-3">
      <div className="flex items-center justify-between">
        <span className="font-medium text-gray-700 text-sm">
          Max. Prep. Time (mins)
        </span>
        <div className="flex items-center">
          <input
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyDown={handleKeyDown}
            min="1"
            max="45"
            className="w-14 h-9 px-0 border border-gray-300 rounded-md text-sm text-center focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  )
}
