"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Upload, Car, Bike, Truck, AlertCircle, CheckCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"
import { useAuth } from "@/context/unified-auth-context"

export default function DriverApplicationPage() {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState<"email" | "details" | "application">("email")
  const [existingUserData, setExistingUserData] = useState<any>(null)
  const [isCheckingEmail, setIsCheckingEmail] = useState(false)
  const [formData, setFormData] = useState({
    // Personal Information (from users table)
    firstName: "",
    lastName: "",
    email: "",
    phone: "",

    // Driver Profile Information
    vehicleType: "",
    licenseNumber: "",
    insuranceNumber: "",
    vehicleRegistration: "",
    profileImageUrl: "",
    notes: "",

    // Additional application fields
    hasValidLicense: false,
    hasInsurance: false,
    hasVehicleRegistration: false,
    agreeToTerms: false,
    agreeToBackgroundCheck: false,
    previousDeliveryExperience: "",
    availabilityHours: "",
    preferredAreas: ""
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle")

  // Pre-populate form with user data if logged in
  useEffect(() => {
    if (user?.profile) {
      setFormData(prev => ({
        ...prev,
        firstName: user.profile.first_name || "",
        lastName: user.profile.last_name || "",
        email: user.profile.email || "",
        phone: user.profile.phone || ""
      }))
      setExistingUserData(user.profile)
      // Skip email step if user is logged in
      setCurrentStep("details")
    }
  }, [user])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const checkEmailForExistingUser = async (email: string) => {
    if (!email || !email.includes('@')) return

    setIsCheckingEmail(true)
    try {
      const response = await fetch('/api/user/check-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: email.toLowerCase() })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.exists && data.user) {
          setExistingUserData(data.user)
          setFormData(prev => ({
            ...prev,
            firstName: data.user.first_name || "",
            lastName: data.user.last_name || "",
            email: data.user.email || email,
            phone: data.user.phone || ""
          }))

          // Check if they already have a driver profile
          if (data.hasDriverProfile) {
            setSubmitStatus("error")
            return
          }
        } else {
          setExistingUserData(null)
        }
      }
    } catch (error) {
      console.error('Error checking email:', error)
    } finally {
      setIsCheckingEmail(false)
    }
  }

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.email) return

    await checkEmailForExistingUser(formData.email)
    setCurrentStep("details")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare the data for submission
      const submissionData = {
        // Personal information for user creation (if not logged in)
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,

        // Driver profile data
        vehicleType: formData.vehicleType,
        licenseNumber: formData.licenseNumber,
        insuranceNumber: formData.insuranceNumber,
        vehicleRegistration: formData.vehicleRegistration,
        notes: formData.notes,

        // Additional application data
        previousDeliveryExperience: formData.previousDeliveryExperience,
        availabilityHours: formData.availabilityHours,
        preferredAreas: formData.preferredAreas,

        // Current user ID if logged in
        userId: user?.id || null
      }

      console.log('Submitting driver application with data:', submissionData)

      const response = await fetch('/api/driver/apply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submissionData)
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.error || 'Failed to submit application')
      }

      const result = await response.json()
      console.log('Application submitted successfully:', result)
      setSubmitStatus("success")
    } catch (error) {
      console.error('Error submitting application:', error)
      setSubmitStatus("error")
    } finally {
      setIsSubmitting(false)
    }
  }

  const isDetailsValid = () => {
    return formData.firstName && formData.lastName && formData.phone
  }

  const isFormValid = () => {
    const basicFieldsValid = (
      formData.firstName &&
      formData.lastName &&
      formData.email &&
      formData.phone &&
      formData.vehicleType &&
      formData.agreeToTerms &&
      formData.agreeToBackgroundCheck
    )

    // Check if vehicle requires documentation
    const requiresDocumentation = ['car', 'van', 'motorcycle'].includes(formData.vehicleType)

    if (requiresDocumentation) {
      return basicFieldsValid &&
             formData.hasValidLicense &&
             formData.hasInsurance &&
             formData.hasVehicleRegistration
    }

    return basicFieldsValid
  }

  if (submitStatus === "success") {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <CheckCircle className="h-16 w-16 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Application Submitted Successfully!</CardTitle>
              <CardDescription>Thank you for applying to become a Loop Jersey driver/rider</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">What happens next?</h3>
                <ul className="text-sm space-y-1 text-left">
                  <li>• Our team will review your application within 48 hours</li>
                  <li>• We'll verify your documents and conduct a background check</li>
                  <li>• You'll receive an email with your approval status</li>
                  <li>• Once approved, you can start applying to businesses</li>
                </ul>
              </div>
              <div className="pt-4">
                <Link href="/partners/riders">
                  <Button variant="outline" className="w-full">
                    Back to Driver/Rider Information
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (submitStatus === "error") {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <AlertCircle className="h-16 w-16 text-red-600" />
              </div>
              <CardTitle className="text-2xl">Application Already Exists</CardTitle>
              <CardDescription>You already have a driver application on file</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-red-50 p-4 rounded-lg">
                <p className="text-red-800 text-sm">
                  We found an existing driver application for this email address.
                  If you need to update your information or check your application status,
                  please contact our support team.
                </p>
              </div>
              <div className="flex gap-4 pt-4">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    setSubmitStatus("idle")
                    setCurrentStep("email")
                    setFormData(prev => ({ ...prev, email: "" }))
                  }}
                >
                  Try Different Email
                </Button>
                <Link href="/contact" className="flex-1">
                  <Button className="w-full">
                    Contact Support
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Email Step Component
  const EmailStep = () => (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Driver/Rider Application</h1>
          <p className="text-muted-foreground">Let's start with your email address</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Email Address</CardTitle>
            <CardDescription>
              We'll check if you already have an account with us to make the process easier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={!formData.email || isCheckingEmail}
              >
                {isCheckingEmail ? "Checking..." : "Continue"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  // Details Step Component
  const DetailsStep = () => (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Personal Information</h1>
          {existingUserData ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <p className="text-green-800">
                ✅ We found your existing account! You can review and update your information below.
              </p>
            </div>
          ) : (
            <p className="text-muted-foreground">Please provide your personal details</p>
          )}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Your Details</CardTitle>
            <CardDescription>
              {existingUserData
                ? "Review and update your information if needed"
                : "This information will be used to create your account"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email-readonly">Email Address</Label>
                <Input
                  id="email-readonly"
                  type="email"
                  value={formData.email}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+44 1534 ..."
                  required
                />
              </div>
            </div>

            <div className="flex gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep("email")}
              >
                Back
              </Button>
              <Button
                type="button"
                onClick={() => setCurrentStep("application")}
                disabled={!isDetailsValid()}
                className="flex-1"
              >
                Continue to Application
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  // Show appropriate step
  if (currentStep === "email") {
    return <EmailStep />
  }

  if (currentStep === "details") {
    return <DetailsStep />
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Driver/Rider Application</h1>
          <p className="text-muted-foreground">Complete your application to join Loop Jersey's delivery network</p>
          <div className="flex justify-center gap-2 mt-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Car className="h-3 w-3" />
              Cars
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Bike className="h-3 w-3" />
              Motorcycles
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Bike className="h-3 w-3" />
              Bicycles
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Truck className="h-3 w-3" />
              Vans
            </Badge>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center justify-center gap-2 mt-6">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
              <span className="ml-2 text-sm text-green-600">Email</span>
            </div>
            <div className="w-8 h-px bg-green-600"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
              <span className="ml-2 text-sm text-green-600">Details</span>
            </div>
            <div className="w-8 h-px bg-green-600"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
              <span className="ml-2 text-sm text-blue-600">Application</span>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Application Summary</CardTitle>
              <CardDescription>Review your information before continuing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Name:</span>
                  <p>{formData.firstName} {formData.lastName}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Email:</span>
                  <p>{formData.email}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Phone:</span>
                  <p>{formData.phone}</p>
                </div>
                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentStep("details")}
                  >
                    Edit Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Information</CardTitle>
              <CardDescription>Details about your delivery vehicle</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="vehicleType">Vehicle Type *</Label>
                <Select value={formData.vehicleType} onValueChange={(value) => handleInputChange("vehicleType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your vehicle type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="car">Car</SelectItem>
                    <SelectItem value="motorcycle">Motorcycle</SelectItem>
                    <SelectItem value="bicycle">Bicycle</SelectItem>
                    <SelectItem value="van">Van</SelectItem>
                    <SelectItem value="scooter">Scooter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="licenseNumber">Driver's License Number</Label>
                  <Input
                    id="licenseNumber"
                    value={formData.licenseNumber}
                    onChange={(e) => handleInputChange("licenseNumber", e.target.value)}
                    placeholder="Jersey license number"
                  />
                </div>
                <div>
                  <Label htmlFor="vehicleRegistration">Vehicle Registration</Label>
                  <Input
                    id="vehicleRegistration"
                    value={formData.vehicleRegistration}
                    onChange={(e) => handleInputChange("vehicleRegistration", e.target.value)}
                    placeholder="Vehicle registration number"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="insuranceNumber">Insurance Policy Number</Label>
                <Input
                  id="insuranceNumber"
                  value={formData.insuranceNumber}
                  onChange={(e) => handleInputChange("insuranceNumber", e.target.value)}
                  placeholder="Insurance policy number"
                />
              </div>
            </CardContent>
          </Card>

          {/* Experience & Availability */}
          <Card>
            <CardHeader>
              <CardTitle>Experience & Availability</CardTitle>
              <CardDescription>Tell us about your experience and when you'd like to work</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="previousDeliveryExperience">Previous Delivery Experience</Label>
                <Textarea
                  id="previousDeliveryExperience"
                  value={formData.previousDeliveryExperience}
                  onChange={(e) => handleInputChange("previousDeliveryExperience", e.target.value)}
                  placeholder="Tell us about any previous delivery or driving experience..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="availabilityHours">Preferred Working Hours</Label>
                <Textarea
                  id="availabilityHours"
                  value={formData.availabilityHours}
                  onChange={(e) => handleInputChange("availabilityHours", e.target.value)}
                  placeholder="When would you prefer to work? (e.g., weekday evenings, weekends, flexible)"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="preferredAreas">Preferred Delivery Areas</Label>
                <Textarea
                  id="preferredAreas"
                  value={formData.preferredAreas}
                  onChange={(e) => handleInputChange("preferredAreas", e.target.value)}
                  placeholder="Which parishes or areas would you prefer to deliver to?"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Anything else you'd like us to know?"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Document Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Profile Photo</CardTitle>
              <CardDescription>Upload a clear photo of yourself for your profile</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 mb-2">Click to upload or drag and drop</p>
                <p className="text-xs text-gray-500">PNG, JPG up to 5MB</p>
                <Input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    // Handle file upload
                    const file = e.target.files?.[0]
                    if (file) {
                      // You would upload to your storage service here
                      console.log("File selected:", file.name)
                    }
                  }}
                />
              </div>
            </CardContent>
          </Card>

          {/* Verification Checklist */}
          <Card>
            <CardHeader>
              <CardTitle>Verification Checklist</CardTitle>
              <CardDescription>Please confirm you have the required documents</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {['car', 'van', 'motorcycle'].includes(formData.vehicleType) && (
                <>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasValidLicense"
                      checked={formData.hasValidLicense}
                      onCheckedChange={(checked) => handleInputChange("hasValidLicense", checked as boolean)}
                    />
                    <Label htmlFor="hasValidLicense">I have a valid driver's license *</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasInsurance"
                      checked={formData.hasInsurance}
                      onCheckedChange={(checked) => handleInputChange("hasInsurance", checked as boolean)}
                    />
                    <Label htmlFor="hasInsurance">I have valid vehicle insurance *</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasVehicleRegistration"
                      checked={formData.hasVehicleRegistration}
                      onCheckedChange={(checked) => handleInputChange("hasVehicleRegistration", checked as boolean)}
                    />
                    <Label htmlFor="hasVehicleRegistration">I have vehicle registration documents *</Label>
                  </div>
                </>
              )}

              {['bicycle', 'scooter'].includes(formData.vehicleType) && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Good news!</strong> No additional documentation is required for {formData.vehicleType} delivery.
                  </p>
                </div>
              )}

              {!formData.vehicleType && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">
                    Please select your vehicle type above to see the required documentation.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          <Card>
            <CardHeader>
              <CardTitle>Terms and Conditions *</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked as boolean)}
                />
                <Label htmlFor="agreeToTerms" className="text-sm leading-relaxed">
                  I agree to the <Link href="/terms" className="text-emerald-600 hover:underline">Terms of Service</Link> and understand that I will be working as an independent contractor, not an employee of Loop Jersey. *
                </Label>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="agreeToBackgroundCheck"
                  checked={formData.agreeToBackgroundCheck}
                  onCheckedChange={(checked) => handleInputChange("agreeToBackgroundCheck", checked as boolean)}
                />
                <Label htmlFor="agreeToBackgroundCheck" className="text-sm leading-relaxed">
                  I consent to a background check and document verification as part of the application process. *
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="space-y-4">
            {submitStatus === "error" && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  There was an error submitting your application. Please try again.
                </AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-lg py-6"
              disabled={!isFormValid() || isSubmitting}
            >
              {isSubmitting ? "Submitting Application..." : "Submit Application"}
            </Button>

            <p className="text-center text-sm text-muted-foreground">
              By submitting this application, you acknowledge that you understand the independent contractor relationship and business approval process.
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
