"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface CartItem {
  id: string
  name: string
  quantity: number
  price: number
  weight_class_kg: number | null
  thermal_requirement: string | null
  size_category: string | null
  business_id: number
  cart_id: string
}

export default function TestCartAttributesPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCartItems = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Get cart items directly from database
      const response = await fetch('/api/test-cart-items')
      const result = await response.json()
      
      if (response.ok) {
        setCartItems(result.cartItems || [])
      } else {
        setError(result.error || 'Failed to fetch cart items')
      }
    } catch (err) {
      console.error('Error fetching cart items:', err)
      setError('Failed to fetch cart items')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCartItems()
  }, [])

  const getThermalBadge = (thermal: string | null) => {
    if (!thermal) return <Badge variant="secondary">Undeclared</Badge>
    
    switch (thermal) {
      case 'hot':
        return <Badge className="bg-red-500 text-white">Hot</Badge>
      case 'cold':
        return <Badge className="bg-blue-500 text-white">Cold</Badge>
      case 'none':
        return <Badge className="bg-green-500 text-white">None</Badge>
      default:
        return <Badge variant="secondary">{thermal}</Badge>
    }
  }

  const getWeightBadge = (weight: number | null) => {
    if (weight === null) return <Badge variant="outline" className="text-amber-600">Undeclared</Badge>
    return <Badge variant="outline">{weight}kg</Badge>
  }

  const getSizeBadge = (size: string | null) => {
    if (!size) return <Badge variant="secondary">Undeclared</Badge>
    return <Badge variant="outline" className="capitalize">{size.replace('_', ' ')}</Badge>
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-900">
              🛒 Cart Items Delivery Attributes Test
            </CardTitle>
            <p className="text-gray-600">
              Testing that delivery attributes are correctly transferred from products to cart_items table
            </p>
            <div className="flex space-x-2">
              <Button onClick={fetchCartItems} disabled={loading}>
                {loading ? 'Loading...' : 'Refresh Cart Items'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 mb-4">
                {error}
              </div>
            )}
            
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading cart items...</p>
              </div>
            ) : cartItems.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-600">No cart items found</p>
                <p className="text-sm text-gray-500 mt-2">
                  Add some items to cart from Jersey Grill or Jersey Wings to test
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Found {cartItems.length} cart items with delivery attributes:
                </h3>
                
                <div className="grid gap-4">
                  {cartItems.map((item, index) => (
                    <Card key={index} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{item.name}</h4>
                            <p className="text-sm text-gray-600">
                              Quantity: {item.quantity} | Price: £{item.price.toFixed(2)} | Business: {item.business_id}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              Cart ID: {item.cart_id}
                            </p>
                          </div>
                          
                          <div className="flex flex-col space-y-2 ml-4">
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium text-gray-600">Weight:</span>
                              {getWeightBadge(item.weight_class_kg)}
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium text-gray-600">Thermal:</span>
                              {getThermalBadge(item.thermal_requirement)}
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium text-gray-600">Size:</span>
                              {getSizeBadge(item.size_category)}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>1. Add items to cart:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• Visit <a href="/businesses/jersey-grill" className="text-blue-600 hover:underline">Jersey Grill</a></li>
                <li>• Visit <a href="/businesses/jersey-wings" className="text-blue-600 hover:underline">Jersey Wings</a></li>
                <li>• Add various items to your cart</li>
              </ul>
              
              <p><strong>2. Check this page:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• Refresh this page to see cart items</li>
                <li>• Verify delivery attributes are populated</li>
                <li>• Check that attributes match the products</li>
              </ul>
              
              <p><strong>3. Test order creation:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• Complete checkout to create an order</li>
                <li>• Check weight chart at: <a href="/test-weight-chart" className="text-blue-600 hover:underline">/test-weight-chart</a></li>
                <li>• Verify the chart shows your order data</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
