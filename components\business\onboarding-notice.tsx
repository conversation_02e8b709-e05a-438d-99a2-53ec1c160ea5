"use client"

import { useState, useEffect } from "react"
import { X, CheckCircle, AlertCircle, Settings, Store, Clock, MapPin, DollarSign } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"

interface OnboardingNoticeProps {
  business?: {
    id: number
    name: string
    description?: string
    address?: string
    postcode?: string
    phone?: string
    email?: string
    logo_url?: string
    opening_hours?: any
    delivery_radius?: number
    minimum_order_amount?: number
    delivery_fee?: number
    is_approved?: boolean | null
  }
  className?: string
}

interface SetupStep {
  id: string
  title: string
  description: string
  completed: boolean
  required: boolean
  icon: any
  link: string
}

export default function OnboardingNotice({ business, className = "" }: OnboardingNoticeProps) {
  const [isDismissed, setIsDismissed] = useState(false)
  const [visitCount, setVisitCount] = useState(0)

  // Check if notice should be shown
  useEffect(() => {
    const dismissedKey = `business_onboarding_dismissed_${business?.id || 'default'}`
    const visitCountKey = `business_onboarding_visits_${business?.id || 'default'}`

    const dismissed = localStorage.getItem(dismissedKey) === 'true'
    const visits = parseInt(localStorage.getItem(visitCountKey) || '0')

    setIsDismissed(dismissed)
    setVisitCount(visits)

    // Increment visit count
    if (!dismissed) {
      const newVisitCount = visits + 1
      localStorage.setItem(visitCountKey, newVisitCount.toString())
      setVisitCount(newVisitCount)
    }
  }, [business?.id])

  // Calculate setup completion
  const setupSteps: SetupStep[] = [
    {
      id: 'basic_info',
      title: 'Business Information',
      description: 'Name, description, and contact details',
      completed: !!(business?.name && business?.description && business?.phone && business?.email),
      required: true,
      icon: Store,
      link: '/business-admin/settings?tab=basic'
    },
    {
      id: 'location',
      title: 'Location & Delivery',
      description: 'Address, postcode, and delivery settings',
      completed: !!(business?.address && business?.postcode && business?.delivery_radius),
      required: true,
      icon: MapPin,
      link: '/business-admin/settings?tab=location'
    },
    {
      id: 'hours',
      title: 'Opening Hours',
      description: 'When customers can place orders',
      completed: !!(business?.opening_hours && Object.keys(business.opening_hours).length > 0),
      required: true,
      icon: Clock,
      link: '/business-admin/settings?tab=hours'
    },
    {
      id: 'pricing',
      title: 'Pricing & Fees',
      description: 'Minimum order amount and delivery fees',
      completed: !!(business?.minimum_order_amount && business?.delivery_fee),
      required: true,
      icon: DollarSign,
      link: '/business-admin/settings?tab=pricing'
    },
    {
      id: 'branding',
      title: 'Logo & Branding',
      description: 'Upload your business logo',
      completed: !!(business?.logo_url),
      required: false,
      icon: Settings,
      link: '/business-admin/settings?tab=branding'
    }
  ]

  const requiredSteps = setupSteps.filter(step => step.required)
  const completedRequired = requiredSteps.filter(step => step.completed).length
  const totalRequired = requiredSteps.length
  const completionPercentage = (completedRequired / totalRequired) * 100

  const allOptionalSteps = setupSteps.filter(step => !step.required)
  const completedOptional = allOptionalSteps.filter(step => step.completed).length

  // Only hide if explicitly dismissed by the user
  if (isDismissed) {
    return null
  }

  const handleDismiss = () => {
    const dismissedKey = `business_onboarding_dismissed_${business?.id || 'default'}`
    localStorage.setItem(dismissedKey, 'true')
    setIsDismissed(true)
  }

  const getStatusMessage = () => {
    if (business?.is_approved === false) {
      return {
        type: 'warning' as const,
        title: 'Application Under Review',
        message: 'Your business application is being reviewed by our team. Complete your setup while you wait!'
      }
    } else if (business?.is_approved === null) {
      return {
        type: 'info' as const,
        title: 'Complete Your Setup',
        message: 'Finish setting up your business profile to start receiving orders.'
      }
    } else if (completionPercentage < 100) {
      return {
        type: 'info' as const,
        title: 'Almost Ready!',
        message: 'Complete the remaining setup steps to optimize your business profile.'
      }
    } else {
      return {
        type: 'success' as const,
        title: 'Setup Complete!',
        message: 'Your business is ready to start receiving orders. Great work!'
      }
    }
  }

  const status = getStatusMessage()

  return (
    <Card className={`border-blue-200 bg-gradient-to-r from-blue-50 to-emerald-50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 rounded-full p-2">
              <Store className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-blue-900">
                Welcome to Loop Jersey Business Admin
              </CardTitle>
              <CardDescription className="text-blue-700">
                Let's get your business set up and ready to receive orders
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status Alert */}
        <Alert variant={status.type === 'warning' ? 'destructive' : status.type === 'success' ? 'default' : 'default'}
               className={`border-none ${
                 status.type === 'warning' ? 'bg-orange-50 text-orange-800' :
                 status.type === 'success' ? 'bg-green-50 text-green-800' :
                 'bg-blue-50 text-blue-800'
               }`}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>{status.title}:</strong> {status.message}
          </AlertDescription>
        </Alert>

        {/* Progress Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">Setup Progress</span>
            <span className="text-sm text-blue-700">
              {completedRequired}/{totalRequired} required steps
              {completedOptional > 0 && ` + ${completedOptional} optional`}
            </span>
          </div>

          <Progress
            value={completionPercentage}
            className="h-2 bg-blue-100"
          />

          <div className="text-xs text-blue-600">
            {completionPercentage === 100 ?
              "🎉 All required setup done!" :
              `${Math.round(completionPercentage)}% done`
            }
          </div>
        </div>

        {/* Setup Steps */}
        <div className="grid gap-2">
          {setupSteps.map((step) => (
            <div key={step.id} className="flex items-center justify-between p-2 rounded-lg bg-white/50">
              <div className="flex items-center gap-3">
                <div className={`p-1 rounded ${
                  step.completed ? 'bg-green-100 text-green-600' :
                  step.required ? 'bg-orange-100 text-orange-600' :
                  'bg-gray-100 text-gray-500'
                }`}>
                  {step.completed ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <step.icon className="h-4 w-4" />
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${
                      step.completed ? 'text-green-800' : 'text-blue-900'
                    }`}>
                      {step.title}
                    </span>
                    {step.required && !step.completed && (
                      <span className="text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-blue-600">{step.description}</div>
                </div>
              </div>

              {!step.completed && (
                <Link href={step.link}>
                  <Button size="sm" variant="outline" className="text-xs h-7 px-2 border-blue-200 text-blue-700 hover:bg-blue-100">
                    {step.required ? 'Set Up' : 'Add'}
                  </Button>
                </Link>
              )}
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Link href="/business-admin/settings-wizard" className="flex-1">
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
              <Settings className="mr-2 h-4 w-4" />
              Use Settings Wizard
            </Button>
          </Link>

          {completionPercentage === 100 && (
            <Link href="/business-admin/orders">
              <Button variant="outline" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                View Orders
              </Button>
            </Link>
          )}
        </div>

        {/* Help Link */}
        <div className="text-center pt-2 border-t border-blue-200">
          <p className="text-xs text-blue-600">
            Need help? Contact us via the{" "}
            <Link href="/messages" className="font-medium hover:underline">
              Connections Hub
            </Link>
            {" "}or check our{" "}
            <Link href="/help/business" className="font-medium hover:underline">
              Business Guide
            </Link>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
