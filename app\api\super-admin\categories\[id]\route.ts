import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify super admin access
async function verifySuperAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user is super admin
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (profile.role !== 'super_admin') {
      return { authorized: false, error: 'Super admin access required', status: 403 }
    }

    return { authorized: true, user, profile }
  } catch (error) {
    console.error('Error verifying super admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// PUT - Update a category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const categoryId = parseInt(params.id)
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      name,
      slug,
      description,
      business_type_id,
      category_purpose,
      display_order,
      is_active
    } = body

    // Check if this is a partial update (only is_active)
    const isPartialUpdate = Object.keys(body).length === 1 && 'is_active' in body

    // Validate required fields for full updates
    if (!isPartialUpdate && (!name || !slug)) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      )
    }

    // Check if category exists
    const { data: existingCategory, error: fetchError } = await adminClient
      .from('categories')
      .select('id, level')
      .eq('id', categoryId)
      .single()

    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    let updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (isPartialUpdate) {
      // For partial updates, only update the provided fields
      updateData.is_active = is_active
    } else {
      // For full updates, check slug uniqueness and update all fields
      const { data: slugCheck } = await adminClient
        .from('categories')
        .select('id')
        .eq('slug', slug)
        .neq('id', categoryId)
        .single()

      if (slugCheck) {
        return NextResponse.json(
          { error: 'A category with this slug already exists' },
          { status: 400 }
        )
      }

      updateData = {
        ...updateData,
        name,
        slug,
        description: description || null,
        parent_id: null, // All categories are top-level now
        level: 1, // All categories are level 1
        business_type_id: business_type_id || null,
        category_purpose: category_purpose || 'specialization',
        display_order: display_order || 0,
        is_active: is_active !== undefined ? is_active : true
      }
    }

    const { data: category, error } = await adminClient
      .from('categories')
      .update(updateData)
      .eq('id', categoryId)
      .select()
      .single()

    if (error) {
      console.error('Error updating category:', error)
      return NextResponse.json(
        { error: 'Failed to update category' },
        { status: 500 }
      )
    }

    return NextResponse.json(category)
  } catch (error) {
    console.error('Error in PUT /api/super-admin/categories/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const categoryId = parseInt(params.id)
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      )
    }

    // Check if category exists
    const { data: existingCategory, error: fetchError } = await adminClient
      .from('categories')
      .select('id, name')
      .eq('id', categoryId)
      .single()

    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Note: No need to check for child categories since we're using single-level structure

    // Check if category is being used by businesses
    const { data: businessCategories } = await adminClient
      .from('business_categories')
      .select('business_id')
      .eq('category_id', categoryId)

    if (businessCategories && businessCategories.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that is being used by businesses. Please remove business associations first.' },
        { status: 400 }
      )
    }

    // Delete the category
    const { error } = await adminClient
      .from('categories')
      .delete()
      .eq('id', categoryId)

    if (error) {
      console.error('Error deleting category:', error)
      return NextResponse.json(
        { error: 'Failed to delete category' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Category deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/super-admin/categories/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
