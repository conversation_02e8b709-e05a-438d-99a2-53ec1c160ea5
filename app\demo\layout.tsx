'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function DemoLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()

  const demoPages = [
    { name: 'Card Comparison', path: '/demo/card-comparison' },
    { name: 'Portrait Carousel', path: '/demo/portrait-carousel' },
    { name: 'Portrait Grid', path: '/demo/portrait-grid' },
  ]

  return (
    <div>
      <div className="bg-emerald-600 text-white">
        <div className="container-fluid py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <h1 className="text-xl font-bold mb-4 sm:mb-0">Portrait Business Card Demos</h1>
            <nav>
              <ul className="flex flex-wrap gap-4">
                {demoPages.map((page) => (
                  <li key={page.path}>
                    <Link
                      href={page.path}
                      className={`px-3 py-2 rounded-md text-sm font-medium ${
                        pathname === page.path
                          ? 'bg-emerald-700 text-white'
                          : 'text-white hover:bg-emerald-500'
                      }`}
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
                <li>
                  <Link
                    href="/"
                    className="px-3 py-2 rounded-md text-sm font-medium text-white hover:bg-emerald-500"
                  >
                    Back to Main Site
                  </Link>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
      <main className="container-fluid py-4">{children}</main>
    </div>
  )
}
