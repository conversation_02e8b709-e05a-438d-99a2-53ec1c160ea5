/**
 * Debug script to check Jersey Co-op category relationships
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugCoopCategories() {
  try {
    console.log('🔍 Debugging Jersey Co-op categories...\n');

    // Find Jersey Co-op
    const { data: coop, error: coopError } = await supabase
      .from('businesses')
      .select('id, name, slug')
      .eq('slug', 'jersey-coop')
      .single();

    if (coopError || !coop) {
      console.error('❌ Error finding Jersey Co-op:', coopError);
      return;
    }

    console.log(`📋 Business: ${coop.name} (ID: ${coop.id})\n`);

    // Get all categories with detailed info
    const { data: categories, error: categoriesError } = await supabase
      .from('business_custom_categories')
      .select('*')
      .eq('business_id', coop.id)
      .order('level', { ascending: true })
      .order('display_order', { ascending: true });

    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
      return;
    }

    console.log(`📂 Total categories found: ${categories.length}\n`);

    // Group by level
    const level0Categories = categories.filter(cat => cat.level === 0);
    const level1Categories = categories.filter(cat => cat.level === 1);

    console.log('📁 Level 0 Categories (Aisles):');
    level0Categories.forEach(cat => {
      console.log(`   ID: ${cat.id}, Name: "${cat.name}", Parent: ${cat.parent_category || 'null'}`);
    });

    console.log('\n📁 Level 1 Categories (Subcategories):');
    level1Categories.forEach(cat => {
      const parent = categories.find(p => p.id === cat.parent_category);
      console.log(`   ID: ${cat.id}, Name: "${cat.name}", Parent ID: ${cat.parent_category || 'null'}, Parent Name: "${parent?.name || 'NOT FOUND'}"`);
    });

    // Check for orphaned categories
    const orphanedCategories = level1Categories.filter(cat => !cat.parent_category);
    if (orphanedCategories.length > 0) {
      console.log('\n⚠️  Orphaned Level 1 Categories (no parent):');
      orphanedCategories.forEach(cat => {
        console.log(`   - ${cat.name} (ID: ${cat.id})`);
      });
    }

    // Show parent-child relationships (handle type mismatch)
    console.log('\n🔗 Parent-Child Relationships:');
    level0Categories.forEach(parent => {
      const children = level1Categories.filter(child => child.parent_category == parent.id); // Use loose equality
      console.log(`📁 ${parent.name} (ID: ${parent.id}) -> ${children.length} children:`);
      children.forEach(child => {
        console.log(`   - ${child.name} (ID: ${child.id})`);
      });
      if (children.length === 0) {
        console.log(`   (no children)`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the debug
debugCoopCategories();
