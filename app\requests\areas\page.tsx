"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Heart, TrendingUp, Plus, Vote, Users, MapPin, Search, LogIn } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/context/unified-auth-context"
import Link from "next/link"

interface AreaRequest {
  id: number
  area_name: string
  parish: string
  postcode: string
  vote_count: number
  created_at: string
  notes?: string
}

export default function RequestAreasPage() {
  const [requests, setRequests] = useState<AreaRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showRequestDialog, setShowRequestDialog] = useState(false)
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    areaName: '',
    parish: '',
    postcode: '',
    notes: ''
  })
  const { toast } = useToast()
  const { user, userProfile } = useAuth()

  // Helper function to get user display name
  const getUserDisplayName = () => {
    if (userProfile?.name) return userProfile.name
    if (userProfile?.first_name && userProfile?.last_name) {
      return `${userProfile.first_name} ${userProfile.last_name}`
    }
    if (userProfile?.first_name) return userProfile.first_name
    if (user?.email) return user.email.split('@')[0]
    return 'User'
  }

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/area-requests?limit=50&sortBy=vote_count&order=desc')
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleVote = async (areaName: string) => {
    if (!user) {
      setShowLoginPrompt(true)
      return
    }

    try {
      const response = await fetch('/api/area-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          areaName,
          parish: 'Unknown',
          postcode: '',
          customerName: getUserDisplayName(),
          customerEmail: user.email,
          notes: 'Voted from public request page'
        })
      })

      if (response.ok) {
        toast({
          title: "Vote counted!",
          description: `Your vote for ${areaName} has been recorded.`,
        })
        fetchRequests() // Refresh the list
      } else {
        throw new Error('Failed to vote')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to record your vote. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setShowLoginPrompt(true)
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/area-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          customerName: getUserDisplayName(),
          customerEmail: user.email
        })
      })

      if (response.ok) {
        toast({
          title: "Request submitted!",
          description: "Your delivery area request has been added to the list.",
        })
        setShowRequestDialog(false)
        setFormData({
          areaName: '',
          parish: '',
          postcode: '',
          notes: ''
        })
        fetchRequests() // Refresh the list
      } else {
        throw new Error('Failed to submit request')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit your request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const filteredRequests = requests.filter(request =>
    request.area_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    request.parish?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    request.postcode?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Most Requested Delivery Areas</h1>
            <p className="text-xl text-blue-100 mb-6">
              Help us expand delivery coverage across Jersey
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => {
                  if (!user) {
                    setShowLoginPrompt(true)
                  } else {
                    setShowRequestDialog(true)
                  }
                }}
                className="bg-white text-blue-600 hover:bg-blue-50"
                size="lg"
              >
                <Plus className="h-5 w-5 mr-2" />
                Request Delivery Area
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search areas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">{requests.length}</div>
              <div className="text-gray-600">Areas Requested</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">
                {requests.reduce((sum, req) => sum + req.vote_count, 0)}
              </div>
              <div className="text-gray-600">Total Votes</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">
                {requests.length > 0 ? Math.max(...requests.map(r => r.vote_count)) : 0}
              </div>
              <div className="text-gray-600">Most Votes</div>
            </CardContent>
          </Card>
        </div>

        {/* Area Requests List */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'No areas found' : 'No requests yet'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Be the first to request a delivery area!'
                }
              </p>
              {!searchQuery && (
                <Button onClick={() => {
                  if (!user) {
                    setShowLoginPrompt(true)
                  } else {
                    setShowRequestDialog(true)
                  }
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Request Delivery Area
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredRequests.map((request, index) => {
              const isTopRequest = index < 3
              const isTopMostRequest = index === 0

              return (
                <Card
                  key={request.id}
                  className={`hover:shadow-md transition-shadow ${
                    isTopMostRequest
                      ? 'ring-2 ring-green-400 bg-gradient-to-r from-green-50 to-emerald-50'
                      : isTopRequest
                        ? 'ring-1 ring-green-300 bg-green-50'
                        : ''
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-sm ${
                            isTopMostRequest
                              ? 'bg-green-400 text-green-900'
                              : isTopRequest
                                ? 'bg-green-500 text-white'
                                : 'bg-green-100 text-green-600'
                          }`}>
                            #{index + 1}
                          </div>
                          <h3 className={`text-lg font-semibold ${
                            isTopMostRequest ? 'text-green-900' : ''
                          }`}>
                            {request.area_name}
                          </h3>
                          {request.parish && (
                            <Badge variant="outline">{request.parish}</Badge>
                          )}
                          {isTopMostRequest && (
                            <Badge className="bg-green-400 text-green-900 hover:bg-green-500">
                              🏆 Most Wanted
                            </Badge>
                          )}
                          {isTopRequest && !isTopMostRequest && (
                            <Badge className="bg-green-500 text-white">
                              🔥 Popular
                            </Badge>
                          )}
                        </div>

                        {request.postcode && (
                          <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                            <MapPin className="h-4 w-4" />
                            {request.postcode}
                          </div>
                        )}

                        <div className="text-sm text-gray-500">
                          Requested on {formatDate(request.created_at)}
                        </div>
                      </div>

                      <div className="text-right">
                        <div className={`flex items-center gap-2 mb-3 ${
                          isTopMostRequest ? 'text-green-900' : ''
                        }`}>
                          <Vote className="h-5 w-5 text-gray-500" />
                          <span className={`text-3xl font-bold ${
                            isTopMostRequest
                              ? 'text-green-600'
                              : isTopRequest
                                ? 'text-green-600'
                                : 'text-green-600'
                          }`}>
                            {request.vote_count}
                          </span>
                          <span className="text-sm text-gray-500">
                            {request.vote_count === 1 ? 'vote' : 'votes'}
                          </span>
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVote(request.area_name)}
                          className={`${
                            isTopMostRequest
                              ? 'text-green-700 border-green-400 hover:bg-green-100'
                              : isTopRequest
                                ? 'text-green-700 border-green-400 hover:bg-green-100'
                                : 'text-green-600 border-green-200 hover:bg-green-50'
                          }`}
                        >
                          <Heart className="h-4 w-4 mr-1" />
                          {user ? 'Vote' : 'Login to Vote'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Request Area Dialog */}
      <Dialog open={showRequestDialog} onOpenChange={setShowRequestDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Request Delivery Area</DialogTitle>
            <DialogDescription>
              Tell us which area you'd like delivery coverage for
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleRequestSubmit} className="space-y-4">
            <div>
              <Label htmlFor="areaName">Area Name *</Label>
              <Input
                id="areaName"
                value={formData.areaName}
                onChange={(e) => setFormData(prev => ({ ...prev, areaName: e.target.value }))}
                placeholder="e.g. Gorey Village, Trinity Hill"
                required
              />
            </div>
            <div>
              <Label htmlFor="parish">Parish</Label>
              <Input
                id="parish"
                value={formData.parish}
                onChange={(e) => setFormData(prev => ({ ...prev, parish: e.target.value }))}
                placeholder="e.g. St. Helier, St. Brelade"
              />
            </div>
            <div>
              <Label htmlFor="postcode">Postcode (if known)</Label>
              <Input
                id="postcode"
                value={formData.postcode}
                onChange={(e) => setFormData(prev => ({ ...prev, postcode: e.target.value }))}
                placeholder="e.g. JE3 4EQ"
              />
            </div>
            {user && (
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm text-green-700">
                  Submitting as: <strong>{getUserDisplayName()}</strong> ({user.email})
                </p>
              </div>
            )}
            <div>
              <Label htmlFor="notes">Why do you need delivery in this area?</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Tell us why this area would benefit from delivery coverage..."
                rows={3}
              />
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowRequestDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Login Prompt Dialog */}
      <Dialog open={showLoginPrompt} onOpenChange={setShowLoginPrompt}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              Login Required
            </DialogTitle>
            <DialogDescription>
              You need to be logged in to vote for delivery areas or submit requests.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">Why do I need to login?</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Prevents duplicate votes</li>
                <li>• Ensures authentic community feedback</li>
                <li>• Allows us to notify you when areas get coverage</li>
              </ul>
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowLoginPrompt(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Link href="/auth/login" className="flex-1">
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  <LogIn className="h-4 w-4 mr-2" />
                  Login
                </Button>
              </Link>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
