"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import {
  Activity,
  AlertCircle,
  BarChart3,
  CheckCircle,
  ChevronDown,
  Clock,
  Database,
  FileText,
  Globe,
  LayoutDashboard,
  LogOut,
  MapPin,
  Menu,
  Package,
  Search,
  Settings,
  ShoppingBag,
  Star,
  Store,
  Users,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, Sheet<PERSON>rigger } from "@/components/ui/sheet"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useAuthDirect } from "@/context/auth-context-direct"
import { useAdminDashboardStats } from "@/hooks/use-admin-dashboard-stats"

export default function AdminNewDashboard() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isLoading: authLoading } = useAuth()
  const [authChecked, setAuthChecked] = useState(false)

  // Use our real-time stats hook
  const {
    stats,
    pendingBusinesses,
    loading: statsLoading,
    error,
    refetch: refetchStats
  } = useAdminDashboardStats()

  // Fallback stats in case of error
  const fallbackStats = {
    totalBusinesses: 0,
    pendingBusinesses: 0,
    totalUsers: 0,
    totalOrders: 0,
    activeUsers: 0,
    newUsersThisMonth: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    averageRating: 0
  }

  // Use fallback stats if there's an error
  const displayStats = error ? fallbackStats : stats

  // First, check authentication before showing any data
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!authLoading && (!user || !isAdmin)) {
      console.log("AdminNewDashboard: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin-new")
      return
    }

    // If user is admin, mark auth as checked
    if (!authLoading && user && isAdmin) {
      console.log("AdminNewDashboard: User is authorized, proceeding to fetch data")
      setAuthChecked(true)
    }
  }, [user, isAdmin, authLoading, router])

  // Show loading state if auth is not checked or data is loading
  if (!authChecked || (authChecked && statsLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
          {!authLoading && !user && (
            <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md max-w-md">
              <p className="font-semibold">Authentication Error</p>
              <p className="text-sm mt-1">You need to be logged in as an admin to access this page.</p>
              <Button
                className="mt-3 bg-red-600 hover:bg-red-700 text-white"
                onClick={() => router.push("/login?redirectTo=/admin-new")}
              >
                Go to Login
              </Button>
            </div>
          )}
          {authLoading && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">
                If loading takes too long, you can try refreshing the page or go back to the classic dashboard.
              </p>
              <div className="mt-3 flex justify-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/admin")}
                >
                  Classic Dashboard
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  // We're not showing a full error page anymore since we're using fallback data
  // Instead, we show an error banner at the top of the page (see below)

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Get business type name
  const getBusinessTypeName = (typeId: number) => {
    const businessTypes: Record<number, string> = {
      1: "Restaurant",
      2: "Cafe",
      3: "Shop",
      4: "Pharmacy",
      5: "Grocery",
      6: "Bakery",
      7: "Errands"
    }
    return businessTypes[typeId] || "Business"
  }

  return (
    <>
      {/* Error Banner */}
      {error && (
        <div className="bg-amber-50 border-l-4 border-amber-500 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-amber-400" />
            </div>
            <div className="ml-3 flex-1">
              <div className="flex justify-between items-start">
                <p className="text-sm text-amber-700 font-medium">
                  {error?.name === 'TimeoutError'
                    ? 'Data fetch timed out'
                    : 'Real-time data connection issue'}
                </p>
                <button
                  className="ml-2 text-xs bg-amber-100 hover:bg-amber-200 text-amber-800 px-2 py-1 rounded"
                  onClick={() => refetchStats()}
                >
                  Retry Connection
                </button>
              </div>
              <p className="text-sm text-amber-700 mt-1">
                {error?.name === 'TimeoutError'
                  ? 'The dashboard data took too long to load. Showing fallback data instead.'
                  : 'There was an error loading real-time data. Showing fallback data instead.'}
              </p>
              <details className="mt-2">
                <summary className="text-xs text-amber-700 cursor-pointer">View error details</summary>
                <p className="mt-1 text-xs font-mono bg-amber-100 p-2 rounded text-amber-800 overflow-auto max-h-24">
                  {error?.message || "Unknown error occurred"}
                </p>
              </details>
              <p className="text-xs text-amber-700 mt-2">
                {error?.name === 'TimeoutError'
                  ? 'This could be due to slow network connection, database load, or missing tables. Try refreshing the page or contact support if the problem persists.'
                  : 'This could be due to a temporary connection issue or database permissions. Try refreshing the page or contact support if the problem persists.'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background px-4 sm:px-6">
          <div className="flex flex-1 items-center gap-4">
            <h1 className="text-xl font-semibold">Admin Dashboard</h1>
            <span className="text-sm text-muted-foreground">Welcome to your delivery platform control center</span>
          </div>
          <div className="flex items-center gap-4">
            <form className="relative hidden md:block">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search..." className="w-64 pl-8" />
            </form>
            <Button variant="outline" size="sm" className="hidden md:flex">
              <FileText className="mr-2 h-4 w-4" />
              Download Report
            </Button>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" className="relative">
                    <AlertCircle className="h-4 w-4" />
                    <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
                      {displayStats.pendingBusinesses > 0 ? displayStats.pendingBusinesses : 0}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{displayStats.pendingBusinesses} businesses need approval</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </header>

        {/* Dashboard Content */}
        <div className="p-4 sm:p-6 lg:p-8">
          {/* Stats Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
                <Store className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{displayStats.totalBusinesses}</div>
                <p className="text-xs text-muted-foreground">+{Math.floor(displayStats.totalBusinesses * 0.1)} from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{displayStats.pendingBusinesses}</div>
                <div className="mt-2">
                  <Progress
                    value={displayStats.totalBusinesses > 0 ? (displayStats.pendingBusinesses / displayStats.totalBusinesses) * 100 : 0}
                    className="h-2"
                  />
                </div>
                <p className="mt-1 text-xs text-muted-foreground">
                  {displayStats.totalBusinesses > 0
                    ? `${Math.round((displayStats.pendingBusinesses / displayStats.totalBusinesses) * 100)}% of total businesses`
                    : "No businesses yet"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {displayStats.averageRating > 0
                    ? displayStats.averageRating.toFixed(1)
                    : 'N/A'}
                </div>
                {displayStats.averageRating > 0 ? (
                  <div className="flex items-center mt-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-3 w-3 ${
                          star <= displayStats.averageRating
                            ? 'text-yellow-400 fill-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="mt-1">
                    <Link href="/admin-new/database-tools" className="text-xs text-emerald-600 hover:underline">
                      Calculate ratings
                    </Link>
                  </div>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {displayStats.averageRating > 0
                    ? `Based on business reviews`
                    : "No ratings available"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{displayStats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">+{displayStats.newUsersThisMonth} this month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{displayStats.totalOrders}</div>
                <p className="text-xs text-muted-foreground">
                  {displayStats.totalOrders > 0
                    ? `Avg. ${formatCurrency(displayStats.averageOrderValue)}`
                    : "Awaiting first orders"}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Tabs */}
          <Tabs defaultValue="approvals" className="mt-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-auto">
              <TabsTrigger value="approvals">Business Approvals</TabsTrigger>
              <TabsTrigger value="actions">Quick Actions</TabsTrigger>
              <TabsTrigger value="system">System Status</TabsTrigger>
            </TabsList>

            {/* Business Approvals Tab */}
            <TabsContent value="approvals" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Businesses Awaiting Approval</CardTitle>
                  <CardDescription>
                    These businesses need your review before they can be listed on the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {pendingBusinesses.length > 0 ? (
                      pendingBusinesses.map((business) => (
                        <div key={business.id} className="flex items-center justify-between rounded-lg border p-4">
                          <div className="flex items-center gap-4">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                              <Store className="h-5 w-5" />
                            </div>
                            <div>
                              <h3 className="font-medium">{business.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {getBusinessTypeName(business.business_type_id)} • {formatDate(business.created_at)}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Link href={`/admin/businesses/${business.id}`}>
                              <Button variant="outline" size="sm">
                                Review
                              </Button>
                            </Link>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No businesses awaiting approval
                      </div>
                    )}
                  </div>
                </CardContent>
                {pendingBusinesses.length > 0 && displayStats.pendingBusinesses > pendingBusinesses.length && (
                  <CardFooter className="flex justify-between">
                    <Link href="/admin/businesses?filter=pending">
                      <Button variant="outline">View All ({displayStats.pendingBusinesses})</Button>
                    </Link>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            {/* Quick Actions Tab */}
            <TabsContent value="actions" className="mt-6">
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Store className="h-5 w-5 text-emerald-500" />
                      Manage Businesses
                    </CardTitle>
                    <CardDescription>Add, edit, or remove businesses from the platform</CardDescription>
                  </CardHeader>
                  <CardFooter>
                    <Link href="/admin/businesses" className="w-full">
                      <Button className="w-full">View Businesses</Button>
                    </Link>
                  </CardFooter>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-emerald-500" />
                      Update Coordinates
                    </CardTitle>
                    <CardDescription>Update delivery zones, addresses, and coordinates</CardDescription>
                  </CardHeader>
                  <CardFooter>
                    <Link href="/admin/update-coordinates" className="w-full">
                      <Button className="w-full">Update Coordinates</Button>
                    </Link>
                  </CardFooter>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-emerald-500" />
                      Missing Data Report
                    </CardTitle>
                    <CardDescription>View businesses with missing information or coordinates</CardDescription>
                  </CardHeader>
                  <CardFooter>
                    <Button className="w-full">Generate Report</Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>

            {/* System Status Tab */}
            <TabsContent value="system" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                  <CardDescription>Current status of system components</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "Database", status: "Operational" },
                      { name: "API Services", status: "Operational" },
                      { name: "Payment Processing", status: "Operational" },
                      { name: "Geocoding Services", status: "Operational" },
                    ].map((service, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-500" />
                          <span>{service.name}</span>
                        </div>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          {service.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    View System Status
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Activity Feed */}
          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { action: "New business registered", business: "Island Grocers", time: "10 minutes ago" },
                    { action: "Business approved", business: "Jersey Bakery", time: "1 hour ago" },
                    { action: "User registered", business: "Sarah Johnson", time: "3 hours ago" },
                    { action: "Business rejected", business: "Quick Mart", time: "5 hours ago" },
                    { action: "System update completed", business: "v2.3.1", time: "Yesterday" },
                  ].map((activity, index) => (
                    <div key={index} className="flex items-start gap-4 rounded-lg border p-4">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                        <Globe className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">{activity.business}</p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
  )
}
