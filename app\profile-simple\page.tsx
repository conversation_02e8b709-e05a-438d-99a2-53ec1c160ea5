"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface UserData {
  id: number
  name: string
  email: string
  role: string
  phone?: string
  address?: string
}

export default function SimpleProfilePage() {
  const router = useRouter()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        console.log("Fetching profile data...")
        
        const response = await fetch('/api/user/profile', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log("Profile data received:", result)

        if (result.data) {
          setUserData(result.data)
        } else {
          setError("No profile data found")
        }
      } catch (err: any) {
        console.error("Error fetching profile:", err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProfile()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f9fafa] flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-sm p-8 flex flex-col items-center">
          <div className="h-12 w-12 border-2 border-emerald-600 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-[#2e3333] font-medium">Loading your profile...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#f9fafa] flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-sm p-8 flex flex-col items-center max-w-md">
          <div className="text-red-500 mb-4">
            <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-[#2e3333] mb-2">Error Loading Profile</h2>
          <p className="text-[#585c5c] text-center mb-4">{error}</p>
          <div className="flex gap-3">
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700"
            >
              Retry
            </button>
            <button 
              onClick={() => router.push('/login')} 
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Sign In
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-[#f9fafa] flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-sm p-8 flex flex-col items-center">
          <p className="text-[#2e3333] font-medium">No profile data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#f9fafa]">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <h1 className="text-xl font-bold text-[#2e3333]">Account (Simple)</h1>
          <button 
            onClick={() => router.push('/')}
            className="text-emerald-600 hover:text-emerald-700"
          >
            Home
          </button>
        </div>
      </header>

      <div className="container mx-auto py-6 px-4 md:py-10">
        <div className="max-w-3xl mx-auto">
          {/* Profile hero section */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              <div className="relative">
                <div className="h-24 w-24 rounded-full bg-emerald-600 flex items-center justify-center text-white text-2xl font-medium">
                  {userData.name?.substring(0, 1).toUpperCase() || 'U'}
                </div>
              </div>
              <div className="flex-1 text-center md:text-left">
                <h2 className="text-2xl font-bold text-[#2e3333] mb-1">
                  {userData.name || 'User'}
                </h2>
                <p className="text-[#585c5c] mb-3">{userData.email}</p>
                <div className="flex flex-wrap justify-center md:justify-start gap-3">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-600">
                    {userData.role?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Customer'}
                  </span>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    ID: {userData.id}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Profile details */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-[#2e3333] mb-4">Profile Details</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#585c5c] mb-1">Name</label>
                <p className="text-[#2e3333]">{userData.name || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#585c5c] mb-1">Email</label>
                <p className="text-[#2e3333]">{userData.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#585c5c] mb-1">Phone</label>
                <p className="text-[#2e3333]">{userData.phone || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#585c5c] mb-1">Role</label>
                <p className="text-[#2e3333]">{userData.role}</p>
              </div>
            </div>
          </div>

          {/* Debug info */}
          <div className="mt-6 bg-gray-50 rounded-xl p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Debug Information</h4>
            <pre className="text-xs text-gray-600 overflow-auto">
              {JSON.stringify(userData, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}
