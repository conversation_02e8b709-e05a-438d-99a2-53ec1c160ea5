// Mock data for analytics dashboard

// Sales data
export const salesData = [
  { date: "Apr 01", revenue: 120, orders: 8, avg: 15 },
  { date: "Apr 02", revenue: 145, orders: 10, avg: 14.5 },
  { date: "Apr 03", revenue: 135, orders: 9, avg: 15 },
  { date: "Apr 04", revenue: 160, orders: 12, avg: 13.3 },
  { date: "Apr 05", revenue: 180, orders: 14, avg: 12.9 },
  { date: "Apr 06", revenue: 210, orders: 16, avg: 13.1 },
  { date: "Apr 07", revenue: 190, orders: 15, avg: 12.7 },
  { date: "Apr 08", revenue: 170, orders: 13, avg: 13.1 },
  { date: "Apr 09", revenue: 150, orders: 11, avg: 13.6 },
  { date: "Apr 10", revenue: 140, orders: 10, avg: 14 },
  { date: "Apr 11", revenue: 130, orders: 9, avg: 14.4 },
  { date: "Apr 12", revenue: 150, orders: 11, avg: 13.6 },
  { date: "Apr 13", revenue: 170, orders: 13, avg: 13.1 },
  { date: "Apr 14", revenue: 190, orders: 15, avg: 12.7 },
  { date: "Apr 15", revenue: 210, orders: 16, avg: 13.1 },
  { date: "Apr 16", revenue: 180, orders: 14, avg: 12.9 },
  { date: "Apr 17", revenue: 160, orders: 12, avg: 13.3 },
  { date: "Apr 18", revenue: 135, orders: 9, avg: 15 },
  { date: "Apr 19", revenue: 145, orders: 10, avg: 14.5 },
  { date: "Apr 20", revenue: 120, orders: 8, avg: 15 },
  { date: "Apr 21", revenue: 130, orders: 9, avg: 14.4 },
  { date: "Apr 22", revenue: 150, orders: 11, avg: 13.6 },
  { date: "Apr 23", revenue: 170, orders: 13, avg: 13.1 },
  { date: "Apr 24", revenue: 190, orders: 15, avg: 12.7 },
  { date: "Apr 25", revenue: 210, orders: 16, avg: 13.1 },
  { date: "Apr 26", revenue: 180, orders: 14, avg: 12.9 },
  { date: "Apr 27", revenue: 160, orders: 12, avg: 13.3 },
  { date: "Apr 28", revenue: 135, orders: 9, avg: 15 },
  { date: "Apr 29", revenue: 145, orders: 10, avg: 14.5 },
  { date: "Apr 30", revenue: 120, orders: 8, avg: 15 },
];

// Customer split data
export const customerSplitData = [
  { name: "New", value: 32 },
  { name: "Returning", value: 68 },
];

// Order frequency data
export const orderFrequencyData = [
  { name: "Once", value: 40 },
  { name: "2-3 times", value: 30 },
  { name: "4-6 times", value: 20 },
  { name: "7+ times", value: 10 },
];

// Customer growth data
export const customerGrowthData = [
  { date: "Apr 01", total: 120, new: 5 },
  { date: "Apr 02", total: 125, new: 3 },
  { date: "Apr 03", total: 128, new: 2 },
  { date: "Apr 04", total: 130, new: 4 },
  { date: "Apr 05", total: 134, new: 6 },
  { date: "Apr 06", total: 140, new: 5 },
  { date: "Apr 07", total: 145, new: 3 },
  { date: "Apr 08", total: 148, new: 2 },
  { date: "Apr 09", total: 150, new: 4 },
  { date: "Apr 10", total: 154, new: 3 },
  { date: "Apr 11", total: 157, new: 2 },
  { date: "Apr 12", total: 159, new: 1 },
  { date: "Apr 13", total: 160, new: 3 },
  { date: "Apr 14", total: 163, new: 5 },
];

// Customer retention data
export const customerRetentionData = [
  { month: "January", rate: 62 },
  { month: "February", rate: 65 },
  { month: "March", rate: 68 },
  { month: "April", rate: 71 },
];

// Customer segment data
export const customerSegmentData = [
  { name: "Loyal", value: 35, color: "#10b981", description: "5+ orders in last 3 months" },
  { name: "Regular", value: 42, color: "#3b82f6", description: "2-4 orders in last 3 months" },
  { name: "Occasional", value: 15, color: "#8b5cf6", description: "1 order in last 3 months" },
  { name: "At Risk", value: 8, color: "#f59e0b", description: "No orders in last 2 months" },
];

// Customer feedback data
export const customerFeedbackData = [
  { category: "Food Quality", rating: 4.7 },
  { category: "Value for Money", rating: 4.2 },
  { category: "Delivery Speed", rating: 4.5 },
  { category: "Order Accuracy", rating: 4.8 },
  { category: "Customer Service", rating: 4.6 },
];

// Top customers data
export const topCustomersData = [
  { id: 1, name: "Sarah Johnson", orders: 12, spent: 245.80, lastOrder: "2 days ago" },
  { id: 2, name: "Michael Brown", orders: 10, spent: 198.50, lastOrder: "5 days ago" },
  { id: 3, name: "Emma Wilson", orders: 8, spent: 176.40, lastOrder: "1 week ago" },
  { id: 4, name: "James Taylor", orders: 7, spent: 142.90, lastOrder: "3 days ago" },
  { id: 5, name: "Olivia Davis", orders: 6, spent: 128.70, lastOrder: "2 weeks ago" },
];

// Order trend data
export const orderTrendData = [
  { date: "Apr 01", orders: 8, completed: 8, canceled: 0 },
  { date: "Apr 02", orders: 10, completed: 9, canceled: 1 },
  { date: "Apr 03", orders: 9, completed: 9, canceled: 0 },
  { date: "Apr 04", orders: 12, completed: 11, canceled: 1 },
  { date: "Apr 05", orders: 14, completed: 13, canceled: 1 },
  { date: "Apr 06", orders: 16, completed: 15, canceled: 1 },
  { date: "Apr 07", orders: 15, completed: 14, canceled: 1 },
  { date: "Apr 08", orders: 13, completed: 12, canceled: 1 },
  { date: "Apr 09", orders: 11, completed: 10, canceled: 1 },
  { date: "Apr 10", orders: 10, completed: 10, canceled: 0 },
  { date: "Apr 11", orders: 9, completed: 8, canceled: 1 },
  { date: "Apr 12", orders: 11, completed: 10, canceled: 1 },
  { date: "Apr 13", orders: 13, completed: 12, canceled: 1 },
  { date: "Apr 14", orders: 15, completed: 14, canceled: 1 },
];

// Order status data
export const orderStatusData = [
  { name: "Completed", value: 215, color: "#10b981" },
  { name: "In Progress", value: 18, color: "#3b82f6" },
  { name: "Canceled", value: 13, color: "#ef4444" },
];

// Order source data
export const orderSourceData = [
  { name: "Website", value: 142, color: "#10b981" },
  { name: "Mobile App", value: 98, color: "#3b82f6" },
  { name: "Phone", value: 6, color: "#8b5cf6" },
];

// Order value trend data
export const orderValueTrendData = [
  { date: "Week 1", value: 12.85 },
  { date: "Week 2", value: 13.25 },
  { date: "Week 3", value: 14.10 },
  { date: "Week 4", value: 13.95 },
];

// Order time distribution data
export const orderTimeData = [
  { hour: "6-8 AM", orders: 5 },
  { hour: "8-10 AM", orders: 12 },
  { hour: "10-12 PM", orders: 18 },
  { hour: "12-2 PM", orders: 35 },
  { hour: "2-4 PM", orders: 20 },
  { hour: "4-6 PM", orders: 15 },
  { hour: "6-8 PM", orders: 42 },
  { hour: "8-10 PM", orders: 38 },
  { hour: "10-12 AM", orders: 15 },
];

// Top dishes data
export const topDishesData = [
  { id: 1, name: "Jersey Crab Cakes", orders: 32, revenue: 318.40, rating: 4.9, costPercentage: 28, category: "Starters" },
  { id: 2, name: "Grilled Sea Bass", orders: 28, revenue: 642.60, rating: 4.8, costPercentage: 35, category: "Mains" },
  { id: 3, name: "Jersey Beef Burger", orders: 24, revenue: 358.80, rating: 4.7, costPercentage: 30, category: "Mains" },
  { id: 4, name: "Jersey Cream Brûlée", orders: 22, revenue: 174.90, rating: 4.9, costPercentage: 22, category: "Desserts" },
  { id: 5, name: "Sautéed Jersey Scallops", orders: 18, revenue: 233.10, rating: 4.6, costPercentage: 40, category: "Starters" },
  { id: 6, name: "Jersey Royal Potato Salad", orders: 16, revenue: 127.20, rating: 4.5, costPercentage: 18, category: "Sides" },
  { id: 7, name: "Lobster Thermidor", orders: 14, revenue: 419.30, rating: 4.8, costPercentage: 45, category: "Mains" },
  { id: 8, name: "Chocolate Fondant", orders: 12, revenue: 95.40, rating: 4.7, costPercentage: 25, category: "Desserts" },
];

// Menu category data
export const menuCategoryData = [
  { name: "Starters", orders: 68, revenue: 612.00, avgRating: 4.7, color: "#10b981" },
  { name: "Mains", orders: 92, revenue: 1842.50, avgRating: 4.6, color: "#3b82f6" },
  { name: "Sides", orders: 45, revenue: 315.00, avgRating: 4.4, color: "#8b5cf6" },
  { name: "Desserts", orders: 41, revenue: 328.00, avgRating: 4.8, color: "#f59e0b" },
  { name: "Drinks", orders: 76, revenue: 456.00, avgRating: 4.5, color: "#ef4444" },
];

// Menu trend data
export const menuTrendData = [
  { week: "Week 1", starters: 14, mains: 22, sides: 10, desserts: 8, drinks: 18 },
  { week: "Week 2", starters: 16, mains: 24, sides: 12, desserts: 10, drinks: 20 },
  { week: "Week 3", starters: 18, mains: 23, sides: 11, desserts: 12, drinks: 19 },
  { week: "Week 4", starters: 20, mains: 25, sides: 12, desserts: 11, drinks: 19 },
];

// Menu modification data
export const menuModificationData = [
  { name: "Extra Cheese", count: 42, revenue: 84.00 },
  { name: "Gluten-Free Option", count: 28, revenue: 56.00 },
  { name: "Extra Sauce", count: 24, revenue: 48.00 },
  { name: "Spicy Option", count: 18, revenue: 18.00 },
  { name: "Vegetarian Substitute", count: 15, revenue: 30.00 },
];

// Menu availability data
export const menuAvailabilityData = [
  { name: "Jersey Crab Cakes", stockouts: 2, impact: 63.68 },
  { name: "Lobster Thermidor", stockouts: 3, impact: 89.85 },
  { name: "Grilled Sea Bass", stockouts: 1, impact: 22.95 },
  { name: "Jersey Royal Potato Salad", stockouts: 0, impact: 0 },
  { name: "Chocolate Fondant", stockouts: 0, impact: 0 },
];

// Colors for charts
export const COLORS = ["#10b981", "#3b82f6", "#8b5cf6", "#f59e0b", "#ef4444"];
