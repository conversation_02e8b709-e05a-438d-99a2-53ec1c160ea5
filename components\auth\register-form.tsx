"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Lock, Mail, User, Phone, Building2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/context/unified-auth-context"

export default function RegisterForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { signUp, linkBusinessToUser } = useAuth()
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [registeredEmail, setRegisteredEmail] = useState<string>("")
  const [pendingBusinessLink, setPendingBusinessLink] = useState<any>(null)

  // Check for business linking parameters and pending business data
  useEffect(() => {
    // Pre-fill email from URL params
    const emailParam = searchParams.get('email')
    if (emailParam) {
      setEmail(emailParam)
    }

    // Pre-fill name from URL params
    const nameParam = searchParams.get('name')
    if (nameParam) {
      setName(nameParam)
    }

    // Check for pending business link
    const linkBusiness = searchParams.get('linkBusiness')
    if (linkBusiness === 'true') {
      const pendingData = localStorage.getItem('pending_business_link')
      if (pendingData) {
        const businessData = JSON.parse(pendingData)
        setPendingBusinessLink(businessData)
      }
    }
  }, [searchParams])

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)
    setIsLoading(true)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match")
      setIsLoading(false)
      return
    }

    try {
      const { error, data } = await signUp(email, password, {
        name,
        phone,
      })

      if (error) {
        // Handle specific Supabase auth errors with user-friendly messages
        if (error.message.includes("email already registered")) {
          setError("This email is already registered. Please use a different email or try logging in.")
        } else if (error.message.includes("password")) {
          setError("Password error: " + error.message)
        } else {
          setError(error.message)
        }
        setIsLoading(false)
        return
      }

      // Clear form
      setName("")
      setEmail("")
      setPhone("")
      setPassword("")
      setConfirmPassword("")

      // Store the registered email before clearing the form
      const registeredEmailValue = email;
      setRegisteredEmail(registeredEmailValue);

      // Show success message with email confirmation instructions
      setSuccess(
        "Registration successful! 🎉 " +
        "We've sent a confirmation email to " + registeredEmailValue + ". " +
        "Please check your inbox (and spam folder) and click the confirmation link to verify your account. " +
        "You'll need to verify your email before you can access all features."
      )

      // If the user was created in auth but there might have been an issue with the profile
      // we still want to show a success message because they can still log in
      if (data?.user) {
        console.log("User created successfully with ID:", data.user.id);
        // The user profile is created automatically in the unified auth context

        // Handle business linking if there's a pending business
        if (pendingBusinessLink) {
          console.log("Business already linked during registration:", pendingBusinessLink);

          // Clear the pending business link since it's already linked
          localStorage.removeItem('pending_business_link');
          localStorage.removeItem('business_registration_success_auth_pending');

          // Update success message to include business linking
          setSuccess(
            "Account created successfully! 🎉 " +
            "Welcome to Loop Jersey! Your business '" + pendingBusinessLink.businessName + "' " +
            "is now linked to your account. You can access your business dashboard immediately."
          );
        }
      }

      // Clear form fields to prevent resubmission
      setName("")
      setEmail("")
      setPhone("")
      setPassword("")
      setConfirmPassword("")
    } catch (err) {
      console.error("Registration error:", err)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create an Account</CardTitle>
        <CardDescription>
          {pendingBusinessLink
            ? `Complete your account to access your business: ${pendingBusinessLink.businessName}`
            : "Enter your details to create a new account"
          }
        </CardDescription>
        {pendingBusinessLink && (
          <Alert className="bg-blue-50 border-blue-200">
            <Building2 className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Business Registration Complete!</strong>
              <br />
              Your business "{pendingBusinessLink.businessName}" is registered and waiting.
              Create your account to access your business dashboard.
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {success && (
          <div className="space-y-4">
            <Alert className="mb-4 bg-emerald-50 text-emerald-800 border-emerald-200">
              <div className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-emerald-600 mt-0.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <AlertDescription className="font-medium">{success}</AlertDescription>
                  <p className="mt-2 text-sm">
                    Please check your email and click the confirmation link to complete your registration.
                  </p>
                  {pendingBusinessLink ? (
                    <p className="mt-2 text-sm text-emerald-700">
                      <strong>Next:</strong> Complete your email verification to access your business dashboard and start managing your business profile.
                    </p>
                  ) : (
                    <p className="mt-2 text-sm text-emerald-700">
                      <strong>Tip:</strong> Add your delivery address in your profile to get accurate delivery times as you browse the site.
                    </p>
                  )}
                  <p className="mt-1 text-sm font-medium">
                    Registered email: <span className="text-emerald-700">{registeredEmail}</span>
                  </p>
                </div>
              </div>
            </Alert>

            <div className="flex flex-col space-y-3">
              <Button
                variant="default"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => router.push("/auth/confirm-email")}
              >
                <Mail className="mr-2 h-4 w-4" />
                Check Email Confirmation Status
              </Button>
              {pendingBusinessLink ? (
                <Button
                  variant="outline"
                  className="w-full border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                  onClick={() => router.push("/business-admin/dashboard")}
                >
                  Go to Business Dashboard
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="w-full border-gray-200 text-gray-700 hover:bg-gray-50"
                  onClick={() => router.push("/")}
                >
                  Continue Browsing
                </Button>
              )}
            </div>
          </div>
        )}
        {!success && <form onSubmit={handleRegister}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="name"
                  placeholder="John Doe"
                  type="text"
                  className="pl-10"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number (optional)</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  placeholder="+44 7911 123456"
                  type="tel"
                  className="pl-10"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              <p className="text-xs text-gray-500">Password must be at least 8 characters long</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  className="pl-10"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
            </div>
            <Button type="submit" className="w-full bg-emerald-600 hover:bg-emerald-700" disabled={isLoading}>
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Account...
                </>
              ) : (
                "Create Account"
              )}
            </Button>
          </div>
        </form>}
      </CardContent>
      <CardFooter className="flex justify-center">
        {success ? (
          <p className="text-sm text-gray-500">
            Thank you for registering with Loop Jersey! We're excited to have you join our community. Enjoy your experience!
          </p>
        ) : (
          <p className="text-sm text-gray-500">
            Already have an account?{" "}
            <Link href="/login" className="text-emerald-600 hover:text-emerald-700 font-medium">
              Sign in
            </Link>
          </p>
        )}
      </CardFooter>
    </Card>
  )
}
