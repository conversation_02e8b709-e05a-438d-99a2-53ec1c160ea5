"use client"

import { ShoppingBag, MapPin, Clock, CreditCard, Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface CheckoutProgressProps {
  currentStep: number
}

export default function CheckoutProgress({ currentStep }: CheckoutProgressProps) {
  const steps = [
    { id: 1, name: "Customer Info", shortName: "Info", icon: ShoppingBag },
    { id: 2, name: "Delivery", shortName: "Address", icon: MapPin },
    { id: 3, name: "Timing", shortName: "Time", icon: Clock },
    { id: 4, name: "Payment", shortName: "Payment", icon: CreditCard },
  ]

  return (
    <div className="w-full mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step) => (
          <div key={step.id} className="flex flex-col items-center relative">
            {/* Connector line */}
            {step.id < steps.length && (
              <div
                className={cn(
                  "absolute top-4 w-full h-1 left-1/2",
                  currentStep >= step.id ? "bg-emerald-500" : "bg-gray-200"
                )}
              />
            )}

            {/* Circle with icon */}
            <div
              className={cn(
                "z-10 flex items-center justify-center w-8 h-8 rounded-full border-2",
                currentStep > step.id
                  ? "bg-emerald-500 border-emerald-500 text-white"
                  : currentStep === step.id
                    ? "bg-white border-emerald-500 text-emerald-500"
                    : "bg-white border-gray-300 text-gray-400"
              )}
            >
              {currentStep > step.id ? (
                <Check className="h-4 w-4" />
              ) : (
                <step.icon className="h-4 w-4" />
              )}
            </div>

            {/* Step name - responsive */}
            <span
              className={cn(
                "mt-2 text-xs font-medium text-center",
                currentStep >= step.id ? "text-emerald-600" : "text-gray-500"
              )}
            >
              <span className="hidden sm:inline">{step.name}</span>
              <span className="sm:hidden">{step.shortName}</span>
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}
