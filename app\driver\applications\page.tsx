"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Building2, 
  MapPin, 
  Phone, 
  Mail,
  RefreshCw,
  FileText,
  Calendar,
  User,
  MessageSquare
} from "lucide-react"

interface Application {
  id: number
  business: {
    id: number
    name: string
    slug: string
    type: string
    logo_url: string
    address: string
    parish: string
    phone: string
    email: string
    is_active: boolean
  }
  status: string
  application_date: string
  decision_date: string | null
  rejection_reason: string | null
  ban_reason: string | null
  notes: string | null
  can_reapply: boolean
}

interface ApplicationsResponse {
  success: boolean
  applications: Application[]
  summary: {
    total: number
    pending: number
    approved: number
    rejected: number
    banned: number
  }
  pagination: {
    limit: number
    offset: number
    total: number
  }
}

export default function DriverApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([])
  const [summary, setSummary] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    banned: 0
  })
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const { toast } = useToast()

  useEffect(() => {
    fetchApplications()
  }, [])

  const fetchApplications = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/driver/applications')
      const data: ApplicationsResponse = await response.json()

      if (data.success) {
        setApplications(data.applications)
        setSummary(data.summary)
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load applications"
        })
      }
    } catch (error) {
      console.error('Error fetching applications:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load applications"
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending Review
          </Badge>
        )
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        )
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        )
      case 'banned':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Banned
          </Badge>
        )
      default:
        return null
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'banned':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const filterApplications = (status: string) => {
    if (status === "all") return applications
    return applications.filter(app => app.status === status)
  }

  const handleReapply = async (businessId: number) => {
    // This would redirect to the businesses page with the specific business highlighted
    window.location.href = `/driver/businesses?highlight=${businessId}`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Applications</h1>
          <p className="text-gray-600">Track your business application status</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchApplications}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total</p>
                <p className="text-2xl font-bold">{summary.total}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{summary.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Approved</p>
                <p className="text-2xl font-bold text-green-600">{summary.approved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{summary.rejected}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Banned</p>
                <p className="text-2xl font-bold text-red-700">{summary.banned}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All ({summary.total})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({summary.pending})</TabsTrigger>
          <TabsTrigger value="approved">Approved ({summary.approved})</TabsTrigger>
          <TabsTrigger value="rejected">Rejected ({summary.rejected})</TabsTrigger>
          <TabsTrigger value="banned">Banned ({summary.banned})</TabsTrigger>
        </TabsList>

        {["all", "pending", "approved", "rejected", "banned"].map((status) => (
          <TabsContent key={status} value={status} className="space-y-4">
            {loading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filterApplications(status).length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No {status === "all" ? "" : status} applications
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {status === "all" 
                      ? "You haven't applied to any businesses yet."
                      : `You don't have any ${status} applications.`
                    }
                  </p>
                  {status === "all" && (
                    <Button onClick={() => window.location.href = '/driver/businesses'}>
                      Browse Businesses
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filterApplications(status).map((application) => (
                  <Card key={application.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          {application.business.logo_url ? (
                            <img 
                              src={application.business.logo_url} 
                              alt={application.business.name}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                          ) : (
                            <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center">
                              <Building2 className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                          <div className="flex-1">
                            <CardTitle className="text-lg">{application.business.name}</CardTitle>
                            <CardDescription className="flex items-center gap-1 mt-1">
                              <Building2 className="h-3 w-3" />
                              {application.business.type}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(application.status)}
                          {getStatusBadge(application.status)}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-gray-600">
                            <MapPin className="h-4 w-4" />
                            <span>{application.business.address}, {application.business.parish}</span>
                          </div>
                          {application.business.phone && (
                            <div className="flex items-center gap-2 text-gray-600">
                              <Phone className="h-4 w-4" />
                              <span>{application.business.phone}</span>
                            </div>
                          )}
                          {application.business.email && (
                            <div className="flex items-center gap-2 text-gray-600">
                              <Mail className="h-4 w-4" />
                              <span>{application.business.email}</span>
                            </div>
                          )}
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>Applied: {format(new Date(application.application_date), 'MMM d, yyyy')}</span>
                          </div>
                          {application.decision_date && (
                            <div className="flex items-center gap-2 text-gray-600">
                              <User className="h-4 w-4" />
                              <span>Decided: {format(new Date(application.decision_date), 'MMM d, yyyy')}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Rejection/Ban Reason */}
                      {application.rejection_reason && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                          <div className="flex items-start gap-2">
                            <XCircle className="h-4 w-4 text-red-500 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-red-800">Rejection Reason</p>
                              <p className="text-sm text-red-700">{application.rejection_reason}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {application.ban_reason && (
                        <div className="bg-red-100 border border-red-300 rounded-lg p-3">
                          <div className="flex items-start gap-2">
                            <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-red-900">Ban Reason</p>
                              <p className="text-sm text-red-800">{application.ban_reason}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Notes */}
                      {application.notes && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <div className="flex items-start gap-2">
                            <MessageSquare className="h-4 w-4 text-blue-500 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-blue-800">Business Notes</p>
                              <p className="text-sm text-blue-700">{application.notes}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-2 pt-2">
                        {application.can_reapply && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReapply(application.business.id)}
                          >
                            Apply Again
                          </Button>
                        )}
                        {application.status === 'approved' && (
                          <Badge variant="outline" className="bg-green-50 text-green-700">
                            You can now receive orders from this business
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
