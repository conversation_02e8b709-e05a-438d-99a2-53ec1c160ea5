"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import PendingApprovalMessage from "@/components/business/pending-approval"

interface ApprovalCheckProps {
  children: React.ReactNode
}

export default function BusinessApprovalCheck({ children }: ApprovalCheckProps) {
  const router = useRouter()
  const [isApproved, setIsApproved] = useState<boolean | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  console.log("🏢 BusinessApprovalCheck: Checking approval status...")

  // Simple approval check - since middleware already protects the route, we just need to check business approval
  useEffect(() => {
    const checkApprovalStatus = async () => {
      try {
        // First check if there's a registration in localStorage
        const registrationData = localStorage.getItem('business_registration_success')
        if (registrationData) {
          console.log("🏢 Found registration data in localStorage")
          setIsPendingApproval(true)
          setIsApproved(false)
          setIsLoading(false)
          return
        }

        console.log("🏢 Checking business approval via API");

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';

        // Use the server API to check business approval
        const response = await fetch('/api/business-admin/approval-check', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          console.error("🏢 Error checking business approval:", response.status);
          // If middleware let us through, we're probably an admin - allow access
          setIsApproved(true);
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        console.log("🏢 Business approval check result:", data);

        if (data.isAdminUser) {
          console.log("🏢 Admin user detected, bypassing business approval check");
          setIsApproved(true);
        } else if (data.isPendingApproval) {
          console.log("🏢 Business is pending approval");
          setIsPendingApproval(true);
          setIsApproved(false);
        } else if (data.isApproved) {
          console.log("🏢 Business is approved");
          setIsApproved(true);
        } else {
          console.log("🏢 Business is not approved:", data.message);
          setIsApproved(false);
        }
      } catch (err) {
        console.error("🏢 Error checking approval status:", err)
        // If middleware let us through, we're probably an admin - allow access
        setIsApproved(true)
      } finally {
        setIsLoading(false)
      }
    }

    checkApprovalStatus()
  }, [])

  // Debug the current state
  console.log("🏢 BusinessApprovalCheck render state:", {
    isLoading,
    isApproved,
    isPendingApproval
  })

  // If we're checking business status, show loading
  if (isLoading) {
    console.log("🏢 BusinessApprovalCheck: Showing loading state")
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking business status...</p>
        </div>
      </div>
    )
  }

  // If pending approval, show pending message
  if (isPendingApproval) {
    return <PendingApprovalMessage />
  }

  // If not approved, redirect to register page
  if (isApproved === false) {
    console.log("🏢 User not approved, redirecting to register page");
    router.push('/business/register')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting...</p>
        </div>
      </div>
    )
  }

  // If approved or admin (middleware already checked permissions), render children
  console.log("🏢 Access granted, rendering children");
  return <>{children}</>
}
