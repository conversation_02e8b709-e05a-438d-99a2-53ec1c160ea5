"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Plus, Edit, Trash2, GripVertical, MoreHorizontal } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
// Sample menu categories for the admin interface
const initialMenuCategories = [
  {
    id: "starters",
    name: "Starters",
    items: [
      { id: "item-1", name: "Garlic Bread" },
      { id: "item-2", name: "Soup of the Day" }
    ]
  },
  {
    id: "mains",
    name: "Main Courses",
    items: [
      { id: "item-3", name: "Steak" },
      { id: "item-4", name: "Pasta" }
    ]
  }
]

export default function MenuCategoriesPage() {
  const [categories, setCategories] = useState(initialMenuCategories)
  const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentCategory, setCurrentCategory] = useState<any>(null)
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null)

  const handleAddEditCategory = (formData: any) => {
    if (currentCategory) {
      // Edit existing category
      setCategories((prev) =>
        prev.map((cat) => (cat.id === currentCategory.id ? { ...cat, name: formData.name } : cat)),
      )
    } else {
      // Add new category
      const newCategory = {
        id: `category-${Date.now()}`,
        name: formData.name,
        items: [],
      }
      setCategories((prev) => [...prev, newCategory])
    }
    setIsAddEditDialogOpen(false)
    setCurrentCategory(null)
  }

  const handleDeleteCategory = () => {
    if (!categoryToDelete) return

    setCategories((prev) => prev.filter((cat) => cat.id !== categoryToDelete))
    setIsDeleteDialogOpen(false)
    setCategoryToDelete(null)
  }

  const openAddDialog = () => {
    setCurrentCategory(null)
    setIsAddEditDialogOpen(true)
  }

  const openEditDialog = (category: any) => {
    setCurrentCategory(category)
    setIsAddEditDialogOpen(true)
  }

  const confirmDelete = (categoryId: string) => {
    setCategoryToDelete(categoryId)
    setIsDeleteDialogOpen(true)
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Link href="/restaurant-admin/menu" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Menu Categories</h1>
            <p className="text-gray-500">Manage your menu categories and their order</p>
          </div>
        </div>
        <div className="mt-4 md:mt-0">
          <Button className="bg-emerald-600 hover:bg-emerald-700" onClick={openAddDialog}>
            <Plus className="mr-2 h-4 w-4" />
            Add New Category
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <p className="text-sm text-gray-500">
          Drag and drop categories to reorder them. Changes will be reflected in how they appear on your menu.
        </p>

        {categories.map((category) => (
          <Card key={category.id}>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="mr-3 cursor-move">
                  <GripVertical className="h-5 w-5 text-gray-400" />
                </div>
                <div className="flex-grow">
                  <h3 className="font-medium">{category.name}</h3>
                  <p className="text-sm text-gray-500">{category.items.length} items</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-emerald-600"
                    onClick={() => openEditDialog(category)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openEditDialog(category)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600" onClick={() => confirmDelete(category.id)}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Category Dialog */}
      <Dialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{currentCategory ? "Edit Category" : "Add New Category"}</DialogTitle>
            <DialogDescription>
              {currentCategory
                ? "Update the name of this menu category"
                : "Create a new category to organize your menu items"}
            </DialogDescription>
          </DialogHeader>
          <CategoryForm
            category={currentCategory}
            onSubmit={handleAddEditCategory}
            onCancel={() => setIsAddEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this category? All items in this category will need to be moved or they
              will be deleted.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteCategory}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Category Form Component
function CategoryForm({
  category,
  onSubmit,
  onCancel,
}: {
  category: any
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [name, setName] = useState(category?.name || "")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ name })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="name" className="text-right">
            Category Name
          </Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="col-span-3"
            required
            placeholder="e.g., Starters, Main Courses, Desserts"
          />
        </div>
      </div>
      <div className="flex justify-end space-x-2 mt-4">
        <Button variant="outline" type="button" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" className="bg-emerald-600 hover:bg-emerald-700">
          {category ? "Update Category" : "Add Category"}
        </Button>
      </div>
    </form>
  )
}
