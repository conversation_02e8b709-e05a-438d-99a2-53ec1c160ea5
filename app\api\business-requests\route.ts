import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      businessName,
      businessType,
      suggestedAddress,
      customerName,
      customerEmail,
      notes
    } = body

    if (!businessName || !customerName || !customerEmail) {
      return NextResponse.json(
        { error: 'Business name, customer name, and email are required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get user session if available
    const { data: { session } } = await supabase.auth.getSession()

    // Check if user has already voted for this business
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('user_votes')
      .select('id')
      .eq('user_email', customerEmail)
      .eq('request_type', 'business')
      .eq('request_name', businessName)
      .single()

    if (voteCheckError && voteCheckError.code !== 'PGRST116') {
      console.error('Error checking existing vote:', voteCheckError)
      return NextResponse.json(
        { error: 'Failed to check existing votes' },
        { status: 500 }
      )
    }

    if (existingVote) {
      return NextResponse.json(
        { error: 'You have already voted for this business' },
        { status: 400 }
      )
    }

    // Check if this business already exists
    const { data: existingBusiness, error: businessError } = await supabase
      .from('business_requests')
      .select('id, vote_count')
      .eq('business_name', businessName)
      .eq('status', 'pending')
      .single()

    if (businessError && businessError.code !== 'PGRST116') {
      console.error('Error checking existing business:', businessError)
      return NextResponse.json(
        { error: 'Failed to check existing businesses' },
        { status: 500 }
      )
    }

    if (existingBusiness) {
      // Business exists, increment vote count
      const { data: updatedRequest, error: updateError } = await supabase
        .from('business_requests')
        .update({
          vote_count: existingBusiness.vote_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingBusiness.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating business request:', updateError)
        return NextResponse.json(
          { error: 'Failed to update business request' },
          { status: 500 }
        )
      }

      // Record this user's vote
      await supabase
        .from('user_votes')
        .insert({
          user_id: session?.user?.id || null,
          user_email: customerEmail,
          request_type: 'business',
          request_name: businessName
        })

      return NextResponse.json({
        message: 'Vote added successfully',
        request: updatedRequest
      })
    }

    // Create new business request
    const { data: newRequest, error: insertError } = await supabase
      .from('business_requests')
      .insert({
        business_name: businessName,
        business_type: businessType,
        suggested_address: suggestedAddress,
        customer_name: customerName,
        customer_email: customerEmail,
        user_id: session?.user?.id || null,
        notes: notes || null,
        vote_count: 1,
        status: 'pending'
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating business request:', insertError)
      return NextResponse.json(
        { error: 'Failed to create business request' },
        { status: 500 }
      )
    }

    // Record this user's vote for the new request
    await supabase
      .from('user_votes')
      .insert({
        user_id: session?.user?.id || null,
        user_email: customerEmail,
        request_type: 'business',
        request_name: businessName
      })

    // Create admin notification
    try {
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')

      if (adminUsers && adminUsers.length > 0) {
        const notifications = adminUsers.map(admin => ({
          admin_user_id: admin.id,
          type: 'business_request',
          title: 'New Business Request',
          message: `${customerName} has requested ${businessName} to join Loop`,
          action_url: `/admin/business-requests/${newRequest.id}`,
          priority: 'medium',
          related_table: 'business_requests',
          related_record_id: newRequest.id,
          metadata: {
            business_name: businessName,
            customer_email: customerEmail,
            vote_count: 1
          }
        }))

        await supabase
          .from('admin_notifications')
          .insert(notifications)
      }
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification creation fails
    }

    return NextResponse.json({
      message: 'Business request submitted successfully!',
      request: newRequest
    })

  } catch (error) {
    console.error('Error in business requests API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'vote_count'
    const order = searchParams.get('order') || 'desc'

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get business requests sorted by vote count
    const { data: requests, error, count } = await supabase
      .from('business_requests')
      .select('*', { count: 'exact' })
      .eq('status', 'pending')
      .order(sortBy, { ascending: order === 'asc' })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching business requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      requests: requests || [],
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in business requests GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
