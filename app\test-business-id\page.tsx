"use client"

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"

export default function TestBusinessIdPage() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message])
  }

  const runTest = async () => {
    setIsLoading(true)
    setTestResults([])
    
    try {
      addResult('Testing business ID conversion...')
      
      // 1. Get a business from the database
      addResult('\nFetching a business from the database:')
      
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('id, name, slug')
        .limit(1)
        .single()
      
      if (businessError || !business) {
        throw new Error('Could not find a business to use')
      }
      
      addResult(`Found business: id=${business.id}, name=${business.name}, slug=${business.slug}`)
      
      // 2. Create a test cart item with string business ID
      const testCartItem = {
        id: '123',
        businessId: business.slug, // Use the slug as the business ID
        businessName: business.name,
        businessType: 'restaurant',
        name: 'Test Product',
        price: 10.99,
        quantity: 1
      }
      
      addResult('\nCreated test cart item with string business ID:')
      addResult(JSON.stringify(testCartItem, null, 2))
      
      // 3. Add businessNumericId to the cart item
      const updatedCartItem = {
        ...testCartItem,
        businessNumericId: business.id // Add the numeric business ID
      }
      
      addResult('\nUpdated cart item with businessNumericId:')
      addResult(JSON.stringify(updatedCartItem, null, 2))
      
      // 4. Simulate the conversion process
      let numericBusinessId = null
      
      // Priority order for setting numericBusinessId:
      // 1. Use businessNumericId if available
      if (updatedCartItem.businessNumericId && !isNaN(Number(updatedCartItem.businessNumericId))) {
        numericBusinessId = Number(updatedCartItem.businessNumericId)
        addResult(`\nUsing businessNumericId=${numericBusinessId} for business ${updatedCartItem.businessName}`)
      }
      // 2. Check if businessId is already a number
      else if (updatedCartItem.businessId && !isNaN(Number(updatedCartItem.businessId))) {
        numericBusinessId = Number(updatedCartItem.businessId)
        addResult(`\nBusiness ID ${updatedCartItem.businessId} is already a number: ${numericBusinessId}`)
      }
      // 3. Try to extract a numeric ID from the slug
      else if (typeof updatedCartItem.businessId === 'string') {
        const numericMatch = updatedCartItem.businessId.match(/\d+/)
        if (numericMatch) {
          numericBusinessId = parseInt(numericMatch[0])
          addResult(`\nExtracted numeric ID ${numericBusinessId} from slug ${updatedCartItem.businessId}`)
        }
      }
      
      if (numericBusinessId === null) {
        addResult('\nCould not determine numeric business ID')
      } else {
        addResult('\nSuccessfully converted business ID to numeric: ' + numericBusinessId)
        
        // 5. Verify that the numeric ID exists in the database
        const { data: verifyBusiness, error: verifyError } = await supabase
          .from('businesses')
          .select('id, name, slug')
          .eq('id', numericBusinessId)
          .single()
        
        if (verifyError || !verifyBusiness) {
          addResult(`\nWarning: Converted business ID ${numericBusinessId} does not exist in the database`)
        } else {
          addResult(`\nVerified business ID ${numericBusinessId} exists in the database:`)
          addResult(JSON.stringify(verifyBusiness, null, 2))
        }
      }
      
      // 6. Test with a string business ID that doesn't have a numeric part
      const nonNumericCartItem = {
        ...testCartItem,
        businessId: 'jersey-wings', // A slug without numbers
        businessName: 'Jersey Wings',
        businessNumericId: null // No numeric ID
      }
      
      addResult('\n\nTesting with a non-numeric business ID:')
      addResult(JSON.stringify(nonNumericCartItem, null, 2))
      
      // Try to convert the non-numeric business ID
      let nonNumericResult = null
      
      // 1. Use businessNumericId if available
      if (nonNumericCartItem.businessNumericId && !isNaN(Number(nonNumericCartItem.businessNumericId))) {
        nonNumericResult = Number(nonNumericCartItem.businessNumericId)
        addResult(`\nUsing businessNumericId=${nonNumericResult} for business ${nonNumericCartItem.businessName}`)
      }
      // 2. Check if businessId is already a number
      else if (nonNumericCartItem.businessId && !isNaN(Number(nonNumericCartItem.businessId))) {
        nonNumericResult = Number(nonNumericCartItem.businessId)
        addResult(`\nBusiness ID ${nonNumericCartItem.businessId} is already a number: ${nonNumericResult}`)
      }
      // 3. Try to extract a numeric ID from the slug
      else if (typeof nonNumericCartItem.businessId === 'string') {
        const numericMatch = nonNumericCartItem.businessId.match(/\d+/)
        if (numericMatch) {
          nonNumericResult = parseInt(numericMatch[0])
          addResult(`\nExtracted numeric ID ${nonNumericResult} from slug ${nonNumericCartItem.businessId}`)
        } else {
          addResult(`\nNo numeric part found in business ID: ${nonNumericCartItem.businessId}`)
          
          // 4. Look up the business by slug
          const { data: lookupBusiness, error: lookupError } = await supabase
            .from('businesses')
            .select('id, name, slug')
            .eq('slug', nonNumericCartItem.businessId)
            .single()
          
          if (lookupError || !lookupBusiness) {
            addResult(`\nCould not find business with slug: ${nonNumericCartItem.businessId}`)
          } else {
            nonNumericResult = lookupBusiness.id
            addResult(`\nFound business ID ${nonNumericResult} by looking up slug ${nonNumericCartItem.businessId}`)
          }
        }
      }
      
      if (nonNumericResult === null) {
        addResult('\nCould not determine numeric business ID for non-numeric case')
        addResult('\nIn this case, we would generate a unique ID based on the index')
        nonNumericResult = 1000 // Example of a generated ID
        addResult(`\nGenerated unique business ID: ${nonNumericResult}`)
      }
      
      addResult('\nBusiness ID conversion test completed successfully!')
      
    } catch (error: any) {
      addResult(`Test failed: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Business ID Conversion Test</h1>
      
      <Button 
        onClick={runTest} 
        disabled={isLoading}
        className="mb-6"
      >
        {isLoading ? 'Running Test...' : 'Run Test'}
      </Button>
      
      <div className="bg-gray-100 p-4 rounded-md">
        <h2 className="text-lg font-semibold mb-2">Test Results:</h2>
        <pre className="whitespace-pre-wrap text-sm">
          {testResults.length > 0 
            ? testResults.join('\n') 
            : 'Click "Run Test" to start the test'}
        </pre>
      </div>
    </div>
  )
}
