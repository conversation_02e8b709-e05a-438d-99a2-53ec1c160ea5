import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'week' // day, week, month, all
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id, name')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active, total_deliveries')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Calculate date range based on period
    const now = new Date()
    let startDate = new Date()

    switch (period) {
      case 'day':
        // Use UTC to avoid timezone issues
        startDate.setUTCHours(0, 0, 0, 0)
        break
      case 'week':
        // Calculate start of week (Monday at 00:00:00 UTC)
        const dayOfWeek = now.getUTCDay() // 0 = Sunday, 1 = Monday, etc.
        const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // Convert to Monday-based week
        startDate.setUTCDate(now.getUTCDate() - daysFromMonday)
        startDate.setUTCHours(0, 0, 0, 0)
        break
      case 'month':
        startDate.setUTCDate(1) // Start of month
        startDate.setUTCHours(0, 0, 0, 0)
        break
      case 'all':
        startDate = new Date('2020-01-01') // Far back date
        break
      default:
        // Default to week - Calculate start of week (Monday at 00:00:00 UTC)
        const defaultDayOfWeek = now.getUTCDay()
        const defaultDaysFromMonday = defaultDayOfWeek === 0 ? 6 : defaultDayOfWeek - 1
        startDate.setUTCDate(now.getUTCDate() - defaultDaysFromMonday)
        startDate.setUTCHours(0, 0, 0, 0)
    }

    // Get earnings for the period
    let earningsQuery = supabase
      .from('driver_earnings')
      .select(`
        id,
        order_id,
        base_amount,
        tip_amount,
        bonus_amount,
        total_amount,
        status,
        payout_id,
        payout_date,
        eligible_for_payout_at,
        created_at,
        orders!driver_earnings_order_id_fkey (
          id,
          order_number,
          business_name,
          customer_name,
          delivery_type,
          total
        )
      `)
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: earnings, error: earningsError } = await earningsQuery

    if (earningsError) {
      console.error('Error fetching earnings:', earningsError)
      // Don't fail if it's just a missing relationship - return empty data
      if (earningsError.message.includes('Could not embed')) {
        console.log('No earnings data found, returning empty results')
      } else {
        return NextResponse.json(
          { error: "Failed to fetch earnings" },
          { status: 500 }
        )
      }
    }

    // Handle case where earnings is null or empty
    const safeEarnings = earnings || []

    // Calculate summary statistics
    const totalEarnings = safeEarnings.reduce((sum, earning) =>
      sum + (earning.total_amount || 0), 0
    )

    const totalBase = safeEarnings.reduce((sum, earning) =>
      sum + (earning.base_amount || 0), 0
    )

    const totalTips = safeEarnings.reduce((sum, earning) =>
      sum + (earning.tip_amount || 0), 0
    )

    const totalBonuses = safeEarnings.reduce((sum, earning) =>
      sum + (earning.bonus_amount || 0), 0
    )

    const deliveryCount = safeEarnings.length
    const averageEarningPerDelivery = deliveryCount > 0 ? totalEarnings / deliveryCount : 0

    // Get all earnings for this driver to calculate payout status
    const { data: allEarnings } = await supabase
      .from('driver_earnings')
      .select('total_amount, status, eligible_for_payout_at, created_at')
      .eq('driver_id', driverProfile.id)

    // Calculate different payout categories
    const paidAmount = allEarnings?.filter(e => e.status === 'paid')
      .reduce((sum, earning) => sum + (earning.total_amount || 0), 0) || 0

    const pendingAmount = allEarnings?.filter(e => {
      if (e.status !== 'pending') return false
      // If no eligible_for_payout_at, use created_at + 2 hours
      const eligibleTime = e.eligible_for_payout_at
        ? new Date(e.eligible_for_payout_at)
        : new Date(new Date(e.created_at).getTime() + 2 * 60 * 60 * 1000)
      return now < eligibleTime
    }).reduce((sum, earning) => sum + (earning.total_amount || 0), 0) || 0

    const readyForPayoutAmount = allEarnings?.filter(e => {
      if (e.status !== 'pending') return false
      // If no eligible_for_payout_at, use created_at + 2 hours
      const eligibleTime = e.eligible_for_payout_at
        ? new Date(e.eligible_for_payout_at)
        : new Date(new Date(e.created_at).getTime() + 2 * 60 * 60 * 1000)
      return now >= eligibleTime
    }).reduce((sum, earning) => sum + (earning.total_amount || 0), 0) || 0

    // Get this week's stats for comparison
    const startOfWeek = new Date()
    const weekDayOfWeek = now.getUTCDay()
    const weekDaysFromMonday = weekDayOfWeek === 0 ? 6 : weekDayOfWeek - 1
    startOfWeek.setUTCDate(now.getUTCDate() - weekDaysFromMonday)
    startOfWeek.setUTCHours(0, 0, 0, 0)

    const { data: weekEarnings } = await supabase
      .from('driver_earnings')
      .select('total_amount')
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startOfWeek.toISOString())

    const weekTotal = weekEarnings?.reduce((sum, earning) =>
      sum + (earning.total_amount || 0), 0
    ) || 0

    // Get today's stats
    const startOfDay = new Date()
    startOfDay.setUTCHours(0, 0, 0, 0)

    const { data: todayEarnings } = await supabase
      .from('driver_earnings')
      .select('total_amount')
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startOfDay.toISOString())

    const todayTotal = todayEarnings?.reduce((sum, earning) =>
      sum + (earning.total_amount || 0), 0
    ) || 0

    return NextResponse.json({
      period,
      summary: {
        totalEarnings,
        totalBase,
        totalTips,
        totalBonuses,
        deliveryCount,
        averageEarningPerDelivery: Math.round(averageEarningPerDelivery * 100) / 100,
        pendingAmount,
        paidAmount,
        readyForPayoutAmount,
        currency: 'GBP'
      },
      quickStats: {
        today: todayTotal,
        thisWeek: weekTotal,
        totalDeliveries: driverProfile.total_deliveries
      },
      earnings: safeEarnings.map(earning => ({
        id: earning.id,
        orderId: earning.order_id,
        orderNumber: earning.orders?.order_number,
        businessName: earning.orders?.business_name,
        customerName: earning.orders?.customer_name,
        deliveryType: earning.orders?.delivery_type,
        orderTotal: earning.orders?.total,
        baseAmount: earning.base_amount,
        tipAmount: earning.tip_amount,
        bonusAmount: earning.bonus_amount,
        totalAmount: earning.total_amount,
        paymentStatus: earning.status,
        payoutId: earning.payout_id,
        payoutDate: earning.payout_date,
        eligibleForPayoutAt: earning.eligible_for_payout_at,
        earnedAt: earning.created_at
      })) || [],
      pagination: {
        limit,
        offset,
        hasMore: safeEarnings.length === limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in driver earnings API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
