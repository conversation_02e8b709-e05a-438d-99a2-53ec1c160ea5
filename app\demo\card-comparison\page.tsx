'use client'

import { useState } from 'react'
import SquareBusinessCard from '@/components/square-business-card'
import PortraitBusinessCard from '@/components/portrait-business-card'

export default function CardComparisonPage() {
  // Sample business data for demonstration
  const sampleBusinesses = [
    {
      id: '1',
      name: '<PERSON>\'s Jersey',
      image: '/images/businesses/mcdonalds.jpg',
      businessType: 'restaurant',
      rating: 4.5,
      deliveryTime: '25',
      deliveryTimeRange: '20-30 min',
      deliveryFee: '£2.50',
      location: 'St Helier',
      offers: [
        { text: '20% OFF', color: 'bg-red-500' },
        { text: 'Free Delivery', color: 'bg-emerald-500' }
      ]
    },
    {
      id: '2',
      name: 'Burger King',
      image: '/images/businesses/burgerking.jpg',
      businessType: 'restaurant',
      rating: 4.2,
      deliveryTime: '30',
      deliveryTimeRange: '25-35 min',
      deliveryFee: '£3.00',
      location: 'St Brelade',
      offers: [
        { text: 'Buy One Get One Free', color: 'bg-orange-500' }
      ]
    },
    {
      id: '3',
      name: 'Harbour Coffee',
      image: '/images/businesses/coffee.jpg',
      businessType: 'cafe',
      rating: 4.8,
      deliveryTime: '15',
      deliveryTimeRange: '10-20 min',
      deliveryFee: 'Free delivery',
      location: 'St Aubin',
      sponsored: true
    }
  ]

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Business Card Comparison</h1>

      <div className="grid grid-cols-1 gap-8">
        {/* Description of the changes */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-3">Portrait Cards with Square Images</h2>
          <p className="mb-4">
            The portrait business card maintains the square dimensions of the image while making the overall card taller than wide.
            This creates a portrait-oriented card with a square image at the top and metadata below.
          </p>
          <ul className="list-disc pl-6 space-y-1 text-gray-700">
            <li>Square image dimensions are maintained (aspect-square)</li>
            <li>Overall card is in portrait orientation (taller than wide)</li>
            <li>Card layout is vertical with image at top and metadata below</li>
            <li>Consistent sizing across all cards</li>
            <li>Same metadata display as the original cards</li>
          </ul>
        </div>

        {/* Square vs Portrait Card Comparison */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Square vs Portrait Card</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Square Card (Original)</h3>
              <SquareBusinessCard
                id={sampleBusinesses[0].id}
                name={sampleBusinesses[0].name}
                image={sampleBusinesses[0].image}
                businessType={sampleBusinesses[0].businessType}
                rating={sampleBusinesses[0].rating}
                deliveryTime={sampleBusinesses[0].deliveryTime}
                deliveryTimeRange={sampleBusinesses[0].deliveryTimeRange}
                deliveryFee={sampleBusinesses[0].deliveryFee}
                location={sampleBusinesses[0].location}
                offers={sampleBusinesses[0].offers}
              />
            </div>
            <div>
              <h3 className="text-lg font-medium mb-3">Portrait Card (New)</h3>
              <PortraitBusinessCard
                id={sampleBusinesses[0].id}
                name={sampleBusinesses[0].name}
                image={sampleBusinesses[0].image}
                businessType={sampleBusinesses[0].businessType}
                rating={sampleBusinesses[0].rating}
                deliveryTime={sampleBusinesses[0].deliveryTime}
                deliveryTimeRange={sampleBusinesses[0].deliveryTimeRange}
                deliveryFee={sampleBusinesses[0].deliveryFee}
                location={sampleBusinesses[0].location}
                offers={sampleBusinesses[0].offers}
              />
            </div>
          </div>
        </div>

        {/* Portrait Cards Grid */}
        <div className="mt-12">
          <h2 className="text-xl font-semibold mb-4">Portrait Cards Grid</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 grid-auto-rows-fr">
            {sampleBusinesses.map((business) => (
              <PortraitBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>

        {/* Square Cards Grid (for comparison) */}
        <div className="mt-12">
          <h2 className="text-xl font-semibold mb-4">Square Cards Grid (Original)</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {sampleBusinesses.map((business) => (
              <SquareBusinessCard
                key={business.id}
                id={business.id}
                name={business.name}
                image={business.image}
                businessType={business.businessType}
                rating={business.rating}
                deliveryTime={business.deliveryTime}
                deliveryTimeRange={business.deliveryTimeRange}
                deliveryFee={business.deliveryFee}
                location={business.location}
                offers={business.offers || []}
                sponsored={business.sponsored}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
