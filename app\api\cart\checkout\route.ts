import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { calculateBusinessDeliveryFee } from '@/lib/delivery-fee-calculator';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// POST endpoint to calculate checkout totals
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    const { data: { user } } = await supabaseClient.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Get delivery address for calculations
    const deliveryAddress = body.deliveryAddress;

    if (!deliveryAddress) {
      return NextResponse.json(
        { error: 'Delivery address is required' },
        { status: 400 }
      );
    }

    // First, validate the cart
    const { data: validationData, error: validationError } = await supabase.rpc(
      'validate_cart',
      { p_user_id: user.id }
    );

    if (validationError) {
      console.error('Error validating cart:', validationError);
      return NextResponse.json(
        { error: 'Failed to validate cart', details: validationError.message },
        { status: 500 }
      );
    }

    if (!validationData.valid) {
      return NextResponse.json(
        { error: 'Cart validation failed', details: validationData.message },
        { status: 400 }
      );
    }

    // Get the cart to calculate totals
    const { data: cartData, error: cartError } = await supabase.rpc(
      'get_user_cart_enhanced',
      { p_user_id: user.id }
    );

    if (cartError) {
      console.error('Error fetching cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch cart', details: cartError.message },
        { status: 500 }
      );
    }

    if (!cartData || !cartData.cart || !cartData.cart.items || cartData.cart.items.length === 0) {
      return NextResponse.json(
        { error: 'Cart is empty' },
        { status: 400 }
      );
    }

    // Group items by business
    const businessItems: Record<string, any[]> = {};
    const items = cartData.cart.items;

    for (const item of items) {
      if (!businessItems[item.businessId]) {
        businessItems[item.businessId] = [];
      }
      businessItems[item.businessId].push(item);
    }

    // Calculate totals for each business
    const businessTotals: Record<string, any> = {};
    let orderSubtotal = 0;
    let orderDeliveryFee = 0;
    let orderServiceFee = 0;
    let orderTax = 0;

    for (const businessId in businessItems) {
      // Get business details with coordinates and delivery fee model
      const { data: business } = await supabase
        .from('businesses')
        .select('name, delivery_fee, delivery_fee_model, delivery_fee_per_km, coordinates, service_fee_percentage, tax_rate')
        .eq('slug', businessId)
        .single();

      // Calculate subtotal for this business
      const items = businessItems[businessId];
      let subtotal = 0;

      for (const item of items) {
        // Base price * quantity
        let itemTotal = item.price * item.quantity;

        // Add customization costs
        if (item.customizations) {
          for (const group of item.customizations) {
            for (const option of group.options) {
              itemTotal += option.price * item.quantity;
            }
          }
        }

        subtotal += itemTotal;
      }

      // Calculate delivery fee based on model and distance
      let deliveryFee = business?.delivery_fee || 0;
      let deliveryDistance = 0;

      // Only calculate distance-based fee if we have the necessary data
      if (business?.coordinates && deliveryAddress?.postcode) {
        try {
          // Parse coordinates from database (could be stored as a string, array, or object)
          let businessCoords: [number, number] = [0, 0];

          if (typeof business.coordinates === 'string') {
            // If stored as a string like "lng,lat" or as a stringified object
            try {
              const coords = JSON.parse(business.coordinates);
              businessCoords = Array.isArray(coords) ? coords : [coords.lng || coords.longitude, coords.lat || coords.latitude];
            } catch {
              // If it's a simple string like "lng,lat"
              businessCoords = business.coordinates.split(',').map(Number) as [number, number];
            }
          } else if (Array.isArray(business.coordinates)) {
            // If stored as an array
            businessCoords = business.coordinates as [number, number];
          } else if (typeof business.coordinates === 'object') {
            // If stored as an object with lng/lat properties
            const coords = business.coordinates as any;
            businessCoords = [coords.lng || coords.longitude, coords.lat || coords.latitude];
          }

        
          // Log the business delivery model for debugging
          console.log(`Checkout - Business ${businessId} delivery model: ${business.delivery_fee_model}, fee: ${business.delivery_fee}, fee per km: ${business.delivery_fee_per_km}`);

          const deliveryResult = await calculateBusinessDeliveryFee(
            {
              coordinates: businessCoords,
              delivery_fee_model: business.delivery_fee_model,
              delivery_fee: business.delivery_fee,
              delivery_fee_per_km: business.delivery_fee_per_km
            },
            deliveryAddress.postcode,
            deliveryAddress.coordinates ? [deliveryAddress.coordinates.lng, deliveryAddress.coordinates.lat] : undefined,
            true // isCheckout = true
          );

          deliveryFee = deliveryResult.fee;
          deliveryDistance = deliveryResult.routeDistance || deliveryResult.distance;

          console.log(`Calculated delivery fee for ${businessId}: £${deliveryFee.toFixed(2)} (${deliveryDistance.toFixed(1)}km using ${deliveryResult.calculationMethod})`);
        } catch (error) {
          console.error(`Error calculating delivery fee for ${businessId}:`, error);
          // Fall back to fixed fee if calculation fails
          deliveryFee = business?.delivery_fee || 0;
        }
      }

      const serviceFeePercentage = business?.service_fee_percentage || 0;
      const taxRate = business?.tax_rate || 0;

      const serviceFee = (subtotal * serviceFeePercentage) / 100;
      const tax = (subtotal * taxRate) / 100;

      // Store business totals
      businessTotals[businessId] = {
        businessId,
        businessName: business?.name || cartData.businessNames[businessId] || 'Unknown Business',
        subtotal,
        deliveryFee,
        deliveryDistance,
        serviceFee,
        tax,
        total: subtotal + deliveryFee + serviceFee + tax,
        items: items.length
      };

      // Add to order totals
      orderSubtotal += subtotal;
      orderDeliveryFee += deliveryFee;
      orderServiceFee += serviceFee;
      orderTax += tax;
    }

    // Calculate order total
    const orderTotal = orderSubtotal + orderDeliveryFee + orderServiceFee + orderTax;

    return NextResponse.json({
      success: true,
      orderTotals: {
        subtotal: orderSubtotal,
        deliveryFee: orderDeliveryFee,
        serviceFee: orderServiceFee,
        tax: orderTax,
        total: orderTotal
      },
      businessTotals,
      deliveryAddress
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
