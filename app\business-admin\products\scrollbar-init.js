// This script ensures the scrollbar is properly initialized
// It will be executed on the client side

export function initScrollbar() {
  // Wait for DOM to be fully loaded
  if (typeof window !== 'undefined') {
    setTimeout(() => {
      const tableContainers = document.querySelectorAll('.table-container');
      const isMobileOrTablet = window.innerWidth < 1024; // Check if device is mobile or tablet

      tableContainers.forEach(container => {
        // Force a reflow to ensure scrollbar is rendered
        container.style.display = 'none';
        // This line forces a reflow
        void container.offsetHeight;
        container.style.display = 'block';

        // Add a class to indicate the scrollbar is initialized
        container.classList.add('scrollbar-initialized');

        // Set explicit scrollbar properties
        container.style.overflowX = 'auto';
        container.style.overflowY = 'auto';
        container.style.maxWidth = '100%';
        container.style.width = '100%';
        container.style.maxHeight = isMobileOrTablet ? '500px' : '600px'; // Smaller height for mobile/tablet
        container.style.scrollbarWidth = 'auto';
        container.style.scrollbarColor = '#10b981 #f1f1f1';

        // Add touch-specific properties for mobile/tablet
        if (isMobileOrTablet) {
          container.style.touchAction = 'pan-x pan-y';
          container.style.WebkitOverflowScrolling = 'touch';

          // Add event listeners for touch devices
          container.addEventListener('touchstart', function() {
            // Add visual feedback when user touches the scrollable area
            container.classList.add('touch-active');
          });

          container.addEventListener('touchend', function() {
            // Remove visual feedback when touch ends
            container.classList.remove('touch-active');
          });
        }

        // Ensure the table inside has proper width settings
        const table = container.querySelector('table');
        if (table) {
          table.style.width = 'auto';
          table.style.minWidth = '100%';
          table.style.tableLayout = 'auto'; // Allow columns to size based on content

          // Adjust font size for mobile/tablet
          if (isMobileOrTablet) {
            table.style.fontSize = '0.75rem'; // Smaller font for mobile/tablet
          }

          // Ensure all table cells have proper sizing
          const cells = table.querySelectorAll('th, td');
          cells.forEach(cell => {
            cell.style.whiteSpace = 'nowrap';
            cell.style.overflow = 'hidden';
            cell.style.textOverflow = 'ellipsis';
          });
        }

        // Check if horizontal scrollbar is needed
        if (container.scrollWidth > container.clientWidth) {
          // Make scrollbar more visible by adding a subtle animation
          container.classList.add('has-horizontal-scrollbar');

          // Scroll slightly to show the user there's content to scroll horizontally
          setTimeout(() => {
            container.scrollLeft = 10;
            setTimeout(() => {
              container.scrollLeft = 0;
            }, 500);
          }, 1000);
        }

        // Check if vertical scrollbar is needed
        if (container.scrollHeight > container.clientHeight) {
          container.classList.add('has-vertical-scrollbar');

          // Scroll slightly to show the user there's content to scroll vertically
          if (container.scrollHeight > (isMobileOrTablet ? 600 : 800)) {
            setTimeout(() => {
              container.scrollTop = 10;
              setTimeout(() => {
                container.scrollTop = 0;
              }, 500);
            }, 1500);
          }
        }

        // Add resize listener to handle orientation changes on tablets
        window.addEventListener('resize', function() {
          const isNowMobileOrTablet = window.innerWidth < 1024;

          // Update container height if device type changed
          if (isNowMobileOrTablet !== isMobileOrTablet) {
            container.style.maxHeight = isNowMobileOrTablet ? '500px' : '600px';

            if (table) {
              table.style.fontSize = isNowMobileOrTablet ? '0.75rem' : '0.875rem';
            }
          }
        });
      });
    }, 500);
  }
}
