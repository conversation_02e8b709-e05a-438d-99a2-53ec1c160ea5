// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function analyzeBusinessCategories() {
  try {
    console.log('=== ANALYZING BUSINESSES AND POPULAR CATEGORIES ===\n');

    // Get all businesses with their types
    const { data: businesses, error: businessError } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        is_approved,
        business_types!business_type_id (
          name,
          slug
        )
      `)
      .eq('is_approved', true)
      .order('name');

    if (businessError) {
      console.error('Error fetching businesses:', businessError);
      return;
    }

    console.log(`📊 Found ${businesses.length} approved businesses\n`);

    // Group businesses by type
    const businessesByType = {};
    businesses.forEach(business => {
      const typeName = business.business_types?.name || 'Unknown';
      if (!businessesByType[typeName]) {
        businessesByType[typeName] = [];
      }
      businessesByType[typeName].push(business);
    });

    // Show businesses by type
    Object.keys(businessesByType).forEach(type => {
      console.log(`\n🏢 ${type.toUpperCase()} (${businessesByType[type].length} businesses)`);
      console.log('='.repeat(50));
      businessesByType[type].forEach(business => {
        console.log(`   • ${business.name} (${business.slug}) - ID: ${business.id}`);
      });
    });

    // Get popular categories that should be used
    console.log('\n\n🎯 RECOMMENDED POPULAR CATEGORIES BY BUSINESS TYPE');
    console.log('='.repeat(60));

    const popularCategories = {
      'Restaurant': [
        'Pizza', 'Burgers & Sandwiches', 'Asian Cuisine', 'Italian Cuisine', 
        'British Cuisine', 'French Cuisine', 'Seafood', 'Vegetarian & Vegan',
        'Desserts', 'Main Courses', 'Starters & Appetizers'
      ],
      'Shop': [
        'Groceries', 'Convenience', 'Home Goods', 'Clothing & Accessories',
        'Electronics & Tech', 'Fresh Produce', 'Dairy & Eggs', 'Personal Care',
        'Cleaning Supplies', 'Frozen Foods'
      ],
      'Pharmacy': [
        'Prescription', 'Wellness', 'Over-the-Counter', 'Pain Relief',
        'Cold & Flu', 'Vitamins & Minerals', 'Personal Care', 'First Aid',
        'Skincare'
      ],
      'Cafe': [
        'Coffee & Drinks', 'Breakfast', 'Pastries & Baked Goods',
        'Light Lunch', 'Hot Drinks', 'Cold Drinks', 'Specialty Coffee'
      ],
      'Errand': [
        'Shopping', 'Delivery', 'Tasks', 'Grocery Shopping', 'Restaurant Pickup',
        'Pharmacy Pickup', 'Package Delivery', 'Airport Runs'
      ]
    };

    // Get actual categories from database to match names
    const { data: allCategories, error: catError } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        business_types!business_type_id (
          name
        )
      `)
      .eq('is_active', true);

    if (catError) {
      console.error('Error fetching categories:', catError);
      return;
    }

    // Create mapping of category names to IDs
    const categoryMap = {};
    allCategories.forEach(cat => {
      const businessTypeName = cat.business_types?.name || 'Unknown';
      if (!categoryMap[businessTypeName]) {
        categoryMap[businessTypeName] = {};
      }
      categoryMap[businessTypeName][cat.name] = cat.id;
    });

    // Show recommended category subscriptions for each business
    console.log('\n\n📋 RECOMMENDED CATEGORY SUBSCRIPTIONS');
    console.log('='.repeat(60));

    const subscriptionPlan = [];

    Object.keys(businessesByType).forEach(businessType => {
      if (!popularCategories[businessType]) return;

      console.log(`\n🏢 ${businessType} businesses:`);
      
      businessesByType[businessType].forEach(business => {
        console.log(`\n   📍 ${business.name}:`);
        
        const recommendedCats = getRecommendedCategories(business, popularCategories[businessType], categoryMap[businessType] || {});
        
        recommendedCats.forEach(catInfo => {
          console.log(`      • ${catInfo.name} (ID: ${catInfo.id})`);
          subscriptionPlan.push({
            businessId: business.id,
            businessName: business.name,
            categoryId: catInfo.id,
            categoryName: catInfo.name
          });
        });
      });
    });

    console.log(`\n\n📊 SUBSCRIPTION SUMMARY`);
    console.log('='.repeat(60));
    console.log(`Total planned subscriptions: ${subscriptionPlan.length}`);
    
    // Group by business
    const subscriptionsByBusiness = {};
    subscriptionPlan.forEach(sub => {
      if (!subscriptionsByBusiness[sub.businessName]) {
        subscriptionsByBusiness[sub.businessName] = [];
      }
      subscriptionsByBusiness[sub.businessName].push(sub);
    });

    Object.keys(subscriptionsByBusiness).forEach(businessName => {
      console.log(`   ${businessName}: ${subscriptionsByBusiness[businessName].length} categories`);
    });

    // Save subscription plan for next script
    require('fs').writeFileSync(
      'scripts/subscription-plan.json', 
      JSON.stringify(subscriptionPlan, null, 2)
    );

    console.log('\n✅ Subscription plan saved to scripts/subscription-plan.json');

  } catch (error) {
    console.error('Error analyzing business categories:', error);
  }
}

function getRecommendedCategories(business, popularCategoryNames, categoryMap) {
  const businessName = business.name.toLowerCase();
  const businessSlug = business.slug.toLowerCase();
  
  const recommendations = [];
  
  // Match categories based on business name/type
  popularCategoryNames.forEach(catName => {
    if (categoryMap[catName]) {
      // Smart matching based on business name
      if (shouldSubscribeToCategory(businessName, businessSlug, catName)) {
        recommendations.push({
          id: categoryMap[catName],
          name: catName
        });
      }
    }
  });

  // Ensure at least 2-3 categories per business
  if (recommendations.length < 2) {
    popularCategoryNames.slice(0, 3).forEach(catName => {
      if (categoryMap[catName] && !recommendations.find(r => r.name === catName)) {
        recommendations.push({
          id: categoryMap[catName],
          name: catName
        });
      }
    });
  }

  return recommendations;
}

function shouldSubscribeToCategory(businessName, businessSlug, categoryName) {
  const catLower = categoryName.toLowerCase();
  
  // Direct matches
  if (businessName.includes('pizza') && catLower.includes('pizza')) return true;
  if (businessName.includes('burger') && catLower.includes('burger')) return true;
  if (businessName.includes('coffee') && catLower.includes('coffee')) return true;
  if (businessName.includes('grocer') && catLower.includes('grocer')) return true;
  if (businessName.includes('pharmacy') && catLower.includes('prescription')) return true;
  if (businessName.includes('convenience') && catLower.includes('convenience')) return true;
  if (businessName.includes('wellness') && catLower.includes('wellness')) return true;
  if (businessName.includes('home') && catLower.includes('home')) return true;
  if (businessName.includes('task') && catLower.includes('task')) return true;
  if (businessName.includes('runner') && catLower.includes('shopping')) return true;

  // Asian restaurants
  if ((businessName.includes('asian') || businessName.includes('chinese') || businessName.includes('thai')) 
      && catLower.includes('asian')) return true;

  // Italian restaurants  
  if ((businessName.includes('italian') || businessName.includes('pasta')) 
      && catLower.includes('italian')) return true;

  // British/Local food
  if ((businessName.includes('british') || businessName.includes('pub') || businessName.includes('local')) 
      && catLower.includes('british')) return true;

  // Seafood
  if ((businessName.includes('fish') || businessName.includes('seafood') || businessName.includes('coastal')) 
      && catLower.includes('seafood')) return true;

  return false;
}

analyzeBusinessCategories()
  .then(() => {
    console.log('\n✨ Analysis completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
