import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { type = 'driver_order' } = await request.json()

    // Get any available subscription for testing
    let subscriptions = []
    let error = null

    // Get appropriate subscription based on test type
    if (type === 'customer_test') {
      // For customer tests, get the current user's active subscription
      const { data: customerSubs, error: customerError } = await supabase
        .from('push_subscriptions')
        .select('*')
        .eq('is_active', true)
        .eq('user_id', '43560081-d469-4d4e-85e2-457bda286397') // Your user ID
        .limit(1)

      subscriptions = customerSubs || []
      error = customerError
    } else {
      // For driver tests, try to find any active subscription
      const { data: anySubs, error: anyError } = await supabase
        .from('push_subscriptions')
        .select('*')
        .eq('is_active', true)
        .limit(1)

      subscriptions = anySubs || []
      error = anyError
    }

    if (error || !subscriptions || subscriptions.length === 0) {
      return NextResponse.json(
        { error: "No subscriptions found for testing" },
        { status: 404 }
      )
    }

    const subscription = subscriptions[0]

    // Create test notification payload
    let payload
    if (type === 'driver_order') {
      payload = {
        title: "New Order Available! 🚗",
        body: "£15.50 delivery to St. Helier - Ready for pickup",
        icon: "/android-chrome-192x192.png",
        badge: "/favicon-32x32.png",
        data: {
          type: "new_order",
          orderId: "123",
          url: "/driver-mobile/dashboard"
        },
        actions: [
          {
            action: "accept_order",
            title: "Accept Order"
          },
          {
            action: "decline_order", 
            title: "Decline"
          }
        ]
      }
    } else if (type === 'customer_ready') {
      payload = {
        title: "Order Ready for Pickup! 🍕",
        body: "Your order from Jersey Grill is ready for collection",
        icon: "/android-chrome-192x192.png",
        badge: "/favicon-32x32.png",
        data: {
          type: "order_ready",
          orderId: "456",
          url: "/account/orders"
        }
      }
    } else if (type === 'customer_test') {
      payload = {
        title: "Test Notification from Loop Jersey! 🎉",
        body: "This is a test notification to verify your push notifications are working correctly.",
        icon: "/android-chrome-192x192.png",
        badge: "/favicon-32x32.png",
        data: {
          type: "test_notification",
          url: "/account/settings"
        }
      }
    }

    // Send notification via Supabase Edge Function
    const { data, error: notificationError } = await supabase.functions.invoke(
      'send-push-notification',
      {
        body: {
          userId: subscription.user_id,
          title: payload.title,
          body: payload.body,
          data: payload.data,
          icon: payload.icon,
          badge: payload.badge,
          actions: payload.actions
        }
      }
    )

    if (notificationError) {
      console.error('Error sending notification:', notificationError)
      return NextResponse.json(
        { error: "Failed to send notification", details: notificationError },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Test notification sent successfully",
      payload,
      result: data
    })

  } catch (error) {
    console.error('Error in test notification API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
