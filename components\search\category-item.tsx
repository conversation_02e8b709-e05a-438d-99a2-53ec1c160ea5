"use client"

import React from 'react'

interface CategoryItemProps {
  icon: string
  label: string
  onClick?: () => void
  active?: boolean
}

export default function CategoryItem({ icon, label, onClick, active = false }: CategoryItemProps) {
  return (
    <button
      onClick={onClick}
      className={`flex flex-col items-center justify-center min-w-[90px] max-w-[130px] rounded-xl transition-all ${
        active
          ? 'bg-emerald-50 border border-emerald-200 shadow-md transform scale-105 p-3 pt-6 mt-2'
          : 'bg-white border border-gray-100 hover:bg-gray-50 hover:shadow-sm p-3'
      }`}
      style={{
        scrollSnapAlign: 'center'
      }}
    >
      <div className={`text-2xl mb-2 ${active ? 'scale-110 transform' : ''}`}>{icon}</div>
      <span className={`text-xs font-medium text-center w-full overflow-hidden text-ellipsis whitespace-normal ${
        active ? 'text-emerald-700 font-semibold' : 'text-gray-700'
      }`}>{label}</span>
    </button>
  )
}
