import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

// Function to log detailed information for debugging
function logDebug(message: string, data?: any) {
  console.log(`[SETTINGS-DATA-API] ${message}`, data ? data : '');
}

// Function to log errors
function logError(message: string, error?: any) {
  console.error(`[SETTINGS-DATA-API ERROR] ${message}`, error ? error : '');
}

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    logDebug("Starting business-admin settings-data API request");

    // Get the URL parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');
    logDebug("Request parameters", { businessId });

    // Get the authorization header
    const authHeader = request.headers.get('Authorization');
    logDebug("Authorization header", {
      present: !!authHeader,
      length: authHeader ? authHeader.length : 0,
      start: authHeader ? authHeader.substring(0, 15) + '...' : 'none'
    });

    // Create a client with the user's session
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    logDebug("Created server component client");

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    logDebug("Auth check result", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      logDebug("Using email from cookie", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      logError("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    logDebug("Authentication found for user", { userEmail });

    // Use the admin client to bypass RLS and check the user's role
    logDebug("Fetching user profile from database");
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", userEmail)
      .single();

    if (profileError) {
      logError("Error fetching user profile", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      logError("User profile not found for email", { email: userEmail });
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    logDebug("Found user profile", {
      name: userProfile.name,
      role: userProfile.role,
      id: userProfile.id
    });

    // Check if the user has appropriate role
    const allowedRoles = ['business_staff', 'business_manager', 'admin', 'super_admin'];
    logDebug("Checking user role against allowed roles", {
      userRole: userProfile.role,
      allowedRoles,
      isAllowed: allowedRoles.includes(userProfile.role)
    });

    if (!allowedRoles.includes(userProfile.role)) {
      logError("Unauthorized access attempt", {
        email: userEmail,
        role: userProfile.role,
        allowedRoles
      });

      // No special cases - enforce consistent permissions
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      );
    }

    // For admin users, return a default business
    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      logDebug("Admin user detected, returning default admin view", {
        role: userProfile.role,
        businessId: businessId
      });

      // If a specific business ID was requested, try to get that business
      if (businessId) {
        logDebug("Admin user requested specific business", { businessId });

        try {
          const { data: business, error: businessError } = await adminClient
            .from("businesses")
            .select("*, business_types(name)")
            .eq("id", businessId)
            .single();

          if (businessError) {
            logError("Error fetching specific business for admin", {
              businessId,
              error: businessError
            });
          } else if (business) {
            logDebug("Found requested business for admin", business);

            // Fetch business managers
            const { data: managers, error: managersError } = await adminClient
              .from("business_managers")
              .select("*, users(id, name, email, role)")
              .eq("business_id", business.id);

            if (managersError) {
              logError("Error fetching business managers:", managersError);
              // Continue anyway, this is not critical
            }

            // Fetch business staff
            const { data: staff, error: staffError } = await adminClient
              .from("business_staff")
              .select("*, users(id, name, email, role)")
              .eq("business_id", business.id);

            if (staffError) {
              logError("Error fetching business staff:", staffError);
              // Continue anyway, this is not critical
            }

            return NextResponse.json({
              business: {
                ...business,
                business_type: business.business_types?.name || "Business"
              },
              managers: managers || [],
              staff: staff || []
            });
          }
        } catch (err) {
          logError("Exception fetching specific business for admin", err);
        }
      }

      // Default admin view if no specific business was found
      logDebug("Returning default admin view");
      return NextResponse.json({
        business: {
          id: 0,
          name: "Admin View",
          business_type_id: 1,
          business_type: "Admin",
          logo_url: null,
          banner_url: null,
          description: "",
          address: "",
          postcode: "",
          phone: "",
          delivery_radius: 5,
          preparation_time_minutes: 15,
          minimum_order_amount: 15.00,
          delivery_fee: 2.50,
          delivery_fee_model: "fixed",
          delivery_fee_per_km: 0.50,
          hygiene_rating: "",
          allergens_info: "",
          attributes: [],
          opening_hours: {
            monday: { open: "09:00", close: "17:00" },
            tuesday: { open: "09:00", close: "17:00" },
            wednesday: { open: "09:00", close: "17:00" },
            thursday: { open: "09:00", close: "17:00" },
            friday: { open: "09:00", close: "17:00" },
            saturday: { open: "10:00", close: "16:00" },
            sunday: { open: "11:00", close: "15:00" }
          }
        },
        managers: [],
        staff: []
      });
    }

    // For other business users, get their business data
    // First try to get the business manager relationship
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    if (managerError) {
      logError("Error fetching business manager data:", managerError);

      // Try to find any approved business directly
      const { data: directBusinesses, error: directError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("is_approved", true)
        .limit(20);

      if (directError || !directBusinesses || directBusinesses.length === 0) {
        logError("Error fetching businesses directly:", directError);

        // Check for pending business registrations
        const { data: pendingBusiness, error: pendingError } = await adminClient
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (!pendingError && pendingBusiness) {
          logDebug("Found pending business registration:", pendingBusiness);
          return NextResponse.json({
            isPendingApproval: true,
            pendingBusiness
          });
        }

        return NextResponse.json({
          error: "No business found for this user",
          errorDetails: managerError
        }, { status: 404 });
      }

      // Find an approved business
      const approvedBusiness = directBusinesses.find(b => b.is_approved === true);
      if (approvedBusiness) {
        logDebug("Found approved business:", approvedBusiness);

        // Fetch business managers
        const { data: managers, error: managersError } = await adminClient
          .from("business_managers")
          .select("*, users(id, name, email, role)")
          .eq("business_id", approvedBusiness.id);

        if (managersError) {
          logError("Error fetching business managers:", managersError);
          // Continue anyway, this is not critical
        }

        // Fetch business staff
        const { data: staff, error: staffError } = await adminClient
          .from("business_staff")
          .select("*, users(id, name, email, role)")
          .eq("business_id", approvedBusiness.id);

        if (staffError) {
          logError("Error fetching business staff:", staffError);
          // Continue anyway, this is not critical
        }

        return NextResponse.json({
          business: {
            ...approvedBusiness,
            business_type: approvedBusiness.business_types?.name || "Business"
          },
          managers: managers || [],
          staff: staff || []
        });
      }
    }

    // If we found a manager relationship, get the business details
    if (managerData && managerData.business_id) {
      logDebug("Fetching business details for business_id:", managerData.business_id);

      const { data: business, error: businessError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("id", managerData.business_id)
        .single();

      if (businessError || !business) {
        logError("Error fetching business details:", businessError);
        return NextResponse.json({
          error: "Failed to fetch business details",
          errorDetails: businessError
        }, { status: 500 });
      }

      // Check if the business is approved
      if (business.is_approved === false) {
        logDebug("Business exists but is not approved:", business);
        return NextResponse.json({
          isPendingApproval: true,
          business
        });
      }

      // Fetch business managers
      const { data: managers, error: managersError } = await adminClient
        .from("business_managers")
        .select("*, users(id, name, email, role)")
        .eq("business_id", managerData.business_id);

      if (managersError) {
        logError("Error fetching business managers:", managersError);
        // Continue anyway, this is not critical
      }

      // Fetch business staff
      const { data: staff, error: staffError } = await adminClient
        .from("business_staff")
        .select("*, users(id, name, email, role)")
        .eq("business_id", managerData.business_id);

      if (staffError) {
        logError("Error fetching business staff:", staffError);
        // Continue anyway, this is not critical
      }

      // Get the business manager approval status
      const { data: managerApprovalData, error: managerApprovalError } = await adminClient
        .from("business_managers")
        .select("is_approved, created_at, updated_at")
        .eq("business_id", managerData.business_id)
        .eq("user_id", userProfile.id)
        .single();

      if (managerApprovalError) {
        logError("Error fetching business manager approval status:", managerApprovalError);
        // Continue anyway, this is not critical
      }

      // Format dates for display
      const formatDate = (dateString: string | null) => {
        if (!dateString) return null;
        return new Date(dateString).toLocaleString('en-GB', {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      };

      // Prepare approval status information
      const approvalStatus = {
        business_is_approved: business.is_approved,
        manager_is_approved: managerApprovalData?.is_approved,
        business_created_at: formatDate(business.created_at),
        business_updated_at: formatDate(business.updated_at),
        business_approval_date: business.is_approved ? formatDate(business.updated_at) : null,
        manager_created_at: formatDate(managerApprovalData?.created_at),
        manager_updated_at: formatDate(managerApprovalData?.updated_at),
        manager_approval_date: managerApprovalData?.is_approved ? formatDate(managerApprovalData?.updated_at) : null,
      };

      // Return the business data, managers, staff, and approval status
      return NextResponse.json({
        business: business,
        managers: managers || [],
        staff: staff || [],
        approvalStatus
      });
    }

    // If we get here, no business was found
    logError("No business found for this user", {
      userId: userProfile.id,
      userEmail: userEmail,
      userRole: userProfile.role
    });

    // Return a detailed error response
    return NextResponse.json({
      error: "No business found for this user",
      details: {
        userId: userProfile.id,
        userEmail: userEmail,
        userRole: userProfile.role
      }
    }, { status: 404 });
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/settings-data:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// PATCH handler to update business settings
export async function PATCH(request: Request) {
  try {
    logDebug("Starting business-admin settings-data PATCH request");

    // Get the URL parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');
    logDebug("Request parameters", { businessId });

    // Get the authorization header
    const authHeader = request.headers.get('Authorization');
    logDebug("Authorization header", {
      present: !!authHeader,
      length: authHeader ? authHeader.length : 0,
      start: authHeader ? authHeader.substring(0, 15) + '...' : 'none'
    });

    // Create a client with the user's session
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    logDebug("Created server component client");

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    logDebug("Auth check result", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      logDebug("Using email from cookie", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      logError("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    logDebug("Authentication found for user", { userEmail });

    // Use the admin client to bypass RLS and check the user's role
    logDebug("Fetching user profile from database");
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", userEmail)
      .single();

    if (profileError) {
      logError("Error fetching user profile", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      logError("User profile not found for email", { email: userEmail });
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    logDebug("Found user profile", {
      name: userProfile.name,
      role: userProfile.role,
      id: userProfile.id
    });

    // Check if the user has appropriate role
    const allowedRoles = ['business_manager', 'admin', 'super_admin']; // Note: business_staff can't update settings
    logDebug("Checking user role against allowed roles", {
      userRole: userProfile.role,
      allowedRoles,
      isAllowed: allowedRoles.includes(userProfile.role)
    });

    if (!allowedRoles.includes(userProfile.role)) {
      logError("Unauthorized access attempt", {
        email: userEmail,
        role: userProfile.role,
        allowedRoles
      });

      return NextResponse.json(
        { error: "You do not have permission to update business settings" },
        { status: 403 }
      );
    }

    // For admin users with a specific business ID, use that instead of looking up manager relationship
    let targetBusinessId = businessId;

    // If admin user and a specific business ID was provided, use that
    if ((userProfile.role === 'admin' || userProfile.role === 'super_admin') && businessId) {
      logDebug("Admin user updating specific business", { businessId });
      targetBusinessId = businessId;
    } else {
      // For business managers or admins without a specific business ID, get the business managed by this user
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .maybeSingle();

      if (managerError) {
        logError("Error fetching business manager data:", managerError);
        return NextResponse.json(
          { error: "Business manager data not found" },
          { status: 404 }
        );
      }

      if (!managerData || !managerData.business_id) {
        // For admin users without a specific business ID and no manager relationship, return an error
        logError("No business ID found for this user", {
          role: userProfile.role,
          userId: userProfile.id
        });
        return NextResponse.json(
          { error: "No business found for this user. Admin users must specify a businessId parameter." },
          { status: 404 }
        );
      }

      targetBusinessId = managerData.business_id;
    }

    // Verify the business exists
    if (!targetBusinessId) {
      logError("No target business ID determined");
      return NextResponse.json(
        { error: "No target business ID determined" },
        { status: 400 }
      );
    }

    let updatedBusiness = null;

    try {
      // Parse the request body
      let formData;
      try {
        formData = await request.json();
      } catch (parseError) {
        logError("Error parsing request JSON:", parseError);
        return NextResponse.json(
          { error: "Invalid JSON in request body" },
          { status: 400 }
        );
      }

      logDebug("Received form data:", {
        businessId: targetBusinessId,
        formDataKeys: Object.keys(formData),
        coordinates: formData.coordinates,
        hygiene_rating: formData.hygiene_rating,
        opening_hours: typeof formData.opening_hours
      })

      // Prepare update data with type checking
      const updateData: any = {
        name: formData.name || "",
        description: formData.description || "",
        address: formData.address || "",
        postcode: formData.postcode || "",
        location: formData.location || "",
        phone: formData.phone || "",
        delivery_radius: typeof formData.delivery_radius === 'number' ? formData.delivery_radius : 5,
        preparation_time_minutes: typeof formData.preparation_time_minutes === 'number' ? formData.preparation_time_minutes : 15,
        minimum_order_amount: typeof formData.minimum_order_amount === 'number' ? formData.minimum_order_amount : 15.00,
        delivery_fee: typeof formData.delivery_fee === 'number' ? formData.delivery_fee : 2.50,
        delivery_fee_model: formData.delivery_fee_model || "fixed",
        // PHASE 3 STEP 6: Add delivery fulfillment and granular delivery options
        use_loop_delivery: typeof formData.use_loop_delivery === 'boolean' ? formData.use_loop_delivery : true,
        pickup_available: typeof formData.pickup_available === 'boolean' ? formData.pickup_available : true,
        pickup_asap_available: typeof formData.pickup_asap_available === 'boolean' ? formData.pickup_asap_available : true,
        pickup_scheduled_time_available: typeof formData.pickup_scheduled_time_available === 'boolean' ? formData.pickup_scheduled_time_available : false,
        pickup_scheduled_period_available: typeof formData.pickup_scheduled_period_available === 'boolean' ? formData.pickup_scheduled_period_available : false,
        delivery_asap_available: typeof formData.delivery_asap_available === 'boolean' ? formData.delivery_asap_available : false,
        delivery_scheduled_time_available: typeof formData.delivery_scheduled_time_available === 'boolean' ? formData.delivery_scheduled_time_available : false,
        delivery_scheduled_period_available: typeof formData.delivery_scheduled_period_available === 'boolean' ? formData.delivery_scheduled_period_available : false,
        min_advance_booking_minutes: typeof formData.min_advance_booking_minutes === 'number' ? formData.min_advance_booking_minutes : 30,
        max_advance_booking_days: typeof formData.max_advance_booking_days === 'number' ? formData.max_advance_booking_days : 2,
        delivery_fee_per_km: typeof formData.delivery_fee_per_km === 'number' ? formData.delivery_fee_per_km : 0.50,
        logo_url: formData.logo_url || null,
        banner_url: formData.banner_url || null,
        updated_at: new Date().toISOString()
      }

      // Add business_type_id if provided
      if (formData.business_type_id) {
        updateData.business_type_id = parseInt(formData.business_type_id);
      }

      // Only add these fields if they exist to prevent null/undefined errors
      if (formData.coordinates) updateData.coordinates = formData.coordinates
      if (formData.hygiene_rating !== undefined) updateData.hygiene_rating = formData.hygiene_rating

      // Handle allergens_info - use allergen_info as the database field name
      if (formData.allergens_info !== undefined) {
        updateData.allergen_info = formData.allergens_info;
        logDebug("Setting allergen_info field:", formData.allergens_info);
      }

      if (formData.opening_hours) updateData.opening_hours = formData.opening_hours

      logDebug("Updating business with ID:", targetBusinessId)
      logDebug("Update data prepared:", updateData)

      // Update the business
      const { data, error: updateError } = await adminClient
        .from("businesses")
        .update(updateData)
        .eq("id", targetBusinessId)
        .select()
        .single()

      if (updateError) {
        logError("Error updating business:", updateError)
        logError("Error details:", JSON.stringify(updateError, null, 2))
        return NextResponse.json(
          { error: `Failed to update business settings: ${updateError.message}`, details: updateError },
          { status: 500 }
        )
      }

      updatedBusiness = data;
      logDebug("Business updated successfully:", updatedBusiness?.id)

      // Handle business attributes if provided
      if (formData.businessAttributes && typeof formData.businessAttributes === 'object') {
        try {
          logDebug("Updating business attributes:", formData.businessAttributes)

          // First, delete existing attributes for this business
          const { error: deleteError } = await adminClient
            .from('business_attributes')
            .delete()
            .eq('business_id', targetBusinessId)

          if (deleteError) {
            logError("Error deleting existing business attributes:", deleteError)
            // Continue anyway - this might not be critical
          } else {
            logDebug("Existing business attributes deleted successfully")
          }

          // Insert new attributes
          const attributesToInsert: any[] = []
          Object.entries(formData.businessAttributes).forEach(([attributeType, values]) => {
            if (Array.isArray(values)) {
              values.forEach(value => {
                if (value && value.trim()) {
                  attributesToInsert.push({
                    business_id: targetBusinessId,
                    attribute_type: attributeType,
                    attribute_value: value.trim()
                  })
                }
              })
            }
          })

          logDebug("Attributes to insert:", attributesToInsert)

          if (attributesToInsert.length > 0) {
            const { error: insertError } = await adminClient
              .from('business_attributes')
              .insert(attributesToInsert)

            if (insertError) {
              logError("Error inserting business attributes:", insertError)
              // Don't throw here, just log the error and continue
              // The main business update was successful
            } else {
              logDebug("Business attributes inserted successfully:", attributesToInsert)
            }
          } else {
            logDebug("No business attributes to insert")
          }
        } catch (attributeError) {
          logError("Error handling business attributes:", attributeError)
          // Don't throw here - the main business update was successful
        }
      } else {
        logDebug("No business attributes provided or invalid format")
      }

      // Handle business categories if provided
      if (formData.selectedCategories && Array.isArray(formData.selectedCategories)) {
        try {
          logDebug("Updating business categories:", formData.selectedCategories)

          // First, delete existing categories for this business
          const { error: deleteError } = await adminClient
            .from('business_categories')
            .delete()
            .eq('business_id', targetBusinessId)

          if (deleteError) {
            logError("Error deleting existing business categories:", deleteError)
            // Continue anyway - this might not be critical
          } else {
            logDebug("Existing business categories deleted successfully")
          }

          // Insert new categories
          if (formData.selectedCategories.length > 0) {
            const categoriesToInsert = formData.selectedCategories.map((categoryId: number) => ({
              business_id: targetBusinessId,
              category_id: categoryId,
              is_primary: false // You could make the first one primary if needed
            }))

            const { error: insertError } = await adminClient
              .from('business_categories')
              .insert(categoriesToInsert)

            if (insertError) {
              logError("Error inserting business categories:", insertError)
              // Don't throw here, just log the error and continue
            } else {
              logDebug("Business categories inserted successfully:", categoriesToInsert)
            }
          } else {
            logDebug("No business categories to insert")
          }
        } catch (categoryError) {
          logError("Error handling business categories:", categoryError)
          // Don't throw here - the main business update was successful
        }
      } else {
        logDebug("No business categories provided or invalid format")
      }

    } catch (innerError: any) {
      logError("Exception in update process:", innerError)
      logError("Inner error details:", {
        message: innerError.message,
        stack: innerError.stack,
        name: innerError.name
      })
      return NextResponse.json(
        {
          error: `Exception during update: ${innerError.message || 'Unknown error'}`,
          details: {
            timestamp: new Date().toISOString(),
            errorType: innerError.name || 'UnknownError'
          }
        },
        { status: 500 }
      )
    }

    // Ensure we have a business object to return
    if (!updatedBusiness) {
      logError("No updated business data available")
      return NextResponse.json(
        { error: "Business update completed but no data returned" },
        { status: 500 }
      )
    }

    logDebug("Returning successful response with business:", updatedBusiness.id)
    return NextResponse.json({
      message: "Business settings updated successfully",
      business: updatedBusiness
    })
  } catch (error: any) {
    // Ensure we have a valid error message
    const errorMessage = error.message || "An unexpected error occurred";
    const errorStack = error.stack || "No stack trace available";

    logError("Unhandled exception in settings-data PATCH API", {
      message: errorMessage,
      stack: errorStack
    });

    // Return a detailed error response
    return NextResponse.json(
      {
        error: errorMessage,
        details: {
          timestamp: new Date().toISOString(),
          path: "/api/business-admin/settings-data",
          type: error.name || "UnknownError"
        }
      },
      { status: 500 }
    );
  }
}
