"use client"

import { useTheme } from "next-themes"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

// Sample data - in a real implementation, this would come from the API
const data = [
  { time: "6 AM", orders: 2 },
  { time: "7 AM", orders: 3 },
  { time: "8 AM", orders: 5 },
  { time: "9 AM", orders: 8 },
  { time: "10 AM", orders: 12 },
  { time: "11 AM", orders: 18 },
  { time: "12 PM", orders: 24 },
  { time: "1 PM", orders: 28 },
  { time: "2 PM", orders: 22 },
  { time: "3 PM", orders: 16 },
  { time: "4 PM", orders: 14 },
  { time: "5 PM", orders: 18 },
  { time: "6 PM", orders: 24 },
  { time: "7 PM", orders: 30 },
  { time: "8 PM", orders: 26 },
  { time: "9 PM", orders: 20 },
  { time: "10 PM", orders: 12 },
  { time: "11 PM", orders: 6 },
]

interface OrdersByTimeChartProps {
  data?: any[]
  isLoading?: boolean
}

export function OrdersByTimeChart({ data: propData, isLoading = false }: OrdersByTimeChartProps) {
  const { theme } = useTheme()
  const chartData = propData || data

  if (isLoading) {
    return (
      <div className="h-[300px] w-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-4 w-24 bg-gray-200 rounded mb-2.5"></div>
          <div className="h-32 w-full bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={chartData}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis
            dataKey="time"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 10 }}
            interval={2}
          />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            }}
          />
          <Area
            type="monotone"
            dataKey="orders"
            stroke="#8b5cf6"
            fill="#c4b5fd"
            fillOpacity={0.5}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
