"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Mail, CheckCircle, AlertCircle, Loader2 } from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"

export default function ConfirmEmailPage() {
  const router = useRouter()
  const { user, session } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [emailConfirmed, setEmailConfirmed] = useState(false)

  useEffect(() => {
    // Check if user is logged in and email is confirmed
    if (session && user) {
      setEmailConfirmed(user.email_confirmed_at !== null)
      setIsLoading(false)
    } else if (session === null) {
      // No session, redirect to login
      router.push('/login')
    }
  }, [session, user, router])

  const handleResendConfirmation = async () => {
    // TODO: Implement resend confirmation email
    console.log("Resend confirmation email")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            {emailConfirmed ? (
              <CheckCircle className="w-8 h-8 text-green-600" />
            ) : (
              <Mail className="w-8 h-8 text-blue-600" />
            )}
          </div>
          <CardTitle>
            {emailConfirmed ? "Email Confirmed!" : "Confirm Your Email"}
          </CardTitle>
          <CardDescription>
            {emailConfirmed 
              ? "Your email address has been verified successfully."
              : "Please check your email and click the confirmation link."
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {emailConfirmed ? (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Your email <strong>{user?.email}</strong> has been confirmed. 
                You now have full access to all features.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="bg-blue-50 border-blue-200">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                We've sent a confirmation email to <strong>{user?.email}</strong>. 
                Please check your inbox (and spam folder) and click the confirmation link.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            {emailConfirmed ? (
              <Button
                onClick={() => router.push('/account/profile')}
                className="w-full bg-emerald-600 hover:bg-emerald-700"
              >
                Go to Profile
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleResendConfirmation}
                  variant="outline"
                  className="w-full"
                >
                  Resend Confirmation Email
                </Button>
                <Button
                  onClick={() => router.push('/')}
                  className="w-full bg-emerald-600 hover:bg-emerald-700"
                >
                  Continue Browsing
                </Button>
              </>
            )}
          </div>

          <div className="text-center text-sm text-gray-500">
            <p>
              Need help? Contact us at{" "}
              <a href="mailto:<EMAIL>" className="text-emerald-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
