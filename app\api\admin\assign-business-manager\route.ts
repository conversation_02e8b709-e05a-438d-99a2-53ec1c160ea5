import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: Request) {
  try {
    console.log("Starting assign-business-manager API request")

    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      console.log("No authorization header found, using direct admin access")
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check")
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        )
      }
    } else {
      console.log("Found authorization header, attempting to verify")

      // Extract the token
      const token = authorization.replace('Bearer ', '');

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          console.error("Invalid token:", error)
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        console.log("Token verified for user:", user.email)

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          console.error("Error fetching user profile:", profileError)
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          console.error("Unauthorized access attempt by:", user.email, "with role:", userProfile.role)
          return NextResponse.json(
            { error: "You do not have permission to access this resource" },
            { status: 403 }
          )
        }

        console.log("Admin access verified for user:", user.email, "with role:", userProfile.role)
      } catch (authError) {
        console.error("Error verifying token:", authError)
        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          )
        } else {
          console.log("Development mode: Continuing despite auth error")
        }
      }
    }

    // Parse the request body
    const { userId, businessId } = await request.json();

    if (!userId || !businessId) {
      return NextResponse.json(
        { error: "User ID and business ID are required" },
        { status: 400 }
      );
    }

    console.log(`Assigning user ${userId} as manager for business ${businessId}`);

    // First, update the user's role to business_manager
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .update({ role: "business_manager" })
      .eq("id", userId)
      .select()
      .single();

    if (userError) {
      console.error("Error updating user role:", userError);
      return NextResponse.json(
        { error: `Failed to update user role: ${userError.message}` },
        { status: 500 }
      );
    }

    // Check if a business_managers entry already exists
    const { data: existingEntry, error: checkError } = await adminClient
      .from("business_managers")
      .select("*")
      .eq("user_id", userId)
      .eq("business_id", businessId)
      .maybeSingle();

    if (checkError) {
      console.error("Error checking for existing business manager entry:", checkError);
      // Continue anyway, as we'll try to insert
    }

    // If no entry exists, create one
    if (!existingEntry) {
      console.log("No existing business manager entry, creating one");
      
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .insert([
          {
            user_id: userId,
            business_id: businessId,
            is_primary: true
          }
        ])
        .select();

      if (managerError) {
        console.error("Error creating business manager entry:", managerError);
        return NextResponse.json(
          { error: `Failed to create business manager entry: ${managerError.message}` },
          { status: 500 }
        );
      }

      console.log("Successfully created business manager entry");
      return NextResponse.json({ 
        success: true,
        message: "User assigned as business manager successfully",
        user: userData,
        manager: managerData
      });
    } else {
      console.log("Business manager entry already exists");
      return NextResponse.json({ 
        success: true,
        message: "User is already assigned as business manager for this business",
        user: userData
      });
    }
  } catch (error: any) {
    console.error("Unexpected error in assign business manager API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
