"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Car,
  Truck,
  Bike,
  Package,
  Clock,
  MapPin,
  Star,
  CheckCircle,
  AlertCircle,
  Info,
  Settings,
  User,
  Shield
} from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"
import { VEHICLE_CAPACITIES, DELIVERY_TYPES, STORE_TYPES, EQUIPMENT_TYPES, getVehicleCapacity, getRecommendedEquipment } from "@/lib/vehicle-capacity"

interface DriverProfile {
  id: string
  user_id: number
  vehicle_type: string
  vehicle_make?: string
  vehicle_model?: string
  vehicle_year?: number
  vehicle_color?: string
  cargo_capacity_category?: string
  max_weight_kg?: number
  has_thermal_bag: boolean
  has_cooler_bag: boolean
  has_large_bag: boolean
  delivery_types: string[]
  store_types: string[]
  equipment_notes?: string
  license_number?: string
  insurance_number?: string
  vehicle_registration?: string
  is_verified: boolean
  is_active: boolean
  average_rating?: number
  total_deliveries: number
  created_at: string
}

export default function DriverProfilePage() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<DriverProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    fetchDriverProfile()
  }, [])

  const fetchDriverProfile = async () => {
    try {
      const response = await fetch('/api/driver/profile')
      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
      }
    } catch (error) {
      console.error('Error fetching driver profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<DriverProfile>) => {
    setSaving(true)
    try {
      const response = await fetch('/api/driver/profile', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })

      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
      }
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleEquipmentChange = (equipmentType: string, checked: boolean) => {
    const updates: any = {}
    
    switch (equipmentType) {
      case 'thermal_bag':
        updates.has_thermal_bag = checked
        break
      case 'cooler_bag':
        updates.has_cooler_bag = checked
        break
      case 'large_bag':
        updates.has_large_bag = checked
        break
    }
    
    updateProfile(updates)
  }

  const handleStoreTypesChange = (storeTypeId: string, checked: boolean) => {
    if (!profile) return
    
    const currentStoreTypes = profile.store_types || []
    const newStoreTypes = checked
      ? [...currentStoreTypes, storeTypeId]
      : currentStoreTypes.filter(id => id !== storeTypeId)
    
    updateProfile({ store_types: newStoreTypes })
  }

  const handleDeliveryTypesChange = (deliveryTypeId: string, checked: boolean) => {
    if (!profile) return
    
    const currentDeliveryTypes = profile.delivery_types || []
    const newDeliveryTypes = checked
      ? [...currentDeliveryTypes, deliveryTypeId]
      : currentDeliveryTypes.filter(id => id !== deliveryTypeId)
    
    updateProfile({ delivery_types: newDeliveryTypes })
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading your driver profile...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardHeader>
              <CardTitle>No Driver Profile Found</CardTitle>
              <CardDescription>You need to apply as a driver first</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild>
                <a href="/partners/riders/apply">Apply to Become a Driver</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const vehicleCapacity = getVehicleCapacity(profile.vehicle_type)
  const recommendedEquipment = getRecommendedEquipment(profile.store_types || [])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Driver Profile</h1>
              <p className="text-gray-600">Manage your delivery capabilities and preferences</p>
            </div>
            <div className="flex items-center gap-2">
              {profile.is_verified ? (
                <Badge className="bg-green-600">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Badge variant="secondary">
                  <Clock className="h-3 w-3 mr-1" />
                  Pending Verification
                </Badge>
              )}
              {profile.is_active ? (
                <Badge className="bg-blue-600">Active</Badge>
              ) : (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="vehicle">Vehicle & Capacity</TabsTrigger>
            <TabsTrigger value="capabilities">Delivery Capabilities</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Stats Cards */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Deliveries</p>
                      <p className="text-2xl font-bold">{profile.total_deliveries}</p>
                    </div>
                    <Package className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Average Rating</p>
                      <p className="text-2xl font-bold">
                        {profile.average_rating ? profile.average_rating.toFixed(1) : 'N/A'}
                      </p>
                    </div>
                    <Star className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Vehicle Type</p>
                      <p className="text-2xl font-bold capitalize">{profile.vehicle_type}</p>
                    </div>
                    <Car className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" onClick={() => setActiveTab("vehicle")}>
                    <Car className="h-4 w-4 mr-2" />
                    Update Vehicle Details
                  </Button>
                  <Button variant="outline" onClick={() => setActiveTab("capabilities")}>
                    <Package className="h-4 w-4 mr-2" />
                    Manage Delivery Types
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vehicle & Capacity Tab */}
          <TabsContent value="vehicle" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Information</CardTitle>
                <CardDescription>Details about your delivery vehicle</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Vehicle Type</Label>
                    <p className="text-sm text-gray-600 capitalize">{profile.vehicle_type}</p>
                  </div>
                  <div>
                    <Label htmlFor="vehicle_make">Make</Label>
                    <Input
                      id="vehicle_make"
                      value={profile.vehicle_make || ''}
                      onChange={(e) => updateProfile({ vehicle_make: e.target.value })}
                      placeholder="e.g., Toyota, Honda"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle_model">Model</Label>
                    <Input
                      id="vehicle_model"
                      value={profile.vehicle_model || ''}
                      onChange={(e) => updateProfile({ vehicle_model: e.target.value })}
                      placeholder="e.g., Corolla, Civic"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle_year">Year</Label>
                    <Input
                      id="vehicle_year"
                      type="number"
                      value={profile.vehicle_year || ''}
                      onChange={(e) => updateProfile({ vehicle_year: parseInt(e.target.value) || undefined })}
                      placeholder="e.g., 2020"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle_color">Color</Label>
                    <Input
                      id="vehicle_color"
                      value={profile.vehicle_color || ''}
                      onChange={(e) => updateProfile({ vehicle_color: e.target.value })}
                      placeholder="e.g., Red, Blue"
                    />
                  </div>
                </div>

                {vehicleCapacity && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Capacity Guidelines:</strong> {vehicleCapacity.description}
                      <br />
                      <strong>Max Weight:</strong> {vehicleCapacity.maxWeightKg}kg | 
                      <strong> Max Volume:</strong> {vehicleCapacity.maxVolumeL}L
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Delivery Capabilities Tab */}
          <TabsContent value="capabilities" className="space-y-6">
            {/* Equipment */}
            <Card>
              <CardHeader>
                <CardTitle>Delivery Equipment</CardTitle>
                <CardDescription>Equipment you have available for deliveries</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {EQUIPMENT_TYPES.map((equipment) => (
                  <div key={equipment.id} className="flex items-start space-x-3">
                    <Checkbox
                      id={equipment.id}
                      checked={
                        equipment.id === 'thermal_bag' ? profile.has_thermal_bag :
                        equipment.id === 'cooler_bag' ? profile.has_cooler_bag :
                        equipment.id === 'large_bag' ? profile.has_large_bag : false
                      }
                      onCheckedChange={(checked) => handleEquipmentChange(equipment.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <Label htmlFor={equipment.id} className="flex items-center gap-2">
                        <span>{equipment.icon}</span>
                        {equipment.label}
                      </Label>
                      <p className="text-sm text-gray-600">{equipment.description}</p>
                      {equipment.requiredFor.length > 0 && (
                        <p className="text-xs text-blue-600 mt-1">
                          Required for: {equipment.requiredFor.join(', ')}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Store Types */}
            <Card>
              <CardHeader>
                <CardTitle>Store Types You'll Deliver From</CardTitle>
                <CardDescription>Select the types of businesses you want to deliver from</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {STORE_TYPES.map((store) => {
                    const isSelected = profile.store_types?.includes(store.id) || false
                    const hasRequiredEquipment = store.equipment.every(eq => {
                      switch (eq) {
                        case 'thermal_bag': return profile.has_thermal_bag
                        case 'cooler_bag': return profile.has_cooler_bag
                        case 'large_bag': return profile.has_large_bag
                        default: return true
                      }
                    })

                    return (
                      <div key={store.id} className="relative">
                        <div className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        } ${!hasRequiredEquipment ? 'opacity-50' : ''}`}>
                          <Checkbox
                            id={store.id}
                            checked={isSelected}
                            onCheckedChange={(checked) => handleStoreTypesChange(store.id, checked as boolean)}
                            disabled={!hasRequiredEquipment}
                            className="absolute top-2 right-2"
                          />
                          <div className="flex items-center gap-3 mb-2">
                            <span className="text-2xl">{store.icon}</span>
                            <Label htmlFor={store.id} className="font-medium">{store.label}</Label>
                          </div>
                          {store.equipment.length > 0 && (
                            <div className="text-xs text-gray-600">
                              Requires: {store.equipment.map(eq => 
                                EQUIPMENT_TYPES.find(e => e.id === eq)?.label
                              ).join(', ')}
                            </div>
                          )}
                          {!hasRequiredEquipment && (
                            <div className="text-xs text-red-600 mt-1">
                              Missing required equipment
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {recommendedEquipment.length > 0 && (
                  <Alert className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Recommended Equipment:</strong> Based on your selected store types, consider getting: {
                        recommendedEquipment.map(eq => 
                          EQUIPMENT_TYPES.find(e => e.id === eq)?.label
                        ).join(', ')
                      }
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Delivery Types */}
            <Card>
              <CardHeader>
                <CardTitle>Delivery Types</CardTitle>
                <CardDescription>Types of items you're willing to deliver</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {DELIVERY_TYPES.map((delivery) => {
                    const isSelected = profile.delivery_types?.includes(delivery.id) || false
                    
                    return (
                      <div key={delivery.id} className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                      }`}>
                        <Checkbox
                          id={delivery.id}
                          checked={isSelected}
                          onCheckedChange={(checked) => handleDeliveryTypesChange(delivery.id, checked as boolean)}
                          className="absolute top-2 right-2"
                        />
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{delivery.icon}</span>
                          <Label htmlFor={delivery.id} className="font-medium">{delivery.label}</Label>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>Manage your driver profile settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="equipment_notes">Equipment Notes</Label>
                  <Textarea
                    id="equipment_notes"
                    value={profile.equipment_notes || ''}
                    onChange={(e) => updateProfile({ equipment_notes: e.target.value })}
                    placeholder="Additional details about your equipment or delivery capabilities..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
