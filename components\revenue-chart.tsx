"use client"

import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

const data = [
  {
    name: "<PERSON>",
    revenue: 400,
  },
  {
    name: "<PERSON><PERSON>",
    revenue: 300,
  },
  {
    name: "Wed",
    revenue: 500,
  },
  {
    name: "Thu",
    revenue: 580,
  },
  {
    name: "<PERSON><PERSON>",
    revenue: 800,
  },
  {
    name: "Sat",
    revenue: 1000,
  },
  {
    name: "Sun",
    revenue: 700,
  },
]

export function RevenueChart() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis tickFormatter={(value) => `£${value}`} />
        <Tooltip formatter={(value) => [`£${value}`, "Revenue"]} />
        <Area type="monotone" dataKey="revenue" stroke="#10b981" fill="#10b98133" />
      </AreaChart>
    </ResponsiveContainer>
  )
}
