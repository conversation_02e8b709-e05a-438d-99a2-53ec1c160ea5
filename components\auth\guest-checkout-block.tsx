"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Lock, Mail, LogIn, UserPlus, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
// import { signInWithEmailPassword } from "@/services/auth-service-direct"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/context/unified-auth-context"

export default function GuestCheckoutBlock() {
  const router = useRouter()
  const { user, signIn } = useAuth()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("guest")

  const handleLogin = async () => {
    setError(null)
    setIsLoading(true)

    try {
      console.log("Checkout login: Attempting to sign in with email:", email);

      // Use the unified auth context for sign in - same as the login page
      const { data, error } = await signIn(email, password)

      if (error) {
        console.error("Checkout login: Login error:", error);
        setError(error.message)
        setIsLoading(false)
        return
      }

      console.log("Checkout login: Login successful, session established:", data?.session ? "Yes" : "No");

      if (!data?.session) {
        console.error("Checkout login: No session returned after successful login");
        setError("Authentication succeeded but no session was created. Please try again.")
        setIsLoading(false)
        return
      }

      // Dispatch an event to notify the app that a user has logged in
      window.dispatchEvent(new CustomEvent('auth-login-success', {
        detail: { user: data.session.user }
      }));

      console.log("Checkout login: Login successful, will reload page");

      // Add a small delay before reloading to allow event listeners to process
      setTimeout(() => {
        // Force a full page reload to ensure the auth state is properly updated
        window.location.href = window.location.pathname;
      }, 500); // Use a longer delay like the login page
    } catch (err) {
      console.error("Checkout login error:", err)
      setError("An unexpected error occurred. Please try again.")
      setIsLoading(false)
    }
  }

  // If user is logged in, don't render the guest checkout block
  if (user) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg p-4 border border-gray-100">
      <Tabs defaultValue="guest" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="guest" className="text-sm font-medium border-2 border-gray-300 data-[state=active]:border-emerald-600">Continue as Guest</TabsTrigger>
          <TabsTrigger value="login" className="text-sm font-medium border-2 border-gray-300 data-[state=active]:border-emerald-600">Login / Register</TabsTrigger>
        </TabsList>

        <TabsContent value="guest" className="space-y-4">
          <div className="p-4 bg-emerald-50 rounded-lg border border-emerald-100">
            <h3 className="font-medium text-emerald-800 mb-2 flex items-center">
              <ArrowRight className="h-4 w-4 mr-2" />
              Checkout as Guest
            </h3>
            <p className="text-sm text-emerald-700 mb-3">
              Complete your purchase without creating an account. Just fill in your details below.
            </p>
            <p className="text-xs text-emerald-600">
              You'll have the option to create an account after your order is placed.
            </p>
          </div>
        </TabsContent>

        <TabsContent value="login" className="space-y-4">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="/reset-password" className="text-xs font-medium text-emerald-600 hover:text-emerald-700">
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <Button
              onClick={handleLogin}
              className="w-full bg-emerald-600 hover:bg-emerald-700"
              disabled={isLoading}
            >
              <LogIn className="mr-2 h-4 w-4" />
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-3">Don&apos;t have an account yet?</p>
            <Link href={`/register?redirectTo=${encodeURIComponent('/checkout')}`}>
              <Button variant="outline" className="w-full">
                <UserPlus className="mr-2 h-4 w-4" />
                Create an Account
              </Button>
            </Link>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
