import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify business admin access
async function verifyBusinessAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user has business admin access
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role, business_id')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (!['business_manager', 'business_staff', 'admin', 'super_admin'].includes(profile.role)) {
      return { authorized: false, error: 'Business admin access required', status: 403 }
    }

    if (!profile.business_id) {
      return { authorized: false, error: 'No business associated with user', status: 403 }
    }

    return { authorized: true, user, profile, businessId: profile.business_id }
  } catch (error) {
    console.error('Error verifying business admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// GET - Fetch business category subscriptions
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const businessId = accessCheck.businessId

    // Get all available categories
    const { data: allCategories, error: categoriesError } = await adminClient
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        business_type_id,
        business_types!business_type_id (
          name
        ),
        category_purpose,
        display_order
      `)
      .eq('is_active', true)
      .order('business_type_id', { ascending: true })
      .order('display_order', { ascending: true })

    if (categoriesError) {
      console.error('Error fetching categories:', categoriesError)
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      )
    }

    // Get business's current subscriptions
    const { data: subscriptions, error: subscriptionsError } = await adminClient
      .from('business_categories')
      .select('category_id, is_primary')
      .eq('business_id', businessId)

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError)
      return NextResponse.json(
        { error: 'Failed to fetch subscriptions' },
        { status: 500 }
      )
    }

    // Create a map of subscribed categories
    const subscriptionMap = new Map()
    subscriptions?.forEach(sub => {
      subscriptionMap.set(sub.category_id, sub.is_primary)
    })

    // Transform categories with subscription status
    const categoriesWithSubscription = allCategories.map(category => ({
      ...category,
      business_type_name: category.business_types?.name || null,
      business_types: undefined, // Remove nested object
      is_subscribed: subscriptionMap.has(category.id),
      is_primary: subscriptionMap.get(category.id) || false
    }))

    return NextResponse.json(categoriesWithSubscription)
  } catch (error) {
    console.error('Error in GET /api/business-admin/category-subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Subscribe to a category
export async function POST(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const businessId = accessCheck.businessId
    const body = await request.json()
    const { category_id, is_primary = false } = body

    if (!category_id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // Check if category exists and is active
    const { data: category, error: categoryError } = await adminClient
      .from('categories')
      .select('id, name')
      .eq('id', category_id)
      .eq('is_active', true)
      .single()

    if (categoryError || !category) {
      return NextResponse.json(
        { error: 'Category not found or inactive' },
        { status: 404 }
      )
    }

    // Check if already subscribed
    const { data: existingSubscription } = await adminClient
      .from('business_categories')
      .select('category_id')
      .eq('business_id', businessId)
      .eq('category_id', category_id)
      .single()

    if (existingSubscription) {
      return NextResponse.json(
        { error: 'Already subscribed to this category' },
        { status: 400 }
      )
    }

    // If setting as primary, remove primary status from other categories
    if (is_primary) {
      await adminClient
        .from('business_categories')
        .update({ is_primary: false })
        .eq('business_id', businessId)
    }

    // Create subscription
    const { error: insertError } = await adminClient
      .from('business_categories')
      .insert({
        business_id: businessId,
        category_id: category_id,
        is_primary: is_primary
      })

    if (insertError) {
      console.error('Error creating subscription:', insertError)
      return NextResponse.json(
        { error: 'Failed to subscribe to category' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'Successfully subscribed to category',
      category_name: category.name
    })
  } catch (error) {
    console.error('Error in POST /api/business-admin/category-subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Unsubscribe from a category
export async function DELETE(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const businessId = accessCheck.businessId
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('category_id')

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // Check if subscribed
    const { data: subscription, error: subscriptionError } = await adminClient
      .from('business_categories')
      .select('category_id')
      .eq('business_id', businessId)
      .eq('category_id', parseInt(categoryId))
      .single()

    if (subscriptionError || !subscription) {
      return NextResponse.json(
        { error: 'Not subscribed to this category' },
        { status: 404 }
      )
    }

    // Remove subscription
    const { error: deleteError } = await adminClient
      .from('business_categories')
      .delete()
      .eq('business_id', businessId)
      .eq('category_id', parseInt(categoryId))

    if (deleteError) {
      console.error('Error removing subscription:', deleteError)
      return NextResponse.json(
        { error: 'Failed to unsubscribe from category' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'Successfully unsubscribed from category'
    })
  } catch (error) {
    console.error('Error in DELETE /api/business-admin/category-subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
