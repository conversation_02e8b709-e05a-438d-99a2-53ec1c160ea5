import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    // Get the user's session using the server client
    const supabase = await createServerSupabase()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      return NextResponse.json(
        { error: "Session error", details: sessionError },
        { status: 500 }
      )
    }

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    const email = session.user.email

    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      )
    }

    // Try to get user role from the database using admin client
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("*")
      .eq("email", email)
      .single()

    if (userError) {
      return NextResponse.json(
        { error: "Database error", details: userError },
        { status: 500 }
      )
    }

    // Try to get user role from the database using server client
    const { data: userData2, error: userError2 } = await supabase
      .from("users")
      .select("*")
      .eq("email", email)
      .single()

    // Return all the debug information
    return NextResponse.json({
      session: {
        id: session.user.id,
        email: session.user.email,
        aud: session.user.aud,
        role: session.user.role,
      },
      adminClientResult: {
        data: userData,
        error: userError,
      },
      serverClientResult: {
        data: userData2,
        error: userError2,
      },
      headers: {
        cookie: request.headers.get("cookie") ? "Present" : "Not present",
        authorization: request.headers.get("authorization") ? "Present" : "Not present",
      },
      env: {
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL || "Not set",
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || "Not set",
      }
    })
  } catch (error: any) {
    console.error("Unexpected error in GET /api/auth/debug-middleware:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
