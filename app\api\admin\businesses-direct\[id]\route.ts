import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { headers } from "next/headers"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Helper function to verify admin access
async function verifyAdminAccess(request: Request) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check")
      return { authorized: true };
    } else {
      return { 
        authorized: false, 
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await adminClient.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token:", error)
      return { 
        authorized: false, 
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user:", user.email)

    // Check if the user has admin permissions
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("role")
      .eq("email", user.email)
      .single()

    if (profileError || !userProfile) {
      console.error("Error fetching user profile:", profileError)
      return { 
        authorized: false, 
        error: "User profile not found",
        status: 403
      };
    }

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", user.email, "with role:", userProfile.role)
      return { 
        authorized: false, 
        error: "You do not have permission to access this resource",
        status: 403
      };
    }

    console.log("Admin access verified for user:", user.email, "with role:", userProfile.role)
    return { authorized: true };
  } catch (authError) {
    console.error("Error verifying token:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return { 
        authorized: false, 
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error")
      return { authorized: true };
    }
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const businessId = params.id;
    console.log(`Starting admin business update API request for business ID: ${businessId}`)

    // Verify admin access
    const accessCheck = await verifyAdminAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status }
      );
    }

    // Parse the request body
    const updateData = await request.json();
    console.log(`Update data for business ${businessId}:`, updateData);

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Update the business
    const { data, error } = await supabase
      .from("businesses")
      .update(updateData)
      .eq("id", businessId)
      .select();

    if (error) {
      console.error(`Error updating business ${businessId}:`, error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data,
      success: true
    })
  } catch (error: any) {
    console.error("Unexpected error in business update API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
