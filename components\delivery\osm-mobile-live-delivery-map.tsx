"use client"

import { useEffect, useRef, useState } from "react"
import { useLocationStore, type DriverLocation } from "@/services/location-service"
import { Badge } from "@/components/ui/badge"
import { Navigation, Clock } from "lucide-react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface OSMMobileLiveDeliveryMapProps {
  orderId: string
  restaurantLocation: [number, number] // [longitude, latitude]
  customerLocation: [number, number] // [longitude, latitude]
}

export default function OSMMobileLiveDeliveryMap({
  orderId,
  restaurantLocation,
  customerLocation,
}: OSMMobileLiveDeliveryMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const restaurantMarkerRef = useRef<L.Marker | null>(null)
  const customerMarkerRef = useRef<L.Marker | null>(null)
  const driverMarkerRef = useRef<L.Marker | null>(null)
  const routeRef = useRef<L.Polyline | null>(null)
  const [mapError, setMapError] = useState(false)
  const [driverLocation, setDriverLocation] = useState<DriverLocation | null>(null)

  const getDriverLocationByOrder = useLocationStore((state) => state.getDriverLocationByOrder)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Get driver location updates
  useEffect(() => {
    const checkDriverLocation = () => {
      const location = getDriverLocationByOrder(orderId)
      if (location) {
        setDriverLocation(location)
      }
    }

    // Check immediately
    checkDriverLocation()

    // Then check periodically
    const intervalId = setInterval(checkDriverLocation, 3000)

    return () => clearInterval(intervalId)
  }, [getDriverLocationByOrder, orderId])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    try {
      // Create map instance - note that Leaflet uses [lat, lng] order, opposite of our [lng, lat]
      const map = L.map(mapContainer.current, {
        center: [restaurantLocation[1], restaurantLocation[0]], // [lat, lng]
        zoom: 12,
        attributionControl: false, // Hide attribution for cleaner mobile view
        zoomControl: false, // We'll add a smaller zoom control
      })

      // Add OpenStreetMap tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map)

      // Add zoom control (smaller for mobile)
      L.control.zoom({
        position: 'topright',
        zoomInTitle: 'Zoom in',
        zoomOutTitle: 'Zoom out'
      }).addTo(map)

      // Create custom restaurant icon (green)
      const restaurantIcon = L.divIcon({
        html: `<div style="background-color: #10b981; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                  <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                  <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                  <path d="M12 21v-9"></path>
                </svg>
              </div>`,
        className: '',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })

      // Create custom customer icon (blue)
      const customerIcon = L.divIcon({
        html: `<div style="background-color: #3b82f6; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
              </div>`,
        className: '',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })

      // Create custom driver icon (red)
      const driverIcon = L.divIcon({
        html: `<div style="background-color: #ef4444; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>
                </svg>
              </div>`,
        className: 'driver-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })

      // Add restaurant marker
      const restaurantMarker = L.marker(
        [restaurantLocation[1], restaurantLocation[0]], 
        { icon: restaurantIcon }
      ).addTo(map)
      restaurantMarker.bindPopup("Restaurant")
      restaurantMarkerRef.current = restaurantMarker

      // Add customer marker
      const customerMarker = L.marker(
        [customerLocation[1], customerLocation[0]], 
        { icon: customerIcon }
      ).addTo(map)
      customerMarker.bindPopup("Delivery Address")
      customerMarkerRef.current = customerMarker

      // Add driver marker (will be updated)
      const driverMarker = L.marker(
        [restaurantLocation[1], restaurantLocation[0]], // Start at restaurant
        { icon: driverIcon }
      ).addTo(map)
      driverMarker.bindPopup("Driver")
      driverMarkerRef.current = driverMarker

      // Add route line
      const route = L.polyline(
        [
          [restaurantLocation[1], restaurantLocation[0]],
          [customerLocation[1], customerLocation[0]]
        ],
        {
          color: '#10b981',
          weight: 4,
          opacity: 0.7,
          dashArray: '5, 5'
        }
      ).addTo(map)
      routeRef.current = route

      // Fit bounds to include all markers
      const bounds = L.latLngBounds(
        [restaurantLocation[1], restaurantLocation[0]],
        [customerLocation[1], customerLocation[0]]
      )
      map.fitBounds(bounds, { padding: [40, 40] })

      mapRef.current = map

      return () => {
        map.remove()
        mapRef.current = null
        restaurantMarkerRef.current = null
        customerMarkerRef.current = null
        driverMarkerRef.current = null
        routeRef.current = null
      }
    } catch (error) {
      console.error("Error initializing map:", error)
      setMapError(true)
    }
  }, [restaurantLocation, customerLocation])

  // Update driver marker when location changes
  useEffect(() => {
    if (!mapRef.current || !driverMarkerRef.current || !driverLocation) return

    const map = mapRef.current
    const driverMarker = driverMarkerRef.current

    // Update driver marker position
    const newPos: [number, number] = [driverLocation.latitude, driverLocation.longitude]
    driverMarker.setLatLng(newPos)

    // Update rotation based on heading
    const markerElement = driverMarker.getElement()
    if (markerElement) {
      const iconElement = markerElement.querySelector('.driver-marker div')
      if (iconElement) {
        (iconElement as HTMLElement).style.transform = `rotate(${driverLocation.heading}deg)`
      }
    }

    // Extend bounds to include driver
    const bounds = L.latLngBounds(
      [restaurantLocation[1], restaurantLocation[0]],
      [customerLocation[1], customerLocation[0]]
    )
    bounds.extend(newPos)
    map.fitBounds(bounds, { padding: [40, 40] })
  }, [driverLocation, restaurantLocation, customerLocation])

  if (mapError) {
    return (
      <div className="h-[250px] bg-gray-100 flex items-center justify-center">
        <div className="text-center p-6">
          <p className="text-gray-500 mb-2">Unable to load the map</p>
          <p className="text-sm text-gray-400">We're having trouble loading the delivery tracking map.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Live Tracking</h3>
        {driverLocation ? (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <Navigation className="mr-1 h-3 w-3" />
            Live
          </Badge>
        ) : (
          <Badge className="bg-amber-100 text-amber-800 border-amber-200">
            <Clock className="mr-1 h-3 w-3" />
            Waiting
          </Badge>
        )}
      </div>

      <div ref={mapContainer} className="h-[250px] rounded-lg overflow-hidden" />

      {driverLocation && (
        <div className="text-sm">
          <div className="flex justify-between">
            <div className="flex items-center">
              <Navigation className="h-4 w-4 text-red-500 mr-1" />
              <span>Driver is on the way</span>
            </div>
            <span className="text-gray-500">
              Updated {Math.floor((Date.now() - driverLocation.timestamp) / 1000)}s ago
            </span>
          </div>

          {driverLocation.speed > 0 && (
            <div className="mt-1 text-gray-500">Moving at {Math.round(driverLocation.speed)} km/h</div>
          )}
        </div>
      )}

      {!driverLocation && (
        <div className="text-sm text-gray-500">Waiting for the driver to start sharing their location...</div>
      )}
    </div>
  )
}
