"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>gerprint, ScanFaceIcon as Face, AlertCircle, ArrowLeft, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { BiometricRegistration } from "@/components/biometric-registration"
import { biometricAuthService } from "@/services/biometric-auth-service"

export default function BiometricSettingsPage() {
  const router = useRouter()
  const [isSupported, setIsSupported] = useState<boolean | null>(null)
  const [biometricType, setBiometricType] = useState<string>("Biometric Authentication")
  const [isRegistered, setIsRegistered] = useState(false)
  const [isRemoving, setIsRemoving] = useState(false)

  useEffect(() => {
    const checkSupport = async () => {
      try {
        const supported = await biometricAuthService.isSupported()
        setIsSupported(supported)

        if (supported) {
          const type = await biometricAuthService.getBiometricType()
          setBiometricType(type)
          setIsRegistered(biometricAuthService.hasCredentials())
        }
      } catch (err) {
        console.error("Error checking biometric support:", err)
        setIsSupported(false)
      }
    }

    checkSupport()
  }, [])

  const handleRemoveCredentials = () => {
    setIsRemoving(true)

    // Simulate API call
    setTimeout(() => {
      biometricAuthService.removeAllCredentials()
      setIsRegistered(false)
      setIsRemoving(false)
    }, 1000)
  }

  const handleRegistrationSuccess = () => {
    setIsRegistered(true)
  }

  return (
    <div className="container max-w-md mx-auto p-4">
      <div className="mb-4">
        <Button
          variant="ghost"
          className="p-0 flex items-center text-gray-600"
          onClick={() => router.push("/driver-mobile/settings")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Settings
        </Button>
      </div>

      <h1 className="text-2xl font-bold mb-6">Biometric Authentication</h1>

      {isSupported === null ? (
        <Card>
          <CardContent className="flex justify-center items-center py-8">
            <div className="animate-pulse h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
              <Fingerprint className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      ) : !isSupported ? (
        <Card>
          <CardHeader>
            <CardTitle>Not Supported</CardTitle>
            <CardDescription>Your device doesn't support biometric authentication.</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Device Not Compatible</AlertTitle>
              <AlertDescription>
                This feature requires a device with fingerprint or face recognition capabilities.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      ) : isRegistered ? (
        <Card>
          <CardHeader>
            <CardTitle>{biometricType} Enabled</CardTitle>
            <CardDescription>You can use {biometricType} to log in to your account.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center py-4">
              {biometricType.toLowerCase().includes("face") ? (
                <Face className="h-16 w-16 text-emerald-600" />
              ) : (
                <Fingerprint className="h-16 w-16 text-emerald-600" />
              )}
            </div>

            <Alert className="mt-4 bg-emerald-50 text-emerald-800 border-emerald-200">
              <Fingerprint className="h-4 w-4" />
              <AlertTitle>Active</AlertTitle>
              <AlertDescription>{biometricType} is set up and ready to use for secure login.</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button
              variant="destructive"
              className="w-full flex items-center gap-2"
              onClick={handleRemoveCredentials}
              disabled={isRemoving}
            >
              {isRemoving ? (
                <>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Removing...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Remove {biometricType}
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <BiometricRegistration
          userId="driver-123" // In a real app, this would be the actual user ID
          username="<EMAIL>" // In a real app, this would be the actual username
          displayName="Jersey Eats Driver" // In a real app, this would be the actual display name
          onSuccess={handleRegistrationSuccess}
        />
      )}
    </div>
  )
}
