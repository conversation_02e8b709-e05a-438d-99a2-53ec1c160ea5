"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import {
  Lightbulb,
  TrendingUp,
  Users,
  Heart,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  Vote,
  Tag
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface FeatureRequest {
  id: number
  feature_name: string
  feature_category: string
  description: string
  customer_name: string
  customer_email: string
  vote_count: number
  status: string
  created_at: string
  notes?: string
}

export default function AdminFeatureRequestsPage() {
  const [requests, setRequests] = useState<FeatureRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<FeatureRequest | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const { toast } = useToast()

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/admin/feature-requests')
      console.log('Feature requests response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Feature requests data:', data)
        setRequests(data.requests || [])
      } else {
        const errorData = await response.text()
        console.error('Feature requests API error:', response.status, errorData)
        toast({
          title: "Error loading data",
          description: `Failed to load feature requests: ${response.status}`,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
      toast({
        title: "Error loading data",
        description: "Failed to load feature requests. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateStatus = async (requestId: number, newStatus: string) => {
    try {
      const response = await fetch('/api/admin/feature-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestId,
          status: newStatus
        })
      })

      if (response.ok) {
        toast({
          title: "Status updated",
          description: `Feature request has been ${newStatus}.`,
        })
        fetchRequests() // Refresh the list
      } else {
        throw new Error('Failed to update status')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update request status.",
        variant: "destructive"
      })
    }
  }

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.feature_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.feature_category?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.customer_name?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === "all" || request.status === statusFilter
    const matchesCategory = categoryFilter === "all" || request.feature_category === categoryFilter

    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-300">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-300">Approved</Badge>
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-300">Rejected</Badge>
      case 'in_progress':
        return <Badge variant="outline" className="text-blue-600 border-blue-300">In Progress</Badge>
      case 'completed':
        return <Badge variant="outline" className="text-emerald-600 border-emerald-300">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const pendingRequests = requests.filter(r => r.status === 'pending')
  const totalVotes = requests.reduce((sum, r) => sum + r.vote_count, 0)
  const topVotes = requests.length > 0 ? Math.max(...requests.map(r => r.vote_count)) : 0
  const categories = [...new Set(requests.map(r => r.feature_category).filter(Boolean))]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Feature Requests</h1>
        <p className="text-gray-600 mt-2">
          Manage community suggestions for new app features and improvements
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Lightbulb className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Features</p>
                <p className="text-2xl font-bold text-gray-900">{requests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{pendingRequests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Votes</p>
                <p className="text-2xl font-bold text-gray-900">{totalVotes}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Top Votes</p>
                <p className="text-2xl font-bold text-gray-900">{topVotes}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search features, categories, or customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Top Requests */}
      {pendingRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-yellow-600" />
              Most Requested Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pendingRequests
                .sort((a, b) => b.vote_count - a.vote_count)
                .slice(0, 5)
                .map((request, index) => (
                  <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold ${
                        index === 0 ? 'bg-yellow-400 text-yellow-900' :
                        index < 3 ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{request.feature_name}</p>
                        <p className="text-sm text-gray-600">{request.feature_category}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Vote className="h-4 w-4 text-gray-500" />
                      <span className="font-bold text-yellow-600">{request.vote_count}</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Requests List */}
      {loading ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading feature requests...</p>
          </CardContent>
        </Card>
      ) : filteredRequests.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Lightbulb className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== "all" || categoryFilter !== "all" ? 'No features found' : 'No feature requests yet'}
            </h3>
            <p className="text-gray-500">
              {searchQuery || statusFilter !== "all" || categoryFilter !== "all"
                ? 'Try adjusting your search or filter criteria'
                : 'Feature requests will appear here when customers submit them'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{request.feature_name}</h3>
                      {request.feature_category && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {request.feature_category}
                        </Badge>
                      )}
                      {getStatusBadge(request.status)}
                    </div>

                    <div className="text-sm text-gray-600 mb-3">
                      <p className="mb-2">{request.description}</p>
                    </div>

                    <div className="text-sm text-gray-600 mb-2">
                      <p><strong>Requested by:</strong> {request.customer_name} ({request.customer_email})</p>
                      <p><strong>Date:</strong> {formatDate(request.created_at)}</p>
                    </div>

                    {request.notes && (
                      <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg mb-2">
                        <strong>Notes:</strong> {request.notes}
                      </div>
                    )}
                  </div>

                  <div className="text-right ml-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Vote className="h-5 w-5 text-gray-500" />
                      <span className="text-2xl font-bold text-yellow-600">{request.vote_count}</span>
                      <span className="text-sm text-gray-500">
                        {request.vote_count === 1 ? 'vote' : 'votes'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedRequest(request)
                          setShowDetailsDialog(true)
                        }}
                        className="w-full text-gray-600 border-gray-200 hover:bg-gray-50"
                      >
                        View Details
                      </Button>

                      {request.status === 'pending' && (
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUpdateStatus(request.id, 'approved')}
                            className="flex-1 text-green-600 border-green-200 hover:bg-green-50"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUpdateStatus(request.id, 'rejected')}
                            className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              {selectedRequest?.feature_name}
            </DialogTitle>
            <DialogDescription>
              Feature request details and community feedback
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Category</label>
                  <p className="text-sm">{selectedRequest.feature_category || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedRequest.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Votes</label>
                  <p className="text-sm font-bold text-yellow-600">{selectedRequest.vote_count}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Submitted</label>
                  <p className="text-sm">{formatDate(selectedRequest.created_at)}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Description</label>
                <p className="text-sm mt-1 p-3 bg-gray-50 rounded-lg">{selectedRequest.description}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Requested by</label>
                <p className="text-sm mt-1">{selectedRequest.customer_name} ({selectedRequest.customer_email})</p>
              </div>

              {selectedRequest.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Additional Notes</label>
                  <p className="text-sm mt-1 p-3 bg-gray-50 rounded-lg">{selectedRequest.notes}</p>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsDialog(false)}
                  className="flex-1"
                >
                  Close
                </Button>
                {selectedRequest.status === 'pending' && (
                  <>
                    <Button
                      onClick={() => {
                        handleUpdateStatus(selectedRequest.id, 'approved')
                        setShowDetailsDialog(false)
                      }}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      Approve
                    </Button>
                    <Button
                      onClick={() => {
                        handleUpdateStatus(selectedRequest.id, 'rejected')
                        setShowDetailsDialog(false)
                      }}
                      variant="outline"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      Reject
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
