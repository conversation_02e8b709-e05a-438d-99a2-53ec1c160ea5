-- Enhance order status tracking for comprehensive driver performance metrics
-- This adds specific timestamp fields for key order lifecycle events

-- 1. Add specific timestamp fields to orders table for key events
ALTER TABLE orders ADD COLUMN IF NOT EXISTS offered_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS assigned_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS picked_up_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS out_for_delivery_at TIMESTAMP WITH TIME ZONE;
<PERSON><PERSON><PERSON> TABLE orders ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMP WITH TIME ZONE;
<PERSON><PERSON><PERSON> TABLE orders ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP WITH TIME ZONE;

-- 2. Add driver response tracking
ALTER TABLE orders ADD COLUMN IF NOT EXISTS driver_response_time_seconds INTEGER;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS expected_pickup_time TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS pickup_delay_minutes INTEGER;

-- 3. Create function to automatically update timestamps when status changes
CREATE OR REPLACE FUNCTION update_order_status_timestamps()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    
    -- Update specific timestamp based on new status
    CASE NEW.status
      WHEN 'offered' THEN
        NEW.offered_at = NOW();
        
      WHEN 'assigned' THEN
        NEW.assigned_at = NOW();
        -- Calculate expected pickup time (ready_time + 10 minute buffer)
        IF NEW.ready_time IS NOT NULL THEN
          NEW.expected_pickup_time = NEW.ready_time + INTERVAL '10 minutes';
        END IF;
        -- Calculate driver response time if we have offered_at
        IF OLD.offered_at IS NOT NULL THEN
          NEW.driver_response_time_seconds = EXTRACT(EPOCH FROM (NOW() - OLD.offered_at));
        END IF;
        
      WHEN 'picked_up' THEN
        NEW.picked_up_at = NOW();
        -- Calculate pickup delay if we have expected pickup time
        IF NEW.expected_pickup_time IS NOT NULL THEN
          NEW.pickup_delay_minutes = EXTRACT(EPOCH FROM (NOW() - NEW.expected_pickup_time)) / 60;
        END IF;
        
      WHEN 'out_for_delivery' THEN
        NEW.out_for_delivery_at = NOW();
        
      WHEN 'delivered' THEN
        NEW.delivered_at = NOW();
        
      WHEN 'cancelled' THEN
        NEW.cancelled_at = NOW();
    END CASE;
    
    -- Always update the general updated_at timestamp
    NEW.updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Create trigger to automatically update timestamps
DROP TRIGGER IF EXISTS trigger_update_order_status_timestamps ON orders;
CREATE TRIGGER trigger_update_order_status_timestamps
  BEFORE UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION update_order_status_timestamps();

-- 5. Update the shift order tracking function to include offer timestamp
CREATE OR REPLACE FUNCTION record_shift_order_offer(
  p_driver_id UUID,
  p_order_id INTEGER,
  p_action VARCHAR(20)
)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
  v_order_data RECORD;
  v_offered_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Get order details from SOURCE OF TRUTH (orders table)
  SELECT 
    o.id, 
    o.business_id, 
    o.parish,
    o.total,                    -- SOURCE OF TRUTH for order value
    o.delivery_fee,             -- SOURCE OF TRUTH for delivery fee
    o.delivery_distance_km,     -- SOURCE OF TRUTH for distance
    o.offered_at                -- When order was offered
  INTO v_order_data
  FROM orders o
  WHERE o.id = p_order_id;
  
  IF v_order_data IS NULL THEN
    RAISE EXCEPTION 'Order not found: %', p_order_id;
  END IF;
  
  -- Use offered_at from order if available, otherwise use NOW()
  v_offered_at = COALESCE(v_order_data.offered_at, NOW());
  
  -- Insert or update shift order record with source data
  INSERT INTO driver_shift_orders (
    shift_id, 
    order_id, 
    offered_at, 
    action_taken, 
    action_taken_at,
    business_id, 
    parish, 
    order_total,              -- FROM orders.total
    delivery_fee,             -- FROM orders.delivery_fee  
    distance_km               -- FROM orders.delivery_distance_km
  )
  VALUES (
    v_shift_id, 
    p_order_id, 
    v_offered_at,             -- Use actual offer time
    p_action, 
    NOW(),
    v_order_data.business_id, 
    v_order_data.parish,
    v_order_data.total,       -- SOURCE OF TRUTH
    v_order_data.delivery_fee, -- SOURCE OF TRUTH
    v_order_data.delivery_distance_km -- SOURCE OF TRUTH
  )
  ON CONFLICT (shift_id, order_id)
  DO UPDATE SET
    action_taken = p_action,
    action_taken_at = NOW(),
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 6. Create view for comprehensive order performance metrics
CREATE OR REPLACE VIEW order_performance_metrics AS
SELECT 
  o.id,
  o.order_number,
  o.business_id,
  o.driver_id,
  o.status,
  o.parish,
  o.delivery_fee,
  o.total,
  o.delivery_distance_km,
  
  -- Timing metrics
  o.offered_at,
  o.assigned_at,
  o.picked_up_at,
  o.out_for_delivery_at,
  o.delivered_at,
  o.cancelled_at,
  
  -- Performance metrics
  o.driver_response_time_seconds,
  o.expected_pickup_time,
  o.pickup_delay_minutes,
  
  -- Calculated durations (in minutes)
  CASE 
    WHEN o.assigned_at IS NOT NULL AND o.offered_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM (o.assigned_at - o.offered_at)) / 60
  END as offer_to_accept_minutes,
  
  CASE 
    WHEN o.picked_up_at IS NOT NULL AND o.assigned_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM (o.picked_up_at - o.assigned_at)) / 60
  END as accept_to_pickup_minutes,
  
  CASE 
    WHEN o.delivered_at IS NOT NULL AND o.picked_up_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM (o.delivered_at - o.picked_up_at)) / 60
  END as pickup_to_delivery_minutes,
  
  CASE 
    WHEN o.delivered_at IS NOT NULL AND o.offered_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM (o.delivered_at - o.offered_at)) / 60
  END as total_order_duration_minutes,
  
  -- Performance flags
  CASE 
    WHEN o.pickup_delay_minutes > 0 THEN true 
    ELSE false 
  END as was_pickup_late,
  
  CASE 
    WHEN o.driver_response_time_seconds > 300 THEN true  -- More than 5 minutes
    ELSE false 
  END as slow_response,
  
  o.created_at,
  o.updated_at
FROM orders o
WHERE o.driver_id IS NOT NULL;

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_orders_offered_at ON orders(offered_at);
CREATE INDEX IF NOT EXISTS idx_orders_assigned_at ON orders(assigned_at);
CREATE INDEX IF NOT EXISTS idx_orders_picked_up_at ON orders(picked_up_at);
CREATE INDEX IF NOT EXISTS idx_orders_delivered_at ON orders(delivered_at);
CREATE INDEX IF NOT EXISTS idx_orders_driver_status ON orders(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_orders_performance_lookup ON orders(driver_id, delivered_at) WHERE status = 'delivered';

-- 8. Add comments for documentation
COMMENT ON COLUMN orders.offered_at IS 'When order was first offered to a driver';
COMMENT ON COLUMN orders.assigned_at IS 'When driver accepted the order';
COMMENT ON COLUMN orders.picked_up_at IS 'When driver confirmed pickup from business';
COMMENT ON COLUMN orders.out_for_delivery_at IS 'When driver started delivery journey';
COMMENT ON COLUMN orders.delivered_at IS 'When order was delivered to customer';
COMMENT ON COLUMN orders.driver_response_time_seconds IS 'How long driver took to respond to offer (seconds)';
COMMENT ON COLUMN orders.expected_pickup_time IS 'When driver was expected to pickup (ready_time + buffer)';
COMMENT ON COLUMN orders.pickup_delay_minutes IS 'How many minutes late the pickup was (negative = early)';

COMMENT ON VIEW order_performance_metrics IS 'Comprehensive view of order timing and driver performance metrics';
COMMENT ON FUNCTION update_order_status_timestamps() IS 'Automatically updates timestamp fields when order status changes';
