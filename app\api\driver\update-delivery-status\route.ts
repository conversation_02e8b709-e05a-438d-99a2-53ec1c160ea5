import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { orderId, status, notes } = await request.json()

    if (!orderId || !status) {
      return NextResponse.json(
        { error: "Order ID and status are required" },
        { status: 400 }
      )
    }

    const validStatuses = ['assigned', 'out_for_delivery', 'delivered']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Verify order belongs to this driver
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, business_name, order_number, customer_name')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.driver_id !== driverProfile.id) {
      return NextResponse.json(
        { error: "Order is not assigned to this driver" },
        { status: 403 }
      )
    }

    // Validate status transition
    const statusTransitions = {
      'offered': ['assigned'],
      'assigned': ['out_for_delivery'],
      'out_for_delivery': ['delivered']
    }

    const allowedNextStatuses = statusTransitions[order.status as keyof typeof statusTransitions]
    if (!allowedNextStatuses || !allowedNextStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Cannot transition from ${order.status} to ${status}` },
        { status: 409 }
      )
    }

    let result = false
    let functionName = ''

    // Call appropriate database function based on status
    switch (status) {
      case 'assigned':
        // Simple status update for assigned (no special function needed)
        const { data: assignedResult, error: assignedError } = await supabase
          .from('orders')
          .update({
            status: 'assigned',
            updated_at: new Date().toISOString()
          })
          .eq('id', orderId)
          .select()
          .single()
        if (assignedError) throw assignedError
        result = assignedResult
        break

      case 'out_for_delivery':
        // Use confirm_pickup function which now directly sets status to out_for_delivery
        functionName = 'confirm_pickup'
        const { data: pickupResult, error: pickupError } = await supabase
          .rpc('confirm_pickup', {
            order_id: orderId,
            confirming_party: 'driver',
            driver_auth_id: user.auth_id
          })
        if (pickupError) throw pickupError
        result = pickupResult

        // Check if confirmation was successful
        if (!result || !result.success) {
          return NextResponse.json({
            success: false,
            error: result?.error || 'Failed to confirm pickup'
          }, { status: 400 })
        }

        // Trigger enhanced notifications for pickup confirmation
        try {
          const { updateOrderStatusWithNotifications } = await import('../../orders/enhanced-status-update')
          await updateOrderStatusWithNotifications({
            orderId: orderId,
            newStatus: 'out_for_delivery',
            notes: `Pickup confirmed by driver - order is now out for delivery`,
            updatedBy: driverProfile.id,
            driverId: driverProfile.id
          })
        } catch (notificationError) {
          console.warn('Failed to send pickup confirmation notifications:', notificationError)
          // Don't fail the request if notifications fail
        }
        break

      case 'out_for_delivery':
        functionName = 'driver_start_delivery'
        const { data: deliveryResult, error: deliveryError } = await supabase
          .rpc('driver_start_delivery', {
            order_id: orderId,
            driver_auth_id: user.auth_id
          })
        if (deliveryError) throw deliveryError
        result = deliveryResult
        break

      case 'delivered':
        functionName = 'driver_complete_delivery'
        const { data: completeResult, error: completeError } = await supabase
          .rpc('driver_complete_delivery', {
            order_id: orderId,
            driver_auth_id: user.auth_id,
            delivery_notes: notes || 'Order delivered successfully'
          })
        if (completeError) throw completeError
        result = completeResult
        break
    }

    if (!result) {
      return NextResponse.json(
        { error: `Failed to update status to ${status}` },
        { status: 500 }
      )
    }

    // Get updated order details
    const { data: updatedOrder, error: updatedOrderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        customer_name,
        customer_phone,
        delivery_address,
        postcode,
        parish,
        delivery_type,
        total,
        status,
        businesses!inner (
          id,
          name,
          address,
          phone,
          latitude,
          coordinates
        )
      `)
      .eq('id', orderId)
      .single()

    if (updatedOrderError) {
      console.error('Error fetching updated order:', updatedOrderError)
    }

    // Determine next action for driver
    let nextAction = ''
    let nextActionText = ''

    switch (status) {
      case 'assigned':
        nextAction = 'confirm_pickup'
        nextActionText = `Head to ${order.business_name} to pick up the order`
        break
      case 'out_for_delivery':
        nextAction = 'complete_delivery'
        nextActionText = `Complete delivery to ${order.customer_name}`
        break
      case 'delivered':
        nextAction = 'delivery_complete'
        nextActionText = 'Delivery completed! You can now accept new orders.'
        break
    }

    return NextResponse.json({
      success: true,
      message: `Order ${order.order_number} status updated to ${status}`,
      order: updatedOrder,
      previousStatus: order.status,
      newStatus: status,
      nextAction,
      nextActionText,
      isDeliveryComplete: status === 'delivered'
    })

  } catch (error) {
    console.error('Error in update delivery status API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
