"use client"

import { createContext, useContext, useState, useEffect, useCallback, type ReactNode } from "react"
import type { CartItem } from "@/types/cart"
import { useAuth } from "@/context/unified-auth-context"
import { usePathname } from "next/navigation"

/**
 * REFACTORED CART CONTEXT - MULTI-BUSINESS ARCHITECTURE
 *
 * This cart context has been updated to work with the new multi-business cart system:
 * - Uses /api/cart-direct endpoint (simplified from 1700+ to 465 lines)
 * - Supports one cart per business per session (database verified)
 * - Works with both registered users (user_id) and guests (session_id)
 * - Business-specific delivery methods and fees
 * - Automatic business isolation and payment allocation
 *
 * Database Tables:
 * - user_carts: One row per business per session
 * - cart_items: Individual items linked to business carts
 * - orders: Business-specific order processing
 * - cart_item_customizations: Product customizations
 *
 * Updated: December 2024
 */

// Modern cart context interface using new architecture
interface CartContextType {
  // Core cart state
  cart: CartItem[]
  totalItems: number
  totalPrice: number

  // Cart operations
  addToCart: (item: CartItem) => Promise<void>
  removeFromCart: (itemId: string, variantId?: string) => Promise<void>
  updateQuantity: (itemId: string, quantity: number, variantId?: string) => Promise<void>
  clearCart: () => Promise<void>
  clearSessionId: () => void

  // Business organization
  getItemsByBusiness: () => Record<string, CartItem[]>
  getBusinessNames: () => Record<string, string>
  getItemQuantity: (itemId: string, variantId?: string) => number
  getBusinessTotal: (businessId: string) => number
  getBusinessItemCount: (businessId: string) => number
  getBusinessCartIds: () => Promise<Record<string, string>>

  // Delivery state (synchronized - pickup = £0, delivery = calculated fee)
  deliveryMethods: Record<number, 'delivery' | 'pickup'>
  deliveryFees: Record<number, number>
  setDeliveryMethod: (businessId: number, method: 'delivery' | 'pickup') => Promise<void>
  getDeliveryMethod: (businessId: number) => 'delivery' | 'pickup'
  getDeliveryFee: (businessId: number) => number

  // Timing
  preparationTimes: Record<number, number>
  setPreparationTime: (businessId: number, time: number) => void
  getPreparationTime: (businessId: number) => number

  // Legacy compatibility - only setDeliveryFee needed for cart display
  setDeliveryFee: (businessId: number, fee: number) => void

  // Checkout-only functions (local state only, not persisted to database)
  getDeliveryType: (businessId: string) => 'asap' | 'scheduled'
  setDeliveryType: (businessId: string, type: 'asap' | 'scheduled') => void
  getScheduledTime: (businessId: string) => string
  setScheduledTime: (businessId: string, time: string) => void

  // Loading and connection state
  isInitialized: boolean
  isConnected: boolean
  syncStatus: string
  lastSyncTime: Date | null

  // Order processing state
  isProcessingOrder: boolean
  setIsProcessingOrder: (processing: boolean) => void

  // Session management
  sessionId: string | null
  userId: string | null

  // Cart maintenance
  refreshCart: () => Promise<{ success: boolean; message: string; changes?: any[] }>
}

// Create context
const CartContext = createContext<CartContextType | undefined>(undefined)

// Generate session ID for order grouping (used by both guests and logged-in users)
const generateSessionId = () => crypto.randomUUID()

// Modern cart provider using new architecture
export function CartProvider({ children }: { children: ReactNode }) {
  // Check if we're on a driver page - disable cart functionality for drivers
  const pathname = usePathname()
  const isDriverPage = pathname?.startsWith('/driver') || pathname?.startsWith('/driver-mobile')

  // Core cart state
  const [cart, setCart] = useState<CartItem[]>([])
  const [businessNames, setBusinessNames] = useState<Record<string, string>>({})

  // Delivery state (synchronized - when method changes, fee updates automatically)
  const [deliveryMethods, setDeliveryMethods] = useState<Record<number, 'delivery' | 'pickup'>>({})
  const [deliveryFees, setDeliveryFees] = useState<Record<number, number>>({})

  // Timing
  const [preparationTimes, setPreparationTimes] = useState<Record<number, number>>({})

  // Checkout-only state (local only, not persisted to database)
  const [deliveryTypes, setDeliveryTypes] = useState<Record<string, 'asap' | 'scheduled'>>({})
  const [scheduledTimes, setScheduledTimes] = useState<Record<string, string>>({})

  // Loading and connection state
  const [isInitialized, setIsInitialized] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [syncStatus, setSyncStatus] = useState('Initializing...')
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  // Order processing state
  const [isProcessingOrder, setIsProcessingOrder] = useState(false)

  // Auth and session
  const { user, userProfile } = useAuth()
  const [sessionId, setSessionId] = useState<string | null>(null)

  // Helper function to get the correct user identifier for cart operations
  // Cart API expects UUID (auth ID), not numeric database ID
  const getUserId = useCallback(() => {
    if (!user) return null;

    // Use auth_id from userProfile if available (this is the UUID)
    if (userProfile?.auth_id) {
      return userProfile.auth_id;
    }

    // Fallback to user.id (might be UUID from Supabase auth)
    return user.id || null;
  }, [user, userProfile])

  // Initialize session for ALL users (both guests and logged-in users)
  // Skip cart initialization on driver pages
  useEffect(() => {
    if (isDriverPage) {
      // Skip cart initialization on driver pages
      return
    }

    if (!sessionId) {
      // Try to get existing session from localStorage first
      const existingSessionId = localStorage.getItem('cart_session_id')

      // Check if the existing session ID is in the old "guest_" format and clear it
      if (existingSessionId && existingSessionId.startsWith('guest_')) {
        console.log(`🆔 CART: Clearing old format session ID: ${existingSessionId}`)
        localStorage.removeItem('cart_session_id')
        // Generate new UUID format session ID
        const newSessionId = generateSessionId()
        setSessionId(newSessionId)
        localStorage.setItem('cart_session_id', newSessionId)
        console.log(`🆔 CART: Generated new UUID session ID: ${newSessionId}`)
      } else if (existingSessionId) {
        setSessionId(existingSessionId)
        console.log(`🆔 CART: Using existing session ID: ${existingSessionId}`)
      } else {
        // Create new session for both guests and logged-in users
        const newSessionId = generateSessionId()
        setSessionId(newSessionId)
        localStorage.setItem('cart_session_id', newSessionId)
        console.log(`🆔 CART: Generated new session ID: ${newSessionId}`)
      }
    }
    // Note: We no longer clear session when user logs in - session persists for order grouping
  }, [sessionId, isDriverPage])

  // Computed values
  const totalItems = cart.reduce((total, item) => total + item.quantity, 0)
  const totalPrice = cart.reduce((total, item) => total + item.price * item.quantity, 0)

  // Get cart identifier (user ID or session ID)
  const getCartIdentifier = useCallback(() => user?.id || sessionId, [user?.id, sessionId])

  // Load cart from API on mount and manage initialization
  // Skip cart loading on driver pages
  useEffect(() => {
    if (isDriverPage) {
      // Skip cart loading on driver pages, but still initialize
      setIsInitialized(true)
      setIsConnected(true)
      setSyncStatus('Driver mode - cart disabled')
      setLastSyncTime(new Date())
      return
    }

    const identifier = getCartIdentifier()
    if (identifier) {
      setSyncStatus('Loading cart...')
      setIsConnected(false)
      loadCart().finally(() => {
        setIsInitialized(true)
        setIsConnected(true)
        setSyncStatus('Connected')
        setLastSyncTime(new Date())
      })
    } else {
      // No identifier yet, but we can still initialize
      setSyncStatus('Waiting for session...')
      setTimeout(() => {
        setIsInitialized(true)
        setIsConnected(true)
        setSyncStatus('Ready')
        setLastSyncTime(new Date())
      }, 500) // Small delay to show loading state
    }
  }, [user, sessionId, getCartIdentifier, isDriverPage])

  // Load cart from API
  const loadCart = async () => {
    // Skip cart loading on driver pages
    if (isDriverPage) {
      return
    }

    const identifier = getCartIdentifier()
    if (!identifier) {
      return
    }

    try {

      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'get',
          userId: user?.id,
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (response.ok) {
        const data = await response.json()

        // Update all cart state
        setCart(data.cart || [])
        setBusinessNames(data.businessNames || {})
        setDeliveryMethods(data.deliveryMethods || {})

        // Only update delivery fees if we don't already have calculated fees
        // This prevents database fees from overriding correctly calculated fees
        setDeliveryFees(prev => {
          const newFees = { ...prev }
          const dbFees = data.deliveryFees || {}

          // For each business in the database response
          Object.keys(dbFees).forEach(businessId => {
            const existingFee = prev[businessId] || 0
            const dbFee = dbFees[businessId] || 0

            // Only use database fee if we don't have an existing calculated fee
            // or if the existing fee is 0 (not calculated yet)
            if (existingFee === 0 && dbFee > 0) {
              newFees[businessId] = dbFee
              console.log(`📦 LOAD CART: Using database fee for business ${businessId}: £${dbFee.toFixed(2)}`)
            } else if (existingFee > 0) {
              console.log(`📦 LOAD CART: Preserving existing calculated fee for business ${businessId}: £${existingFee.toFixed(2)} (database had £${dbFee.toFixed(2)})`)
            }
          })

          return newFees
        })

        setPreparationTimes(data.preparationTimes || {})
      }
    } catch (error) {
      // Silently handle cart loading errors
    }
  }






  // Remove item from cart
  const removeFromCart = useCallback(async (itemId: string, variantId?: string) => {
    try {
      // Ensure sessionId is available before proceeding
      if (!sessionId) {
        throw new Error('Session not initialized yet. Please try again.')
      }

      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'remove',
          itemId,
          variantId,
          userId: user?.id,
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (!response.ok) {
        throw new Error('Failed to remove item')
      }

      await loadCart()
    } catch (error) {
      throw error
    }
  }, [user, sessionId, getCartIdentifier])

  // Update quantity
  const updateQuantity = useCallback(async (itemId: string, quantity: number, variantId?: string) => {
    try {
      // Ensure sessionId is available before proceeding
      if (!sessionId) {
        throw new Error('Session not initialized yet. Please try again.')
      }

      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateQuantity',
          itemId,
          quantity,
          variantId,
          userId: user?.id,
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update quantity')
      }

      await loadCart()
    } catch (error) {
      throw error
    }
  }, [user, sessionId, getCartIdentifier])

  // Clear cart
  const clearCart = useCallback(async () => {
    try {
      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'clear',
          userId: user?.id,
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (!response.ok) {
        throw new Error('Failed to clear cart')
      }

      setCart([])
      setBusinessNames({})
      setDeliveryMethods({})
      setDeliveryFees({})
      setPreparationTimes({})
    } catch (error) {
      throw error
    }
  }, [user, sessionId, getCartIdentifier])

  // Clear session ID (used after successful order completion)
  const clearSessionId = useCallback(() => {
    console.log(`🆔 CART: Clearing session ID: ${sessionId}`)
    localStorage.removeItem('cart_session_id')
    setSessionId(null)
    console.log('🆔 CART: Session ID cleared - new session will be generated for next order')
  }, [sessionId])

  // Refresh cart data by validating items against current product information
  const refreshCart = useCallback(async () => {
    console.log('🔄 CART: Starting cart refresh...')

    try {
      const response = await fetch('/api/cart/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: sessionId,
          userId: getUserId()
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ CART: Cart refresh failed:', errorData)
        return {
          success: false,
          message: errorData.error || 'Failed to refresh cart'
        }
      }

      const result = await response.json()
      console.log('✅ CART: Cart refresh completed:', result)

      // Reload cart data to reflect changes
      await loadCart()

      return {
        success: true,
        message: result.message,
        changes: result.results
      }
    } catch (error) {
      console.error('❌ CART: Exception during cart refresh:', error)
      return {
        success: false,
        message: 'An unexpected error occurred while refreshing cart'
      }
    }
  }, [sessionId, getUserId, loadCart])

  // CRITICAL: Synchronized delivery method update (fixes pickup/delivery fee mismatch)
  const setDeliveryMethod = useCallback(async (businessId: number, method: 'delivery' | 'pickup') => {

    try {
      console.log(`🔄 setDeliveryMethod called with businessId: ${businessId} (${typeof businessId}), method: ${method}`)

      // Ensure businessId is a number (required by new API)
      const businessIdNum = typeof businessId === 'string' ? parseInt(businessId, 10) : businessId

      if (isNaN(businessIdNum)) {
        throw new Error('Invalid business ID')
      }

      // Check if session is initialized
      if (!sessionId) {
        throw new Error('Session not initialized. Please wait and try again.')
      }

      console.log(`📋 Session ID: ${sessionId}, User ID: ${user?.id}`)

      // Get user location data (same as business page)
      let userLocationData = null
      try {
        const { getUserLocationData } = await import('@/lib/delivery-calculation-service')
        userLocationData = getUserLocationData()
        console.log(`📍 User location data:`, userLocationData)
      } catch (error) {
        console.warn('Could not get user location data:', error)
        // Silently handle location data errors
      }

      const requestBody = {
        action: 'updateDeliveryMethod',
        businessId: businessIdNum, // Send as number
        deliveryMethod: method, // Use correct field name
        deliveryFee: method === 'pickup' ? 0 : undefined, // Let API calculate delivery fee
        userId: user?.id,
        sessionId: sessionId, // Always pass session_id for both users and guests
        // Pass user location data for consistent delivery fee calculation
        userLocation: userLocationData
      }

      console.log(`🚀 Making API call to /api/cart-direct with:`, requestBody)

      // Update delivery method via API (this will automatically calculate correct fee)
      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      console.log(`📡 API response status: ${response.status} ${response.statusText}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`❌ API error response:`, errorText)
        throw new Error(`Failed to update delivery method: ${errorText}`)
      }

      const responseData = await response.json()
      console.log(`✅ API response data:`, responseData)

      // Update local state immediately for UI responsiveness
      setDeliveryMethods(prev => ({ ...prev, [businessId]: method }))

      // Set fee based on method (pickup = £0, delivery = calculated)
      if (method === 'pickup') {
        setDeliveryFees(prev => ({ ...prev, [businessId]: 0 }))
      }

      // Reload cart to get the calculated delivery fee for delivery method
      await loadCart()
      setLastSyncTime(new Date())
    } catch (error) {
      console.error(`❌ setDeliveryMethod error:`, error)
      throw error
    }
  }, [user, sessionId, getCartIdentifier])

  // Utility functions
  const getItemsByBusiness = useCallback(() => {
    const businessGroups: Record<string, CartItem[]> = {}
    cart.forEach(item => {
      const businessId = item.businessId.toString()
      if (!businessGroups[businessId]) {
        businessGroups[businessId] = []
      }
      businessGroups[businessId].push(item)
    })
    return businessGroups
  }, [cart])

  const getBusinessNames = useCallback(() => businessNames, [businessNames])

  const setPreparationTime = useCallback((businessId: number, time: number) => {
    setPreparationTimes(prev => ({ ...prev, [businessId]: time }))
  }, [])

  const getPreparationTime = useCallback((businessId: number) => {
    return preparationTimes[businessId] || 0
  }, [preparationTimes])

  const getDeliveryMethod = useCallback((businessId: number) => {
    const method = deliveryMethods[businessId] || 'delivery';
    return method;
  }, [deliveryMethods])

  const getDeliveryFee = useCallback((businessId: number) => {
    return deliveryFees[businessId] || 0
  }, [deliveryFees])

  const getItemQuantity = useCallback((itemId: string, variantId?: string) => {
    const item = cart.find(cartItem =>
      cartItem.id === itemId && cartItem.variantId === variantId
    )
    return item ? item.quantity : 0
  }, [cart])

  // Get total price for a specific business
  const getBusinessTotal = useCallback((businessId: string) => {
    const businessIdNum = typeof businessId === 'string' ? parseInt(businessId, 10) : businessId
    return cart
      .filter(item => item.businessId === businessIdNum)
      .reduce((total, item) => total + (item.price * item.quantity), 0)
  }, [cart])

  // Get total item count for a specific business
  const getBusinessItemCount = useCallback((businessId: string) => {
    const businessIdNum = typeof businessId === 'string' ? parseInt(businessId, 10) : businessId
    return cart
      .filter(item => item.businessId === businessIdNum)
      .reduce((total, item) => total + item.quantity, 0)
  }, [cart])

  // Get cart IDs for all businesses with items
  const getBusinessCartIds = useCallback(async (): Promise<Record<string, string>> => {
    console.log('🆔 CART CONTEXT: getBusinessCartIds called')
    const identifier = getCartIdentifier()
    console.log('🆔 CART CONTEXT: Cart identifier:', identifier)
    console.log('🆔 CART CONTEXT: User object:', user)
    console.log('🆔 CART CONTEXT: User ID:', user?.id)
    console.log('🆔 CART CONTEXT: User ID type:', typeof user?.id)
    console.log('🆔 CART CONTEXT: Is UUID format?', user?.id ? /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id) : false)
    console.log('🆔 CART CONTEXT: Current cart items:', cart.length)

    if (!identifier) {
      console.warn('🆔 CART CONTEXT: No cart identifier available for getting business cart IDs')
      return {}
    }

    try {
      console.log('🆔 CART CONTEXT: Making API call to get cart IDs...')
      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getCartIds',
          userId: getUserId(),
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (response.ok) {
        const data = await response.json()
        console.log('🆔 CART CONTEXT: getBusinessCartIds response:', data)
        return data.cartIds || {}
      } else {
        console.error('Failed to get business cart IDs:', response.status)
        const errorText = await response.text()
        console.error('Error details:', errorText)
        return {}
      }
    } catch (error) {
      console.error('Error getting business cart IDs:', error)
      return {}
    }
  }, [user, getCartIdentifier, getUserId])

  // Update delivery fee (synchronized with database)
  const setDeliveryFee = useCallback(async (businessId: number, fee: number) => {
    try {
      // Ensure businessId is a number (required by new API)
      const businessIdNum = typeof businessId === 'string' ? parseInt(businessId, 10) : businessId

      if (isNaN(businessIdNum)) {
        throw new Error('Invalid business ID')
      }

      // Update delivery fee via API to persist to database
      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateDeliveryFee',
          businessId: businessIdNum,
          deliveryFee: fee,
          userId: getUserId(),
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (!response.ok) {
        const errorText = await response.text()

        try {
          const errorData = JSON.parse(errorText)

          // If cart doesn't exist yet, that's expected - just update local state
          if (errorData.error?.includes('No cart found')) {
            console.log(`Cart not found for business ${businessIdNum} - will be created when items are added`)
            // Update local state and return - this is normal behavior
            setDeliveryFees(prev => ({ ...prev, [businessId]: fee }))
            return
          }
        } catch (parseError) {
          // Error response wasn't JSON, continue with generic error handling
        }

        console.error(`Failed to update delivery fee: ${errorText}`)
        // Continue with local update even if API fails
      }

      // Update local state immediately for UI responsiveness
      setDeliveryFees(prev => ({ ...prev, [businessId]: fee }))
      setLastSyncTime(new Date())
    } catch (error) {
      console.error('Error updating delivery fee:', error)
      // Update local state even if API call fails
      setDeliveryFees(prev => ({ ...prev, [businessId]: fee }))
    }
  }, [user, sessionId, getCartIdentifier, getUserId])

  // Helper function to calculate and set delivery fee immediately
  const calculateAndSetDeliveryFee = useCallback(async (businessId: number) => {
    try {
      // First, check if the business offers delivery by fetching business details
      const businessResponse = await fetch(`/api/businesses/${businessId}/details`);
      let business = null;

      if (businessResponse.ok) {
        business = await businessResponse.json();
      }

      // If business doesn't offer delivery, set to pickup with £0 fee
      if (business?.delivery_available === false) {
        console.log(`🚫 Business ${businessId} is pickup-only, setting delivery method to pickup`);
        await setDeliveryMethod(businessId, 'pickup');
        await setDeliveryFee(businessId, 0);
        return;
      }

      // Business offers delivery, so calculate delivery fee
      console.log(`🚚 Business ${businessId} offers delivery, calculating delivery fee...`);

      // Get user location data
      let userCoords: [number, number] | null = null;
      let userPostcode: string | null = null;

      // Try to get coordinates from localStorage (check both possible keys)
      if (typeof window !== 'undefined') {
        const coordsStr = localStorage.getItem('loop_jersey_coordinates') || localStorage.getItem('user_coordinates');
        if (coordsStr) {
          try {
            userCoords = JSON.parse(coordsStr);
            console.log(`📍 Found user coordinates in localStorage: [${userCoords[0]}, ${userCoords[1]}]`);
          } catch (e) {
            console.warn('Error parsing coordinates from localStorage:', e);
          }
        }

        // Get postcode (check both possible keys)
        userPostcode = localStorage.getItem('loop_jersey_postcode') || localStorage.getItem('user_postcode');
        if (userPostcode) {
          console.log(`📮 Found user postcode in localStorage: ${userPostcode}`);
        }
      }

      // Use default Jersey coordinates if none found
      if (!userCoords) {
        userCoords = [-2.1065, 49.1868]; // Default Jersey coordinates (St Helier)
        console.warn(`⚠️ No user coordinates found in localStorage, using default St Helier coordinates: [${userCoords[0]}, ${userCoords[1]}]`);
      }
      if (!userPostcode) {
        userPostcode = "JE2 3QN"; // Default Jersey postcode (St Helier)
        console.warn(`⚠️ No user postcode found in localStorage, using default St Helier postcode: ${userPostcode}`);
      }

      // Use the cart-direct API to set delivery method and calculate fee in one call
      // This is the same API that's working correctly in other parts of the app
      const updateResponse = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateDeliveryMethod',
          businessId,
          deliveryMethod: 'delivery',
          userLocation: {
            postcode: userPostcode,
            coordinates: userCoords
          },
          userId: getUserId(),
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      });

      if (!updateResponse.ok) {
        // Fallback to default fee if API call fails
        console.log(`⚠️ Failed to calculate delivery fee for business ${businessId}, using default`);
        await setDeliveryMethod(businessId, 'delivery');
        await setDeliveryFee(businessId, 2.50);
      } else {
        console.log(`✅ Successfully set delivery method and fee for business ${businessId}`);
      }

    } catch (error) {
      console.error(`❌ Error in calculateAndSetDeliveryFee for business ${businessId}:`, error);
      // Fallback: set default delivery method and fee
      try {
        await setDeliveryMethod(businessId, 'delivery');
        await setDeliveryFee(businessId, 2.50);
      } catch (fallbackError) {
        // Even fallback failed, but don't block item addition
      }
    }
  }, [setDeliveryMethod, setDeliveryFee, user, getCartIdentifier, getUserId])

  // Add item to cart
  const addToCart = useCallback(async (item: CartItem) => {
    try {
      // Ensure sessionId is available before proceeding
      if (!sessionId) {
        throw new Error('Session not initialized yet. Please try again.')
      }

      // Ensure businessId is a number (required by new API)
      const businessId = typeof item.businessId === 'string' ? parseInt(item.businessId, 10) : item.businessId

      if (isNaN(businessId)) {
        throw new Error('Invalid business ID')
      }

      // Check if this is the first item for this business
      const businessItems = cart.filter(cartItem => cartItem.businessId === businessId);
      const isFirstItemForBusiness = businessItems.length === 0;

      // Add the item to cart first
      const response = await fetch('/api/cart-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add',
          item: {
            id: item.id,
            businessId: businessId, // Ensure it's a number
            businessType: item.businessType,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            variantId: item.variantId,
            imageUrl: item.imageUrl,
            customizations: item.customizations // Add customizations to the request
          },
          userId: getUserId(),
          sessionId: sessionId // Always pass session_id for both users and guests
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to add item: ${errorText}`)
      }

      // Reload cart to get updated state
      await loadCart()

      // If this is the first item for this business, immediately calculate and set delivery fee
      if (isFirstItemForBusiness) {
        try {
          // Calculate delivery fee immediately and wait for completion to prevent checkout delay
          await calculateAndSetDeliveryFee(businessId);
        } catch (feeError) {
          // If delivery fee calculation fails, set a default but don't fail the entire add operation
          // The user can still add items and the fee can be recalculated later
          try {
            await setDeliveryMethod(businessId, 'delivery');
            await setDeliveryFee(businessId, 2.50);
          } catch (fallbackError) {
            // Even fallback failed, but don't block item addition
          }
        }
      }

      setLastSyncTime(new Date())
    } catch (error) {
      throw error
    }
  }, [user, sessionId, cart, deliveryMethods, deliveryFees, calculateAndSetDeliveryFee, getCartIdentifier, getUserId])

  // Checkout-only functions (local state only, not persisted to database)
  const getDeliveryType = useCallback((businessId: string) => {
    return deliveryTypes[businessId] || 'asap'
  }, [deliveryTypes])

  const setDeliveryType = useCallback((businessId: string, type: 'asap' | 'scheduled') => {
    setDeliveryTypes(prev => ({ ...prev, [businessId]: type }))
  }, [])

  const getScheduledTime = useCallback((businessId: string) => {
    return scheduledTimes[businessId] || ''
  }, [scheduledTimes])

  const setScheduledTime = useCallback((businessId: string, time: string) => {
    setScheduledTimes(prev => ({ ...prev, [businessId]: time }))
  }, [])

  // Context value
  const contextValue: CartContextType = {
    // Core cart state
    cart,
    totalItems,
    totalPrice,

    // Cart operations
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    clearSessionId,

    // Business organization
    getItemsByBusiness,
    getBusinessNames,
    getItemQuantity,
    getBusinessTotal,
    getBusinessItemCount,
    getBusinessCartIds,

    // Delivery state (synchronized)
    deliveryMethods,
    deliveryFees,
    setDeliveryMethod,
    getDeliveryMethod,
    getDeliveryFee,

    // Timing
    preparationTimes,
    setPreparationTime,
    getPreparationTime,

    // Legacy compatibility functions
    setDeliveryFee,

    // Checkout-only functions (local state only)
    getDeliveryType,
    setDeliveryType,
    getScheduledTime,
    setScheduledTime,

    // Loading and connection state
    isInitialized,
    isConnected,
    syncStatus,
    lastSyncTime,

    // Order processing state
    isProcessingOrder,
    setIsProcessingOrder,

    // Session management
    sessionId,
    userId: getUserId() || null,

    // Cart maintenance
    refreshCart
  }

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  )
}

// Hook to use cart context
export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}

// Export for backward compatibility
export const RealtimeCartProvider = CartProvider
export const useRealtimeCart = useCart
