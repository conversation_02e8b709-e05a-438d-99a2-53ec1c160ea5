// PHASE 6 STEP 11: Order Requirements API
// API endpoint to view order requirements data

import { NextRequest, NextResponse } from 'next/server';
import { 
  getOrderRequirements, 
  getOrderRequirementsStats 
} from '@/services/order-requirements-service';

// GET endpoint to fetch order requirements
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    const action = searchParams.get('action');

    // If orderId is provided, get requirements for specific order
    if (orderId) {
      const requirements = await getOrderRequirements(parseInt(orderId));
      
      if (!requirements) {
        return NextResponse.json(
          { error: 'Order requirements not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: requirements
      });
    }

    // If action is 'stats', get aggregated statistics
    if (action === 'stats') {
      const stats = await getOrderRequirementsStats();
      
      if (!stats) {
        return NextResponse.json(
          { error: 'Failed to fetch order requirements statistics' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    // Default: return error for missing parameters
    return NextResponse.json(
      { error: 'Missing required parameters. Provide orderId or action=stats' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ Error in order requirements API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
