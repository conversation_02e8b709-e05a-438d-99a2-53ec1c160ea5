import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface OrderStatusUpdate {
  orderId: number // INTEGER based on actual schema
  newStatus: string
  notes?: string
  updatedBy: string
  businessId?: string
}

interface NotificationConfig {
  shouldPush: boolean
  priority: number
  isUrgent: boolean
  title: string
  body: string
}

export async function updateOrderStatusWithNotifications({
  orderId,
  newStatus,
  notes,
  updatedBy,
  businessId
}: OrderStatusUpdate) {

  try {
    console.log(`🔍 [ENHANCED-STATUS] Looking for order with ID: ${orderId} (type: ${typeof orderId})`)

    // Start transaction
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      console.error(`❌ [ENHANCED-STATUS] Order not found:`, { orderId, orderError })
      throw new Error(`Order not found: ${orderError?.message || 'No order returned'}`)
    }

    console.log(`✅ [ENHANCED-STATUS] Found order: ${order.order_number} (ID: ${order.id})`)
    console.log(`🔍 [ENHANCED-STATUS] Order data:`, {
      business_id: order.business_id,
      business_name: order.business_name,
      delivery_method: order.delivery_method,
      delivery_type: order.delivery_type,
      user_id: order.user_id,
      driver_id: order.driver_id
    })

    // 1. Update order status
    const { error: updateError } = await supabaseAdmin
      .from('orders')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)

    if (updateError) {
      throw new Error(`Failed to update order: ${updateError.message}`)
    }

    // 2. Get driver auth_id if order has driver assigned
    let driverAuthId = null
    if (order.driver_id) {
      const { data: driverData } = await supabaseAdmin
        .from('driver_profiles')
        .select('user_id, users!inner(auth_id)')
        .eq('id', order.driver_id)
        .single()

      if (driverData && driverData.users) {
        driverAuthId = driverData.users.auth_id
      }
    }

    // 3. Add status history entry and handle notifications
    const { data: statusHistoryData, error: historyError } = await supabaseAdmin
      .from('order_status_history')
      .insert({
        order_id: orderId,
        status: newStatus,
        notes: notes || `Status updated to ${newStatus}`,
        created_by: updatedBy,
        business_id: businessId || order.business_id,
        driver_id: driverAuthId,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (historyError) {
      console.warn('Failed to create status history:', historyError.message)
      throw new Error(`Failed to create status history: ${historyError.message}`)
    }

    console.log(`Status history created for order ${orderId}: ${newStatus}`)
    const statusHistoryId = statusHistoryData.id

    // 4. Handle immediate notifications for urgent statuses
    const urgentStatuses = ['confirmed', 'preparing', 'ready', 'offered', 'assigned', 'accepted', 'out_for_delivery', 'delivered', 'cancelled']
    let customerNotified = false
    let businessNotified = false
    let driverNotified = false

    if (urgentStatuses.includes(newStatus)) {
      try {
        console.log(`🔔 Triggering immediate notification for order ${orderId} with status ${newStatus}`)

        // First, try to send any pending notifications (smart retry system)
        await processPendingNotifications()

        // Send customer notification
        if (order.user_id) {
          const notificationConfig = getNotificationConfig(newStatus, order)
          if (notificationConfig && notificationConfig.shouldPush) {
            console.log(`📧 Sending customer notification for ${newStatus} status`)

            customerNotified = await sendPushNotification({
              userId: order.user_id,
              title: notificationConfig.title,
              body: notificationConfig.body,
              data: {
                orderId: orderId.toString(),
                orderNumber: order.order_number,
                type: 'order_update',
                url: `/account/orders/${order.order_number}`
              }
            })
          } else {
            console.log(`ℹ️ No push notification configured for status: ${newStatus}`)
            customerNotified = true // Mark as notified since no notification was needed
          }
        } else {
          console.log(`ℹ️ No user_id found for order ${orderId}, skipping customer notification`)
          customerNotified = true // Mark as notified since no customer to notify
        }

        // TODO: Add business notification logic here when business push notifications are implemented
        businessNotified = true // For now, mark as notified

        // Send driver notification for driver-specific statuses
        if (statusHistoryData.driver_id && ['offered', 'assigned', 'accepted', 'out_for_delivery', 'delivered'].includes(newStatus)) {
          const driverNotificationConfig = getDriverNotificationConfig(newStatus, order)
          if (driverNotificationConfig && driverNotificationConfig.shouldPush) {
            console.log(`📱 Sending driver notification for ${newStatus} status`)

            driverNotified = await sendPushNotification({
              userId: statusHistoryData.driver_id,
              title: driverNotificationConfig.title,
              body: driverNotificationConfig.body,
              data: {
                orderId: orderId.toString(),
                orderNumber: order.order_number,
                type: 'driver_order_update',
                url: `/driver-mobile/deliveries/${order.order_number}`
              }
            })
          } else {
            console.log(`ℹ️ No driver push notification configured for status: ${newStatus}`)
            driverNotified = true // Mark as notified since no notification was needed
          }
        } else {
          driverNotified = !statusHistoryData.driver_id // Mark as notified if no driver assigned
        }

      } catch (error) {
        console.error('❌ Failed to send immediate notification:', error)
        // Don't fail the whole operation if notification processing fails
      }

      // 5. Update the status history record with notification results
      const allNotified = customerNotified && businessNotified && driverNotified

      const { error: updateNotificationError } = await supabaseAdmin
        .from('order_status_history')
        .update({
          notification_sent: allNotified,
          notification_sent_at: allNotified ? new Date().toISOString() : null,
          customer_notified: customerNotified,
          business_notified: businessNotified,
          driver_notified: driverNotified
        })
        .eq('id', statusHistoryId)

      if (updateNotificationError) {
        console.error('❌ Failed to update notification status:', updateNotificationError.message)
      } else {
        console.log(`✅ Notification status updated for status history ${statusHistoryId}`)
      }
    }

    // 6. Create communication notification (for in-app messaging)
    const notificationConfig = getNotificationConfig(newStatus, order)

    if (notificationConfig) {
      await createOrderStatusCommunication({
        orderId,
        customerId: order.user_id,
        businessId: businessId || order.business_id,
        businessName: order.business_name,
        status: newStatus,
        notes,
        updatedBy,
        config: notificationConfig
      })
    }

    // Auto-offer delivery orders to drivers when they become ready
    if (newStatus === 'ready' && order.delivery_method === 'delivery') {
      console.log(`🚗 Auto-offering delivery order ${orderId} to drivers...`)

      try {
        // Automatically transition ready delivery orders to offered status
        const { data: offeredOrder, error: offerError } = await supabaseAdmin
          .from('orders')
          .update({
            status: 'offered',
            updated_at: new Date().toISOString()
          })
          .eq('id', orderId)
          .select()
          .single()

        if (offerError) {
          console.error('Failed to auto-offer order:', offerError)
        } else {
          console.log(`✅ Order ${orderId} automatically offered to drivers`)

          // Update the returned order status
          order.status = 'offered'

          // Create status history record for the offered transition
          await supabaseAdmin
            .from('order_status_history')
            .insert({
              order_id: orderId,
              status: 'offered',
              notes: 'Automatically offered to drivers when ready',
              updated_by: updatedBy,
              business_id: businessId,
              notification_sent: true, // Mark as sent since we'll notify drivers below
              customer_notified: true, // No customer notification needed for auto-offer
              business_notified: true, // No business notification needed for auto-offer
              driver_notified: false   // Will notify drivers about new available order
            })

          // Notify all available drivers about the new order
          await notifyAvailableDrivers(order)
        }
      } catch (autoOfferError) {
        console.error('Error in auto-offer process:', autoOfferError)
        // Don't fail the main request if auto-offer fails
      }
    }

    return { success: true, order }

  } catch (error: any) {
    console.error('Error in enhanced status update:', error)
    throw error
  }
}

function getNotificationConfig(status: string, order: any): NotificationConfig | null {
  const businessName = order.business_name || 'Business'

  switch (status) {
    case 'confirmed':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: false,
        title: 'Order Confirmed',
        body: `Your order from ${businessName} has been confirmed`
      }

    case 'preparing':
      return {
        shouldPush: true,
        priority: 0,
        isUrgent: false,
        title: 'Order Being Prepared',
        body: `${businessName} is preparing your order`
      }

    case 'ready':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Order Ready',
        body: order.delivery_type === 'pickup'
          ? `Your order is ready for pickup at ${businessName}`
          : `Your order is ready for delivery from ${businessName}`
      }

    case 'assigned':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Driver Assigned',
        body: `A driver has been assigned to deliver your order from ${businessName}`
      }

    case 'accepted':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Driver Accepted',
        body: `Your driver has accepted the delivery and is heading to ${businessName}`
      }

    case 'picked_up':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Order Picked Up',
        body: `Your driver has collected your order from ${businessName} and is on the way`
      }

    case 'out_for_delivery':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Out for Delivery',
        body: `Your order from ${businessName} is out for delivery`
      }

    case 'delivered':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: false,
        title: 'Order Delivered',
        body: `Your order from ${businessName} has been delivered`
      }

    case 'cancelled':
      return {
        shouldPush: true,
        priority: 2,
        isUrgent: true,
        title: 'Order Cancelled',
        body: `Your order from ${businessName} has been cancelled`
      }

    default:
      // For 'pending' and other statuses, create in-app notification only
      return {
        shouldPush: false,
        priority: 0,
        isUrgent: false,
        title: 'Order Update',
        body: `Order status updated to ${status}`
      }
  }
}

function getDriverNotificationConfig(status: string, order: any): NotificationConfig | null {
  const businessName = order.business_name || 'Business'
  const customerName = order.customer_name || 'Customer'

  switch (status) {
    case 'offered':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'New Delivery Request',
        body: `You have been offered a delivery from ${businessName}. Please accept or decline.`
      }

    case 'assigned':
      return {
        shouldPush: false, // Driver initiated this action by accepting, no need to notify them
        priority: 0,
        isUrgent: false,
        title: 'Delivery Assigned',
        body: `You have been assigned the delivery from ${businessName}`
      }

    case 'out_for_delivery':
      return {
        shouldPush: false, // Driver initiated this action, no need to notify them
        priority: 0,
        isUrgent: false,
        title: 'Out for Delivery',
        body: `You are delivering the order to ${customerName}`
      }

    case 'delivered':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: false,
        title: 'Delivery Completed',
        body: `Great job! You have successfully delivered the order from ${businessName}`
      }

    default:
      return null
  }
}

// Smart pending notification processor - runs whenever any notification is sent
async function processPendingNotifications() {
  try {
    console.log('🔄 Checking for pending notifications...')

    // Get a small batch of pending notifications (limit to avoid timeouts)
    const { data: pendingRecords, error: fetchError } = await supabaseAdmin
      .from('order_status_history')
      .select(`
        id, order_id, status, driver_id, business_id, notification_data,
        customer_notified, business_notified, driver_notified,
        orders!inner(
          id, order_number, user_id, business_name, delivery_type, customer_name
        )
      `)
      .eq('notification_sent', false)
      .order('created_at', { ascending: true })
      .limit(5) // Process small batches to avoid timeouts

    if (fetchError) {
      console.error('❌ Error fetching pending notifications:', fetchError)
      return
    }

    if (!pendingRecords || pendingRecords.length === 0) {
      console.log('✅ No pending notifications found')
      return
    }

    console.log(`📋 Found ${pendingRecords.length} pending notifications to retry`)

    // Process each pending notification
    for (const record of pendingRecords) {
      try {
        const order = record.orders
        let customerNotified = record.customer_notified
        let businessNotified = record.business_notified
        let driverNotified = record.driver_notified

        // Retry customer notification if needed
        if (!customerNotified && order.user_id) {
          const notificationConfig = getNotificationConfig(record.status, order)
          if (notificationConfig?.shouldPush) {
            const success = await sendPushNotification({
              userId: order.user_id,
              title: notificationConfig.title,
              body: notificationConfig.body,
              data: {
                orderId: order.id.toString(),
                orderNumber: order.order_number,
                type: 'order_update',
                url: `/account/orders/${order.order_number}`
              }
            })
            if (success) customerNotified = true
          } else {
            customerNotified = true // No notification needed
          }
        }

        // Retry driver notification if needed
        if (!driverNotified && record.driver_id && ['offered', 'assigned', 'accepted', 'out_for_delivery', 'delivered'].includes(record.status)) {
          const driverNotificationConfig = getDriverNotificationConfig(record.status, order)
          if (driverNotificationConfig?.shouldPush) {
            const success = await sendPushNotification({
              userId: record.driver_id,
              title: driverNotificationConfig.title,
              body: driverNotificationConfig.body,
              data: {
                orderId: order.id.toString(),
                orderNumber: order.order_number,
                type: 'driver_order_update',
                url: `/driver-mobile/deliveries/${order.order_number}`
              }
            })
            if (success) driverNotified = true
          } else {
            driverNotified = true // No notification needed
          }
        }

        // Business notifications (TODO - mark as notified for now)
        businessNotified = true

        // Update the record if any notifications were sent
        const allNotified = customerNotified && businessNotified && driverNotified
        if (allNotified || customerNotified !== record.customer_notified || driverNotified !== record.driver_notified) {
          await supabaseAdmin
            .from('order_status_history')
            .update({
              notification_sent: allNotified,
              notification_sent_at: allNotified ? new Date().toISOString() : null,
              customer_notified: customerNotified,
              business_notified: businessNotified,
              driver_notified: driverNotified
            })
            .eq('id', record.id)

          console.log(`✅ Updated pending notification for order ${order.order_number}`)
        }

      } catch (error) {
        console.error(`❌ Failed to process pending notification for record ${record.id}:`, error)
        // Continue with next record
      }
    }

  } catch (error) {
    console.error('❌ Error in processPendingNotifications:', error)
    // Don't throw - this is a background process
  }
}

// Helper function to send push notifications with error handling
async function sendPushNotification(payload: {
  userId: string
  title: string
  body: string
  data: any
}): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/send-push-notification`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: payload.userId,
        title: payload.title,
        body: payload.body,
        data: payload.data,
        icon: '/wheel-logo.svg',
        badge: '/wheel-logo.svg'
      })
    })

    if (response.ok) {
      const result = await response.json()
      console.log('✅ Push notification sent successfully:', result)
      return true
    } else {
      const errorText = await response.text()
      console.error('❌ Push notification failed:', errorText)
      return false
    }
  } catch (error) {
    console.error('❌ Push notification error:', error)
    return false
  }
}

async function createOrderStatusCommunication({
  orderId,
  customerId,
  businessId,
  businessName,
  status,
  notes,
  updatedBy,
  config
}: {
  orderId: string
  customerId: string
  businessId: string
  businessName: string
  status: string
  notes?: string
  updatedBy: string
  config: NotificationConfig
}) {

  // Create communication entry
  const { error: commError } = await supabaseAdmin
    .from('communications')
    .insert({
      sender_id: updatedBy, // Business user who updated status
      recipient_id: customerId,
      order_id: null, // Set to null since schema mismatch (UUID vs INTEGER)
      business_id: businessId,
      channel_type: 'active_order_delivery', // Use schema-defined channel type
      message_type: 'status_update',
      subject: config.title,
      content: `${notes || config.body} (Order #${orderId})`, // Include order ID in content
      is_automated: true,
      is_urgent: config.isUrgent,
      priority: config.priority,
      created_at: new Date().toISOString()
    })

  if (commError) {
    console.error('Failed to create communication:', commError.message)
  }

  // Push notifications are now handled by the database trigger system
  // This communication entry is for in-app messaging only
  console.log(`Communication created for order ${orderId} status: ${status}`)
}

// Notify all available drivers about a new order
async function notifyAvailableDrivers(order: any) {
  try {
    console.log(`📢 Notifying available drivers about order ${order.id}...`)

    // Get all online drivers who are approved for this business
    const { data: availableDrivers, error: driversError } = await supabaseAdmin
      .from('driver_profiles')
      .select(`
        id, user_id,
        driver_status!inner(is_online, is_on_shift),
        driver_business_approvals!inner(business_id, status),
        users!driver_profiles_user_id_fkey(id, auth_id)
      `)
      .eq('is_verified', true)
      .eq('is_active', true)
      .eq('driver_status.is_online', true)
      .eq('driver_business_approvals.business_id', order.business_id)
      .eq('driver_business_approvals.status', 'approved')

    if (driversError) {
      console.error('Error fetching available drivers:', driversError)
      return
    }

    if (!availableDrivers || availableDrivers.length === 0) {
      console.log('No available drivers found for this business')
      return
    }

    console.log(`Found ${availableDrivers.length} available drivers`)

    // Send notification to each available driver
    const notificationPromises = availableDrivers.map(async (driver) => {
      try {
        const success = await sendPushNotification({
          userId: driver.users.auth_id,
          title: 'New Order Available! 🚗',
          body: `£${order.total} delivery from ${order.business_name} - Ready for pickup`,
          data: {
            orderId: order.id.toString(),
            orderNumber: order.order_number,
            type: 'new_order_available',
            url: `/driver-mobile/orders`
          }
        })

        if (success) {
          console.log(`✅ Notified driver ${driver.id} about new order`)
        } else {
          console.log(`❌ Failed to notify driver ${driver.id}`)
        }

        return success
      } catch (error) {
        console.error(`Error notifying driver ${driver.id}:`, error)
        return false
      }
    })

    const results = await Promise.allSettled(notificationPromises)
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length

    console.log(`📊 Notified ${successCount}/${availableDrivers.length} drivers about new order`)

  } catch (error) {
    console.error('Error in notifyAvailableDrivers:', error)
  }
}


