import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET() {
  try {
    console.log('Starting average rating calculation...');
    
    // Check if we have access to execute SQL
    let useRawSQL = false;
    
    try {
      // Test if the exec_sql RPC function exists
      const { data: rpcTest, error: rpcError } = await supabaseAdmin.rpc('exec_sql', { 
        query_text: 'SELECT 1 as test' 
      });
      
      if (rpcError) {
        console.warn('exec_sql RPC function not available, will use direct SQL:', rpcError);
        useRawSQL = true;
      } else {
        console.log('exec_sql RPC function is available');
      }
    } catch (rpcTestErr) {
      console.warn('Error testing exec_sql RPC function:', rpcTestErr);
      useRawSQL = true;
    }
    
    // Helper function to execute SQL based on availability of exec_sql RPC
    const execSQL = async (sql: string) => {
      try {
        if (useRawSQL) {
          // Use direct SQL execution via the REST API
          console.log('Executing SQL via REST API:', sql.substring(0, 50) + (sql.length > 50 ? '...' : ''));
          
          try {
            // Create a direct SQL query using the REST API
            const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || '',
                'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}`,
                'Prefer': 'return=minimal'
              },
              body: JSON.stringify({ query: sql })
            });
            
            if (!response.ok) {
              let errorMessage = 'SQL execution failed';
              try {
                const errorData = await response.json();
                errorMessage = errorData.message || errorData.error || JSON.stringify(errorData);
              } catch (parseErr) {
                errorMessage = `SQL execution failed with status ${response.status}`;
              }
              
              console.error('SQL execution error:', errorMessage);
              return { error: { message: errorMessage } };
            }
            
            return { data: {}, error: null };
          } catch (fetchErr) {
            console.error('Fetch error executing SQL:', fetchErr);
            return { error: { message: fetchErr instanceof Error ? fetchErr.message : 'Unknown fetch error' } };
          }
        } else {
          // Use the RPC function
          console.log('Executing SQL via RPC:', sql.substring(0, 50) + (sql.length > 50 ? '...' : ''));
          return await supabaseAdmin.rpc('exec_sql', { query_text: sql });
        }
      } catch (execErr) {
        console.error('Exception in execSQL:', execErr);
        return { error: { message: execErr instanceof Error ? execErr.message : 'Unknown SQL execution error' } };
      }
    };
    
    // First, check if the reviews table exists
    let reviewsTableExists = false;
    
    try {
      const { data, error } = await supabaseAdmin
        .from('reviews')
        .select('id')
        .limit(1);
        
      reviewsTableExists = !error;
      console.log('Reviews table exists:', reviewsTableExists);
    } catch (err) {
      console.log('Error checking reviews table:', err);
      reviewsTableExists = false;
    }
    
    if (!reviewsTableExists) {
      return NextResponse.json({
        success: false,
        message: 'Reviews table does not exist. Cannot calculate average ratings.'
      }, { status: 400 });
    }
    
    // Create a function to calculate average ratings
    const { error: functionError } = await execSQL(`
      CREATE OR REPLACE FUNCTION calculate_business_ratings()
      RETURNS void AS $$
      BEGIN
        -- Update each business with the average of its reviews
        UPDATE businesses b
        SET rating = COALESCE(
          (SELECT ROUND(AVG(r.rating)::numeric, 1)
           FROM reviews r
           WHERE r.business_id = b.id),
          0.0
        );
        
        -- Ensure ratings are within valid range
        UPDATE businesses
        SET rating = CASE
          WHEN rating IS NULL THEN 0.0
          WHEN rating < 0 THEN 0.0
          WHEN rating > 5 THEN 5.0
          ELSE rating
        END;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    if (functionError) {
      console.error('Error creating function:', functionError);
      return NextResponse.json({
        success: false,
        message: 'Error creating calculation function: ' + functionError.message
      }, { status: 500 });
    }
    
    // Execute the function to calculate ratings
    const { error: execError } = await execSQL(`SELECT calculate_business_ratings();`);
    
    if (execError) {
      console.error('Error executing calculation function:', execError);
      return NextResponse.json({
        success: false,
        message: 'Error calculating ratings: ' + execError.message
      }, { status: 500 });
    }
    
    // Create a trigger to automatically update ratings when reviews change
    const { error: triggerFunctionError } = await execSQL(`
      CREATE OR REPLACE FUNCTION update_business_rating()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Update the business rating when a review is added, updated, or deleted
        UPDATE businesses
        SET rating = COALESCE(
          (SELECT ROUND(AVG(rating)::numeric, 1)
           FROM reviews
           WHERE business_id = 
             CASE
               WHEN TG_OP = 'DELETE' THEN OLD.business_id
               ELSE NEW.business_id
             END),
          0.0
        )
        WHERE id = 
          CASE
            WHEN TG_OP = 'DELETE' THEN OLD.business_id
            ELSE NEW.business_id
          END;
        
        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    if (triggerFunctionError) {
      console.warn('Warning: Error creating trigger function:', triggerFunctionError);
      // Continue anyway as this is not critical
    }
    
    // Create triggers for insert, update, and delete operations
    try {
      // Drop existing triggers if they exist
      await execSQL(`
        DROP TRIGGER IF EXISTS update_business_rating_insert ON reviews;
        DROP TRIGGER IF EXISTS update_business_rating_update ON reviews;
        DROP TRIGGER IF EXISTS update_business_rating_delete ON reviews;
      `);
      
      // Create new triggers
      await execSQL(`
        CREATE TRIGGER update_business_rating_insert
        AFTER INSERT ON reviews
        FOR EACH ROW
        EXECUTE FUNCTION update_business_rating();
      `);
      
      await execSQL(`
        CREATE TRIGGER update_business_rating_update
        AFTER UPDATE ON reviews
        FOR EACH ROW
        EXECUTE FUNCTION update_business_rating();
      `);
      
      await execSQL(`
        CREATE TRIGGER update_business_rating_delete
        AFTER DELETE ON reviews
        FOR EACH ROW
        EXECUTE FUNCTION update_business_rating();
      `);
      
      console.log('Successfully created triggers');
    } catch (triggerErr) {
      console.warn('Warning: Error creating triggers:', triggerErr);
      // Continue anyway as this is not critical
    }
    
    // Get the count of businesses that were updated
    let businessCount = 0;
    try {
      const { data: countData, error: countError } = await supabaseAdmin
        .from('businesses')
        .select('id', { count: 'exact' });
        
      if (!countError && countData) {
        businessCount = countData.length;
      }
    } catch (countErr) {
      console.warn('Warning: Error getting business count:', countErr);
    }
    
    return NextResponse.json({
      success: true,
      message: `Successfully calculated average ratings for ${businessCount} businesses. Ratings will now automatically update when reviews change.`
    });
    
  } catch (error) {
    console.error('Error calculating average ratings:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}
