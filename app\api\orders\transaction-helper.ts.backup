import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with admin privileges for transaction support
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Creates an order with all related records in a single transaction
 * This ensures that all tables are updated or none are
 */
export async function createOrderWithTransaction(orderData: any, orderItems: any[], orderBusinesses: any[]) {
  // Start a transaction
  const { data: transaction, error: transactionError } = await supabaseAdmin.rpc('begin_transaction');

  if (transactionError) {
    console.error('Error starting transaction:', transactionError);
    throw new Error(`Failed to start transaction: ${transactionError.message}`);
  }

  try {
    // Insert the order
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert(orderData)
      .select('id')
      .single();

    if (orderError) {
      throw new Error(`Failed to create order: ${orderError.message}`);
    }

    // Add order ID to items and businesses
    const itemsWithOrderId = orderItems.map(item => ({
      ...item,
      order_id: order.id
    }));

    const businessesWithOrderId = orderBusinesses.map(business => ({
      ...business,
      order_id: order.id,
      status: orderData.status || 'pending' // Ensure status matches the order
    }));

    // Insert order items
    const { error: itemsError } = await supabaseAdmin
      .from('order_items')
      .insert(itemsWithOrderId);

    if (itemsError) {
      throw new Error(`Failed to create order items: ${itemsError.message}`);
    }

    // Insert order businesses
    if (businessesWithOrderId.length > 0) {
      console.log('Inserting order businesses:', JSON.stringify(businessesWithOrderId, null, 2));

      // Ensure business_id is properly formatted
      const formattedBusinesses = businessesWithOrderId.map(business => {
        // Make a copy of the business object to avoid modifying the original
        const updatedBusiness = { ...business };

        // Store the original business_id as business_slug if it's a string
        if (business.business_id && typeof business.business_id === 'string' && isNaN(Number(business.business_id))) {
          updatedBusiness.business_slug = business.business_id;
        }

        // Priority order for setting business_id:
        // 1. Use businessNumericId if available
        // 2. Use numeric_business_id if available
        // 3. Use business_id if it's a number
        // 4. Use businessId if it's a number
        // 5. Keep as null (don't default to 1)

        if (business.businessNumericId && !isNaN(Number(business.businessNumericId))) {
          // 1. Use businessNumericId if available
          updatedBusiness.business_id = Number(business.businessNumericId);
          console.log(`Using businessNumericId=${updatedBusiness.business_id} for business ${business.business_name || 'unknown'}`);
        } else if (business.numeric_business_id && !isNaN(Number(business.numeric_business_id))) {
          // 2. Use numeric_business_id if available
          updatedBusiness.business_id = Number(business.numeric_business_id);
          console.log(`Using numeric_business_id=${updatedBusiness.business_id} for business ${business.business_name || 'unknown'}`);
        } else if (business.business_id && !isNaN(Number(business.business_id))) {
          // 3. Use business_id if it's a number
          updatedBusiness.business_id = Number(business.business_id);
          console.log(`Using business_id=${updatedBusiness.business_id} for business ${business.business_name || 'unknown'}`);
        } else if (business.businessId && !isNaN(Number(business.businessId))) {
          // 4. Use businessId if it's a number
          updatedBusiness.business_id = Number(business.businessId);
          console.log(`Using businessId=${updatedBusiness.business_id} for business ${business.business_name || 'unknown'}`);
        } else {
          // 5. Keep as null (don't default to 1)
          console.log(`No valid numeric ID found for business ${business.business_name || business.business_slug || business.businessId || 'unknown'}`);
          // Set to null explicitly to make it clear this is intentional
          updatedBusiness.business_id = null;
        }

        return updatedBusiness;
      });

      const { error: businessesError } = await supabaseAdmin
        .from('order_businesses')
        .insert(formattedBusinesses);

      if (businessesError) {
        console.error('Error inserting order businesses:', businessesError);
        console.error('Attempted to insert:', JSON.stringify(formattedBusinesses, null, 2));
        throw new Error(`Failed to create order businesses: ${businessesError.message}`);
      } else {
        console.log(`Successfully inserted ${formattedBusinesses.length} order businesses`);
      }
    } else {
      console.log('No order businesses to insert');
    }

    // Create initial status history entry
    const { error: historyError } = await supabaseAdmin
      .from('order_status_history')
      .insert({
        order_id: order.id,
        status: orderData.status || 'pending',
        notes: 'Order created',
        created_at: new Date().toISOString()
      });

    if (historyError) {
      throw new Error(`Failed to create status history: ${historyError.message}`);
    }

    // Commit the transaction
    const { error: commitError } = await supabaseAdmin.rpc('commit_transaction', {
      transaction_id: transaction.transaction_id
    });

    if (commitError) {
      throw new Error(`Failed to commit transaction: ${commitError.message}`);
    }

    // Return the created order ID
    return order.id;
  } catch (error) {
    // Rollback the transaction on any error
    try {
      await supabaseAdmin.rpc('rollback_transaction', {
        transaction_id: transaction.transaction_id
      });
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }

    // Re-throw the original error
    throw error;
  }
}

/**
 * Updates an order's status and ensures order_businesses status is also updated
 * @deprecated Use updateBusinessOrderStatus instead for multi-business orders
 */
export async function updateOrderStatusWithTransaction(orderId: number, status: string, notes?: string, userId?: string) {
  // Start a transaction
  const { data: transaction, error: transactionError } = await supabaseAdmin.rpc('begin_transaction');

  if (transactionError) {
    console.error('Error starting transaction:', transactionError);
    throw new Error(`Failed to start transaction: ${transactionError.message}`);
  }

  try {
    // Update the order status
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .update({
        status: status,
        order_status: status, // Update both status fields for consistency
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select()
      .single();

    if (orderError) {
      throw new Error(`Failed to update order status: ${orderError.message}`);
    }

    // Update all order_businesses records for this order
    const { error: businessesError } = await supabaseAdmin
      .from('order_businesses')
      .update({
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq('order_id', orderId);

    if (businessesError) {
      throw new Error(`Failed to update order businesses status: ${businessesError.message}`);
    }

    // Create status history entry
    const { error: historyError } = await supabaseAdmin
      .from('order_status_history')
      .insert({
        order_id: orderId,
        status: status,
        notes: notes || null,
        created_by: userId || null,
        created_at: new Date().toISOString()
      });

    if (historyError) {
      throw new Error(`Failed to create status history: ${historyError.message}`);
    }

    // Commit the transaction
    const { error: commitError } = await supabaseAdmin.rpc('commit_transaction', {
      transaction_id: transaction.transaction_id
    });

    if (commitError) {
      throw new Error(`Failed to commit transaction: ${commitError.message}`);
    }

    // Return the updated order
    return order;
  } catch (error) {
    // Rollback the transaction on any error
    try {
      await supabaseAdmin.rpc('rollback_transaction', {
        transaction_id: transaction.transaction_id
      });
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }

    // Re-throw the original error
    throw error;
  }
}

/**
 * Updates a specific business's status within an order
 * This is the preferred method for multi-business orders
 */
export async function updateBusinessOrderStatus(
  orderId: number,
  businessId: number,
  status: string,
  notes?: string,
  userId?: string
) {
  // Start a transaction
  const { data: transaction, error: transactionError } = await supabaseAdmin.rpc('begin_transaction');

  if (transactionError) {
    console.error('Error starting transaction:', transactionError);
    throw new Error(`Failed to start transaction: ${transactionError.message}`);
  }

  try {
    // Update only the specific business's status
    const { data: orderBusiness, error: businessError } = await supabaseAdmin
      .from('order_businesses')
      .update({
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq('order_id', orderId)
      .eq('business_id', businessId)
      .select()
      .single();

    if (businessError) {
      throw new Error(`Failed to update business order status: ${businessError.message}`);
    }

    // Create status history entry for this specific business
    const { error: historyError } = await supabaseAdmin
      .from('order_status_history')
      .insert({
        order_id: orderId,
        business_id: businessId, // Include business ID in history
        status: status,
        notes: notes || `Business ${businessId} status changed to ${status}`,
        created_by: userId || null,
        created_at: new Date().toISOString()
      });

    if (historyError) {
      throw new Error(`Failed to create status history: ${historyError.message}`);
    }

    // The main order status will be updated automatically by the database trigger
    // that calculates the aggregate status based on all businesses

    // Commit the transaction
    const { error: commitError } = await supabaseAdmin.rpc('commit_transaction', {
      transaction_id: transaction.transaction_id
    });

    if (commitError) {
      throw new Error(`Failed to commit transaction: ${commitError.message}`);
    }

    // Fetch the updated order with all businesses to return
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_businesses(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching updated order:', orderError);
      // Return just the business we updated if we can't get the full order
      return { orderBusiness };
    }

    // Return the full updated order
    return order;
  } catch (error) {
    // Rollback the transaction on any error
    try {
      await supabaseAdmin.rpc('rollback_transaction', {
        transaction_id: transaction.transaction_id
      });
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }

    // Re-throw the original error
    throw error;
  }
}
