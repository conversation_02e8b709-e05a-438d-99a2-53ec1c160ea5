import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('🐛 DEBUG:', body.message, body.data || body.error || '')
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Debug log error:', error)
    return NextResponse.json({ error: 'Failed to log debug message' }, { status: 500 })
  }
}
