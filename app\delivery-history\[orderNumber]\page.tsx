"use client"

import { useState } from "react"
import React from "react"
import Link from "next/link"
import {
  ArrowLeft,
  MapPin,
  Phone,
  MessageSquare,
  CheckCircle,
  Package,
  Navigation,
  AlertTriangle,
  User,
  Building,
  ShoppingBag,
  Map,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import DriverLocationTracker from "@/components/driver-location-tracker"
import { simulateDriverMovement } from "@/services/location-service"

// Mock delivery data
const deliveryData = {
  id: "JE-5290",
  status: "delivered", // Show as delivered for history page
  restaurant: {
    name: "Jersey Seafood",
    address: "8 Pier Road, St Helier, JE2 4XW",
    phone: "+44 1534 123456",
    instructions: "Enter through the side door and ask for the manager",
    coordinates: [-2.1199, 49.1858] as [number, number], // [longitude, latitude]
  },
  customer: {
    name: "Sarah Johnson",
    address: "15 Beachfront, St Helier, JE2 3NG",
    phone: "+44 7911 123456",
    instructions: "Please leave at the door. The code for the gate is 1234.",
    coordinates: [-2.0935, 49.1805] as [number, number], // [longitude, latitude]
  },
  items: [
    { name: "Jersey Crab Cakes", quantity: 2, price: 9.95 },
    { name: "Grilled Sea Bass", quantity: 1, price: 22.95, notes: "Extra lemon on the side" },
  ],
  subtotal: 42.85,
  deliveryFee: 2.5,
  serviceFee: 0.5,
  total: 45.85,
  earnings: 7.25,
  estimatedTime: "25 min",
  distance: "3.2 km",
  orderTime: "Today at 14:32",
  pickupTime: "Today at 14:45",
  deliveryTime: "Today at 15:00",
}

export default function DeliveryHistoryDetailsPage({ params }: { params: { orderNumber: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)
  const [deliveryStatus, setDeliveryStatus] = useState(deliveryData.status)
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false)
  const [reportReason, setReportReason] = useState("")
  const [isMapDialogOpen, setIsMapDialogOpen] = useState(false)

  const getStatusBadge = () => {
    switch (deliveryStatus) {
      case "accepted":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Accepted</Badge>
      case "picked_up":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Picked Up</Badge>
      case "on_the_way":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">On The Way</Badge>
      case "delivered":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Delivered</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>
      default:
        return <Badge>Unknown</Badge>
    }
  }

  const getProgressValue = () => {
    switch (deliveryStatus) {
      case "accepted":
        return 25
      case "picked_up":
        return 50
      case "on_the_way":
        return 75
      case "delivered":
        return 100
      default:
        return 0
    }
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Link href="/delivery-history" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Delivery #{unwrappedParams.orderNumber}</h1>
            <div className="flex items-center mt-1">
              {getStatusBadge()}
              <span className="ml-3 text-sm text-gray-500">{deliveryData.orderTime}</span>
            </div>
          </div>
        </div>

        <div className="mt-4 md:mt-0 flex space-x-2">
          <Dialog open={isReportDialogOpen} onOpenChange={setIsReportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="text-amber-600 border-amber-200 hover:bg-amber-50">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Report Issue
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Report Delivery Issue</DialogTitle>
                <DialogDescription>Please describe the issue you experienced with this delivery.</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <RadioGroup value={reportReason} onValueChange={setReportReason}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="payment_issue" id="payment_issue" />
                    <Label htmlFor="payment_issue">Payment issue</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="customer_complaint" id="customer_complaint" />
                    <Label htmlFor="customer_complaint">Customer complaint</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="damaged_items" id="damaged_items" />
                    <Label htmlFor="damaged_items">Damaged items</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="wrong_address" id="wrong_address" />
                    <Label htmlFor="wrong_address">Wrong address</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="other" id="other" />
                    <Label htmlFor="other">Other</Label>
                  </div>
                </RadioGroup>
                <Textarea
                  placeholder="Please provide additional details about the issue..."
                  className="min-h-[100px]"
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsReportDialogOpen(false)}>
                  Cancel
                </Button>
                <Button className="bg-emerald-600 hover:bg-emerald-700">Submit Report</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={() => setIsMapDialogOpen(true)}>
            <Map className="mr-2 h-4 w-4" />
            View Route
          </Button>
        </div>
      </div>

      {/* Map Dialog */}
      <Dialog open={isMapDialogOpen} onOpenChange={setIsMapDialogOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh]">
          <DialogHeader>
            <DialogTitle>Delivery Route</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <div className="h-full">
              {/* We'll use the LiveDeliveryMap component here */}
              <div className="text-center py-12">
                <p className="text-gray-500">Loading map...</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delivery Progress */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="mb-4">
            <Progress value={getProgressValue()} className="h-2" />
          </div>
          <div className="grid grid-cols-4 text-center">
            <div className="space-y-2">
              <div className="mx-auto h-8 w-8 rounded-full flex items-center justify-center bg-emerald-100 text-emerald-600">
                <CheckCircle className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Accepted</p>
            </div>
            <div className="space-y-2">
              <div className="mx-auto h-8 w-8 rounded-full flex items-center justify-center bg-emerald-100 text-emerald-600">
                <Package className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Picked Up</p>
            </div>
            <div className="space-y-2">
              <div className="mx-auto h-8 w-8 rounded-full flex items-center justify-center bg-emerald-100 text-emerald-600">
                <Navigation className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">On The Way</p>
            </div>
            <div className="space-y-2">
              <div className="mx-auto h-8 w-8 rounded-full flex items-center justify-center bg-emerald-100 text-emerald-600">
                <CheckCircle className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Delivered</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Delivery Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Restaurant Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5 text-gray-500" />
                Restaurant Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{deliveryData.restaurant.name}</h3>
                  <div className="flex items-start mt-1">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600">{deliveryData.restaurant.address}</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <Phone className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-600">{deliveryData.restaurant.phone}</p>
                  </div>
                </div>

                {deliveryData.restaurant.instructions && (
                  <div className="p-3 bg-blue-50 rounded-md">
                    <p className="text-sm font-medium text-blue-800">Pickup Instructions:</p>
                    <p className="text-sm text-blue-700 mt-1">{deliveryData.restaurant.instructions}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Customer Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5 text-gray-500" />
                Customer Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{deliveryData.customer.name}</h3>
                  <div className="flex items-start mt-1">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600">{deliveryData.customer.address}</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <Phone className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-600">{deliveryData.customer.phone}</p>
                  </div>
                </div>

                {deliveryData.customer.instructions && (
                  <div className="p-3 bg-blue-50 rounded-md">
                    <p className="text-sm font-medium text-blue-800">Delivery Instructions:</p>
                    <p className="text-sm text-blue-700 mt-1">{deliveryData.customer.instructions}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Order Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <ShoppingBag className="mr-2 h-5 w-5 text-gray-500" />
                Order Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deliveryData.items.map((item, index) => (
                  <div key={index} className="flex justify-between pb-4 border-b last:border-0 last:pb-0">
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium">{item.quantity}×</span>
                        <span className="ml-2">{item.name}</span>
                      </div>
                      {item.notes && <div className="mt-1 text-sm text-gray-500">Note: {item.notes}</div>}
                    </div>
                    <div className="font-medium">£{(item.price * item.quantity).toFixed(2)}</div>
                  </div>
                ))}
              </div>

              <div className="mt-6 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>£{deliveryData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Fee</span>
                  <span>£{deliveryData.deliveryFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Service Fee</span>
                  <span>£{deliveryData.serviceFee.toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>£{deliveryData.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Delivery Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Delivery Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Distance</span>
                  <span className="font-medium">{deliveryData.distance}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Estimated Time</span>
                  <span className="font-medium">{deliveryData.estimatedTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Order Time</span>
                  <span className="font-medium">{deliveryData.orderTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Pickup Time</span>
                  <span className="font-medium">{deliveryData.pickupTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Delivery Time</span>
                  <span className="font-medium">{deliveryData.deliveryTime}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Your Earnings</span>
                  <span className="font-bold text-emerald-600">£{deliveryData.earnings.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Back to History Button */}
          <Link href="/delivery-history">
            <Button className="w-full">Back to Delivery History</Button>
          </Link>
        </div>
      </div>

      {/* Location Tracker for completed deliveries (hidden but still functional) */}
      <div className="hidden">
        <DriverLocationTracker />
      </div>
    </div>
  )
}
