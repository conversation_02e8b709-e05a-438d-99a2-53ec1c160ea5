"use client"

import { useState, useEffect, useRef, memo } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { getRoute } from "@/lib/map-utils"

// Define business colors for different markers
const BUSINESS_COLORS = [
  "#f97316", // Orange
  "#ef4444", // Red
  "#8b5cf6", // Purple
  "#3b82f6", // Blue
  "#14b8a6", // Teal
  "#f59e0b", // Amber
];

interface BusinessLocation {
  id: string;
  name: string;
  coordinates: [number, number];
  type?: string;
}

interface OSMMultiBusinessDeliveryMapProps {
  businesses: BusinessLocation[];
  customerLng: number;
  customerLat: number;
  height?: string;
  className?: string;
  interactive?: boolean;
  showRoutes?: boolean;
}

function OSMMultiBusinessDeliveryMap({
  businesses,
  customerLng,
  customerLat,
  height = "400px",
  className = "",
  interactive = true,
  showRoutes = true,
}: OSMMultiBusinessDeliveryMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const mapRef = useRef<L.Map | null>(null);
  const businessMarkersRef = useRef<L.Marker[]>([]);
  const customerMarkerRef = useRef<L.Marker | null>(null);
  const routesRef = useRef<L.Polyline[]>([]);
  const legendRef = useRef<HTMLDivElement | null>(null);

  // Default coordinates for Jersey (St Helier)
  const DEFAULT_JERSEY_COORDINATES: [number, number] = [-2.1037, 49.1805];

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return;
    if (businesses.length === 0) return;

    // Filter out businesses with invalid coordinates and use default for Jersey if needed
    const validBusinesses = businesses.filter(b => {
      const isValid = b.coordinates &&
        Array.isArray(b.coordinates) &&
        b.coordinates.length === 2 &&
        !isNaN(b.coordinates[0]) &&
        !isNaN(b.coordinates[1]);

      if (!isValid) {
        console.warn(`Invalid business coordinates for ${b.name}:`, b.coordinates);
      }

      return isValid;
    });

    // Validate customer coordinates
    const validCustomerLng = !isNaN(customerLng) ? customerLng : DEFAULT_JERSEY_COORDINATES[0];
    const validCustomerLat = !isNaN(customerLat) ? customerLat : DEFAULT_JERSEY_COORDINATES[1];

    // Calculate center point between all businesses and customer
    let centerLng, centerLat;

    if (validBusinesses.length === 0) {
      console.warn("No valid business coordinates found, using default Jersey coordinates");
      centerLng = DEFAULT_JERSEY_COORDINATES[0];
      centerLat = DEFAULT_JERSEY_COORDINATES[1];
    } else {
      try {
        const lngs = validBusinesses.map(b => b.coordinates[0]).concat(validCustomerLng);
        const lats = validBusinesses.map(b => b.coordinates[1]).concat(validCustomerLat);

        centerLng = lngs.reduce((sum, lng) => sum + lng, 0) / lngs.length;
        centerLat = lats.reduce((sum, lat) => sum + lat, 0) / lats.length;

        // Final validation to ensure we don't have NaN values
        if (isNaN(centerLng) || isNaN(centerLat)) {
          console.warn("Invalid center coordinates calculated, using default Jersey coordinates");
          centerLng = DEFAULT_JERSEY_COORDINATES[0];
          centerLat = DEFAULT_JERSEY_COORDINATES[1];
        }
      } catch (error) {
        console.error("Error calculating center coordinates:", error);
        centerLng = DEFAULT_JERSEY_COORDINATES[0];
        centerLat = DEFAULT_JERSEY_COORDINATES[1];
      }
    }

    // Create map instance - note that Leaflet uses [lat, lng] order, opposite of our [lng, lat]
    const map = L.map(mapContainer.current, {
      center: [centerLat, centerLng], // [lat, lng]
      zoom: 11,
      dragging: interactive,
      touchZoom: interactive,
      scrollWheelZoom: interactive,
      doubleClickZoom: interactive,
      boxZoom: interactive,
      keyboard: interactive,
      zoomControl: interactive,
    });

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    mapRef.current = map;

    return () => {
      // Clean up markers and routes
      businessMarkersRef.current.forEach(marker => marker.remove());
      businessMarkersRef.current = [];

      if (customerMarkerRef.current) {
        customerMarkerRef.current.remove();
        customerMarkerRef.current = null;
      }

      routesRef.current.forEach(route => route.remove());
      routesRef.current = [];

      if (legendRef.current && legendRef.current.parentNode) {
        legendRef.current.parentNode.removeChild(legendRef.current);
        legendRef.current = null;
      }

      map.remove();
      mapRef.current = null;
    };
  }, [businesses.length]); // Only run on mount and when businesses length changes

  // Add markers and routes when map is loaded or when props change
  useEffect(() => {
    if (!mapRef.current || businesses.length === 0) return;

    const map = mapRef.current;

    // Clean up previous markers and routes
    businessMarkersRef.current.forEach(marker => marker.remove());
    businessMarkersRef.current = [];

    if (customerMarkerRef.current) {
      customerMarkerRef.current.remove();
      customerMarkerRef.current = null;
    }

    routesRef.current.forEach(route => route.remove());
    routesRef.current = [];

    if (legendRef.current && legendRef.current.parentNode) {
      legendRef.current.parentNode.removeChild(legendRef.current);
      legendRef.current = null;
    }

    // Filter out businesses with invalid coordinates
    const validBusinesses = businesses.filter(b => {
      const isValid = b.coordinates &&
        Array.isArray(b.coordinates) &&
        b.coordinates.length === 2 &&
        !isNaN(b.coordinates[0]) &&
        !isNaN(b.coordinates[1]);

      if (!isValid) {
        console.warn(`Invalid business coordinates for markers - ${b.name}:`, b.coordinates);
      }

      return isValid;
    });

    if (validBusinesses.length === 0) {
      console.warn("No valid business coordinates found for markers");
      return;
    }

    // Validate customer coordinates
    const validCustomerLng = !isNaN(customerLng) ? customerLng : DEFAULT_JERSEY_COORDINATES[0];
    const validCustomerLat = !isNaN(customerLat) ? customerLat : DEFAULT_JERSEY_COORDINATES[1];

    // Create bounds to fit all markers
    const bounds = L.latLngBounds();

    // Create custom customer icon (green)
    const customerIcon = L.divIcon({
      html: `<div style="background-color: #10b981; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    // Add customer location marker
    try {
      const customerMarker = L.marker(
        [validCustomerLat, validCustomerLng],
        { icon: customerIcon }
      ).addTo(map);

      customerMarker.bindPopup("<div class='p-2'><p class='font-bold'>Delivery Location</p></div>");
      customerMarkerRef.current = customerMarker;

      bounds.extend([validCustomerLat, validCustomerLng]);
    } catch (error) {
      console.error("Error adding customer marker:", error);
    }

    // Add business location markers with different colors
    validBusinesses.forEach((business, index) => {
      try {
        const color = BUSINESS_COLORS[index % BUSINESS_COLORS.length];
        const businessType = business.type ? business.type.charAt(0).toUpperCase() + business.type.slice(1) : 'Business';

        // Final validation of business coordinates
        if (!business.coordinates || !Array.isArray(business.coordinates) || business.coordinates.length !== 2 ||
            isNaN(business.coordinates[0]) || isNaN(business.coordinates[1])) {
          console.warn(`Invalid coordinates for business ${business.name}, skipping marker`);
          return;
        }

        // Create custom business icon
        const businessIcon = L.divIcon({
          html: `<div style="background-color: ${color}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                    <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                    <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                    <path d="M12 21v-9"></path>
                  </svg>
                </div>`,
          className: '',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });

        // Add business marker
        const businessMarker = L.marker(
          [business.coordinates[1], business.coordinates[0]],
          { icon: businessIcon }
        ).addTo(map);

        businessMarker.bindPopup(`
          <div class="p-2">
            <p class="font-bold">${business.name}</p>
            <p class="text-sm text-gray-600">${businessType}</p>
          </div>
        `);

        businessMarkersRef.current.push(businessMarker);
        bounds.extend([business.coordinates[1], business.coordinates[0]]);

        // Add route if requested
        if (showRoutes) {
          const fetchAndDisplayRoute = async () => {
            try {
              const route = await getRoute(
                business.coordinates[0],
                business.coordinates[1],
                validCustomerLng,
                validCustomerLat
              );

              if (route && mapRef.current) {
                // Create a polyline from the route coordinates
                const routeCoordinates = route.geometry.coordinates.map(
                  (coord: [number, number]) => [coord[1], coord[0]] // Convert [lng, lat] to [lat, lng] for Leaflet
                );

                const polyline = L.polyline(routeCoordinates, {
                  color: color,
                  weight: 4,
                  opacity: 0.75
                }).addTo(mapRef.current);

                routesRef.current.push(polyline);
              }
            } catch (error) {
              console.error(`Error fetching route for ${business.name}:`, error);

              // Fallback to simple line if route fetching fails
              if (mapRef.current) {
                const polyline = L.polyline(
                  [
                    [business.coordinates[1], business.coordinates[0]],
                    [validCustomerLat, validCustomerLng]
                  ],
                  { color: color, weight: 4, opacity: 0.75 }
                ).addTo(mapRef.current);

                routesRef.current.push(polyline);
              }
            }
          };

          fetchAndDisplayRoute();
        }
      } catch (error) {
        console.error(`Error setting up business marker for ${business.name}:`, error);
      }
    });

    // Fit map to include all markers
    try {
      // Check if bounds are valid by checking if they have north/south/east/west properties
      if (bounds.getNorth && bounds.getSouth && bounds.getEast && bounds.getWest &&
          bounds.getNorth() !== bounds.getSouth() && bounds.getEast() !== bounds.getWest()) {
        map.fitBounds(bounds, {
          padding: [80, 80],
          maxZoom: 14,
        });
      } else {
        console.warn("Empty or invalid bounds, cannot fit map to markers");
      }
    } catch (error) {
      console.error("Error fitting bounds:", error);
    }

    // Add a legend for multiple businesses
    if (validBusinesses.length > 1 && mapContainer.current) {
      try {
        // Create legend
        const legend = document.createElement('div');
        legend.className = 'absolute bottom-2 left-2 bg-white p-2 rounded-md shadow-md max-w-xs text-xs z-[1000]';
        legend.style.zIndex = '1000';

        // Add legend title
        const title = document.createElement('div');
        title.className = 'font-bold mb-1 text-sm';
        title.textContent = 'Businesses';
        legend.appendChild(title);

        // Add business entries
        validBusinesses.forEach((business, index) => {
          const color = BUSINESS_COLORS[index % BUSINESS_COLORS.length];

          const item = document.createElement('div');
          item.className = 'flex items-center mb-1';

          const colorBox = document.createElement('div');
          colorBox.className = 'w-3 h-3 mr-2 rounded-sm';
          colorBox.style.backgroundColor = color;

          const name = document.createElement('span');
          name.textContent = business.name;
          name.className = 'truncate';

          item.appendChild(colorBox);
          item.appendChild(name);
          legend.appendChild(item);
        });

        // Add customer marker to legend
        const customerItem = document.createElement('div');
        customerItem.className = 'flex items-center mt-2 pt-2 border-t border-gray-200';

        const customerColorBox = document.createElement('div');
        customerColorBox.className = 'w-3 h-3 mr-2 rounded-sm';
        customerColorBox.style.backgroundColor = '#10b981'; // Emerald green

        const customerName = document.createElement('span');
        customerName.textContent = 'Delivery Location';

        customerItem.appendChild(customerColorBox);
        customerItem.appendChild(customerName);
        legend.appendChild(customerItem);

        // Add legend to map container
        mapContainer.current.appendChild(legend);
        legendRef.current = legend;
      } catch (error) {
        console.error("Error creating legend:", error);
      }
    }

  }, [businesses, customerLng, customerLat, showRoutes]);

  return (
    <div
      ref={mapContainer}
      style={{ height }}
      className={`w-full relative ${className}`}
    />
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(OSMMultiBusinessDeliveryMap);
