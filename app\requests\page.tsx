"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Building2, Lightbulb, MapPin, Truck, Globe, TrendingUp, Users, Heart } from "lucide-react"
import Link from "next/link"

export default function RequestsPage() {
  const requestTypes = [
    {
      href: '/requests/businesses',
      title: 'Request Jersey Businesses',
      description: 'Help us bring your favorite local Jersey businesses to Loop',
      icon: Building2,
      color: 'blue',
      stats: { total: 4, votes: 52, topVotes: 16 }
    },
    {
      href: '/requests/features',
      title: 'Request Features',
      description: 'Suggest new features and improvements for the Loop app',
      icon: Lightbulb,
      color: 'yellow',
      stats: { total: 10, votes: 153, topVotes: 35 }
    },
    {
      href: '/requests/areas',
      title: 'Request Delivery Areas',
      description: 'Request delivery coverage in new parishes and areas',
      icon: MapPin,
      color: 'green',
      stats: { total: 10, votes: 81, topVotes: 19 }
    },
    {
      href: '/requests/services',
      title: 'Request Services',
      description: 'Suggest new types of delivery services like grocery, pharmacy, etc.',
      icon: Truck,
      color: 'purple',
      stats: { total: 10, votes: 169, topVotes: 28 }
    },
    {
      href: '/requests/non-jersey-businesses',
      title: 'Request International Chains',
      description: 'Request popular chains and brands not yet in Jersey',
      icon: Globe,
      color: 'indigo',
      stats: { total: 6, votes: 155, topVotes: 35 }
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'from-blue-600 to-blue-700',
        card: 'border-blue-200 hover:border-blue-300',
        button: 'bg-blue-600 hover:bg-blue-700',
        icon: 'text-blue-600',
        stats: 'text-blue-600'
      },
      yellow: {
        bg: 'from-yellow-500 to-yellow-600',
        card: 'border-yellow-200 hover:border-yellow-300',
        button: 'bg-yellow-500 hover:bg-yellow-600',
        icon: 'text-yellow-600',
        stats: 'text-yellow-600'
      },
      green: {
        bg: 'from-green-600 to-green-700',
        card: 'border-green-200 hover:border-green-300',
        button: 'bg-green-600 hover:bg-green-700',
        icon: 'text-green-600',
        stats: 'text-green-600'
      },
      purple: {
        bg: 'from-purple-600 to-purple-700',
        card: 'border-purple-200 hover:border-purple-300',
        button: 'bg-purple-600 hover:bg-purple-700',
        icon: 'text-purple-600',
        stats: 'text-purple-600'
      },
      gray: {
        bg: 'from-gray-600 to-gray-700',
        card: 'border-gray-200 hover:border-gray-300',
        button: 'bg-gray-600 hover:bg-gray-700',
        icon: 'text-gray-600',
        stats: 'text-gray-600'
      },
      indigo: {
        bg: 'from-indigo-600 to-indigo-700',
        card: 'border-indigo-200 hover:border-indigo-300',
        button: 'bg-indigo-600 hover:bg-indigo-700',
        icon: 'text-indigo-600',
        stats: 'text-indigo-600'
      }
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Shape Loop's Future</h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Your voice matters! Request businesses, features, and services to help us build the platform Jersey needs.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Request Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {requestTypes.map((type) => {
            const Icon = type.icon
            const colors = getColorClasses(type.color)

            return (
              <Card key={type.href} className={`transition-all duration-200 hover:shadow-lg ${colors.card}`}>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`p-3 rounded-lg bg-gray-50`}>
                      <Icon className={`h-6 w-6 ${colors.icon}`} />
                    </div>
                    <CardTitle className="text-xl">{type.title}</CardTitle>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {type.description}
                  </p>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <TrendingUp className={`h-4 w-4 ${colors.stats}`} />
                      </div>
                      <div className="text-lg font-bold">{type.stats.total}</div>
                      <div className="text-xs text-gray-600">Requests</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className={`h-4 w-4 ${colors.stats}`} />
                      </div>
                      <div className="text-lg font-bold">{type.stats.votes}</div>
                      <div className="text-xs text-gray-600">Total Votes</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Heart className={`h-4 w-4 ${colors.stats}`} />
                      </div>
                      <div className="text-lg font-bold">{type.stats.topVotes}</div>
                      <div className="text-xs text-gray-600">Top Votes</div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <Link href={type.href}>
                    <Button className={`w-full ${colors.button} text-white`}>
                      View & Vote
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* How It Works Section */}
        <div className="mt-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-center">How It Works</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 font-bold text-lg">1</span>
                  </div>
                  <h3 className="font-semibold mb-2">Make a Request</h3>
                  <p className="text-gray-600 text-sm">
                    Submit your ideas for businesses, features, or services you'd like to see on Loop Jersey.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 font-bold text-lg">2</span>
                  </div>
                  <h3 className="font-semibold mb-2">Community Votes</h3>
                  <p className="text-gray-600 text-sm">
                    The community votes on requests to show which ones are most wanted.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 font-bold text-lg">3</span>
                  </div>
                  <h3 className="font-semibold mb-2">We Take Action</h3>
                  <p className="text-gray-600 text-sm">
                    We prioritize the most popular requests and work to make them happen.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
