import { NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { adminClient } from '@/lib/supabase-admin';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Test both client and admin connections
    const clientResult = await testClientConnection();
    const adminResult = await testAdminConnection();
    const adminClientResult = await testAdminClientConnection();

    // Get environment info
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? "Set" : "Not set",
      NEXT_PUBLIC_SUPABASE_URL_VALUE: process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "Set" : "Not set",
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? "Set" : "Not set"
    };

    // Create a direct client for testing
    const directClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    // Test direct client
    const directClientResult = await testDirectClient(directClient);

    return NextResponse.json({
      status: "API is running",
      timestamp: new Date().toISOString(),
      environment: envInfo,
      clientConnection: clientResult,
      adminConnection: adminResult,
      adminClientConnection: adminClientResult,
      directClientConnection: directClientResult,
      ipAddress: await getIpAddress()
    });
  } catch (error) {
    console.error('Error in test-connection API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}

async function testClientConnection() {
  try {
    // Test basic connection with regular client
    const { data: connectionTest, error: connectionError } = await supabase
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });

    if (connectionError) {
      return {
        success: false,
        error: connectionError.message,
        details: connectionError
      };
    }

    // Test business types table
    const { data: typesData, error: typesError } = await supabase
      .from('business_types')
      .select('*')
      .limit(5);

    return {
      success: true,
      businessCount: connectionTest,
      businessTypes: typesData || [],
      businessTypesError: typesError ? typesError.message : null
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error'
    };
  }
}

async function testAdminConnection() {
  try {
    // Test basic connection with admin client
    const { data: connectionTest, error: connectionError } = await supabaseAdmin
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });

    if (connectionError) {
      return {
        success: false,
        error: connectionError.message,
        details: connectionError
      };
    }

    // Test business types table
    const { data: typesData, error: typesError } = await supabaseAdmin
      .from('business_types')
      .select('*')
      .limit(5);

    return {
      success: true,
      businessCount: connectionTest,
      businessTypes: typesData || [],
      businessTypesError: typesError ? typesError.message : null
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error'
    };
  }
}

async function testAdminClientConnection() {
  try {
    // Test basic connection with admin client from supabase-admin.ts
    const { data: connectionTest, error: connectionError } = await adminClient
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });

    if (connectionError) {
      return {
        success: false,
        error: connectionError.message,
        details: connectionError
      };
    }

    // Test users table
    const { data: usersData, error: usersError } = await adminClient
      .from('users')
      .select('*')
      .limit(5);

    return {
      success: true,
      businessCount: connectionTest,
      users: usersData || [],
      usersError: usersError ? usersError.message : null
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error'
    };
  }
}

async function testDirectClient(client) {
  try {
    // Test basic connection with direct client
    const { data: connectionTest, error: connectionError } = await client
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });

    if (connectionError) {
      return {
        success: false,
        error: connectionError.message,
        details: connectionError
      };
    }

    // Test users table
    const { data: usersData, error: usersError } = await client
      .from('users')
      .select('*')
      .limit(5);

    return {
      success: true,
      businessCount: connectionTest,
      users: usersData || [],
      usersError: usersError ? usersError.message : null
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error'
    };
  }
}

async function getIpAddress() {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.error('Error getting IP address:', error);
    return 'Unknown';
  }
}
