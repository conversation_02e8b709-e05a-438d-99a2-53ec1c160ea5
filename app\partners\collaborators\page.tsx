import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function CollaboratorsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Collaborate with Jersey Eats</h1>
        
        <div className="grid gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Marketing Partnerships</CardTitle>
              <CardDescription>Reach our growing customer base</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Partner with Jersey Eats for marketing collaborations that benefit both our brands. We're always looking for innovative ways to enhance the customer experience and provide added value through strategic partnerships.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Technology Integration</CardTitle>
              <CardDescription>Connect your services with our platform</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Are you a technology provider with solutions that could enhance our delivery ecosystem? We're interested in integrating with complementary services that can improve the experience for our customers, restaurants, and riders.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Community Initiatives</CardTitle>
              <CardDescription>Join forces for local impact</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Jersey Eats is committed to supporting the local community. We welcome partnerships with organizations focused on sustainability, food security, and other community initiatives that align with our values.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Get in Touch</CardTitle>
              <CardDescription>Let's discuss collaboration opportunities</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Interested in exploring a partnership with Jersey Eats? Fill out the form below with details about your organization and your collaboration idea, and our partnerships team will get back to you.</p>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-emerald-600 hover:bg-emerald-700">Contact Our Partnerships Team</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
