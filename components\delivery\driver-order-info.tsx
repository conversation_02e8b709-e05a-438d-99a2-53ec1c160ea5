// PHASE 6 STEP 13: Driver Order Information Component
// Shows order requirements to drivers

"use client"

import { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Package, Thermometer, Clock, MapPin, DollarSign, AlertTriangle } from 'lucide-react';

interface DriverOrderInfo {
  orderId: number;
  totalItems: number;
  requiresThermal: boolean;
  isLargeOrder: boolean;
  orderValue: number;
  businessName: string;
  estimatedPreparationTime: number;
  deliveryAddress: string;
}

interface DriverOrderInfoProps {
  orderId: number;
  className?: string;
}

export function DriverOrderInfo({ orderId, className }: DriverOrderInfoProps) {
  const [orderInfo, setOrderInfo] = useState<DriverOrderInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOrderInfo();
  }, [orderId]);

  const fetchOrderInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/delivery-metrics?type=driver&orderId=${orderId}`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setOrderInfo(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching driver order info:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 animate-pulse" />
            <span className="text-sm text-muted-foreground">Loading order details...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!orderInfo) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Order details unavailable</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <div className="flex items-center">
            <Package className="h-4 w-4 mr-2" />
            Order #{orderInfo.orderId}
          </div>
          {orderInfo.isLargeOrder && (
            <Badge variant="secondary" className="text-xs">
              Large Order
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Business Information */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">{orderInfo.businessName}</h4>
          <div className="flex items-center space-x-2">
            <MapPin className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {orderInfo.deliveryAddress}
            </span>
          </div>
        </div>

        {/* Order Requirements */}
        <div className="space-y-3 pt-2 border-t">
          <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            Requirements
          </h5>
          
          {/* Items Count */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Total items</span>
            </div>
            <Badge variant="outline">
              {orderInfo.totalItems}
            </Badge>
          </div>

          {/* Thermal Requirement */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Thermometer className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Thermal bag</span>
            </div>
            <Badge variant={orderInfo.requiresThermal ? "default" : "secondary"}>
              {orderInfo.requiresThermal ? "Required" : "Not needed"}
            </Badge>
          </div>

          {/* Order Value */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Order value</span>
            </div>
            <span className="text-sm font-medium">
              £{orderInfo.orderValue.toFixed(2)}
            </span>
          </div>

          {/* Preparation Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Prep time</span>
            </div>
            <span className="text-sm font-medium">
              {orderInfo.estimatedPreparationTime} min
            </span>
          </div>
        </div>

        {/* Special Requirements Alert */}
        {(orderInfo.requiresThermal || orderInfo.isLargeOrder) && (
          <div className="pt-2 border-t">
            <div className="flex items-start space-x-2 p-2 bg-amber-50 rounded-md">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="space-y-1">
                <p className="text-xs font-medium text-amber-800">Special Requirements</p>
                <ul className="text-xs text-amber-700 space-y-0.5">
                  {orderInfo.requiresThermal && (
                    <li>• Thermal bag required for hot/cold items</li>
                  )}
                  {orderInfo.isLargeOrder && (
                    <li>• Large order - check vehicle capacity</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Driver Tips */}
        <div className="pt-2 border-t">
          <p className="text-xs text-blue-600">
            💡 Contact the business if you need clarification on special requirements
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
