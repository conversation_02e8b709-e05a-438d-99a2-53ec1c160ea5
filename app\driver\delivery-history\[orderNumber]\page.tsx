"use client"

import { useState, useEffect } from "react"
import React from "react"
import Link from "next/link"
import {
  ArrowLeft,
  MapPin,
  Phone,
  MessageSquare,
  CheckCircle,
  Package,
  Navigation,
  AlertTriangle,
  User,
  Building,
  ShoppingBag,
  Map,
  Loader2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import DriverLocationTracker from "@/components/driver-location-tracker"
import { simulateDriverMovement } from "@/services/location-service"

// Interface for delivery data
interface DeliveryData {
  id: number
  order_number: string
  status: string
  business_name: string
  customer_name: string
  customer_phone: string
  customer_email?: string
  delivery_address: string
  postcode: string
  parish?: string
  delivery_instructions?: string
  delivery_fee: number
  service_fee: number
  subtotal: number
  total: number
  created_at: string
  ready_time?: string
  estimated_delivery_time?: string
  delivery_distance_km?: number
  businesses: {
    id: number
    name: string
    address: string
    postcode: string
    phone: string
  }
  items?: Array<{
    id: number
    product_id: number
    quantity: number
    unit_price: number
    total_price: number
    special_instructions?: string
    products: {
      id: number
      name: string
      description?: string
      image_url?: string
    }
  }>
}

export default function DeliveryHistoryDetailsPage({ params }: { params: { orderNumber: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)
  const [deliveryData, setDeliveryData] = useState<DeliveryData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false)
  const [reportReason, setReportReason] = useState("")
  const [isMapDialogOpen, setIsMapDialogOpen] = useState(false)
  const [isSimulating, setIsSimulating] = useState(false)

  // Fetch delivery data from API
  useEffect(() => {
    const fetchDeliveryData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/driver/deliveries/${unwrappedParams.orderNumber}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch delivery data: ${response.statusText}`)
        }

        const result = await response.json()

        if (!result.success || !result.order) {
          throw new Error('Invalid response format')
        }

        // Transform the API response to match our component's expected format
        const transformedData: DeliveryData = {
          id: result.order.id,
          order_number: result.order.orderNumber,
          status: result.order.status,
          business_name: result.order.business.name,
          customer_name: result.order.customer.name,
          customer_phone: result.order.customer.phone,
          customer_email: result.order.customer.email,
          delivery_address: result.order.customer.deliveryAddress,
          postcode: result.order.customer.postcode,
          parish: result.order.customer.parish,
          delivery_instructions: result.order.customer.instructions,
          delivery_fee: result.order.pricing.deliveryFee,
          service_fee: result.order.pricing.serviceFee,
          subtotal: result.order.pricing.subtotal,
          total: result.order.pricing.total,
          created_at: result.order.timing.createdAt,
          ready_time: result.order.timing.readyTime,
          estimated_delivery_time: result.order.timing.estimatedDeliveryTime,
          delivery_distance_km: result.order.delivery.distance,
          businesses: {
            id: result.order.business.id,
            name: result.order.business.name,
            address: result.order.business.address,
            postcode: result.order.business.postcode,
            phone: result.order.business.phone,
          },
          items: result.order.items?.map((item: any) => ({
            id: item.id,
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            total_price: item.totalPrice,
            special_instructions: item.specialInstructions,
            products: {
              id: item.productId,
              name: item.name,
              description: item.description,
              image_url: item.imageUrl,
            }
          })) || [],
          earnings: result.order.pricing.deliveryFee * 0.8, // Calculate driver earnings (80% of delivery fee)
        }

        setDeliveryData(transformedData)
      } catch (err) {
        console.error('Error fetching delivery data:', err)
        setError(err instanceof Error ? err.message : 'Failed to load delivery data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDeliveryData()
  }, [unwrappedParams.orderNumber])

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-500">Loading delivery details...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !deliveryData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error || 'Delivery not found'}</p>
          <Link href="/driver/delivery-history">
            <Button variant="outline">Back to Delivery History</Button>
          </Link>
        </div>
      </div>
    )
  }

  const updateDeliveryStatus = async (newStatus: string) => {
    if (!deliveryData) return

    try {
      // Update status in database using the driver-specific endpoint
      const response = await fetch('/api/driver/update-delivery-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: deliveryData.id,
          status: newStatus
        }),
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // Update local state
          setDeliveryData(prev => prev ? { ...prev, status: newStatus } : null)
        } else {
          console.error('Failed to update status:', result.error)
        }
      } else {
        console.error('Failed to update status:', response.statusText)
      }
    } catch (error) {
      console.error('Error updating delivery status:', error)
    }
  }

  const getStatusBadge = () => {
    if (!deliveryData) return <Badge>Unknown</Badge>

    switch (deliveryData.status) {
      case "accepted":
      case "confirmed":
      case "assigned":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Accepted</Badge>
      case "picked_up":
      case "pickup_confirmed":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Picked Up</Badge>
      case "on_the_way":
      case "out_for_delivery":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">On The Way</Badge>
      case "delivered":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Delivered</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>
      case "ready":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Ready</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{deliveryData.status}</Badge>
    }
  }

  const getProgressValue = () => {
    if (!deliveryData) return 0

    switch (deliveryData.status) {
      case "accepted":
      case "confirmed":
      case "assigned":
        return 25
      case "ready":
        return 40
      case "picked_up":
      case "pickup_confirmed":
        return 50
      case "on_the_way":
      case "out_for_delivery":
        return 75
      case "delivered":
        return 100
      default:
        return 0
    }
  }

  const getNextActionButton = () => {
    if (!deliveryData) return null

    switch (deliveryData.status) {
      case "accepted":
      case "confirmed":
      case "ready":
      case "assigned":
        return (
          <Button
            className="w-full bg-emerald-600 hover:bg-emerald-700"
            onClick={() => updateDeliveryStatus("out_for_delivery")}
          >
            <Package className="mr-2 h-4 w-4" />
            Confirm Pickup & Start Delivery
          </Button>
        )
      case "on_the_way":
      case "out_for_delivery":
        return (
          <Button
            className="w-full bg-emerald-600 hover:bg-emerald-700"
            onClick={() => updateDeliveryStatus("delivered")}
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Complete Delivery
          </Button>
        )
      case "delivered":
        return (
          <Link href="/driver/delivery-history">
            <Button className="w-full">Back to Delivery History</Button>
          </Link>
        )
      default:
        return (
          <Link href="/driver/delivery-history">
            <Button variant="outline" className="w-full">Back to Delivery History</Button>
          </Link>
        )
    }
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Link href="/driver/delivery-history" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Delivery #{unwrappedParams.orderNumber}</h1>
            <div className="flex items-center mt-1">
              {getStatusBadge()}
              <span className="ml-3 text-sm text-gray-500">
                {new Date(deliveryData.created_at).toLocaleDateString('en-GB', {
                  day: 'numeric',
                  month: 'short',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>

        <div className="mt-4 md:mt-0 flex space-x-2">
          <Dialog open={isReportDialogOpen} onOpenChange={setIsReportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="text-amber-600 border-amber-200 hover:bg-amber-50">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Report Issue
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Report Delivery Issue</DialogTitle>
                <DialogDescription>Please describe the issue you're experiencing with this delivery.</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <RadioGroup value={reportReason} onValueChange={setReportReason}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="restaurant_closed" id="restaurant_closed" />
                    <Label htmlFor="restaurant_closed">Restaurant is closed</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="order_not_ready" id="order_not_ready" />
                    <Label htmlFor="order_not_ready">Order not ready</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="customer_unavailable" id="customer_unavailable" />
                    <Label htmlFor="customer_unavailable">Customer unavailable</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="address_issue" id="address_issue" />
                    <Label htmlFor="address_issue">Address issue</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="other" id="other" />
                    <Label htmlFor="other">Other</Label>
                  </div>
                </RadioGroup>
                <Textarea
                  placeholder="Please provide additional details about the issue..."
                  className="min-h-[100px]"
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsReportDialogOpen(false)}>
                  Cancel
                </Button>
                <Button className="bg-emerald-600 hover:bg-emerald-700">Submit Report</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={() => setIsMapDialogOpen(true)}>
            <Map className="mr-2 h-4 w-4" />
            View Map
          </Button>

          {deliveryData.status !== "delivered" && deliveryData.status !== "cancelled" && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                  Cancel Delivery
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Cancel Delivery</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to cancel this delivery? This action cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button variant="outline">No, Keep Delivery</Button>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      updateDeliveryStatus("cancelled")
                      // Close dialog
                    }}
                  >
                    Yes, Cancel Delivery
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* Map Dialog */}
      <Dialog open={isMapDialogOpen} onOpenChange={setIsMapDialogOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh]">
          <DialogHeader>
            <DialogTitle>Delivery Route</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <div className="h-full">
              {/* We'll use the LiveDeliveryMap component here */}
              <div className="text-center py-12">
                <p className="text-gray-500">Loading map...</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delivery Progress */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="mb-4">
            <Progress value={getProgressValue()} className="h-2" />
          </div>
          <div className="grid grid-cols-4 text-center">
            <div className="space-y-2">
              <div
                className={`mx-auto h-8 w-8 rounded-full flex items-center justify-center ${
                  ["accepted", "confirmed", "assigned", "ready", "picked_up", "pickup_confirmed", "on_the_way", "out_for_delivery", "delivered"].includes(deliveryData.status)
                    ? "bg-emerald-100 text-emerald-600"
                    : "bg-gray-200 text-gray-500"
                }`}
              >
                <CheckCircle className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Accepted</p>
            </div>
            <div className="space-y-2">
              <div
                className={`mx-auto h-8 w-8 rounded-full flex items-center justify-center ${
                  ["picked_up", "pickup_confirmed", "on_the_way", "out_for_delivery", "delivered"].includes(deliveryData.status)
                    ? "bg-emerald-100 text-emerald-600"
                    : "bg-gray-200 text-gray-500"
                }`}
              >
                <Package className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Picked Up</p>
            </div>
            <div className="space-y-2">
              <div
                className={`mx-auto h-8 w-8 rounded-full flex items-center justify-center ${
                  ["on_the_way", "out_for_delivery", "delivered"].includes(deliveryData.status)
                    ? "bg-emerald-100 text-emerald-600"
                    : "bg-gray-200 text-gray-500"
                }`}
              >
                <Navigation className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">On The Way</p>
            </div>
            <div className="space-y-2">
              <div
                className={`mx-auto h-8 w-8 rounded-full flex items-center justify-center ${
                  deliveryData.status === "delivered" ? "bg-emerald-100 text-emerald-600" : "bg-gray-200 text-gray-500"
                }`}
              >
                <CheckCircle className="h-4 w-4" />
              </div>
              <p className="text-xs font-medium">Delivered</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Delivery Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Restaurant Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5 text-gray-500" />
                Restaurant Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{deliveryData.businesses.name}</h3>
                  <div className="flex items-start mt-1">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600">{deliveryData.businesses.address}</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <Phone className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-600">{deliveryData.businesses.phone}</p>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1">
                    <Navigation className="mr-2 h-4 w-4" />
                    Navigate
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Phone className="mr-2 h-4 w-4" />
                    Call Restaurant
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5 text-gray-500" />
                Customer Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{deliveryData.customer_name}</h3>
                  <div className="flex items-start mt-1">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600">{deliveryData.delivery_address}</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <Phone className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-600">{deliveryData.customer_phone}</p>
                  </div>
                </div>

                {deliveryData.delivery_instructions && (
                  <div className="p-3 bg-blue-50 rounded-md">
                    <p className="text-sm font-medium text-blue-800">Delivery Instructions:</p>
                    <p className="text-sm text-blue-700 mt-1">{deliveryData.delivery_instructions}</p>
                  </div>
                )}

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1">
                    <Navigation className="mr-2 h-4 w-4" />
                    Navigate
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Phone className="mr-2 h-4 w-4" />
                    Call Customer
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Message
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Details */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <ShoppingBag className="mr-2 h-5 w-5 text-gray-500" />
                Order Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deliveryData.items && deliveryData.items.length > 0 ? (
                  deliveryData.items.map((item, index) => (
                    <div key={item.id || index} className="flex justify-between pb-4 border-b last:border-0 last:pb-0">
                      <div>
                        <div className="flex items-center">
                          <span className="font-medium">{item.quantity}×</span>
                          <span className="ml-2">{item.products.name}</span>
                        </div>
                        {item.special_instructions && (
                          <div className="mt-1 text-sm text-gray-500">Note: {item.special_instructions}</div>
                        )}
                      </div>
                      <div className="font-medium">£{item.total_price.toFixed(2)}</div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    Order items not available
                  </div>
                )}
              </div>

              <div className="mt-6 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>£{deliveryData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Fee</span>
                  <span>£{deliveryData.delivery_fee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Service Fee</span>
                  <span>£{deliveryData.service_fee.toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>£{deliveryData.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Delivery Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Order ID</span>
                  <span className="font-medium">{deliveryData.order_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Order Time</span>
                  <span className="font-medium">
                    {new Date(deliveryData.created_at).toLocaleDateString('en-GB', {
                      day: 'numeric',
                      month: 'short',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
                {deliveryData.ready_time && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Ready Time</span>
                    <span className="font-medium">
                      {new Date(deliveryData.ready_time).toLocaleDateString('en-GB', {
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                )}
                {deliveryData.estimated_delivery_time && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Estimated Delivery</span>
                    <span className="font-medium">
                      {new Date(deliveryData.estimated_delivery_time).toLocaleDateString('en-GB', {
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                )}
                <Separator />
                {deliveryData.delivery_distance_km && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Distance</span>
                    <span className="font-medium">{deliveryData.delivery_distance_km.toFixed(1)} km</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Your Earnings</span>
                  <span className="text-xl font-bold text-emerald-600">
                    £{(deliveryData.earnings || (deliveryData.delivery_fee * 0.8)).toFixed(2)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Tracking */}
          {(deliveryData.status === "picked_up" || deliveryData.status === "on_the_way") && (
            <DriverLocationTracker
              driverId="driver-1"
              orderId={unwrappedParams.orderNumber}
              destinationLat={deliveryData.customer.coordinates[1]}
              destinationLng={deliveryData.customer.coordinates[0]}
            />
          )}

          <Card>
            <CardContent className="p-6">{getNextActionButton()}</CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
