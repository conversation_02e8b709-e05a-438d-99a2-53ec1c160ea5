import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // For now, we'll implement basic protection
  // In a full implementation, you'd check the user's driver status here
  
  const { pathname } = request.nextUrl
  
  // Allow login page
  if (pathname === '/driver/login') {
    return NextResponse.next()
  }
  
  // For other driver pages, we'll let the page components handle the auth check
  // This is because we need to check both authentication and driver verification
  return NextResponse.next()
}

export const config = {
  matcher: '/driver/:path*'
}
