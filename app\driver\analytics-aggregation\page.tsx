"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  Star,
  Package,
  Clock,
  DollarSign,
  Users,
  Trophy,
  MapPin,
  Zap,
  Target,
  Calendar,
  Fuel,
  Heart,
  AlertTriangle,
  CheckCircle,
  Filter,
  Info,
} from "lucide-react"
import { useState } from "react"

// Sample data with anonymized driver names
const driverRankings = [
  {
    name: "Driver 1",
    rating: 4.9,
    deliveries: 156,
    earnings: 2840,
    avatar: "/placeholder.svg?height=40&width=40",
    efficiency: 95,
    customerSatisfaction: 98,
    streak: 12,
    badge: "Elite",
  },
  {
    name: "Driver 2",
    rating: 4.8,
    deliveries: 142,
    earnings: 2650,
    avatar: "/placeholder.svg?height=40&width=40",
    efficiency: 92,
    customerSatisfaction: 94,
    streak: 8,
    badge: "Pro",
  },
  {
    name: "Driver 3",
    rating: 4.7,
    deliveries: 138,
    earnings: 2580,
    avatar: "/placeholder.svg?height=40&width=40",
    efficiency: 88,
    customerSatisfaction: 91,
    streak: 5,
    badge: "Pro",
  },
  {
    name: "Driver 4",
    rating: 4.6,
    deliveries: 134,
    earnings: 2420,
    avatar: "/placeholder.svg?height=40&width=40",
    efficiency: 85,
    customerSatisfaction: 89,
    streak: 3,
    badge: "Standard",
  },
  {
    name: "Driver 5",
    rating: 4.5,
    deliveries: 128,
    earnings: 2380,
    avatar: "/placeholder.svg?height=40&width=40",
    efficiency: 82,
    customerSatisfaction: 87,
    streak: 1,
    badge: "Standard",
  },
]

const deliveryComparison = [
  { driver: "Driver 1", deliveries: 156, completionRate: 98, tips: 340 },
  { driver: "Driver 2", deliveries: 142, completionRate: 96, tips: 320 },
  { driver: "Driver 3", deliveries: 138, completionRate: 94, tips: 295 },
  { driver: "Driver 4", deliveries: 134, completionRate: 92, tips: 280 },
  { driver: "Driver 5", deliveries: 128, completionRate: 90, tips: 265 },
  { driver: "Driver 6", deliveries: 122, completionRate: 95, tips: 250 },
]

const hourlyPerformance = [
  { hour: "6AM", orders: 8, earnings: 45, avgTime: 25, satisfaction: 4.2 },
  { hour: "8AM", orders: 18, earnings: 95, avgTime: 20, satisfaction: 4.4 },
  { hour: "10AM", orders: 28, earnings: 145, avgTime: 19, satisfaction: 4.6 },
  { hour: "12PM", orders: 42, earnings: 225, avgTime: 16, satisfaction: 4.8 },
  { hour: "2PM", orders: 32, earnings: 175, avgTime: 20, satisfaction: 4.6 },
  { hour: "4PM", orders: 30, earnings: 165, avgTime: 21, satisfaction: 4.6 },
  { hour: "6PM", orders: 55, earnings: 295, avgTime: 18, satisfaction: 4.8 },
  { hour: "8PM", orders: 62, earnings: 335, avgTime: 19, satisfaction: 4.8 },
  { hour: "10PM", orders: 35, earnings: 195, avgTime: 23, satisfaction: 4.6 },
]

const weeklyTrends = [
  { day: "Mon", deliveries: 45, earnings: 285, efficiency: 88 },
  { day: "Tue", deliveries: 52, earnings: 325, efficiency: 92 },
  { day: "Wed", deliveries: 48, earnings: 295, efficiency: 90 },
  { day: "Thu", deliveries: 55, earnings: 345, efficiency: 94 },
  { day: "Fri", deliveries: 68, earnings: 425, efficiency: 96 },
  { day: "Sat", deliveries: 72, earnings: 465, efficiency: 98 },
  { day: "Sun", deliveries: 58, earnings: 375, efficiency: 95 },
]

const onlineDriversData = [
  { time: "6AM", drivers: 12, orders: 8 },
  { time: "8AM", drivers: 25, orders: 18 },
  { time: "10AM", drivers: 35, orders: 28 },
  { time: "12PM", drivers: 48, orders: 42 },
  { time: "2PM", drivers: 52, orders: 38 },
  { time: "4PM", drivers: 45, orders: 35 },
  { time: "6PM", drivers: 58, orders: 55 },
  { time: "8PM", drivers: 62, orders: 68 },
  { time: "10PM", drivers: 45, orders: 52 },
  { time: "12AM", drivers: 28, orders: 35 },
]

const customerFeedback = [
  { category: "Food Quality", score: 4.8, count: 245 },
  { category: "Delivery Speed", score: 4.6, count: 238 },
  { category: "Communication", score: 4.7, count: 189 },
  { category: "Professionalism", score: 4.9, count: 267 },
  { category: "Order Accuracy", score: 4.5, count: 223 },
]

const driverRadarData = [
  { metric: "Speed", value: 85, fullMark: 100 },
  { metric: "Accuracy", value: 92, fullMark: 100 },
  { metric: "Communication", value: 88, fullMark: 100 },
  { metric: "Customer Service", value: 95, fullMark: 100 },
  { metric: "Reliability", value: 90, fullMark: 100 },
  { metric: "Navigation", value: 87, fullMark: 100 },
]

const peakHoursAnalysis = [
  { time: "11AM-1PM", demand: 95, competition: 65, earnings: 185 },
  { time: "5PM-7PM", demand: 88, competition: 78, earnings: 165 },
  { time: "7PM-9PM", demand: 100, competition: 85, earnings: 195 },
  { time: "9PM-11PM", demand: 75, competition: 45, earnings: 145 },
]

const vehicleMetrics = [
  { type: "Bike", avgSpeed: 12, fuelCost: 0, earnings: 18 },
  { type: "Scooter", avgSpeed: 25, fuelCost: 8, earnings: 22 },
  { type: "Car", avgSpeed: 30, fuelCost: 15, earnings: 25 },
  { type: "E-Bike", avgSpeed: 18, fuelCost: 2, earnings: 20 },
]

const deliveryZones = [
  { zone: "Downtown", avgTime: 18, orders: 145, color: "#8884d8" },
  { zone: "Suburbs", avgTime: 25, orders: 98, color: "#82ca9d" },
  { zone: "University", avgTime: 15, orders: 167, color: "#ffc658" },
  { zone: "Business", avgTime: 22, orders: 123, color: "#ff7300" },
]

const chartConfig = {
  deliveries: { label: "Total Deliveries", color: "hsl(var(--chart-1))" },
  completionRate: { label: "Completion Rate (%)", color: "hsl(var(--chart-2))" },
  drivers: { label: "Online Drivers", color: "hsl(var(--chart-3))" },
  orders: { label: "Active Orders", color: "hsl(var(--chart-4))" },
  earnings: { label: "Earnings ($)", color: "hsl(var(--chart-5))" },
  tips: { label: "Tips Earned ($)", color: "hsl(220 70% 50%)" },
  satisfaction: { label: "Customer Satisfaction", color: "hsl(160 70% 50%)" },
  avgTime: { label: "Avg Delivery Time (min)", color: "hsl(30 70% 50%)" },
  efficiency: { label: "Efficiency Score (%)", color: "hsl(200 70% 50%)" },
  demand: { label: "Customer Demand", color: "hsl(var(--chart-1))" },
  competition: { label: "Driver Competition", color: "hsl(var(--chart-2))" },
  avgSpeed: { label: "Average Speed (mph)", color: "hsl(var(--chart-3))" },
  fuelCost: { label: "Daily Fuel Cost ($)", color: "hsl(var(--chart-4))" },
}

// Chart explanation component
const ChartExplanation = ({ title, description }: { title: string; description: string }) => (
  <div className="flex items-start space-x-2 p-3 bg-muted/30 rounded-lg mb-4">
    <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
    <div>
      <p className="text-sm font-medium text-blue-900 dark:text-blue-100">{title}</p>
      <p className="text-xs text-muted-foreground mt-1">{description}</p>
    </div>
  </div>
)

// Data series table component
const DataSeriesTable = ({ series }: { series: { name: string; color: string }[] }) => (
  <div className="overflow-x-auto mb-4">
    <table className="w-full text-sm">
      <thead>
        <tr className="border-b">
          <th className="text-left py-2 px-3 font-medium">Data Series</th>
          <th className="text-left py-2 px-3 font-medium">Color</th>
        </tr>
      </thead>
      <tbody>
        {series.map((item, index) => (
          <tr key={index} className="border-b border-muted/20">
            <td className="py-2 px-3">{item.name}</td>
            <td className="py-2 px-3">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
                <span>{item.color.includes("var") ? item.name : item.color}</span>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
)

export default function DeliveryDashboard() {
  const [selectedTimeframe, setSelectedTimeframe] = useState("week")

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 p-4 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Driver Analytics Hub
          </h1>
          <p className="text-muted-foreground">Advanced insights to maximize your delivery performance</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Drivers</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">58</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12%
              </span>
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-green-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <Package className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">342</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8%
              </span>
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-orange-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Time</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18m</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingDown className="h-3 w-3 mr-1" />
                -2m
              </span>
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Earnings</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$2,870</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +15%
              </span>
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-yellow-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Satisfaction</CardTitle>
            <Heart className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +0.2
              </span>
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-red-600/5"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Efficiency</CardTitle>
            <Zap className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +3%
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="rankings" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="rankings">Rankings</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="rankings" className="space-y-6">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* Top Drivers Leaderboard */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  Elite Driver Leaderboard
                </CardTitle>
                <CardDescription>Top performers ranked by overall performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Driver Rankings Explained"
                  description="Drivers are ranked based on a combination of customer ratings, delivery count, efficiency score, and earnings. Elite drivers maintain the highest standards across all metrics."
                />
                {driverRankings.map((driver, index) => (
                  <div
                    key={driver.name}
                    className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                  >
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                        index === 0
                          ? "bg-yellow-500 text-white"
                          : index === 1
                            ? "bg-gray-400 text-white"
                            : index === 2
                              ? "bg-amber-600 text-white"
                              : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {index + 1}
                    </div>
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={driver.avatar || "/placeholder.svg"} alt={driver.name} />
                      <AvatarFallback>
                        {driver.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium leading-none">{driver.name}</p>
                        <Badge
                          variant={
                            driver.badge === "Elite" ? "default" : driver.badge === "Pro" ? "secondary" : "outline"
                          }
                          className="text-xs"
                        >
                          {driver.badge}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                          {driver.rating}
                        </div>
                        <div className="flex items-center">
                          <Package className="h-3 w-3 mr-1" />
                          {driver.deliveries}
                        </div>
                        <div className="flex items-center">
                          <Target className="h-3 w-3 mr-1" />
                          {driver.efficiency}%
                        </div>
                        <div className="flex items-center">
                          <Zap className="h-3 w-3 mr-1" />
                          {driver.streak} day streak
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-600">${driver.earnings}</p>
                      <p className="text-xs text-muted-foreground">this week</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Performance Radar Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Your Performance Profile</CardTitle>
                <CardDescription>Multi-dimensional skill assessment across key areas</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Performance Radar Chart"
                  description="This radar chart shows your performance across 6 key metrics. Each axis represents a different skill area, with 100 being the maximum score. The larger the blue area, the better your overall performance."
                />
                <DataSeriesTable series={[{ name: "Performance Score (0-100)", color: "Blue" }]} />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={driverRadarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="metric" tick={{ fontSize: 12 }} />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} tick={false} />
                      <Radar
                        name="Performance Score (0-100)"
                        dataKey="value"
                        stroke="var(--color-deliveries)"
                        fill="var(--color-deliveries)"
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                    </RadarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Delivery Comparison Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Driver Performance Comparison</CardTitle>
              <CardDescription>
                Compare delivery volume, completion rates, and tip earnings across drivers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <ChartExplanation
                title="Performance Comparison Chart"
                description="This chart compares drivers across three key metrics: total deliveries completed, completion rate percentage, and tips earned. Higher bars indicate better performance in each category."
              />
              <DataSeriesTable
                series={[
                  { name: "Total Deliveries", color: "Blue" },
                  { name: "Completion Rate (%)", color: "Orange" },
                  { name: "Tips Earned ($)", color: "Purple" },
                ]}
              />
              <ChartContainer config={chartConfig}>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={deliveryComparison}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="driver" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <ChartLegend content={<ChartLegendContent />} />
                    <Bar dataKey="deliveries" fill="var(--color-deliveries)" name="Total Deliveries" />
                    <Bar dataKey="completionRate" fill="var(--color-completionRate)" name="Completion Rate (%)" />
                    <Bar dataKey="tips" fill="var(--color-tips)" name="Tips Earned ($)" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Hourly Performance */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>24-Hour Performance Analysis</CardTitle>
                <CardDescription>Track orders, earnings, and customer satisfaction throughout the day</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Hourly Performance Trends"
                  description="This chart shows how your key metrics change throughout the day. The orange line shows active orders, green shows earnings, and blue shows customer satisfaction. Use this to identify your peak performance hours."
                />
                <DataSeriesTable
                  series={[
                    { name: "Active Orders", color: "Orange" },
                    { name: "Hourly Earnings ($)", color: "Green" },
                    { name: "Customer Satisfaction (1-5)", color: "Blue" },
                  ]}
                />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={hourlyPerformance}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Line
                        type="monotone"
                        dataKey="orders"
                        stroke="var(--color-orders)"
                        strokeWidth={2}
                        name="Active Orders"
                        dot={{ fill: "var(--color-orders)", strokeWidth: 2, r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="earnings"
                        stroke="var(--color-earnings)"
                        strokeWidth={2}
                        name="Hourly Earnings ($)"
                        dot={{ fill: "var(--color-earnings)", strokeWidth: 2, r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="satisfaction"
                        stroke="var(--color-satisfaction)"
                        strokeWidth={2}
                        name="Customer Satisfaction (1-5)"
                        dot={{ fill: "var(--color-satisfaction)", strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Weekly Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Weekly Performance Trends</CardTitle>
                <CardDescription>Day-by-day delivery volume and efficiency tracking</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Weekly Trend Analysis"
                  description="This area chart shows your daily delivery count (blue) and efficiency score (teal) throughout the week. The filled areas help visualize trends and identify your most productive days."
                />
                <DataSeriesTable
                  series={[
                    { name: "Daily Deliveries", color: "Blue" },
                    { name: "Efficiency Score (%)", color: "Teal" },
                  ]}
                />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={weeklyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Area
                        type="monotone"
                        dataKey="deliveries"
                        stroke="var(--color-deliveries)"
                        fill="var(--color-deliveries)"
                        fillOpacity={0.6}
                        name="Daily Deliveries"
                      />
                      <Area
                        type="monotone"
                        dataKey="efficiency"
                        stroke="var(--color-efficiency)"
                        fill="var(--color-efficiency)"
                        fillOpacity={0.4}
                        name="Efficiency Score (%)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Online Drivers Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Driver Activity Pattern</CardTitle>
                <CardDescription>Online drivers vs active orders throughout the day</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Supply vs Demand Analysis"
                  description="This chart compares the number of online drivers (blue line) with active orders (orange line). When orders exceed drivers, there's high demand and potential for increased earnings."
                />
                <DataSeriesTable
                  series={[
                    { name: "Online Drivers", color: "Blue" },
                    { name: "Active Orders", color: "Orange" },
                  ]}
                />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={onlineDriversData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Line
                        type="monotone"
                        dataKey="drivers"
                        stroke="var(--color-drivers)"
                        strokeWidth={2}
                        name="Online Drivers"
                        dot={{ fill: "var(--color-drivers)", strokeWidth: 2, r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="orders"
                        stroke="var(--color-orders)"
                        strokeWidth={2}
                        name="Active Orders"
                        dot={{ fill: "var(--color-orders)", strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Customer Feedback */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Feedback Analysis</CardTitle>
              <CardDescription>Detailed satisfaction metrics across different service areas</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartExplanation
                title="Customer Satisfaction Breakdown"
                description="These progress bars show your average rating in each service category. Scores are out of 5.0, with the number of reviews shown in parentheses. Focus on improving lower-scoring areas to boost overall ratings."
              />
              <div className="space-y-4">
                {customerFeedback.map((item) => (
                  <div key={item.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.category}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-bold">{item.score}</span>
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-muted-foreground">({item.count} reviews)</span>
                      </div>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-yellow-400 to-green-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${(item.score / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Peak Hours Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Peak Hours Strategy</CardTitle>
                <CardDescription>Demand vs competition analysis for optimal scheduling</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Peak Hours Optimization"
                  description="This chart shows customer demand (blue), driver competition (orange), and average earnings (green) for different time periods. High demand with low competition indicates the best earning opportunities."
                />
                <DataSeriesTable
                  series={[
                    { name: "Customer Demand Level", color: "Blue" },
                    { name: "Driver Competition Level", color: "Orange" },
                    { name: "Average Earnings ($)", color: "Green" },
                  ]}
                />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={peakHoursAnalysis}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Bar dataKey="demand" fill="var(--color-demand)" name="Customer Demand Level" />
                      <Bar dataKey="competition" fill="var(--color-competition)" name="Driver Competition Level" />
                      <Bar dataKey="earnings" fill="var(--color-earnings)" name="Average Earnings ($)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Vehicle Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Fuel className="h-5 w-5" />
                  Vehicle Performance Comparison
                </CardTitle>
                <CardDescription>Compare efficiency and costs across different vehicle types</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ChartExplanation
                  title="Vehicle Type Analysis"
                  description="This chart compares average speed (blue), daily fuel costs (orange), and hourly earnings (green) for different vehicle types. Consider the trade-offs between speed, costs, and earnings potential."
                />
                <DataSeriesTable
                  series={[
                    { name: "Average Speed (mph)", color: "Blue" },
                    { name: "Daily Fuel Cost ($)", color: "Orange" },
                    { name: "Hourly Earnings ($)", color: "Green" },
                  ]}
                />
                <ChartContainer config={chartConfig}>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={vehicleMetrics}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="type" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Bar dataKey="avgSpeed" fill="var(--color-avgSpeed)" name="Average Speed (mph)" />
                      <Bar dataKey="fuelCost" fill="var(--color-fuelCost)" name="Daily Fuel Cost ($)" />
                      <Bar dataKey="earnings" fill="var(--color-earnings)" name="Hourly Earnings ($)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Delivery Zones */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Delivery Zone Performance</CardTitle>
                <CardDescription>Order volume and delivery time analysis by geographic area</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartExplanation
                  title="Zone Performance Insights"
                  description="The pie chart shows order distribution across zones, while the insights panel provides delivery time averages. Larger slices indicate higher order volumes, which may correlate with better earning potential."
                />
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <DataSeriesTable
                      series={[
                        { name: "Downtown", color: "#8884d8" },
                        { name: "Suburbs", color: "#82ca9d" },
                        { name: "University", color: "#ffc658" },
                        { name: "Business", color: "#ff7300" },
                      ]}
                    />
                    <ChartContainer config={chartConfig}>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={deliveryZones}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ zone, orders }) => `${zone}: ${orders}`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="orders"
                            name="Order Volume by Zone"
                          >
                            {deliveryZones.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <ChartLegend content={<ChartLegendContent />} />
                        </PieChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                  <div className="space-y-4">
                    <h4 className="font-medium">Zone Performance Details</h4>
                    <div className="space-y-3">
                      {deliveryZones.map((zone) => (
                        <div key={zone.zone} className="flex items-start space-x-3">
                          <div className="w-3 h-3 rounded-full mt-1" style={{ backgroundColor: zone.color }}></div>
                          <div>
                            <p className="text-sm font-medium">{zone.zone}</p>
                            <p className="text-xs text-muted-foreground">
                              {zone.orders} total orders • {zone.avgTime} min average delivery time
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* AI-Powered Insights */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  AI-Powered Insights
                </CardTitle>
                <CardDescription>Personalized recommendations based on your performance data</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3 p-4 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Peak Performance Window</p>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Your best performance is between 6-8 PM. Consider extending hours during this window for +23%
                      earnings potential based on historical data.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                  <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">Route Optimization</p>
                    <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                      University district shows 15% faster delivery times. Focus on this area during lunch hours
                      (11AM-2PM) for optimal efficiency.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 rounded-lg bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-orange-900 dark:text-orange-100">Weather Impact Alert</p>
                    <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
                      Rain expected tomorrow. Historical data shows 40% higher tips but 25% longer delivery times. Plan
                      accordingly for weather conditions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800">
                  <Star className="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-purple-900 dark:text-purple-100">Rating Improvement</p>
                    <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">
                      Improve communication score by 0.2 points to reach Elite status and unlock bonus incentives. Focus
                      on customer updates during delivery.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
                <CardDescription>Key performance metrics at a glance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Days Active</span>
                  </div>
                  <span className="font-bold">127</span>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Miles Driven</span>
                  </div>
                  <span className="font-bold">2,847</span>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-2">
                    <Heart className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Happy Customers</span>
                  </div>
                  <span className="font-bold">1,234</span>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-2">
                    <Trophy className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Achievements</span>
                  </div>
                  <span className="font-bold">15</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Market Trends & Forecasting</CardTitle>
                <CardDescription>Predictive analytics for strategic planning</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <h4 className="font-medium">Upcoming Market Trends</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <div>
                        <p className="text-sm font-medium">Eco-Delivery Bonus Program</p>
                        <p className="text-xs text-muted-foreground">
                          +$2 per delivery for e-bikes starting next month. Environmental incentives increasing.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium">Night Shift Premium</p>
                        <p className="text-xs text-muted-foreground">
                          25% bonus for deliveries after 10 PM due to driver shortage
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                      <div>
                        <p className="text-sm font-medium">Holiday Season Surge</p>
                        <p className="text-xs text-muted-foreground">
                          Expected 40% increase in orders Dec 15-31 with higher tips
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Optimization Recommendations</CardTitle>
                <CardDescription>Actionable strategies to improve performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 rounded-lg border-2 border-dashed border-muted-foreground/25">
                    <h4 className="font-medium mb-2">Route Optimization Strategy</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Start from University district during lunch rush (11AM-2PM)</li>
                      <li>• Move to Downtown for afternoon orders (2PM-5PM)</li>
                      <li>• End in Suburbs for dinner deliveries (5PM-9PM)</li>
                      <li>• Estimated improvement: +18% efficiency, +$45/day</li>
                    </ul>
                  </div>

                  <div className="p-4 rounded-lg border-2 border-dashed border-muted-foreground/25">
                    <h4 className="font-medium mb-2">Time Management Tips</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Focus on 11:00 AM - 2:00 PM (Lunch rush peak)</li>
                      <li>• Target 5:00 PM - 9:00 PM (Dinner peak hours)</li>
                      <li>• Consider break during 2:00 PM - 5:00 PM (Low demand)</li>
                      <li>• Weekend evenings show highest tip potential</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
