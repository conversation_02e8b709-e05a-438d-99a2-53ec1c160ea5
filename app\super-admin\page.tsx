"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import {
  Users,
  Store,
  ShoppingBag,
  Settings,
  Shield,
  Database,
  BarChart3,
  ArrowUpRight,
  FileText
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/context/unified-auth-context"

export default function SuperAdminDashboard() {
  const router = useRouter()
  const { user, userProfile, isLoading: authLoading } = useAuth()
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBusinesses: 0,
    totalOrders: 0,
    usersByRole: {
      customer: 0,
      business_staff: 0,
      business_manager: 0,
      admin: 0,
      super_admin: 0
    },
    businessesByType: {
      restaurant: 0,
      shop: 0,
      pharmacy: 0,
      cafe: 0,
      errand: 0
    }
  })
  const [isLoading, setIsLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)

  // Check authentication - since middleware handles access control, we can be more lenient
  useEffect(() => {
    // If we're not loading and we have some user data, or if we've waited long enough, proceed
    if (!authLoading) {
      console.log("SuperAdminDashboard: Auth loading complete, proceeding to fetch data")
      setAuthChecked(true)
    } else {
      // Set a timeout to proceed even if auth is still loading (middleware protects the route)
      const timeout = setTimeout(() => {
        console.log("SuperAdminDashboard: Auth timeout, proceeding anyway (middleware protects route)")
        setAuthChecked(true)
      }, 2000)

      return () => clearTimeout(timeout)
    }
  }, [authLoading])

  // Only proceed with data fetching if authentication is confirmed
  useEffect(() => {
    if (!authChecked) return

    console.log("SuperAdminDashboard: Authentication confirmed, fetching stats")

    // Set a timeout to stop loading after 5 seconds regardless of data fetch status
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        console.log("Loading timeout reached, showing dashboard with default values")
        setIsLoading(false)
      }
    }, 5000)

    // Fetch stats from the server-side API endpoint
    const fetchStats = async () => {
      try {
        console.log("Fetching super admin stats from API")
        console.log("Sending request to super-admin-stats API")

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';

        console.log("SuperAdminDashboard: Fetching stats with token:", token ? "Token available" : "No token");

        // Add a timestamp to prevent caching and include the authorization header
        const response = await fetch(`/api/admin/dashboard/super-admin-stats?t=${Date.now()}`, {
          headers: {
            'Authorization': token ? `Bearer ${token}` : ''
          }
        })

        console.log("API response status:", response.status, response.statusText)

        let data;

        if (!response.ok) {
          const errorData = await response.json()
          console.error("API error response:", errorData)

          // If we get a 401 or 403, redirect to login
          if (response.status === 401 || response.status === 403) {
            console.log("Authentication error, redirecting to login")
            router.push("/login?redirectTo=/super-admin")
            return
          }

          // For other errors, try the fallback endpoint
          console.log("Trying fallback endpoint due to API error")
          try {
            const fallbackResponse = await fetch(`/api/admin/dashboard/super-admin-stats-fallback?t=${Date.now()}`, {
              headers: {
                'Authorization': token ? `Bearer ${token}` : ''
              }
            })
            if (fallbackResponse.ok) {
              data = await fallbackResponse.json()
              console.log("Fallback stats fetched successfully:", data)
            } else {
              console.log("Fallback endpoint also failed, using default values")
              return
            }
          } catch (fallbackErr) {
            console.error("Error fetching from fallback endpoint:", fallbackErr)
            return
          }
        } else {
          // If the main endpoint succeeded, use its data
          data = await response.json()
          console.log("Stats fetched successfully:", JSON.stringify(data, null, 2))
        }

        // Log the raw data we received
        console.log("Raw data from API:", data);

        // Make sure we have all the expected properties with fallbacks
        const usersByRole = data.usersByRole || {
          customer: 0,
          business_staff: 0,
          business_manager: 0,
          admin: 0,
          super_admin: 0
        };

        const businessesByType = data.businessesByType || {
          restaurant: 0,
          shop: 0,
          pharmacy: 0,
          cafe: 0,
          errand: 0
        };

        // Ensure all properties exist in the objects
        const ensuredUsersByRole = {
          customer: usersByRole.customer || 0,
          business_staff: usersByRole.business_staff || 0,
          business_manager: usersByRole.business_manager || 0,
          admin: usersByRole.admin || 0,
          super_admin: usersByRole.super_admin || 0
        };

        const ensuredBusinessesByType = {
          restaurant: businessesByType.restaurant || 0,
          shop: businessesByType.shop || 0,
          pharmacy: businessesByType.pharmacy || 0,
          cafe: businessesByType.cafe || 0,
          errand: businessesByType.errand || 0
        };

        console.log("Processed usersByRole:", ensuredUsersByRole);
        console.log("Processed businessesByType:", ensuredBusinessesByType);

        // Update stats state with the processed data
        setStats({
          totalUsers: data.totalUsers || 0,
          totalBusinesses: data.totalBusinesses || 0,
          totalOrders: data.totalOrders || 0,
          usersByRole: ensuredUsersByRole,
          businessesByType: ensuredBusinessesByType
        })

        console.log("Stats updated successfully")
      } catch (err) {
        console.error("Error fetching stats from API:", err)

        // Continue with default values
        console.log("Using default values due to error")
      } finally {
        // Always set loading to false, even if there are errors
        setIsLoading(false)
        console.log("Loading complete")
      }
    }

    // Execute the fetch function
    fetchStats()

    // Clean up the timeout when the component unmounts
    return () => {
      clearTimeout(loadingTimeout)
    }
  }, [authChecked])

  // Show loading state if auth is not checked or data is loading
  if (!authChecked || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading super admin dashboard...</p>
          {!authChecked && <p className="text-sm text-gray-500 mt-2">Verifying permissions...</p>}
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Super Admin Dashboard</h1>
          <p className="text-gray-500">Welcome to the Loop Jersey super admin panel</p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Link href="/admin">
            <Button className="bg-purple-600 hover:bg-purple-700">
              Admin Dashboard
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <div className="flex items-center justify-between">
              <Users className="h-4 w-4 text-gray-500" />
              <ArrowUpRight className="h-4 w-4 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-gray-500">Registered users</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
            <div className="flex items-center justify-between">
              <Store className="h-4 w-4 text-gray-500" />
              <ArrowUpRight className="h-4 w-4 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBusinesses}</div>
            <p className="text-xs text-gray-500">Registered businesses</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <div className="flex items-center justify-between">
              <ShoppingBag className="h-4 w-4 text-gray-500" />
              <ArrowUpRight className="h-4 w-4 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-gray-500">Processed orders</p>
          </CardContent>
        </Card>
      </div>

      {/* User Roles */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>User Roles</CardTitle>
          <CardDescription>Distribution of users by role</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-gray-600" />
                </div>
                <p className="text-sm font-medium">Customers</p>
                <p className="text-2xl font-bold">{stats.usersByRole.customer}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <p className="text-sm font-medium">Staff</p>
                <p className="text-2xl font-bold">{stats.usersByRole.business_staff}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-blue-600" />
                </div>
                <p className="text-sm font-medium">Managers</p>
                <p className="text-2xl font-bold">{stats.usersByRole.business_manager}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center mb-2">
                  <Shield className="h-5 w-5 text-red-600" />
                </div>
                <p className="text-sm font-medium">Admins</p>
                <p className="text-2xl font-bold">{stats.usersByRole.admin}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                  <Shield className="h-5 w-5 text-purple-600" />
                </div>
                <p className="text-sm font-medium">Super Admins</p>
                <p className="text-2xl font-bold">{stats.usersByRole.super_admin}</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Business Types */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Business Types</CardTitle>
          <CardDescription>Distribution of businesses by type</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-red-600" />
                </div>
                <p className="text-sm font-medium">Restaurants</p>
                <p className="text-2xl font-bold">{stats.businessesByType.restaurant}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-blue-600" />
                </div>
                <p className="text-sm font-medium">Shops</p>
                <p className="text-2xl font-bold">{stats.businessesByType.shop}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-purple-600" />
                </div>
                <p className="text-sm font-medium">Pharmacies</p>
                <p className="text-2xl font-bold">{stats.businessesByType.pharmacy}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-green-600" />
                </div>
                <p className="text-sm font-medium">Cafes</p>
                <p className="text-2xl font-bold">{stats.businessesByType.cafe}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex flex-col items-center">
                <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center mb-2">
                  <Store className="h-5 w-5 text-orange-600" />
                </div>
                <p className="text-sm font-medium">Errands</p>
                <p className="text-2xl font-bold">{stats.businessesByType.errand}</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Super Admin Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Super Admin Functions</CardTitle>
          <CardDescription>Super admin only system management</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/super-admin/system-logs" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <FileText className="h-5 w-5 mb-2" />
                <span>System Logs</span>
                <span className="text-xs text-gray-500 mt-1">view_system_logs</span>
              </Button>
            </Link>
            <Link href="/super-admin/platform-settings" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <Settings className="h-5 w-5 mb-2" />
                <span>Platform Settings</span>
                <span className="text-xs text-gray-500 mt-1">manage_platform_settings</span>
              </Button>
            </Link>
            <Link href="/super-admin/data-audit" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <Database className="h-5 w-5 mb-2" />
                <span>Data Audit</span>
                <span className="text-xs text-gray-500 mt-1">Active</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Admin Functions */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Functions</CardTitle>
          <CardDescription>Access regular admin tools</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Link href="/admin" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <BarChart3 className="h-5 w-5 mb-2" />
                <span>Admin Dashboard</span>
              </Button>
            </Link>
            <Link href="/admin/users" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <Users className="h-5 w-5 mb-2" />
                <span>Manage Users</span>
              </Button>
            </Link>
            <Link href="/admin/businesses" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <Store className="h-5 w-5 mb-2" />
                <span>Manage Businesses</span>
              </Button>
            </Link>
            <Link href="/super-admin/database" className="w-full">
              <Button variant="outline" className="w-full h-auto py-4 flex flex-col items-center justify-center">
                <Database className="h-5 w-5 mb-2" />
                <span>Database Tools</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
