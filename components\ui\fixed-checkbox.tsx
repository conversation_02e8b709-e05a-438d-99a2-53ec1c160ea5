"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

// This is a modified version of the Checkbox component that attempts to fix the infinite update loop
const FixedCheckbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => {
  // Use a local state to track checked state to prevent infinite loops
  const [localChecked, setLocalChecked] = React.useState(props.checked);

  // Update local state when props change
  React.useEffect(() => {
    setLocalChecked(props.checked);
  }, [props.checked]);

  // Create a stable onCheckedChange handler
  const handleCheckedChange = React.useCallback((checked: boolean | "indeterminate") => {
    if (props.onCheckedChange) {
      props.onCheckedChange(checked);
    }
  }, [props.onCheckedChange]);

  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
        className
      )}
      {...props}
      // Override the checked and onCheckedChange props to avoid conflicts
      checked={localChecked}
      onCheckedChange={handleCheckedChange}
    >
      <CheckboxPrimitive.Indicator
        className={cn("flex items-center justify-center text-current")}
      >
        <Check className="h-4 w-4" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
})
FixedCheckbox.displayName = "FixedCheckbox"

export { FixedCheckbox }
