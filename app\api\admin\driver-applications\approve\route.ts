import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { driverId, userEmail } = await request.json()

    if (!driverId || !userEmail) {
      return NextResponse.json(
        { error: "Driver ID and user email are required" },
        { status: 400 }
      )
    }

    // Update driver profile to verified
    const { error: updateError } = await supabase
      .from('driver_profiles')
      .update({
        is_verified: true,
        is_active: true,
        verification_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', driverId)

    if (updateError) {
      console.error('Error updating driver profile:', updateError)
      return NextResponse.json(
        { error: "Failed to approve driver" },
        { status: 500 }
      )
    }

    // Log the approval activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverId,
        activity_type: 'application_approved',
        timestamp: new Date().toISOString(),
        notes: 'Driver application approved by Loop admin'
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request for this, just log it
    }

    // TODO: Send approval email to driver
    // For now, we'll just log that an email should be sent
    console.log(`TODO: Send approval email to ${userEmail} for driver ${driverId}`)

    // In a real implementation, you would send an email here:
    /*
    await sendDriverApprovalEmail({
      email: userEmail,
      driverId: driverId,
      loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/driver/login`
    })
    */

    return NextResponse.json({
      success: true,
      message: "Driver approved successfully",
      driverId: driverId
    })

  } catch (error) {
    console.error('Error in driver approval:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// TODO: Implement email sending function
/*
async function sendDriverApprovalEmail({ email, driverId, loginUrl }: {
  email: string
  driverId: string
  loginUrl: string
}) {
  // Implementation would use your email service (e.g., SendGrid, AWS SES, etc.)
  // Email template would include:
  // - Congratulations message
  // - Login instructions
  // - Next steps (applying to businesses)
  // - Contact information for support
}
*/
