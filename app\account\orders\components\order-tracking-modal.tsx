'use client'

import { useState, useEffect } from 'react'
import { BusinessOrder } from '@/types/customer-orders'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CheckCircle, 
  Clock, 
  Truck, 
  MapPin,
  Phone,
  Store
} from 'lucide-react'

interface OrderTrackingModalProps {
  order: BusinessOrder | null
  isOpen: boolean
  onClose: () => void
}

interface TrackingStep {
  status: string
  title: string
  description: string
  timestamp?: string
  completed: boolean
  current: boolean
}

export function OrderTrackingModal({ order, isOpen, onClose }: OrderTrackingModalProps) {
  const [trackingSteps, setTrackingSteps] = useState<TrackingStep[]>([])

  useEffect(() => {
    if (!order) return

    // Generate tracking steps based on order status and delivery method
    const steps: TrackingStep[] = []
    const currentStatus = order.status.toLowerCase()

    // Common steps for all orders
    steps.push({
      status: 'pending',
      title: 'Order Placed',
      description: 'Your order has been received',
      timestamp: order.created_at,
      completed: true,
      current: currentStatus === 'pending'
    })

    steps.push({
      status: 'confirmed',
      title: 'Order Confirmed',
      description: 'Business has confirmed your order',
      completed: ['confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'completed'].includes(currentStatus),
      current: currentStatus === 'confirmed'
    })

    steps.push({
      status: 'preparing',
      title: 'Preparing Order',
      description: 'Your order is being prepared',
      completed: ['preparing', 'ready', 'out_for_delivery', 'delivered', 'completed'].includes(currentStatus),
      current: currentStatus === 'preparing'
    })

    if (order.delivery_method === 'delivery') {
      steps.push({
        status: 'ready',
        title: 'Ready for Delivery',
        description: 'Order is ready and waiting for pickup by driver',
        completed: ['ready', 'out_for_delivery', 'delivered', 'completed'].includes(currentStatus),
        current: currentStatus === 'ready'
      })

      steps.push({
        status: 'out_for_delivery',
        title: 'Out for Delivery',
        description: 'Your order is on the way',
        completed: ['out_for_delivery', 'delivered', 'completed'].includes(currentStatus),
        current: currentStatus === 'out_for_delivery'
      })

      steps.push({
        status: 'delivered',
        title: 'Delivered',
        description: 'Order has been delivered',
        completed: ['delivered', 'completed'].includes(currentStatus),
        current: currentStatus === 'delivered'
      })
    } else {
      steps.push({
        status: 'ready',
        title: 'Ready for Pickup',
        description: 'Your order is ready for collection',
        completed: ['ready', 'completed'].includes(currentStatus),
        current: currentStatus === 'ready'
      })

      steps.push({
        status: 'completed',
        title: 'Order Completed',
        description: 'Order has been collected',
        completed: currentStatus === 'completed',
        current: currentStatus === 'completed'
      })
    }

    setTrackingSteps(steps)
  }, [order])

  if (!order) return null

  const getStepIcon = (step: TrackingStep) => {
    if (step.completed && !step.current) {
      return <CheckCircle className="h-5 w-5 text-green-600" />
    }
    
    if (step.current) {
      return <Clock className="h-5 w-5 text-blue-600 animate-pulse" />
    }

    return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
  }

  const formatEstimatedTime = () => {
    if (!order.estimated_delivery_time) return null
    
    const method = order.delivery_method === 'delivery' ? 'delivery' : 'pickup'
    return `Estimated ${method} time: ${order.estimated_delivery_time} minutes`
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Track Order #{order.order_number}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Order Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <Store className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{order.business_name}</span>
            </div>
            
            <div className="text-sm text-gray-600 space-y-1">
              <div>Total: £{order.total.toFixed(2)}</div>
              {formatEstimatedTime() && (
                <div>{formatEstimatedTime()}</div>
              )}
              <div className="flex items-center gap-2">
                <Badge className={order.delivery_method === 'delivery' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}>
                  {order.delivery_method === 'delivery' ? (
                    <>
                      <Truck className="h-3 w-3 mr-1" />
                      Delivery
                    </>
                  ) : (
                    <>
                      <MapPin className="h-3 w-3 mr-1" />
                      Pickup
                    </>
                  )}
                </Badge>
              </div>
            </div>
          </div>

          {/* Tracking Steps */}
          <div className="space-y-4">
            {trackingSteps.map((step, index) => (
              <div key={step.status} className="flex items-start gap-3">
                <div className="flex flex-col items-center">
                  {getStepIcon(step)}
                  {index < trackingSteps.length - 1 && (
                    <div className={`w-0.5 h-8 mt-2 ${step.completed ? 'bg-green-200' : 'bg-gray-200'}`} />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className={`font-medium ${step.current ? 'text-blue-600' : step.completed ? 'text-green-600' : 'text-gray-400'}`}>
                    {step.title}
                  </div>
                  <div className={`text-sm ${step.current ? 'text-blue-500' : step.completed ? 'text-gray-600' : 'text-gray-400'}`}>
                    {step.description}
                  </div>
                  {step.timestamp && (
                    <div className="text-xs text-gray-400 mt-1">
                      {new Date(step.timestamp).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Contact Info */}
          {order.delivery_method === 'delivery' && (order.status === 'out_for_delivery' || order.status === 'ready') && (
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-800 mb-1">
                <Phone className="h-4 w-4" />
                <span className="font-medium">Need help?</span>
              </div>
              <div className="text-sm text-blue-600">
                Contact the business directly for updates on your order.
              </div>
            </div>
          )}

          {/* Close Button */}
          <Button onClick={onClose} className="w-full">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
