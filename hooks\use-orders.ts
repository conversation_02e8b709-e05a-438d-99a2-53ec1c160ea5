import { useState, useCallback, useRef } from 'react'
import { useAuth } from '@/context/unified-auth-context'
import { useToast } from '@/components/ui/use-toast'
import { Order } from '@/components/orders/enhanced-orders-table'
import { OrderFilters } from '@/components/orders/enhanced-order-filters'
import { DateRange } from '@/components/date-range-picker'
import { sanitizeOrderStats } from '@/utils/orders-utils'

interface OrdersResponse {
  orders: Order[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

interface UseOrdersOptions {
  selectedBusinessId?: number | null
  isAdminUser?: boolean
}

export function useOrders(options: UseOrdersOptions = {}) {
  const { user } = useAuth()
  const { toast } = useToast()
  const { selectedBusinessId, isAdminUser } = options

  // State
  const [orders, setOrders] = useState<Order[]>([])
  const [totalPages, setTotalPages] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [orderStats, setOrderStats] = useState({
    total: 0,
    pending: 0,
    confirmed: 0,
    preparing: 0,
    ready: 0,
    outForDelivery: 0,
    completed: 0,
    cancelled: 0,
    averageDeliveryTime: "0 min",
    overdueOrders: 0,
    highPriorityOrders: 0
  })

  // Debounce timer
  const fetchOrdersTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Build API URL with parameters
  const buildApiUrl = useCallback((
    page: number,
    pageSize: number,
    searchQuery: string,
    dateRange: DateRange | undefined,
    activeFilters: OrderFilters
  ) => {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      sortBy: "created_at",
      sortOrder: "desc"
    })

    if (searchQuery.trim()) {
      params.append("search", searchQuery.trim())
    }

    if (dateRange?.from) {
      params.append("startDate", dateRange.from.toISOString())
    }
    if (dateRange?.to) {
      params.append("endDate", dateRange.to.toISOString())
    }

    if (activeFilters.status.length > 0) {
      activeFilters.status.forEach(status => {
        params.append("status", status)
      })
    }

    if (activeFilters.priority.length > 0) {
      params.append("priority", activeFilters.priority.join(','))
    }

    if (activeFilters.deliveryType.length > 0) {
      params.append("deliveryType", activeFilters.deliveryType.join(','))
    }

    if (isAdminUser && selectedBusinessId) {
      params.append("businessId", selectedBusinessId.toString())
    }

    return `/api/business-admin/orders?${params.toString()}`
  }, [isAdminUser, selectedBusinessId])

  // Fetch orders
  const fetchOrders = useCallback(async (
    page: number,
    pageSize: number,
    searchQuery: string,
    dateRange: DateRange | undefined,
    activeFilters: OrderFilters
  ) => {
    if (!user) return

    setIsLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''
      const url = buildApiUrl(page, pageSize, searchQuery, dateRange, activeFilters)

      console.log("🔍 [CLIENT] Fetching orders:", url)

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          'Authorization': token ? `Bearer ${token}` : '',
        },
        credentials: "include"
      })

      if (!response.ok) {
        let errorData = {}
        try {
          errorData = await response.json()
        } catch (jsonError) {
          console.error("Failed to parse error response as JSON:", jsonError)
          errorData = { error: `HTTP ${response.status}: ${response.statusText}` }
        }
        console.error(`❌ [CLIENT] API Error:`, errorData)
        throw new Error(errorData.error || `Failed to fetch orders (${response.status})`)
      }

      const data = await response.json()
      console.log(`✅ [CLIENT] API Success - Orders count: ${data.orders?.length || 0}`)

      setOrders(data.orders || [])
      setTotalPages(data.totalPages || 1)
      setOrderStats(sanitizeOrderStats(data.stats))
    } catch (error: any) {
      console.error("❌ [CLIENT] Error fetching orders:", error)
      setError("Failed to load orders")
      toast({
        variant: "destructive",
        title: "Error loading orders",
        description: error.message || "An unexpected error occurred",
      })
    } finally {
      setIsLoading(false)
    }
  }, [user, buildApiUrl, toast])

  // Debounced version of fetchOrders
  const debouncedFetchOrders = useCallback((
    page: number,
    pageSize: number,
    searchQuery: string,
    dateRange: DateRange | undefined,
    activeFilters: OrderFilters
  ) => {
    if (fetchOrdersTimeoutRef.current) {
      clearTimeout(fetchOrdersTimeoutRef.current)
    }

    fetchOrdersTimeoutRef.current = setTimeout(() => {
      fetchOrders(page, pageSize, searchQuery, dateRange, activeFilters)
    }, 500)
  }, [fetchOrders])

  // Update order status
  const updateOrderStatus = useCallback(async (order: Order, newStatus: string) => {
    try {
      console.log(`🔔 [CLIENT] Updating order ${order.id} to status: ${newStatus}`)

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch("/api/business-admin/orders", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          orderId: order.id,
          status: newStatus,
          notes: `Status updated to ${newStatus} by business admin`
        }),
        credentials: "include"
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error(`❌ [CLIENT] API Error:`, errorData)
        throw new Error(errorData.error || "Failed to update order status")
      }

      const responseData = await response.json()
      console.log(`✅ [CLIENT] API Success:`, responseData)

      // Format the status for display
      const formatStatusForMessage = (status: string) => {
        switch (status.toLowerCase()) {
          case 'confirmed': return 'confirmed'
          case 'cancelled': return 'cancelled'
          case 'preparing': return 'preparing'
          case 'ready': return 'ready for pickup/delivery'
          case 'out_for_delivery': return 'out for delivery'
          case 'delivered': return 'delivered'
          default: return status
        }
      }

      toast({
        title: "Order Status Updated",
        description: `Order #${order.order_number || order.id} has been ${formatStatusForMessage(newStatus)}`,
        duration: 3000
      })

      return true
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error updating status",
        description: error.message || "An unexpected error occurred",
      })
      return false
    }
  }, [toast])

  // Cleanup timeout on unmount
  const cleanup = useCallback(() => {
    if (fetchOrdersTimeoutRef.current) {
      clearTimeout(fetchOrdersTimeoutRef.current)
    }
  }, [])

  return {
    orders,
    totalPages,
    isLoading,
    error,
    orderStats,
    fetchOrders,
    debouncedFetchOrders,
    updateOrderStatus,
    cleanup
  }
}
