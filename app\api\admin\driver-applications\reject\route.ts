import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { driverId, reason } = await request.json()

    if (!driverId || !reason) {
      return NextResponse.json(
        { error: "Driver ID and rejection reason are required" },
        { status: 400 }
      )
    }

    // Update driver profile to rejected (inactive)
    const { error: updateError } = await supabase
      .from('driver_profiles')
      .update({
        is_verified: false,
        is_active: false,
        notes: `REJECTED: ${reason}\n\n${new Date().toISOString()}\n\n--- Previous Notes ---\n`,
        updated_at: new Date().toISOString()
      })
      .eq('id', driverId)

    if (updateError) {
      console.error('Error updating driver profile:', updateError)
      return NextResponse.json(
        { error: "Failed to reject driver" },
        { status: 500 }
      )
    }

    // Log the rejection activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverId,
        activity_type: 'application_rejected',
        timestamp: new Date().toISOString(),
        notes: `Driver application rejected by Loop admin. Reason: ${reason}`
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request for this, just log it
    }

    // TODO: Send rejection email to driver
    // For now, we'll just log that an email should be sent
    console.log(`TODO: Send rejection email for driver ${driverId}. Reason: ${reason}`)

    // In a real implementation, you would send an email here:
    /*
    await sendDriverRejectionEmail({
      driverId: driverId,
      reason: reason,
      reapplyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/partners/riders/apply`
    })
    */

    return NextResponse.json({
      success: true,
      message: "Driver application rejected",
      driverId: driverId
    })

  } catch (error) {
    console.error('Error in driver rejection:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// TODO: Implement email sending function
/*
async function sendDriverRejectionEmail({ driverId, reason, reapplyUrl }: {
  driverId: string
  reason: string
  reapplyUrl: string
}) {
  // Implementation would use your email service (e.g., SendGrid, AWS SES, etc.)
  // Email template would include:
  // - Polite rejection message
  // - Specific reason for rejection
  // - Instructions for reapplying (if applicable)
  // - Contact information for questions
}
*/
