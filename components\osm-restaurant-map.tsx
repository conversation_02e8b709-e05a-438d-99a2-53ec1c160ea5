"use client"

import { useState, useEffect, useRef } from "react"
import { Clock } from "lucide-react"
import dynamic from 'next/dynamic'

// Import Leaflet dynamically to avoid SSR issues
const LeafletMap = dynamic(
  () => import('./delivery/osm-restaurant-map-inner-simple'),
  { ssr: false }
)

interface OSMRestaurantMapProps {
  restaurantId: string
  name: string
  location: string
  coordinates: [number, number] // [longitude, latitude]
  deliveryTime: number
  deliveryRadius: number // in kilometers
}

export default function OSMRestaurantMap({
  restaurantId,
  name,
  location,
  coordinates,
  deliveryTime,
  deliveryRadius,
}: OSMRestaurantMapProps) {
  const [mapError, setMapError] = useState(false)

  return (
    <div className="rounded-lg overflow-hidden border shadow-md">
      <div className="bg-white p-3 border-b">
        <h3 className="font-semibold">Delivery Area</h3>
        <div className="flex items-center text-sm text-gray-500 mt-1">
          <Clock className="h-4 w-4 mr-1 text-emerald-600" />
          <span>Estimated delivery time: {deliveryTime} minutes</span>
        </div>
      </div>
      {mapError ? (
        <div className="h-[400px] bg-gray-100 flex items-center justify-center">
          <div className="text-center p-6">
            <p className="text-gray-500 mb-2">Unable to load the map</p>
            <p className="text-sm text-gray-400">We're having trouble loading the delivery area map.</p>
          </div>
        </div>
      ) : (
        <LeafletMap
          restaurantName={name}
          restaurantLocation={location}
          restaurantCoordinates={coordinates}
          deliveryRadius={deliveryRadius}
        />
      )}
      <div className="bg-gray-50 p-3 text-xs text-gray-500">
        <p>Delivery times may vary based on traffic and weather conditions.</p>
        <p>Green areas indicate faster delivery zones.</p>
      </div>
    </div>
  )
}
