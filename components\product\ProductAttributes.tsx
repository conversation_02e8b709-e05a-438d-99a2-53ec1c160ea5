'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Clock, AlertTriangle, Flame, Award, Leaf } from 'lucide-react';

interface ProductAttributesProps {
  attributes: {
    dietary?: string[];
    allergen?: string[];
    spice_level?: string[];
    prep_time_minutes?: string[];
    popularity?: string[];
    [key: string]: string[] | undefined;
  };
  className?: string;
}

const ProductAttributes: React.FC<ProductAttributesProps> = ({ attributes, className = '' }) => {
  if (!attributes || Object.keys(attributes).length === 0) {
    return null;
  }

  // Helper function to get icon for attribute type
  const getAttributeIcon = (type: string, value: string) => {
    switch (type) {
      case 'dietary':
        return <Leaf className="h-3 w-3 mr-1" />;
      case 'allergen':
        return <AlertTriangle className="h-3 w-3 mr-1" />;
      case 'spice_level':
        return <Flame className="h-3 w-3 mr-1" />;
      case 'prep_time_minutes':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'popularity':
        return <Award className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // Helper function to get color for attribute type
  const getAttributeColor = (type: string, value: string) => {
    switch (type) {
      case 'dietary':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'allergen':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'spice_level':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      case 'prep_time_minutes':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'popularity':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Helper function to get display name for attribute type
  const getAttributeDisplayName = (type: string) => {
    switch (type) {
      case 'dietary':
        return 'Dietary';
      case 'allergen':
        return 'Contains';
      case 'spice_level':
        return 'Spice Level';
      case 'prep_time_minutes':
        return 'Prep Time';
      case 'popularity':
        return '';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ');
    }
  };

  // Helper function to get display value for attribute
  const getAttributeDisplayValue = (type: string, value: string) => {
    if (type === 'prep_time_minutes') {
      return `${value} min`;
    }
    if (type === 'popularity') {
      switch (value) {
        case 'bestseller':
          return 'Bestseller';
        case 'popular':
          return 'Popular';
        case 'chef_special':
          return 'Chef\'s Special';
        default:
          return value;
      }
    }
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      <TooltipProvider>
        {Object.entries(attributes).map(([type, values]) => {
          if (!values || values.length === 0) return null;
          
          return values.map((value, index) => (
            <Tooltip key={`${type}-${index}`}>
              <TooltipTrigger asChild>
                <Badge 
                  variant="outline" 
                  className={`text-xs px-2 py-0.5 ${getAttributeColor(type, value)}`}
                >
                  {getAttributeIcon(type, value)}
                  {getAttributeDisplayValue(type, value)}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>{getAttributeDisplayName(type)}</p>
              </TooltipContent>
            </Tooltip>
          ));
        })}
      </TooltipProvider>
    </div>
  );
};

export default ProductAttributes;
