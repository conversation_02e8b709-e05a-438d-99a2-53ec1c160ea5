"use client"

import { ReactNode } from "react"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface CheckoutSectionProps {
  title: string
  icon: LucideIcon
  children: ReactNode
  className?: string
  active?: boolean
}

export default function CheckoutSection({ 
  title, 
  icon: Icon, 
  children, 
  className,
  active = true
}: CheckoutSectionProps) {
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-md p-6 mb-6 transition-all duration-200",
      active ? "border-l-4 border-emerald-500" : "",
      className
    )}>
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <div className={cn(
          "flex items-center justify-center w-8 h-8 rounded-full mr-3",
          active ? "bg-emerald-100 text-emerald-600" : "bg-gray-100 text-gray-500"
        )}>
          <Icon className="h-5 w-5" />
        </div>
        <span className={active ? "text-emerald-800" : "text-gray-700"}>
          {title}
        </span>
      </h2>
      <div className={active ? "" : "opacity-75"}>
        {children}
      </div>
    </div>
  )
}
