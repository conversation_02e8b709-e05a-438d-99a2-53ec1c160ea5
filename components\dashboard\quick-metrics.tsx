"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  DollarSign,
  ShoppingBag,
  Clock,
  Users,
  Target,
  Activity
} from "lucide-react"

interface MetricTrend {
  value: number // percentage change
  period: string // e.g., "vs yesterday", "vs last hour"
  isPositive?: boolean // override automatic positive/negative detection
}

interface QuickMetricProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  trend?: MetricTrend
  subtitle?: string
  format?: 'currency' | 'number' | 'percentage' | 'time'
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

interface QuickMetricsProps {
  lastHourRevenue: number
  typicalHourRevenue: number
  todayOrders: number
  todayRevenue: number
  avgOrderValue: number
  avgPrepTime: number
  targetPrepTime: number
  trendsVsYesterday: {
    orders: number
    revenue: number
  }
  className?: string
}

function formatValue(value: string | number, format?: string): string {
  if (typeof value === 'string') return value
  
  switch (format) {
    case 'currency':
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(value)
    case 'percentage':
      return `${value}%`
    case 'time':
      return `${value} min`
    case 'number':
    default:
      return value.toLocaleString()
  }
}

function QuickMetric({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  subtitle, 
  format,
  className = "",
  size = 'md'
}: QuickMetricProps) {
  const getTrendIcon = () => {
    if (!trend) return null
    
    const isPositive = trend.isPositive !== undefined ? trend.isPositive : trend.value > 0
    const isNeutral = trend.value === 0
    
    if (isNeutral) return <Minus className="h-3 w-3" />
    return isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />
  }

  const getTrendColor = () => {
    if (!trend) return ""
    
    const isPositive = trend.isPositive !== undefined ? trend.isPositive : trend.value > 0
    const isNeutral = trend.value === 0
    
    if (isNeutral) return "text-gray-500"
    return isPositive ? "text-green-600" : "text-red-600"
  }

  const sizeClasses = {
    sm: {
      card: "p-3",
      icon: "h-4 w-4",
      title: "text-xs",
      value: "text-lg",
      trend: "text-xs"
    },
    md: {
      card: "p-4",
      icon: "h-5 w-5",
      title: "text-sm",
      value: "text-2xl",
      trend: "text-xs"
    },
    lg: {
      card: "p-6",
      icon: "h-6 w-6",
      title: "text-base",
      value: "text-3xl",
      trend: "text-sm"
    }
  }

  const classes = sizeClasses[size]

  return (
    <Card className={`${className}`}>
      <CardContent className={classes.card}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <Icon className={`${classes.icon} text-gray-500`} />
              <p className={`${classes.title} font-medium text-gray-600`}>
                {title}
              </p>
            </div>
            
            <div className={`${classes.value} font-bold text-gray-900`}>
              {formatValue(value, format)}
            </div>
            
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">
                {subtitle}
              </p>
            )}
            
            {trend && (
              <div className={`flex items-center space-x-1 mt-2 ${getTrendColor()}`}>
                {getTrendIcon()}
                <span className={`${classes.trend} font-medium`}>
                  {Math.abs(trend.value)}% {trend.period}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function QuickMetrics({
  lastHourRevenue,
  typicalHourRevenue,
  todayOrders,
  todayRevenue,
  avgOrderValue,
  avgPrepTime,
  targetPrepTime,
  trendsVsYesterday,
  className = ""
}: QuickMetricsProps) {
  const hourlyRevenueChange = typicalHourRevenue > 0 
    ? ((lastHourRevenue - typicalHourRevenue) / typicalHourRevenue) * 100 
    : 0

  const prepTimeVsTarget = targetPrepTime > 0
    ? ((avgPrepTime - targetPrepTime) / targetPrepTime) * 100
    : 0

  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {/* Last Hour Revenue */}
      <QuickMetric
        title="Last Hour Revenue"
        value={lastHourRevenue}
        format="currency"
        icon={DollarSign}
        trend={{
          value: Math.round(hourlyRevenueChange),
          period: "vs typical",
          isPositive: hourlyRevenueChange >= 0
        }}
        subtitle="Revenue in the past 60 minutes"
      />

      {/* Today's Orders */}
      <QuickMetric
        title="Today's Orders"
        value={todayOrders}
        format="number"
        icon={ShoppingBag}
        trend={{
          value: Math.round(Math.abs(trendsVsYesterday.orders)),
          period: "vs yesterday",
          isPositive: trendsVsYesterday.orders >= 0
        }}
        subtitle="Orders placed today"
      />

      {/* Average Order Value */}
      <QuickMetric
        title="Avg Order Value"
        value={avgOrderValue}
        format="currency"
        icon={Target}
        trend={{
          value: Math.round(Math.abs(trendsVsYesterday.revenue - trendsVsYesterday.orders)),
          period: "vs yesterday",
          isPositive: (trendsVsYesterday.revenue - trendsVsYesterday.orders) >= 0
        }}
        subtitle="Average value per order"
      />

      {/* Prep Time Performance */}
      <QuickMetric
        title="Avg Prep Time"
        value={avgPrepTime}
        format="time"
        icon={Clock}
        trend={{
          value: Math.round(Math.abs(prepTimeVsTarget)),
          period: `vs ${targetPrepTime}min target`,
          isPositive: prepTimeVsTarget <= 0 // Lower prep time is better
        }}
        subtitle={`Target: ${targetPrepTime} minutes`}
      />
    </div>
  )
}

// Individual metric components for flexible usage
export function RevenueMetric({ value, trend, size = 'md' }: { 
  value: number
  trend?: MetricTrend
  size?: 'sm' | 'md' | 'lg'
}) {
  return (
    <QuickMetric
      title="Revenue"
      value={value}
      format="currency"
      icon={DollarSign}
      trend={trend}
      size={size}
    />
  )
}

export function OrdersMetric({ value, trend, size = 'md' }: { 
  value: number
  trend?: MetricTrend
  size?: 'sm' | 'md' | 'lg'
}) {
  return (
    <QuickMetric
      title="Orders"
      value={value}
      format="number"
      icon={ShoppingBag}
      trend={trend}
      size={size}
    />
  )
}

export function PrepTimeMetric({ value, target, size = 'md' }: { 
  value: number
  target: number
  size?: 'sm' | 'md' | 'lg'
}) {
  const variance = target > 0 ? ((value - target) / target) * 100 : 0
  
  return (
    <QuickMetric
      title="Prep Time"
      value={value}
      format="time"
      icon={Clock}
      trend={{
        value: Math.round(Math.abs(variance)),
        period: `vs ${target}min target`,
        isPositive: variance <= 0
      }}
      size={size}
    />
  )
}
