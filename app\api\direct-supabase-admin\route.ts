import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get Supabase URL and service role key from environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        error: 'Supabase environment variables not set',
        environment: {
          supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
          supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        }
      }, { status: 500 });
    }
    
    // Make a direct REST API call to Supabase with service role key
    const response = await fetch(`${supabaseUrl}/rest/v1/business_types?select=*&limit=5`, {
      headers: {
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json({
        error: 'Failed to fetch from Supabase REST API',
        status: response.status,
        statusText: response.statusText,
        errorText
      }, { status: 500 });
    }
    
    const data = await response.json();
    
    // Also try to fetch businesses
    const businessesResponse = await fetch(`${supabaseUrl}/rest/v1/businesses?select=*&limit=5`, {
      headers: {
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    let businessesData;
    let businessesError;
    
    if (businessesResponse.ok) {
      businessesData = await businessesResponse.json();
    } else {
      businessesError = {
        status: businessesResponse.status,
        statusText: businessesResponse.statusText,
        text: await businessesResponse.text()
      };
    }
    
    // Try to check if the tables exist
    const tablesResponse = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    let tablesData;
    let tablesError;
    
    if (tablesResponse.ok) {
      tablesData = await tablesResponse.json();
    } else {
      tablesError = {
        status: tablesResponse.status,
        statusText: tablesResponse.statusText,
        text: await tablesResponse.text()
      };
    }
    
    return NextResponse.json({
      success: true,
      businessTypes: data,
      businesses: businessesData || null,
      businessesError: businessesError || null,
      tables: tablesData || null,
      tablesError: tablesError || null
    });
  } catch (error) {
    console.error('Error in direct-supabase-admin API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
