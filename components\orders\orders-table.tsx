"use client"

import { useState } from "react"
import Link from "next/link"
import { format } from "date-fns"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import {
  HelpCircle,
  MoreHorizontal,
  Eye,
  Edit,
  Printer
} from "lucide-react"

export interface Order {
  id: number
  order_id: string
  order_number?: string
  status: string
  total_amount: number
  created_at: string
  updated_at: string
  customer_name?: string
  customer_email?: string
  customer_phone?: string
  delivery_address?: string
  notes?: string
  items_count?: number
  total_items?: number
  delivery_fee?: number
  service_fee?: number
  driver_name?: string
  driver_id?: string
  delivery_type?: string
  scheduled_delivery_time?: string
  business_id?: number
}

interface OrdersTableProps {
  orders: Order[]
  realtimeOrders: Order[]
  isLoading: boolean
  emptyMessage?: string
}

export function OrdersTable({
  orders,
  realtimeOrders,
  isLoading,
  emptyMessage = "No orders found."
}: OrdersTableProps) {

  // Format currency helper
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return '£0.00';

    // Ensure amount is a valid number
    const validAmount = isNaN(amount) ? 0 : amount;

    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(validAmount)
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'dd MMM yyyy')
  }

  // Format time helper - displays time in Jersey timezone (BST/GMT)
  const formatTime = (dateString: string) => {
    try {
      const utcDate = new Date(dateString)
      return new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZone: 'Europe/London'
      }).format(utcDate)
    } catch (error) {
      return format(new Date(dateString), 'HH:mm')
    }
  }

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'delivered':
        return "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100 hover:text-emerald-800 transition-colors"
      case 'processing':
      case 'preparing':
        return "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:text-blue-800 transition-colors"
      case 'ready':
        return "bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 hover:text-indigo-800 transition-colors"
      case 'out_for_delivery':
      case 'out for delivery':
        return "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:text-purple-800 transition-colors"
      case 'pending':
        return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 hover:text-amber-800 transition-colors"
      case 'confirmed':
        return "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100 hover:text-yellow-800 transition-colors"
      case 'cancelled':
        return "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800 transition-colors"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:text-slate-800 transition-colors"
    }
  }

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'out_for_delivery':
        return 'Out for Delivery'
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500"></div>
        <p className="ml-2 text-gray-600">Loading orders...</p>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">
              <Checkbox />
            </TableHead>
            <TableHead className="min-w-[100px]">
              <div className="flex items-center gap-1">
                Order Number
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Order number for reference</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[150px]">
              <div className="flex items-center gap-1">
                Customer
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Customer who placed the order</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[150px]">
              <div className="flex items-center gap-1">
                Date
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Date and time when the order was placed</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[100px]">
              <div className="flex items-center gap-1">
                Status
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Current status of the order</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[100px]">
              <div className="flex items-center gap-1">
                Items
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Number of items in the order</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[100px]">
              <div className="flex items-center gap-1">
                Total
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Total amount of the order</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="min-w-[150px]">
              <div className="flex items-center gap-1">
                Address
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="w-[200px] text-xs">Delivery address for the order</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.length > 0 ? (
            orders.map((order) => {
              // Check if this is a new order from real-time updates
              const isNewOrder = realtimeOrders.some(ro => ro.id === order.id && !orders.some(o => o.id === order.id && o !== order));

              return (
                <TableRow
                  key={order.id}
                  className={isNewOrder ? "bg-emerald-50 animate-pulse" : ""}
                >
                  <TableCell>
                    <Checkbox />
                  </TableCell>
                  <TableCell className="font-medium">
                    {order.order_number || order.order_id}
                    {isNewOrder && (
                      <Badge className="ml-2 bg-emerald-100 text-emerald-800 hover:bg-emerald-200">New</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{order.customer_name || "Guest"}</div>
                    <div className="text-xs text-muted-foreground">{order.customer_email || "No email"}</div>
                  </TableCell>
                  <TableCell>
                    {formatDate(order.created_at)}
                    <div className="text-xs text-muted-foreground">
                      {formatTime(order.created_at)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={getStatusBadgeClass(order.status)}
                    >
                      {formatStatus(order.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>{order.items_count || 0}</TableCell>
                  <TableCell>{formatCurrency(order.total_amount)}</TableCell>
                  <TableCell className="max-w-[200px] truncate" title={order.delivery_address || ""}>
                    {order.delivery_address || "No address"}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Link href={`/business-admin/orders/${order.id}`} className="flex items-center w-full">
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Update Status
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Printer className="mr-2 h-4 w-4" />
                          Print Invoice
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">Cancel Order</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="h-24 text-center">
                {emptyMessage}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TooltipProvider>
  )
}
