'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { StandardizedPostcode } from '@/lib/postcode-standardizer';
import { PostcodeInput } from '@/components/delivery/postcode-input';

// Sample postcodes for testing
const samplePostcodes = [
  { name: 'St Saviour', postcode: 'JE2 4UE' },
  { name: 'St John', postcode: 'JE3 4EQ' },
  { name: 'St Brelade', postcode: 'JE3 8BS' },
  { name: '<PERSON>', postcode: 'JE4 9FL' },
  { name: 'No Space', postcode: 'JE34EQ' },
  { name: 'Lowercase', postcode: 'je3 4eq' },
  { name: 'Extra Spaces', postcode: '  JE3  4EQ  ' },
  { name: 'Invalid Format', postcode: 'JE5 1AA' },
];

export default function PostcodeTestPage() {
  const [result, setResult] = useState<StandardizedPostcode | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentPostcode, setCurrentPostcode] = useState<string>('');

  const handlePostcodeChange = (standardizedPostcode: StandardizedPostcode) => {
    setResult(standardizedPostcode);
    setError(null);
  };

  const handleValidationError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleSamplePostcodeClick = (postcode: string) => {
    // Update the current postcode state
    setCurrentPostcode(postcode);
    // Reset the result and error
    setResult(null);
    setError(null);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Jersey Postcode Standardization Test</h1>
      <p className="mb-6 text-gray-600">
        This page tests the PostGIS address_standardizer extension for Jersey postcodes.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Input Postcode</CardTitle>
            <CardDescription>
              Enter a Jersey postcode to standardize and geocode.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PostcodeInput
              initialPostcode={currentPostcode}
              onPostcodeChange={handlePostcodeChange}
              onValidationError={handleValidationError}
              autoSubmit={true}
            />

            <div className="mt-8">
              <h3 className="text-sm font-medium mb-3">Sample Postcodes</h3>
              <div className="grid grid-cols-2 gap-2">
                {samplePostcodes.map((sample, index) => (
                  <Card
                    key={index}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${currentPostcode === sample.postcode ? 'border-emerald-500 bg-emerald-50' : ''}`}
                    onClick={() => handleSamplePostcodeClick(sample.postcode)}
                  >
                    <div className="text-sm font-medium">{sample.name}</div>
                    <div className="text-xs text-gray-500">{sample.postcode}</div>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Standardization Result</CardTitle>
            <CardDescription>
              The standardized postcode information will appear here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}

            {result && (
              <div className="space-y-4">
                <div className={`px-4 py-3 rounded mb-4 ${
                  result.is_valid
                    ? "bg-green-50 border border-green-200 text-green-700"
                    : "bg-yellow-50 border border-yellow-200 text-yellow-700"
                }`}>
                  {result.is_valid
                    ? 'Postcode standardized successfully'
                    : 'Postcode standardization failed: ' + (result.validation_message || 'Invalid format')}
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">Standardization Details</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Original:</div>
                    <div className="text-sm">{result.original}</div>

                    <div className="text-sm font-medium">Standardized:</div>
                    <div className="text-sm">{result.standardized || 'N/A'}</div>

                    <div className="text-sm font-medium">Valid:</div>
                    <div className="text-sm">{result.is_valid ? 'Yes' : 'No'}</div>

                    {result.validation_message && (
                      <>
                        <div className="text-sm font-medium">Message:</div>
                        <div className="text-sm">{result.validation_message}</div>
                      </>
                    )}
                  </div>
                </div>

                {result.coordinates && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <h3 className="font-semibold">Geocoding Result</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium">Longitude:</div>
                        <div className="text-sm">{result.coordinates[0]}</div>

                        <div className="text-sm font-medium">Latitude:</div>
                        <div className="text-sm">{result.coordinates[1]}</div>

                        <div className="text-sm font-medium">Source:</div>
                        <div className="text-sm">
                          {result.source === 'nominatim' ? (
                            <span className="text-blue-600">OpenStreetMap (Nominatim)</span>
                          ) : result.source === 'database' ? (
                            <span className="text-green-600">Database</span>
                          ) : (
                            <span className="text-orange-600">{result.source || 'Unknown'}</span>
                          )}
                        </div>

                        <div className="text-sm font-medium">Parish:</div>
                        <div className="text-sm">
                          {result.parish ? (
                            <span className="font-medium">{result.parish}</span>
                          ) : (
                            <button
                              className="text-blue-600 hover:text-blue-800 underline"
                              onClick={async () => {
                                try {
                                  const response = await fetch(`/api/postcodes?action=parish&postcode=${encodeURIComponent(result.standardized || result.original)}`);
                                  const data = await response.json();
                                  if (response.ok && data.parish) {
                                    alert(`Parish: ${data.parish}`);
                                  } else {
                                    alert('Could not determine parish');
                                  }
                                } catch (error) {
                                  console.error('Error getting parish:', error);
                                  alert('Error getting parish');
                                }
                              }}
                            >
                              Check Parish
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Map Preview</h4>
                        <div className="aspect-video bg-gray-200 rounded-md overflow-hidden">
                          <iframe
                            width="100%"
                            height="100%"
                            frameBorder="0"
                            src={`https://www.openstreetmap.org/export/embed.html?bbox=${result.coordinates[0] - 0.01},${result.coordinates[1] - 0.01},${result.coordinates[0] + 0.01},${result.coordinates[1] + 0.01}&marker=${result.coordinates[1]},${result.coordinates[0]}`}
                            style={{ border: 0 }}
                          ></iframe>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}

            {!error && !result && (
              <div className="text-center py-8 text-gray-500">
                Enter a postcode and click "Submit" to see the result.
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
