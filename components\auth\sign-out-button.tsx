"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/context/unified-auth-context"
import { useState } from "react"

export default function SignOutButton() {
  const { signOut } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      console.log("Sign-out button clicked")
      await signOut()
      // The signOut function handles the redirect
    } catch (error) {
      console.error("Error signing out:", error)
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant="destructive"
      onClick={handleSignOut}
      disabled={isLoading}
      className="w-full"
    >
      {isLoading ? "Signing out..." : "Sign Out"}
    </Button>
  )
}
