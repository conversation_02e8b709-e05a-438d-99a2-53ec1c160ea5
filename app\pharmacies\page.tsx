"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function PharmaciesPage() {
  const router = useRouter()
  
  // Redirect to the search page with pharmacy filter
  useEffect(() => {
    router.replace('/search?type=pharmacy')
  }, [])

  return (
    <div className="container-fluid py-8">
      <h1 className="text-3xl font-bold mb-6">Redirecting to Search...</h1>
      <p className="text-gray-600">Please wait while we redirect you to the pharmacies search page.</p>
    </div>
  )
}
