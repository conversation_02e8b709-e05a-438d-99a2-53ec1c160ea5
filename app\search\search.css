/* Smaller card styles for search results */
.business-card-small :global(.card) {
  height: 100%;
}

.business-card-small :global(.card .relative) {
  height: 120px !important;
}

.business-card-small :global(.card .p-4) {
  padding: 0.75rem !important;
}

.business-card-small :global(.card h3) {
  font-size: 0.9rem !important;
  line-height: 1.2 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-card-small :global(.card .text-sm) {
  font-size: 0.75rem !important;
}

.business-card-small :global(.card .mb-3) {
  margin-bottom: 0.5rem !important;
}

.business-card-small :global(.card .mb-2) {
  margin-bottom: 0.25rem !important;
}

.business-card-small :global(.card .h-4),
.business-card-small :global(.card .w-4) {
  height: 0.875rem !important;
  width: 0.875rem !important;
}

.business-card-small :global(.card .bg-green-50) {
  padding: 0.125rem 0.375rem !important;
}

.business-card-small :global(.card .h-3),
.business-card-small :global(.card .w-3) {
  height: 0.75rem !important;
  width: 0.75rem !important;
}
