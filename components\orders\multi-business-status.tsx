"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { BusinessStatusBadge } from "./business-status-badge"
import { BusinessStatusUpdater } from "./business-status-updater"
import { Badge } from "@/components/ui/badge"
import { Store, ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { getBusinessTypeIconWithStyle } from "@/utils/business-type-icons"

interface BusinessStatus {
  id: number
  business_id: number
  business_name: string
  business_type: string
  status: string
  subtotal: number
  delivery_fee: number
  total: number
}

interface MultiBusinessStatusProps {
  orderId: number
  businesses: BusinessStatus[]
  mainOrderStatus: string
  isAdmin?: boolean
  onStatusChange?: (businessId: number, newStatus: string) => void
}

export function MultiBusinessStatus({
  orderId,
  businesses,
  mainOrderStatus,
  isAdmin = false,
  onStatusChange
}: MultiBusinessStatusProps) {
  const [expandedBusinesses, setExpandedBusinesses] = useState<number[]>([])

  const toggleBusiness = (businessId: number) => {
    setExpandedBusinesses(prev =>
      prev.includes(businessId)
        ? prev.filter(id => id !== businessId)
        : [...prev, businessId]
    )
  }

  const handleStatusChange = (businessId: number, newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(businessId, newStatus)
    }
  }

  // If there's only one business, don't show the multi-business UI
  if (businesses.length === 1) {
    const business = businesses[0]

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Order Status</CardTitle>
        </CardHeader>
        <CardContent>
          {isAdmin && onStatusChange ? (
            <BusinessStatusUpdater
              orderId={orderId}
              businessId={business.business_id}
              businessName={business.business_name}
              currentStatus={business.status}
              onStatusChange={(newStatus) => handleStatusChange(business.business_id, newStatus)}
            />
          ) : (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Status:</span>
              <BusinessStatusBadge status={business.status} />
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Order Status</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Overall:</span>
            <BusinessStatusBadge status={mainOrderStatus} />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          This order includes items from multiple businesses. Each business is processing their part of your order independently.
        </p>

        <div className="space-y-3">
          {businesses.map((business) => (
            <div key={business.id} className="border rounded-md overflow-hidden">
              <div
                className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer"
                onClick={() => toggleBusiness(business.business_id)}
              >
                <div className="flex items-center space-x-2">
                  {getBusinessTypeIconWithStyle(business.business_type, "h-4 w-4 text-gray-500")}
                  <span className="font-medium">{business.business_name}</span>
                  <Badge
                    variant="outline"
                    className={cn(
                      "ml-2 text-xs",
                      business.business_type === "restaurant" && "bg-orange-50 text-orange-700 border-orange-200",
                      business.business_type === "shop" && "bg-blue-50 text-blue-700 border-blue-200",
                      business.business_type === "cafe" && "bg-amber-50 text-amber-700 border-amber-200",
                      business.business_type === "pharmacy" && "bg-green-50 text-green-700 border-green-200"
                    )}
                  >
                    {business.business_type}
                  </Badge>
                </div>
                <div className="flex items-center space-x-3">
                  <BusinessStatusBadge status={business.status} size="sm" />
                  {expandedBusinesses.includes(business.business_id) ? (
                    <ChevronUp className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>

              {expandedBusinesses.includes(business.business_id) && (
                <div className="p-3 border-t">
                  {isAdmin && onStatusChange ? (
                    <BusinessStatusUpdater
                      orderId={orderId}
                      businessId={business.business_id}
                      businessName={business.business_name}
                      currentStatus={business.status}
                      onStatusChange={(newStatus) => handleStatusChange(business.business_id, newStatus)}
                    />
                  ) : (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">Status:</span>
                        <BusinessStatusBadge status={business.status} />
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {getStatusDescription(business.status, business.business_name)}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function getStatusDescription(status: string, businessName: string): string {
  switch (status) {
    case "pending":
      return `${businessName} has received your order and will confirm it shortly.`
    case "confirmed":
      return `${businessName} has confirmed your order.`
    case "preparing":
      return `${businessName} is preparing your order.`
    case "ready":
      return `Your order is ready at ${businessName}.`
    case "out_for_delivery":
      return `Your order from ${businessName} is out for delivery.`
    case "delivered":
      return `Your order from ${businessName} has been delivered.`
    case "cancelled":
      return `Your order from ${businessName} has been cancelled.`
    default:
      return `Your order status is ${status}.`
  }
}
