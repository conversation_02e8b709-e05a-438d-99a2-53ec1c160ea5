import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// PUT endpoint to update a cart item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: { user } } = await supabaseClient.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Get the cart item ID from the URL
    const cartItemId = params.id;
    
    if (!cartItemId) {
      return NextResponse.json(
        { error: 'Cart item ID is required' },
        { status: 400 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    
    if (!body.quantity) {
      return NextResponse.json(
        { error: 'Quantity is required' },
        { status: 400 }
      );
    }
    
    // Update the cart item quantity
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity: body.quantity })
      .eq('id', cartItemId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating cart item:', error);
      return NextResponse.json(
        { error: 'Failed to update cart item', details: error.message },
        { status: 500 }
      );
    }
    
    // Get the updated cart
    const { data: updatedCart, error: cartError } = await supabase.rpc(
      'get_user_cart_enhanced',
      { p_user_id: user.id }
    );
    
    if (cartError) {
      console.error('Error fetching updated cart:', cartError);
      return NextResponse.json({
        success: true,
        item: data,
        error: 'Failed to fetch updated cart'
      });
    }
    
    return NextResponse.json({
      success: true,
      item: data,
      cart: updatedCart.cart,
      businessNames: updatedCart.businessNames
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove a cart item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: { user } } = await supabaseClient.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Get the cart item ID from the URL
    const cartItemId = params.id;
    
    if (!cartItemId) {
      return NextResponse.json(
        { error: 'Cart item ID is required' },
        { status: 400 }
      );
    }
    
    // Remove the cart item
    const { data, error } = await supabase.rpc(
      'remove_cart_item',
      {
        p_user_id: user.id,
        p_cart_item_id: cartItemId
      }
    );
    
    if (error) {
      console.error('Error removing cart item:', error);
      return NextResponse.json(
        { error: 'Failed to remove cart item', details: error.message },
        { status: 500 }
      );
    }
    
    // Get the updated cart
    const { data: updatedCart, error: cartError } = await supabase.rpc(
      'get_user_cart_enhanced',
      { p_user_id: user.id }
    );
    
    if (cartError) {
      console.error('Error fetching updated cart:', cartError);
      return NextResponse.json({
        success: true,
        removed: data,
        error: 'Failed to fetch updated cart'
      });
    }
    
    return NextResponse.json({
      success: true,
      removed: data,
      cart: updatedCart.cart,
      businessNames: updatedCart.businessNames
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
