"use client"

import { useState, useEffect } from "react"
import { WifiOff, Refresh<PERSON>w, MapPin, Package } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileNavigation } from "@/components/driver-mobile-navigation"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import { offlineDBService } from "@/services/offline-db-service"
import { deliveryService } from "@/services/delivery-service"

export default function OfflinePage() {
  const [deliveries, setDeliveries] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null)

  useEffect(() => {
    async function loadOfflineData() {
      try {
        // Initialize delivery service
        await deliveryService.initialize()

        // Load cached deliveries
        const cachedDeliveries = await offlineDBService.getDeliveries()
        setDeliveries(cachedDeliveries)

        // Get last sync time from localStorage
        const lastSync = localStorage.getItem("lastSyncTime")
        setLastSyncTime(lastSync)
      } catch (error) {
        console.error("Failed to load offline data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadOfflineData()
  }, [])

  const handleCheckConnection = () => {
    if (navigator.onLine) {
      window.location.href = "/driver-mobile/dashboard"
    } else {
      alert("Still offline. Please check your internet connection.")
    }
  }

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      <div className="p-4 space-y-6">
          <div className="text-center py-6">
            <WifiOff className="h-16 w-16 text-amber-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold">You're Offline</h1>
            <p className="text-gray-500 mt-2">
              You can still view your current deliveries and complete essential tasks. We'll sync everything when you're
              back online.
            </p>

            {lastSyncTime && <p className="text-xs text-gray-400 mt-2">Last synced: {lastSyncTime}</p>}

            <Button onClick={handleCheckConnection} className="mt-4" variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Check Connection
            </Button>
          </div>

      <div className="space-y-2">
        <h2 className="text-lg font-bold">Cached Deliveries</h2>

        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Loading cached data...</p>
          </div>
        ) : deliveries.length > 0 ? (
          <div className="space-y-4">
            {deliveries.map((delivery) => (
              <Card key={delivery.id} className="border-2 border-amber-100">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      Offline Mode
                    </div>
                    <span className="ml-2 text-sm font-medium">Order #{delivery.orderId}</span>

                    {delivery.syncStatus === "pending" && (
                      <div className="ml-auto bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        Pending Sync
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <p className="text-xs text-gray-500 mb-1">Pickup</p>
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                        <p className="text-sm">
                          {delivery.restaurant.name}, {delivery.restaurant.address}
                        </p>
                      </div>
                    </div>

                    <div>
                      <p className="text-xs text-gray-500 mb-1">Dropoff</p>
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                        <p className="text-sm">
                          {delivery.customer.name}, {delivery.customer.address}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-2">
                      <div>
                        <p className="text-xs text-gray-500">Distance</p>
                        <p className="text-sm font-medium">{delivery.distance}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Est. Time</p>
                        <p className="text-sm font-medium">{delivery.estimatedTime}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Earnings</p>
                        <p className="text-sm font-bold">£{delivery.earnings.toFixed(2)}</p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full mt-2"
                      onClick={() => (window.location.href = `/driver-mobile/deliveries/${delivery.id}?offline=true`)}
                    >
                      <Package className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <p className="text-gray-500">No cached deliveries available.</p>
          </div>
        )}
      </div>

      <div className="bg-amber-50 p-4 rounded-lg">
        <h3 className="font-medium text-amber-800">Offline Mode Limitations</h3>
        <ul className="mt-2 text-sm text-amber-700 space-y-1">
          <li>• You can view your current deliveries</li>
          <li>• You can mark deliveries as picked up or completed</li>
          <li>• Limited map functionality is available</li>
          <li>• You cannot receive new delivery requests</li>
          <li>• Changes will sync when you're back online</li>
        </ul>
      </div>

          {/* Bottom Spacing for Navigation */}
          <div className="h-20"></div>
        </div>

        <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
