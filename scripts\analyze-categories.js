// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? 'Key is set' : 'Key is not set');

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function analyzeCategories() {
  try {
    console.log('=== ANALYZING CATEGORIES FOR DUPLICATES AND CLEANUP ===\n');

    // Get all categories with business type info
    const { data: categories, error } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        level,
        parent_id,
        business_type_id,
        category_purpose,
        is_active,
        display_order,
        business_types!business_type_id (
          name
        )
      `)
      .order('business_type_id', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      return;
    }

    console.log(`📊 Total categories found: ${categories.length}\n`);

    // Group by business type
    const byBusinessType = {};
    categories.forEach(cat => {
      const businessTypeName = cat.business_types?.name || 'Unknown';
      if (!byBusinessType[businessTypeName]) {
        byBusinessType[businessTypeName] = [];
      }
      byBusinessType[businessTypeName].push(cat);
    });

    // Analyze each business type
    Object.keys(byBusinessType).forEach(businessType => {
      console.log(`\n🏢 ${businessType.toUpperCase()} (${byBusinessType[businessType].length} categories)`);
      console.log('='.repeat(60));
      
      const cats = byBusinessType[businessType];
      cats.forEach(cat => {
        const status = cat.is_active ? '✅' : '❌';
        const level = cat.level || 'N/A';
        const purpose = cat.category_purpose || 'N/A';
        console.log(`${status} ${cat.name} (${cat.slug}) - Level: ${level}, Purpose: ${purpose}, ID: ${cat.id}`);
      });
    });

    // Find potential duplicates by name similarity
    console.log('\n\n🔍 POTENTIAL DUPLICATES AND SIMILAR CATEGORIES');
    console.log('='.repeat(60));

    const duplicateGroups = findSimilarCategories(categories);
    duplicateGroups.forEach((group, index) => {
      if (group.length > 1) {
        console.log(`\n📋 Group ${index + 1} - Similar categories:`);
        group.forEach(cat => {
          const businessTypeName = cat.business_types?.name || 'Unknown';
          const status = cat.is_active ? '✅' : '❌';
          console.log(`   ${status} ${cat.name} (${cat.slug}) - ${businessTypeName} - ID: ${cat.id}`);
        });
      }
    });

    // Find exact slug duplicates
    console.log('\n\n🚨 EXACT SLUG DUPLICATES');
    console.log('='.repeat(60));
    
    const slugGroups = {};
    categories.forEach(cat => {
      if (!slugGroups[cat.slug]) {
        slugGroups[cat.slug] = [];
      }
      slugGroups[cat.slug].push(cat);
    });

    Object.keys(slugGroups).forEach(slug => {
      if (slugGroups[slug].length > 1) {
        console.log(`\n🔴 Duplicate slug: "${slug}"`);
        slugGroups[slug].forEach(cat => {
          const businessTypeName = cat.business_types?.name || 'Unknown';
          const status = cat.is_active ? '✅' : '❌';
          console.log(`   ${status} ${cat.name} - ${businessTypeName} - ID: ${cat.id}`);
        });
      }
    });

    // Summary and recommendations
    console.log('\n\n📋 CLEANUP RECOMMENDATIONS');
    console.log('='.repeat(60));
    
    const inactiveCount = categories.filter(c => !c.is_active).length;
    const duplicateSlugCount = Object.values(slugGroups).filter(group => group.length > 1).length;
    
    console.log(`• Total categories: ${categories.length}`);
    console.log(`• Inactive categories: ${inactiveCount}`);
    console.log(`• Duplicate slugs: ${duplicateSlugCount}`);
    console.log(`• Recommended for removal: ${inactiveCount + (duplicateSlugCount * 0.5)} (estimate)`);

  } catch (error) {
    console.error('Error analyzing categories:', error);
  }
}

function findSimilarCategories(categories) {
  const groups = [];
  const processed = new Set();

  categories.forEach(cat1 => {
    if (processed.has(cat1.id)) return;

    const similarGroup = [cat1];
    processed.add(cat1.id);

    categories.forEach(cat2 => {
      if (cat1.id === cat2.id || processed.has(cat2.id)) return;

      // Check for similarity
      if (areSimilar(cat1, cat2)) {
        similarGroup.push(cat2);
        processed.add(cat2.id);
      }
    });

    groups.push(similarGroup);
  });

  return groups;
}

function areSimilar(cat1, cat2) {
  const name1 = cat1.name.toLowerCase();
  const name2 = cat2.name.toLowerCase();
  const slug1 = cat1.slug.toLowerCase();
  const slug2 = cat2.slug.toLowerCase();

  // Exact name match
  if (name1 === name2) return true;

  // Similar slugs (edit distance)
  if (levenshteinDistance(slug1, slug2) <= 2 && Math.abs(slug1.length - slug2.length) <= 2) return true;

  // Contains relationship
  if (name1.includes(name2) || name2.includes(name1)) return true;

  // Common patterns
  const patterns = [
    ['food', 'foods'],
    ['drink', 'drinks', 'beverage', 'beverages'],
    ['health', 'healthcare', 'medical'],
    ['beauty', 'cosmetic', 'cosmetics'],
    ['home', 'household'],
    ['personal', 'personal-care'],
    ['cleaning', 'clean'],
    ['grocery', 'groceries']
  ];

  for (const pattern of patterns) {
    const inPattern1 = pattern.some(p => name1.includes(p) || slug1.includes(p));
    const inPattern2 = pattern.some(p => name2.includes(p) || slug2.includes(p));
    if (inPattern1 && inPattern2) return true;
  }

  return false;
}

function levenshteinDistance(str1, str2) {
  const matrix = [];
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  return matrix[str2.length][str1.length];
}

analyzeCategories()
  .then(() => {
    console.log('\n✨ Analysis completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
