
import React from 'react';
import { ProductUploadProvider } from '@/context/ProductUploadContext';
import { ProductUploadContainer } from '@/components/product-upload/ProductUploadContainer';

const Index: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <span className="bg-gradient-to-r from-app-blue to-app-blue-dark bg-clip-text text-transparent">
              ProductScribe AI
            </span>
          </h1>
          <div className="text-sm text-gray-500">
            AI-Powered Product Management
          </div>
        </div>
      </header>
      
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Product Data Upload & Enhancement</h2>
          <p className="max-w-2xl mx-auto text-gray-600">
            Upload your product CSV file and our AI will automatically generate rich descriptions,
            appropriate categories, and detailed attributes for your products.
          </p>
        </div>
        
        <ProductUploadProvider>
          <ProductUploadContainer />
        </ProductUploadProvider>
      </main>
      
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <p className="text-sm text-center text-gray-500">
            ProductScribe AI • Enhance product data with artificial intelligence
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
