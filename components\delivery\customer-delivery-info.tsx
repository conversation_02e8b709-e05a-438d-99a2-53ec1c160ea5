// PHASE 6 STEP 13: Customer Delivery Information Component
// Shows delivery availability information to customers

"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Truck, Clock, Thermometer, Users } from 'lucide-react';

interface CustomerDeliveryInfo {
  availableDriversInArea: number;
  estimatedDeliveryTime: string;
  thermalBagAvailable: boolean;
  message: string;
}

interface CustomerDeliveryInfoProps {
  parish?: string;
  className?: string;
}

export function CustomerDeliveryInfo({ parish, className }: CustomerDeliveryInfoProps) {
  const [deliveryInfo, setDeliveryInfo] = useState<CustomerDeliveryInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDeliveryInfo();
  }, [parish]);

  const fetchDeliveryInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/delivery-metrics?type=customer&parish=${parish || ''}`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDeliveryInfo(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching delivery info:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Truck className="h-4 w-4 animate-pulse" />
            <span className="text-sm text-muted-foreground">Loading delivery info...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!deliveryInfo) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Truck className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Delivery info unavailable</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = () => {
    if (deliveryInfo.availableDriversInArea === 0) return 'destructive';
    if (deliveryInfo.availableDriversInArea < 3) return 'secondary';
    return 'default';
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center">
          <Truck className="h-4 w-4 mr-2" />
          Delivery Availability
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Driver Availability */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Drivers available</span>
          </div>
          <Badge variant={getStatusColor()}>
            {deliveryInfo.availableDriversInArea}
          </Badge>
        </div>

        {/* Estimated Delivery Time */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Estimated time</span>
          </div>
          <span className="text-sm font-medium">
            {deliveryInfo.estimatedDeliveryTime}
          </span>
        </div>

        {/* Thermal Bag Availability */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Thermometer className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Hot/cold items</span>
          </div>
          <Badge variant={deliveryInfo.thermalBagAvailable ? 'default' : 'secondary'}>
            {deliveryInfo.thermalBagAvailable ? 'Available' : 'Limited'}
          </Badge>
        </div>

        {/* Status Message */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            {deliveryInfo.message}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
