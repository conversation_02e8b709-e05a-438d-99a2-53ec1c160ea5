import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { adminClient } from "@/lib/supabase-admin"
import { createServerSupabase } from "@/lib/supabase-server"

// Types for our API
interface ConnectionRequest {
  user_id: string
  connection_type: 'customer-business' | 'business-rider' | 'customer-rider' | 'business-business' | 'rider-rider'
  notes?: string
}

interface ConnectionUpdate {
  status?: 'pending' | 'active' | 'blocked' | 'archived'
  is_favorite?: boolean
  notes?: string
}

// Helper function to get authenticated user
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

// GET - Retrieve user's connections
export async function GET(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const status = url.searchParams.get('status') || 'active'
    const type = url.searchParams.get('type')
    const favorites_only = url.searchParams.get('favorites_only') === 'true'

    // Build query
    let query = adminClient
      .from('connections')
      .select(`
        id,
        connection_type,
        status,
        user1_favorite,
        user2_favorite,
        created_at,
        updated_at,
        notes,
        user1_id,
        user2_id
      `)
      .or(`user1_id.eq.${user.id},user2_id.eq.${user.id}`)

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status)
    }

    if (type) {
      query = query.eq('connection_type', type)
    }

    const { data: connections, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error("Error fetching connections:", error)
      return NextResponse.json(
        { error: "Failed to fetch connections" },
        { status: 500 }
      )
    }

    // Transform connections to include other user info and favorite status
    const transformedConnections = connections?.map(conn => {
      const isUser1 = conn.user1_id === user.id
      const otherUserId = isUser1 ? conn.user2_id : conn.user1_id
      const isFavorite = isUser1 ? conn.user1_favorite : conn.user2_favorite

      return {
        id: conn.id,
        connection_type: conn.connection_type,
        status: conn.status,
        other_user_id: otherUserId,
        is_favorite: isFavorite,
        created_at: conn.created_at,
        updated_at: conn.updated_at,
        notes: conn.notes
      }
    }) || []

    // Filter by favorites if requested
    const filteredConnections = favorites_only 
      ? transformedConnections.filter(conn => conn.is_favorite)
      : transformedConnections

    return NextResponse.json({
      data: filteredConnections,
      count: filteredConnections.length
    })

  } catch (error) {
    console.error("Unexpected error in GET /api/connections-hub/connections:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST - Create new connection
export async function POST(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const body: ConnectionRequest = await request.json()
    const { user_id: otherUserId, connection_type, notes } = body

    // Validate required fields
    if (!otherUserId || !connection_type) {
      return NextResponse.json(
        { error: "user_id and connection_type are required" },
        { status: 400 }
      )
    }

    // Prevent self-connection
    if (otherUserId === user.id) {
      return NextResponse.json(
        { error: "Cannot create connection with yourself" },
        { status: 400 }
      )
    }

    // Validate connection type
    const validTypes = ['customer-business', 'business-rider', 'customer-rider', 'business-business', 'rider-rider']
    if (!validTypes.includes(connection_type)) {
      return NextResponse.json(
        { error: "Invalid connection_type" },
        { status: 400 }
      )
    }

    // Order user IDs consistently (user1_id < user2_id)
    const user1_id = user.id < otherUserId ? user.id : otherUserId
    const user2_id = user.id < otherUserId ? otherUserId : user.id

    // Check if connection already exists
    const { data: existingConnection, error: checkError } = await adminClient
      .from('connections')
      .select('id, status')
      .eq('user1_id', user1_id)
      .eq('user2_id', user2_id)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error("Error checking existing connection:", checkError)
      return NextResponse.json(
        { error: "Failed to check existing connection" },
        { status: 500 }
      )
    }

    if (existingConnection) {
      return NextResponse.json(
        { 
          error: "Connection already exists",
          existing_connection: {
            id: existingConnection.id,
            status: existingConnection.status
          }
        },
        { status: 409 }
      )
    }

    // Create new connection
    const connectionData = {
      user1_id,
      user2_id,
      connection_type,
      status: 'pending',
      created_by: user.id,
      notes: notes || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: newConnection, error: createError } = await adminClient
      .from('connections')
      .insert([connectionData])
      .select()
      .single()

    if (createError) {
      console.error("Error creating connection:", createError)
      return NextResponse.json(
        { error: "Failed to create connection" },
        { status: 500 }
      )
    }

    // Transform response to match GET format
    const isUser1 = newConnection.user1_id === user.id
    const response = {
      id: newConnection.id,
      connection_type: newConnection.connection_type,
      status: newConnection.status,
      other_user_id: isUser1 ? newConnection.user2_id : newConnection.user1_id,
      is_favorite: false,
      created_at: newConnection.created_at,
      updated_at: newConnection.updated_at,
      notes: newConnection.notes
    }

    return NextResponse.json({
      data: response,
      message: "Connection request sent successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Unexpected error in POST /api/connections-hub/connections:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
