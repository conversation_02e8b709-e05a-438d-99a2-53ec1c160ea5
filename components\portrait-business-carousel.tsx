"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import PortraitBusinessCard from "@/components/portrait-business-card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

interface Business {
  id: string | number;
  slug: string;
  name: string;
  logo_url?: string;
  banner_url?: string;
  rating?: number;
  review_count?: number;
  delivery_time_minutes?: number;
  preparation_time_minutes?: number;
  delivery_fee?: number;
  delivery_radius?: number;
  minimum_order_amount?: number;
  location?: string;
  business_type: string;
  business_type_slug: string;
}

interface PortraitBusinessCarouselProps {
  businesses: Business[];
  title: string;
}

export default function PortraitBusinessCarousel({ businesses, title }: PortraitBusinessCarouselProps) {
  if (!businesses || businesses.length === 0) {
    return <p className="text-center text-gray-500">No businesses found.</p>;
  }

  return (
    <div className="py-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">{title}</h2>
        <Link href="/search" className="text-emerald-600 hover:text-emerald-700 text-sm font-medium flex items-center">
          View all
          <ArrowRight className="ml-1 h-4 w-4" />
        </Link>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-4">
          {businesses.map((business) => {
            // Check if business has a logo
            const hasLogo = business.logo_url && business.logo_url !== "/placeholder.svg";
            
            // Generate offers based on business properties
            const offers = [];
            
            if (business.delivery_fee === 0) {
              offers.push({ text: "Free Delivery", color: "bg-emerald-500" });
            }
            
            if (business.minimum_order_amount && business.minimum_order_amount < 10) {
              offers.push({ text: "Low Min Order", color: "bg-blue-500" });
            }
            
            // Add a random offer for demo purposes
            if (Math.random() > 0.7) {
              offers.push({ text: "20% OFF", color: "bg-red-500" });
            }

            return (
              <CarouselItem key={business.id} className="md:basis-1/3 lg:basis-1/4 xl:basis-1/5 pl-4">
                <PortraitBusinessCard
                  id={business.slug || business.id.toString()}
                  name={business.name}
                  image={business.logo_url || "/placeholder.svg"}
                  businessType={business.business_type_slug || "business"}
                  rating={business.rating}
                  deliveryTime={business.delivery_time_minutes ? `${business.delivery_time_minutes}` : "30"}
                  deliveryTimeRange={`${business.delivery_time_minutes ? Math.max(5, business.delivery_time_minutes - 5) : 25}-${business.delivery_time_minutes ? business.delivery_time_minutes + 5 : 35} min`}
                  deliveryFee={business.delivery_fee === 0
                    ? "Free delivery"
                    : `£${business.delivery_fee?.toFixed(2) || "N/A"}`}
                  distance={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
                  sponsored={false}
                  offers={offers}
                  location={business.location}
                  deliveryRadius={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
                  preparationTime={business.preparation_time_minutes ? `${business.preparation_time_minutes} min` : undefined}
                />
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <div className="hidden md:block">
          <CarouselPrevious className="left-0" />
          <CarouselNext className="right-0" />
        </div>
      </Carousel>
    </div>
  );
}
