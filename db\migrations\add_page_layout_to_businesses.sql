-- Migration: Add page_layout column to businesses table
-- This migration adds a page_layout column to support standard vs aisle layouts

-- Step 1: Add the page_layout column to the businesses table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'businesses' AND column_name = 'page_layout'
  ) THEN
    -- Add page_layout column with ENUM-like constraint
    ALTER TABLE businesses ADD COLUMN page_layout VARCHAR(20) NOT NULL DEFAULT 'standard';
    
    -- Add constraint to ensure only valid values
    ALTER TABLE businesses ADD CONSTRAINT businesses_page_layout_check 
      CHECK (page_layout IN ('standard', 'aisle'));
    
    -- Add a comment to explain the purpose of the column
    COMMENT ON COLUMN businesses.page_layout IS 'Business page layout: standard (for <100 items) or aisle (for larger inventories)';
  END IF;
END $$;

-- Step 2: Create an index for faster lookups by page_layout
CREATE INDEX IF NOT EXISTS idx_businesses_page_layout ON businesses(page_layout);

-- Step 3: Verify the update
SELECT 
  table_name, 
  column_name, 
  data_type, 
  column_default, 
  is_nullable
FROM 
  information_schema.columns
WHERE 
  table_name = 'businesses' 
  AND column_name = 'page_layout'
ORDER BY 
  table_name, 
  column_name;
