"use client"

import { useState, useEffect } from 'react'
import { DollarSign, Package, TrendingUp, Star, MapPin, Calendar } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DriverDashboardHeader } from '@/components/driver-dashboard-header'
import { DriverMobileNavigation } from '@/components/driver-mobile-navigation'
import { DriverMobileContainer } from '@/components/driver-mobile-container'
import Link from 'next/link'

interface EarningsData {
  period: string
  summary: {
    totalEarnings: number
    totalBase: number
    totalTips: number
    totalBonuses: number
    deliveryCount: number
    averageEarningPerDelivery: number
    pendingAmount: number
    paidAmount: number
    readyForPayoutAmount: number
    currency: string
  }
  quickStats: {
    today: number
    thisWeek: number
    totalDeliveries: number
  }
  earnings: Array<{
    id: number
    orderId: number
    orderNumber: string
    businessName: string
    customerName: string
    deliveryType: string
    orderTotal: number
    baseAmount: number
    tipAmount: number
    bonusAmount: number
    totalAmount: number
    paymentStatus: string
    payoutId?: string
    payoutDate?: string
    eligibleForPayoutAt?: string
    earnedAt: string
  }>
  pagination: {
    limit: number
    offset: number
    hasMore: boolean
  }
  timestamp: string
}

export default function DriverMobileEarningsPage() {
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [period, setPeriod] = useState('week')

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const fetchEarnings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/driver/earnings?period=${period}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch earnings: ${response.statusText}`)
      }

      const data = await response.json()
      setEarningsData(data)
    } catch (err) {
      console.error('Error fetching earnings:', err)
      setError(err instanceof Error ? err.message : 'Failed to load earnings')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchEarnings()
  }, [period])

  if (isLoading) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
          </div>
        </div>
      </DriverMobileContainer>
    )
  }

  if (error) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6 text-center">
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={fetchEarnings} variant="outline" className="border-red-200 text-red-700 hover:bg-red-100">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </DriverMobileContainer>
    )
  }

  if (!earningsData) {
    return null
  }

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      {/* Content */}
      <div className="p-4 space-y-4">

          {/* Period Selector */}
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Earnings</h2>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPeriod('today')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  period === 'today'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Today
              </button>
              <button
                onClick={() => setPeriod('week')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  period === 'week'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                This Week
              </button>
              <button
                onClick={() => setPeriod('month')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  period === 'month'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                This Month
              </button>
            </div>
          </div>

          {/* Quick Stats - Clear period labeling */}
          <div className="grid grid-cols-2 gap-3">
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-3 text-center">
                <div className="text-2xl font-bold text-green-700">
                  {formatCurrency(earningsData.summary.totalEarnings)}
                </div>
                <div className="text-xs text-green-600 font-medium">
                  {period === 'today' ? "Today's" : period === 'week' ? "This Week's" : "This Month's"} Earnings
                </div>
              </CardContent>
            </Card>

            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-3 text-center">
                <div className="text-2xl font-bold text-blue-700">
                  {earningsData.summary.deliveryCount}
                </div>
                <div className="text-xs text-blue-600 font-medium">
                  {period === 'today' ? "Today's" : period === 'week' ? "This Week's" : "This Month's"} Deliveries
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Breakdown */}
          <Card className="bg-white border-gray-200">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">
                {period === 'today' ? "Today's" : period === 'week' ? "This Week's" : "This Month's"} Breakdown
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-gray-700">Base Earnings</span>
                  </div>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(earningsData.summary.totalBase)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-gray-700">Tips</span>
                  </div>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(earningsData.summary.totalTips)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">Bonuses</span>
                  </div>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(earningsData.summary.totalBonuses)}
                  </span>
                </div>

                <div className="border-t border-gray-200 pt-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-bold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-green-700">
                      {formatCurrency(earningsData.summary.totalEarnings)}
                    </span>
                  </div>
                </div>

                {earningsData.summary.deliveryCount > 0 && (
                  <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-700">Average per Delivery</span>
                    <span className="font-semibold text-gray-900">
                      {formatCurrency(earningsData.summary.averageEarningPerDelivery)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Payout Status Cards */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">Payout Status</h3>

            {/* Paid Out */}
            {earningsData.summary.paidAmount > 0 && (
              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      <div>
                        <span className="text-sm font-medium text-green-800">Paid Out</span>
                        <p className="text-xs text-green-600">Completed payouts via Stripe</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold text-green-700">
                      {formatCurrency(earningsData.summary.paidAmount)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Ready for Payout */}
            {earningsData.summary.readyForPayoutAmount > 0 && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <div>
                        <span className="text-sm font-medium text-blue-800">Ready for Payout</span>
                        <p className="text-xs text-blue-600">2-hour validation period completed</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold text-blue-700">
                      {formatCurrency(earningsData.summary.readyForPayoutAmount)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Pending Validation */}
            {earningsData.summary.pendingAmount > 0 && (
              <Card className="bg-orange-50 border-orange-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-orange-600" />
                      <div>
                        <span className="text-sm font-medium text-orange-800">Pending Validation</span>
                        <p className="text-xs text-orange-600">Waiting for 2-hour validation period</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold text-orange-700">
                      {formatCurrency(earningsData.summary.pendingAmount)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Recent Earnings - Consistent with dashboard recent deliveries */}
          {earningsData.earnings.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Deliveries</h3>
                <div className="flex items-center space-x-3 text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <span>Paid</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                    <span>Pending</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                {earningsData.earnings.map((earning) => (
                  <Card key={earning.id} className="bg-white border-gray-200 hover:border-gray-300 transition-colors duration-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            earning.paymentStatus === 'pending' ? 'bg-orange-500' : 'bg-green-500'
                          }`}></div>
                          <div>
                            <p className="font-medium text-gray-900">#{earning.orderNumber}</p>
                            <p className="text-sm text-gray-600">{earning.businessName}</p>
                            <p className="text-xs text-gray-500 capitalize">
                              {earning.paymentStatus === 'paid' ? 'Paid Out' : 'Pending Validation'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">{formatCurrency(earning.totalAmount)}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(earning.earnedAt).toLocaleDateString('en-GB')}
                          </p>
                          {earning.tipAmount > 0 && (
                            <p className="text-xs text-purple-600">
                              +{formatCurrency(earning.tipAmount)} tip
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* No Earnings Message - Consistent with dashboard waiting state */}
          {earningsData.earnings.length === 0 && (
            <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <DollarSign className="h-6 w-6 text-gray-400" />
                </div>
                <p className="font-medium text-gray-700 mb-1">No earnings yet</p>
                <p className="text-sm text-gray-500">Complete deliveries to start earning money</p>
              </CardContent>
            </Card>
          )}

          {/* Bottom Spacing for Navigation */}
          <div className="h-20"></div>
        </div>

        <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
