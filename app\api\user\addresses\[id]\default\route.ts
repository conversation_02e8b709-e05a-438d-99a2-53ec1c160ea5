import { NextResponse } from "next/server";
import { createServerSupabase } from "@/lib/supabase-server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// POST handler to set an address as default
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const addressId = params.id;

    // Get the user's session
    const supabase = await createServerSupabase();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get user ID from the users table using admin client
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Verify the address belongs to the user using admin client
    const { data: addressData, error: addressError } = await adminClient
      .from("user_addresses")
      .select("id")
      .eq("id", addressId)
      .eq("user_id", userData.id)
      .single();

    if (addressError || !addressData) {
      return NextResponse.json(
        { error: "Address not found or not authorized" },
        { status: 404 }
      );
    }

    // First, unset any existing default address using admin client
    await adminClient
      .from("user_addresses")
      .update({ is_default: false })
      .eq("user_id", userData.id)
      .eq("is_default", true);

    // Then set the new default address using admin client
    const { data: updatedAddress, error: updateError } = await adminClient
      .from("user_addresses")
      .update({
        is_default: true,
        updated_at: new Date().toISOString()
      })
      .eq("id", addressId)
      .eq("user_id", userData.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error setting default address:", updateError);
      return NextResponse.json(
        { error: "Failed to set default address" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Default address updated successfully",
      address: updatedAddress
    });
  } catch (error: any) {
    console.error(`Unexpected error in POST /api/user/addresses/${params.id}/default:`, error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
