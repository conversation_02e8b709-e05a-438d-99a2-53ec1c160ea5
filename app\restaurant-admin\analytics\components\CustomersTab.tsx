"use client"

import {
  ArrowUpRight,
  DollarSign,
  Users,
  Star,
  UserPlus,
  UserCheck,
  Heart,
  ThumbsUp,
  Award,
  LineChart,
  MessageSquare,
  UserCircle,
  UserMinus,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { 
  customerGrowthData, 
  customerRetentionData, 
  customerSegmentData, 
  customerFeedbackData, 
  topCustomersData 
} from "../data/mock-data"

export default function CustomersTab() {
  return (
    <>
      {/* Customers Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                5%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Customers</p>
              <h3 className="text-2xl font-bold">163</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+8 from last month</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <UserPlus className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                8%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">New Customers</p>
              <h3 className="text-2xl font-bold">38</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Last 30 days</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <UserCheck className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                3%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Retention Rate</p>
              <h3 className="text-2xl font-bold">71%</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+3% from last month</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-amber-100 p-2 rounded-lg">
                <DollarSign className="h-6 w-6 text-amber-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                7%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Avg. Customer Value</p>
              <h3 className="text-2xl font-bold">£42.50</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£2.80 from last month</div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Growth & Retention */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Customer Growth</CardTitle>
                <CardDescription>Total and new customers over time</CardDescription>
              </div>
              <Select defaultValue="daily">
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for customer growth chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <LineChart className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[200px]">
                <svg width="100%" height="100%" viewBox="0 0 100 20" preserveAspectRatio="none">
                  {/* Total customers line */}
                  <path
                    d="M0,15 L7.1,14 L14.2,13.5 L21.3,13 L28.4,12.5 L35.5,11.5 L42.6,10.5 L49.7,10 L56.8,9.5 L63.9,9 L71,8.5 L78.1,8.3 L85.2,8.2 L92.3,8 L100,7.5"
                    fill="none"
                    stroke="#8b5cf6"
                    strokeWidth="0.5"
                  />
                  {/* New customers bars */}
                  {customerGrowthData.map((item, index) => {
                    const x = (index / (customerGrowthData.length - 1)) * 100;
                    const barWidth = 5;
                    const barHeight = (item.new / 6) * 5; // Scale to max height
                    return (
                      <rect 
                        key={index}
                        x={x - barWidth/2}
                        y={20 - barHeight}
                        width={barWidth}
                        height={barHeight}
                        fill="#10b981"
                        fillOpacity="0.5"
                      />
                    );
                  })}
                </svg>
              </div>
              <div className="absolute top-2 right-2 flex items-center space-x-4 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-1 bg-purple-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Total Customers</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-emerald-500 opacity-50 rounded-sm mr-1"></div>
                  <span className="text-gray-500">New Customers</span>
                </div>
              </div>
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-4 text-xs text-gray-400">
                <span>Apr 1</span>
                <span>Apr 7</span>
                <span>Apr 14</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Retention</CardTitle>
            <CardDescription>Monthly retention rates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 flex flex-col justify-center px-6 py-4">
                <div className="flex items-center justify-between mb-6">
                  <h4 className="text-sm font-medium">Current Retention</h4>
                  <div className="flex items-center">
                    <span className="text-xl font-bold mr-1">71%</span>
                    <span className="text-xs text-emerald-600 flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 3%
                    </span>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {customerRetentionData.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">{item.month}</span>
                        <span className="text-sm font-medium">{item.rate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="h-2 rounded-full bg-blue-500" 
                          style={{ width: `${item.rate}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 pt-4 border-t border-gray-100">
                  <div className="flex items-center justify-between text-sm">
                    <span>Industry Average</span>
                    <span className="font-medium">65%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                    <div className="h-1 rounded-full bg-gray-400" style={{ width: '65%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Segments & Feedback */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Customer Segments</CardTitle>
            <CardDescription>Breakdown of your customer base</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-32 h-32 rounded-full border-8 border-gray-100 relative">
                  {/* Loyal segment - 35% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 0% 0%, 0% 50%)' }}>
                    <div className="absolute inset-0 bg-emerald-500"></div>
                  </div>
                  {/* Regular segment - 42% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 0% 0%, 100% 0%, 100% 50%)' }}>
                    <div className="absolute inset-0 bg-blue-500 origin-bottom-right" style={{ transform: 'rotate(35deg)' }}></div>
                  </div>
                  {/* Occasional segment - 15% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 100% 50%, 100% 100%)' }}>
                    <div className="absolute inset-0 bg-purple-500"></div>
                  </div>
                  {/* At Risk segment - 8% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 100% 100%, 50% 100%, 0% 100%, 0% 50%)' }}>
                    <div className="absolute inset-0 bg-amber-500 origin-top-left" style={{ transform: 'rotate(15deg)' }}></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center rounded-full bg-white w-20 h-20 m-auto">
                    <span className="text-lg font-bold">163</span>
                  </div>
                </div>
              </div>
              <div className="absolute bottom-4 left-0 right-0 flex flex-col items-center space-y-2">
                {customerSegmentData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between w-full px-6">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: item.color }}></div>
                      <span className="text-sm">{item.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{item.value}%</span>
                      <span className="text-xs text-gray-500">{item.description}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Feedback</CardTitle>
            <CardDescription>Average ratings by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute inset-0 flex flex-col justify-center px-6 py-4">
                <div className="flex items-center justify-between mb-6">
                  <h4 className="text-sm font-medium">Overall Rating</h4>
                  <div className="flex items-center">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current opacity-30" />
                    </div>
                    <span className="text-lg font-bold">4.6</span>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {customerFeedbackData.map((item, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">{item.category}</span>
                        <div className="flex items-center">
                          <span className="text-sm font-medium mr-2">{item.rating}</span>
                          <div className="flex items-center text-amber-500">
                            {[...Array(5)].map((_, i) => (
                              <Star 
                                key={i} 
                                className={`h-3 w-3 ${i < Math.floor(item.rating) ? 'fill-current' : 'fill-current opacity-30'}`} 
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-amber-500" 
                          style={{ width: `${(item.rating / 5) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>Your most valuable customers</CardDescription>
            </div>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left font-medium py-3 px-4">Customer</th>
                    <th className="text-center font-medium py-3 px-4">Orders</th>
                    <th className="text-center font-medium py-3 px-4">Total Spent</th>
                    <th className="text-center font-medium py-3 px-4">Last Order</th>
                    <th className="text-right font-medium py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {topCustomersData.map((customer, index) => (
                    <tr key={index} className="border-b last:border-0 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                            <UserCircle className="h-5 w-5 text-gray-500" />
                          </div>
                          <span>{customer.name}</span>
                        </div>
                      </td>
                      <td className="text-center py-3 px-4">{customer.orders}</td>
                      <td className="text-center py-3 px-4">£{customer.spent.toFixed(2)}</td>
                      <td className="text-center py-3 px-4">{customer.lastOrder}</td>
                      <td className="text-right py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Heart className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Engagement Opportunities */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 mr-2 text-amber-500" />
              Loyalty Program
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">Increase retention with a loyalty program</p>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Enrolled Customers</span>
                <span className="text-sm font-medium">42</span>
              </div>
              <Progress value={26} className="h-2" />
              <p className="text-xs text-gray-500">26% of total customers</p>
            </div>
            <Button className="w-full mt-4" size="sm">
              Manage Program
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserMinus className="h-5 w-5 mr-2 text-red-500" />
              At-Risk Customers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">Customers who haven't ordered recently</p>
            <div className="flex items-center justify-center my-2">
              <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                <span className="text-red-700 font-bold text-xl">13</span>
              </div>
            </div>
            <p className="text-xs text-center text-gray-500 mb-4">8% of your customer base</p>
            <Button className="w-full" size="sm">
              Send Offers
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ThumbsUp className="h-5 w-5 mr-2 text-blue-500" />
              Review Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">Customers who haven't left reviews</p>
            <div className="flex items-center justify-center my-2">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-blue-700 font-bold text-xl">28</span>
              </div>
            </div>
            <p className="text-xs text-center text-gray-500 mb-4">Recent orders without reviews</p>
            <Button className="w-full" size="sm">
              Request Reviews
            </Button>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
