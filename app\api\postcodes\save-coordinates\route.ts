import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { standardizeJerseyPostcodeFormat, getParishFromPostcodeSync } from '@/lib/jersey-postcodes';

// Set cache control headers
const headers = {
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

/**
 * POST handler for saving postcode coordinates to the database
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { postcode, longitude, latitude } = body;
    
    if (!postcode || longitude === undefined || latitude === undefined) {
      return NextResponse.json(
        { error: "Postcode, longitude, and latitude are required" },
        { status: 400, headers }
      );
    }
    
    // Standardize the postcode
    const standardized = standardizeJerseyPostcodeFormat(postcode);
    if (!standardized) {
      return NextResponse.json(
        { error: "Invalid postcode format" },
        { status: 400, headers }
      );
    }
    
    // Get the parish for this postcode
    const parish = getParishFromPostcodeSync(standardized) || 'Unknown';
    
    console.log(`Saving coordinates for ${standardized}: [${longitude}, ${latitude}], parish: ${parish}`);
    
    // Check if the postcode already exists
    const { data: existingPostcode, error: fetchError } = await supabase
      .from('jersey_postcodes')
      .select('id')
      .eq('standardized_postcode', standardized)
      .maybeSingle();
    
    if (fetchError) {
      console.error('Error checking for existing postcode:', fetchError);
      return NextResponse.json(
        { error: "Database error checking for existing postcode" },
        { status: 500, headers }
      );
    }
    
    let result;
    
    if (existingPostcode) {
      // Update existing postcode
      const { data, error: updateError } = await supabase
        .from('jersey_postcodes')
        .update({
          longitude,
          latitude,
          coordinates: `(${longitude},${latitude})`,
          parish,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPostcode.id)
        .select();
      
      if (updateError) {
        console.error('Error updating postcode coordinates:', updateError);
        return NextResponse.json(
          { error: "Database error updating coordinates" },
          { status: 500, headers }
        );
      }
      
      result = { updated: true, postcode: data?.[0] };
      console.log(`Updated coordinates for postcode ${standardized}`);
    } else {
      // Insert new postcode
      const { data, error: insertError } = await supabase
        .from('jersey_postcodes')
        .insert({
          postcode: standardized.replace(/\s+/g, ''), // Store without spaces
          standardized_postcode: standardized, // Store with space
          longitude,
          latitude,
          coordinates: `(${longitude},${latitude})`,
          parish
        })
        .select();
      
      if (insertError) {
        console.error('Error inserting postcode coordinates:', insertError);
        return NextResponse.json(
          { error: "Database error inserting coordinates" },
          { status: 500, headers }
        );
      }
      
      result = { inserted: true, postcode: data?.[0] };
      console.log(`Inserted coordinates for postcode ${standardized}`);
    }
    
    return NextResponse.json(result, { headers });
  } catch (error) {
    console.error('Error in POST /api/postcodes/save-coordinates:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers }
    );
  }
}
