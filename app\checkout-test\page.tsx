"use client"

import React from "react"
import { CheckboxTestSuite } from "./components"

export default function CheckoutTestPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Checkbox Test Suite</h1>
      <p className="mb-6 text-gray-600">
        This page contains various checkbox implementations to help isolate the infinite update loop issue.
        Check the browser console for error messages.
      </p>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <CheckboxTestSuite />
      </div>
    </div>
  )
}
