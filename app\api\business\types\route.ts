import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in business/types API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in business/types API")
      return { authorized: true };
    } else {
      return { 
        authorized: false, 
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in business/types API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in business/types API:", error)
      return { 
        authorized: false, 
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in business/types API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in business/types API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return { 
        authorized: false, 
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in business/types API")
      return { authorized: true };
    }
  }
}

// GET endpoint to fetch business types
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Fetch business types
    const { data, error } = await supabase
      .from("business_types")
      .select("id, name, slug")
      .order("name");

    if (error) {
      console.error('Error fetching business types:', error);
      return NextResponse.json(
        { error: 'Failed to fetch business types', details: error.message },
        { status: 500 }
      );
    }

    // If no business types are found, return default ones
    if (!data || data.length === 0) {
      const defaultTypes = [
        { id: 1, name: "Restaurant", slug: "restaurant" },
        { id: 2, name: "Shop", slug: "shop" },
        { id: 3, name: "Pharmacy", slug: "pharmacy" },
        { id: 4, name: "Cafe", slug: "cafe" }
      ];
      
      return NextResponse.json({
        data: defaultTypes,
        message: "Using default business types"
      });
    }

    return NextResponse.json({
      data,
      success: true
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
