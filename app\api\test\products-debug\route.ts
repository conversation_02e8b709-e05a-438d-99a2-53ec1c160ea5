import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== PRODUCTS DEBUG API ===")
    
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers (same as orders API)
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;
    
    // Check for token in Authorization header (same as orders API)
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    console.log("Auth check:", {
      hasSession: !!session,
      sessionError,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie
    })

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found")
      return NextResponse.json({
        error: "Authentication required",
        debug: { hasSession: !!session, hasCustomToken: !!customToken, hasHeaderToken: !!headerToken }
      }, { status: 401 })
    }

    console.log("User authenticated:", userEmail)

    // Get the user's profile to check their role (same as orders API)
    console.log("Checking user profile for email:", userEmail)

    // Get the user profile with role using admin client to bypass RLS
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, auth_id")
      .eq("email", userEmail)
      .single()

    console.log("Admin client profile check:", {
      hasProfile: !!userProfile,
      role: userProfile?.role || null,
      error: profileError ? profileError.message : null
    })

    if (profileError || !userProfile) {
      console.error("Error fetching user profile:", profileError)
      return NextResponse.json({
        error: "User profile not found",
        debug: { userEmail, profileError }
      }, { status: 404 })
    }

    console.log("User profile:", userProfile)

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    console.log("User roles:", { isBusinessManager, isAdmin, isSuperAdmin })

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json({
        error: "Insufficient permissions",
        debug: { role: userProfile.role }
      }, { status: 403 })
    }

    // Determine which business ID to use
    let businessId: number | null = null

    if (isBusinessManager) {
      // For business managers, get their business association from business_managers table
      console.log(`Looking up business association for user ID: ${userProfile.id}`)
      
      // First try business_managers table
      const { data: businessManager, error: bmError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()
      
      console.log("Business manager lookup result:", { businessManager, bmError })
      
      if (!bmError && businessManager) {
        businessId = businessManager.business_id
        console.log(`Found user ${userProfile.id} in business_managers table for business ${businessId}`)
      } else {
        // If not found in managers, try business_staff table
        console.log("Not found in business_managers, trying business_staff table...")
        const { data: staffData, error: staffError } = await adminClient
          .from("business_staff")
          .select("business_id")
          .eq("user_id", userProfile.id)
          .single()
        
        console.log("Business staff lookup result:", { staffData, staffError })
        
        if (!staffError && staffData) {
          businessId = staffData.business_id
          console.log(`Found user ${userProfile.id} in business_staff table for business ${businessId}`)
        } else {
          console.error("Business manager without a business association in either table")
          
          return NextResponse.json({
            error: "No business associated with this account",
            debug: {
              userId: userProfile.id,
              email: userEmail,
              bmError,
              staffError
            }
          }, { status: 400 })
        }
      }
      
      console.log(`Business manager requesting products for business ID: ${businessId}`)
    }

    // Test direct products query
    console.log("Testing direct products query...")
    const { data: directProducts, error: directError } = await adminClient
      .from("products")
      .select("id, name, business_id")
      .eq("business_id", businessId || 4)
      .limit(5)

    console.log("Direct products query:", { directProducts, directError })

    return NextResponse.json({
      debug: {
        userEmail,
        userProfile,
        roles: { isBusinessManager, isAdmin, isSuperAdmin },
        businessId,
        directProducts,
        directError
      },
      success: true
    })

  } catch (error: any) {
    console.error("Error in products debug API:", error)
    return NextResponse.json({
      error: error.message,
      debug: { stack: error.stack }
    }, { status: 500 })
  }
}
