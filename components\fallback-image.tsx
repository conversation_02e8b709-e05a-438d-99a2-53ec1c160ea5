"use client"

import { useState, useRef, useEffect } from 'react'

interface FallbackImageProps {
  src: string
  alt: string
  fallbackSrc: string
  fallbackAlt?: string
  className?: string
  smartFit?: boolean // New prop to enable smart fitting
}

export default function FallbackImage({
  src,
  alt,
  fallbackSrc,
  fallbackAlt,
  className,
  smartFit = false
}: FallbackImageProps) {
  const [imgSrc, setImgSrc] = useState(src)
  const [imgAlt, setImgAlt] = useState(alt)
  const [objectFit, setObjectFit] = useState<'cover' | 'contain'>('cover')
  const imgRef = useRef<HTMLImageElement>(null)

  const handleError = () => {
    setImgSrc(fallbackSrc)
    if (fallbackAlt) {
      setImgAlt(fallbackAlt)
    }
  }

  const handleLoad = () => {
    if (!smartFit || !imgRef.current) return

    const img = imgRef.current
    const container = img.parentElement
    if (!container) return

    // Get natural dimensions
    const naturalAspectRatio = img.naturalWidth / img.naturalHeight
    const containerAspectRatio = container.clientWidth / container.clientHeight

    // Smart fitting logic:
    // 1. Very wide images (panoramic, logos) -> contain
    // 2. Very tall images (portraits, bottles) -> contain
    // 3. Square-ish images that are close to container ratio -> cover
    // 4. Images with extreme aspect ratios -> contain

    const aspectRatioDifference = Math.abs(naturalAspectRatio - containerAspectRatio)
    const isVeryWide = naturalAspectRatio > 2.5
    const isVeryTall = naturalAspectRatio < 0.4
    const isExtremeRatio = aspectRatioDifference > 0.8

    if (isVeryWide || isVeryTall || isExtremeRatio) {
      setObjectFit('contain')
    } else {
      setObjectFit('cover')
    }
  }

  // Reset object fit when src changes
  useEffect(() => {
    setObjectFit('cover')
  }, [src])

  const finalClassName = smartFit
    ? className?.replace(/object-(cover|contain)/, `object-${objectFit}`) || `object-${objectFit}`
    : className

  return (
    <img
      ref={imgRef}
      src={imgSrc}
      alt={imgAlt}
      className={finalClassName}
      onError={handleError}
      onLoad={handleLoad}
    />
  )
}
