// Voice usage indicator component
'use client'

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  Info, 
  Mic, 
  Clock, 
  HardDrive,
  MessageSquare,
  Calendar
} from "lucide-react"
import { getVoiceUsageStats, VOICE_LIMITS } from '@/utils/voice-limits'

interface VoiceUsageIndicatorProps {
  userId: string
  isOpen: boolean
  onClose: () => void
}

interface UsageStats {
  daily: {
    sizeMB: number
    count: number
    conversations: number
  }
  total: {
    sizeMB: number
    count: number
  }
  limits: typeof VOICE_LIMITS
}

export function VoiceUsageIndicator({ userId, isOpen, onClose }: VoiceUsageIndicatorProps) {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen && userId) {
      loadStats()
    }
  }, [isOpen, userId])

  const loadStats = async () => {
    setLoading(true)
    try {
      const usage = await getVoiceUsageStats(userId)
      if (usage) {
        setStats(usage)
      }
    } catch (error) {
      console.error('Failed to load voice usage stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  const formatMB = (mb: number) => mb < 1 ? `${(mb * 1024).toFixed(0)}KB` : `${mb.toFixed(1)}MB`

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Mic className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Voice Message Usage</h3>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Loading usage stats...</p>
            </div>
          ) : stats ? (
            <div className="space-y-6">
              {/* Daily Usage */}
              <div>
                <div className="flex items-center space-x-2 mb-3">
                  <Calendar className="h-4 w-4 text-green-600" />
                  <h4 className="font-medium">Today (24 hours)</h4>
                </div>
                
                <div className="space-y-3">
                  {/* Daily Size */}
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Storage Used</span>
                      <span>{formatMB(stats.daily.sizeMB)} / {stats.limits.MAX_USER_SIZE_MB_24H}MB</span>
                    </div>
                    <Progress 
                      value={(stats.daily.sizeMB / stats.limits.MAX_USER_SIZE_MB_24H) * 100} 
                      className="h-2"
                    />
                  </div>

                  {/* Daily Count */}
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Messages Sent</span>
                      <span>{stats.daily.count} / {stats.limits.MAX_USER_COUNT_24H}</span>
                    </div>
                    <Progress 
                      value={(stats.daily.count / stats.limits.MAX_USER_COUNT_24H) * 100} 
                      className="h-2"
                    />
                  </div>

                  {/* Conversations */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1">
                      <MessageSquare className="h-3 w-3" />
                      <span>Active Conversations</span>
                    </div>
                    <span>{stats.daily.conversations}</span>
                  </div>
                </div>
              </div>

              {/* Total Usage */}
              <div>
                <div className="flex items-center space-x-2 mb-3">
                  <HardDrive className="h-4 w-4 text-purple-600" />
                  <h4 className="font-medium">Total Storage</h4>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Total Used</span>
                      <span>{formatMB(stats.total.sizeMB)} / {stats.limits.MAX_TOTAL_USER_STORAGE_MB}MB</span>
                    </div>
                    <Progress 
                      value={(stats.total.sizeMB / stats.limits.MAX_TOTAL_USER_STORAGE_MB) * 100} 
                      className="h-2"
                    />
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span>Total Messages</span>
                    <span>{stats.total.count}</span>
                  </div>
                </div>
              </div>

              {/* Limits Info */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium text-blue-900">Message Limits</h4>
                </div>
                
                <div className="space-y-2 text-sm text-blue-800">
                  <div className="flex items-center justify-between">
                    <span>Max per message:</span>
                    <span>{stats.limits.MAX_MESSAGE_SIZE_MB}MB</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Max duration:</span>
                    <span>{Math.floor(stats.limits.MAX_MESSAGE_DURATION_SECONDS / 60)} minutes</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Per conversation/day:</span>
                    <span>{stats.limits.MAX_CONVERSATION_SIZE_MB_24H}MB</span>
                  </div>
                </div>
              </div>

              {/* Warning if near limits */}
              {(stats.daily.sizeMB / stats.limits.MAX_USER_SIZE_MB_24H > 0.8 || 
                stats.daily.count / stats.limits.MAX_USER_COUNT_24H > 0.8 ||
                stats.total.sizeMB / stats.limits.MAX_TOTAL_USER_STORAGE_MB > 0.8) && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <h4 className="font-medium text-yellow-900">Usage Warning</h4>
                  </div>
                  <p className="text-sm text-yellow-800">
                    You're approaching your voice message limits. Consider cleaning up old messages or reducing usage.
                  </p>
                </div>
              )}

              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={loadStats}
                  className="flex-1"
                >
                  Refresh
                </Button>
                <Button 
                  size="sm" 
                  onClick={onClose}
                  className="flex-1"
                >
                  Close
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">Failed to load usage statistics</p>
              <Button variant="outline" size="sm" onClick={loadStats} className="mt-2">
                Try Again
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
