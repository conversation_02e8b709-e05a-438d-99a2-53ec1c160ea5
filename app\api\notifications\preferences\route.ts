import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getAuthUserId } from '@/utils/auth-token'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// PATCH - Update notification preferences
export async function PATCH(request: NextRequest) {
  try {
    const authUserId = await getAuthUserId(request)
    if (!authUserId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { preferences } = body

    if (!preferences) {
      return NextResponse.json(
        { error: 'Preferences are required' },
        { status: 400 }
      )
    }

    // Update preferences for active subscriptions
    const { data, error } = await supabase
      .from('push_subscriptions')
      .update({
        preferences,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', authUserId)
      .eq('is_active', true)
      .select()

    if (error) {
      console.error('Error updating notification preferences:', error)
      return NextResponse.json(
        { error: 'Failed to update preferences' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Preferences updated successfully',
      subscriptions: data
    })

  } catch (error: any) {
    console.error('Error in notification preferences update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
