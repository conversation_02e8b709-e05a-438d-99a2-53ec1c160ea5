"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Search, Star, StarOff, RefreshCw, Filter } from "lucide-react"
import { toast } from "sonner"

interface Category {
  id: number
  name: string
  slug: string
  description?: string
  business_type_id?: number
  business_type_name?: string
  category_purpose: string
  display_order: number
  is_subscribed: boolean
  is_primary: boolean
}

export default function BusinessCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterBusinessType, setFilterBusinessType] = useState("all")
  const [filterPurpose, setFilterPurpose] = useState("all")
  const [filterSubscription, setFilterSubscription] = useState("all")

  // Fetch categories
  const fetchCategories = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/business-admin/category-subscriptions')
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      } else {
        toast.error('Failed to load categories')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  // Handle subscription toggle
  const handleSubscriptionToggle = async (categoryId: number, isCurrentlySubscribed: boolean) => {
    try {
      if (isCurrentlySubscribed) {
        // Unsubscribe
        const response = await fetch(`/api/business-admin/category-subscriptions?category_id=${categoryId}`, {
          method: 'DELETE',
        })
        
        if (response.ok) {
          toast.success('Unsubscribed from category')
          fetchCategories()
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to unsubscribe')
        }
      } else {
        // Subscribe
        const response = await fetch('/api/business-admin/category-subscriptions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ category_id: categoryId }),
        })
        
        if (response.ok) {
          toast.success('Subscribed to category')
          fetchCategories()
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to subscribe')
        }
      }
    } catch (error) {
      console.error('Error toggling subscription:', error)
      toast.error('Failed to update subscription')
    }
  }

  // Handle primary category toggle
  const handlePrimaryToggle = async (categoryId: number) => {
    try {
      const response = await fetch('/api/business-admin/category-subscriptions/primary', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ category_id: categoryId }),
      })
      
      if (response.ok) {
        toast.success('Primary category updated')
        fetchCategories()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update primary category')
      }
    } catch (error) {
      console.error('Error setting primary category:', error)
      toast.error('Failed to update primary category')
    }
  }

  // Filter categories
  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.slug.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesBusinessType = filterBusinessType === "all" || 
                               category.business_type_id?.toString() === filterBusinessType
    const matchesPurpose = filterPurpose === "all" || category.category_purpose === filterPurpose
    const matchesSubscription = filterSubscription === "all" ||
                               (filterSubscription === "subscribed" && category.is_subscribed) ||
                               (filterSubscription === "not_subscribed" && !category.is_subscribed)

    return matchesSearch && matchesBusinessType && matchesPurpose && matchesSubscription
  })

  // Get unique business types for filter
  const businessTypes = Array.from(new Set(
    categories
      .filter(cat => cat.business_type_name)
      .map(cat => ({ id: cat.business_type_id, name: cat.business_type_name }))
  )).filter((type, index, self) => 
    index === self.findIndex(t => t.id === type.id)
  )

  const subscribedCount = categories.filter(cat => cat.is_subscribed).length
  const primaryCategory = categories.find(cat => cat.is_primary)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Category Subscriptions</h1>
          <p className="text-gray-600 mt-1">
            Subscribe to categories to appear when customers browse by category
          </p>
        </div>
        
        <Button variant="outline" onClick={fetchCategories}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Subscribed Categories</p>
                <p className="text-2xl font-bold text-emerald-600">{subscribedCount}</p>
              </div>
              <div className="h-8 w-8 bg-emerald-100 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Primary Category</p>
                <p className="text-lg font-semibold text-gray-900">
                  {primaryCategory ? primaryCategory.name : 'None set'}
                </p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-blue-600 fill-current" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
              <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                <Filter className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Categories ({filteredCategories.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={filterBusinessType} onValueChange={setFilterBusinessType}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by business type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Business Types</SelectItem>
                {businessTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id?.toString() || ""}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterPurpose} onValueChange={setFilterPurpose}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by purpose" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Purposes</SelectItem>
                <SelectItem value="specialization">Specialization</SelectItem>
                <SelectItem value="menu">Menu</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterSubscription} onValueChange={setFilterSubscription}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by subscription" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="subscribed">Subscribed</SelectItem>
                <SelectItem value="not_subscribed">Not Subscribed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Categories Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {loading ? (
              // Loading skeleton
              Array(6).fill(0).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-4"></div>
                    <div className="flex justify-between items-center">
                      <div className="h-6 w-16 bg-gray-200 rounded"></div>
                      <div className="h-8 w-20 bg-gray-200 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : filteredCategories.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No categories found</p>
              </div>
            ) : (
              filteredCategories.map((category) => (
                <Card key={category.id} className={`transition-all duration-200 ${
                  category.is_subscribed ? 'ring-2 ring-emerald-200 bg-emerald-50' : 'hover:shadow-md'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                          {category.name}
                          {category.is_primary && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                        </h3>
                        {category.description && (
                          <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 mb-3">
                      {category.business_type_name && (
                        <Badge variant="outline" className="text-xs">
                          {category.business_type_name}
                        </Badge>
                      )}
                      <Badge
                        variant={category.category_purpose === 'specialization' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {category.category_purpose}
                      </Badge>
                      {category.is_subscribed && (
                        <Badge variant="default" className="text-xs bg-emerald-600">
                          Subscribed
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <Button
                        variant={category.is_subscribed ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleSubscriptionToggle(category.id, category.is_subscribed)}
                      >
                        {category.is_subscribed ? "Unsubscribe" : "Subscribe"}
                      </Button>

                      {category.is_subscribed && (
                        <Button
                          variant={category.is_primary ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePrimaryToggle(category.id)}
                          disabled={category.is_primary}
                        >
                          {category.is_primary ? (
                            <>
                              <Star className="h-3 w-3 mr-1 fill-current" />
                              Primary
                            </>
                          ) : (
                            <>
                              <StarOff className="h-3 w-3 mr-1" />
                              Set Primary
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Help Text */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h3 className="font-semibold text-blue-900 mb-2">How Category Subscriptions Work</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Subscribe</strong> to categories to make your business appear when customers browse that category</li>
            <li>• <strong>Primary Category</strong> determines your main business classification and affects search ranking</li>
            <li>• <strong>Specialization</strong> categories show your cuisine/business type (e.g., Pizza, Chinese, Groceries)</li>
            <li>• <strong>Menu</strong> categories organize your products (e.g., Starters, Main Courses, Desserts)</li>
            <li>• You can subscribe to multiple categories but only have one primary category</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
