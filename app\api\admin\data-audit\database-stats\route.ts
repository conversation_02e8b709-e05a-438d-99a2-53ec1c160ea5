import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting database statistics data audit API request")

    // Simple query to get all tables in the public schema
    const { data: tables, error: tablesError } = await adminClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');
    
    if (tablesError) {
      console.error("Error fetching tables:", tablesError);
      return NextResponse.json(
        { error: "Failed to fetch tables" },
        { status: 500 }
      );
    }
    
    if (!tables || tables.length === 0) {
      return NextResponse.json([]);
    }
    
    // Get stats for each table
    const tableStats = [];
    
    for (const table of tables) {
      const tableName = table.table_name;
      
      // Get column count
      const { data: columns, error: columnsError } = await adminClient
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', tableName);
      
      if (columnsError) {
        console.error(`Error getting columns for ${tableName}:`, columnsError);
        continue;
      }
      
      const columnCount = columns ? columns.length : 0;
      
      // Get row count
      const { count: rowCount, error: rowCountError } = await adminClient
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (rowCountError) {
        console.error(`Error counting rows for ${tableName}:`, rowCountError);
        continue;
      }
      
      // Get null count with a single SQL query
      let nullCount = 0;
      let nullPercentage = 0;
      
      if (columnCount > 0 && rowCount > 0) {
        try {
          // Build a query to count nulls in all columns at once
          const nullQuery = `
            SELECT 
              ${columns.map(col => `SUM(CASE WHEN "${col.column_name}" IS NULL THEN 1 ELSE 0 END)`).join(' + ')} as null_count
            FROM "${tableName}"
          `;
          
          const { data: nullData, error: nullError } = await adminClient.rpc("exec_sql", {
            sql_query: nullQuery
          });
          
          if (nullError) {
            console.error(`Error counting nulls for ${tableName}:`, nullError);
          } else if (nullData && nullData.length > 0) {
            nullCount = parseInt(nullData[0].null_count) || 0;
            
            // Calculate null percentage
            const totalCells = rowCount * columnCount;
            nullPercentage = totalCells > 0 ? (nullCount / totalCells) * 100 : 0;
          }
        } catch (err) {
          console.error(`Error counting nulls for ${tableName}:`, err);
        }
      }
      
      // Add table stats
      tableStats.push({
        table_name: tableName,
        column_count: columnCount,
        row_count: rowCount,
        null_count: nullCount,
        null_percentage: nullPercentage
      });
    }
    
    return NextResponse.json(tableStats);
  } catch (error: any) {
    console.error("Unexpected error in database statistics API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
