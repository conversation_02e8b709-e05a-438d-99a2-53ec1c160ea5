import Link from 'next/link'

export default function DemoIndexPage() {
  return (
    <div className="container-fluid py-8">
      <h1 className="text-2xl font-bold mb-6">Portrait Business Card Demos</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link href="/demo/card-comparison">
          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Card Comparison</h2>
            <p className="text-gray-600">Compare the original square business cards with the new portrait-oriented cards.</p>
          </div>
        </Link>

        <Link href="/demo/portrait-carousel">
          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Portrait Carousel</h2>
            <p className="text-gray-600">See the portrait business cards in a carousel layout, similar to the deals section.</p>
          </div>
        </Link>

        <Link href="/demo/portrait-grid">
          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Portrait Grid</h2>
            <p className="text-gray-600">View the portrait business cards in various grid layouts for different screen sizes.</p>
          </div>
        </Link>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Implementation Details</h2>
        <div className="bg-white rounded-lg shadow-md p-6">
          <p className="mb-4">
            The portrait business cards maintain the square image dimensions while creating a portrait-oriented card layout (taller than wide):
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>The image maintains its square dimensions at the top of the card</li>
            <li>The overall card has a portrait orientation (taller than wide)</li>
            <li>All metadata (business name, rating, location, delivery fee, delivery time) is displayed below the image</li>
            <li>The cards are responsive and adapt to different screen sizes</li>
            <li>The same favorite button, offers badges, and other interactive elements are preserved</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
