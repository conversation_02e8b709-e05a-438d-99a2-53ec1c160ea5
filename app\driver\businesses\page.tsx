"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import {
  Search,
  MapPin,
  Clock,
  Phone,
  Mail,
  Star,
  Building2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Send,
  Filter,
  RefreshCw,
  Eye,
  ExternalLink,
  MessageCircle
} from "lucide-react"

interface Business {
  id: number
  name: string
  slug: string
  business_type_id: number
  description: string
  address: string
  location: string
  logo_url: string
  phone: string
  delivery_radius: number
  minimum_order_amount: number
  delivery_fee: number
  preparation_time_minutes: number
  is_approved: boolean
  delivery_available: boolean
  business_types: {
    name: string
  }
  application_status: string | null
  application_date: string | null
  can_apply: boolean
}

interface BusinessesResponse {
  success: boolean
  businesses: Business[]
  pagination: {
    limit: number
    offset: number
    total: number
  }
}

export default function DriverBusinessesPage() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedParish, setSelectedParish] = useState<string>("all")
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null)
  const [applicationMessage, setApplicationMessage] = useState("")
  const [isApplying, setIsApplying] = useState(false)
  const [showApplicationDialog, setShowApplicationDialog] = useState(false)
  const { toast } = useToast()

  const businessTypes = [
    "Restaurant",
    "Grocery Store",
    "Pharmacy",
    "Retail",
    "Convenience Store",
    "Bakery",
    "Butcher",
    "Other"
  ]

  const parishes = [
    "St. Helier",
    "St. Brelade",
    "St. Clement",
    "St. Lawrence",
    "St. Martin",
    "St. Ouen",
    "St. Peter",
    "St. Saviour",
    "Grouville",
    "Trinity"
  ]

  useEffect(() => {
    fetchBusinesses()
  }, [selectedType, selectedParish])

  const fetchBusinesses = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedType && selectedType !== 'all') params.append('type', selectedType)
      if (selectedParish && selectedParish !== 'all') params.append('location', selectedParish)
      params.append('limit', '50')

      const response = await fetch(`/api/driver/businesses?${params}`)
      const data: BusinessesResponse = await response.json()

      if (data.success) {
        setBusinesses(data.businesses)
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load businesses"
        })
      }
    } catch (error) {
      console.error('Error fetching businesses:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load businesses"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApplyToBusiness = async (business: Business) => {
    setSelectedBusiness(business)
    setApplicationMessage("")
    setShowApplicationDialog(true)
  }

  const submitApplication = async () => {
    if (!selectedBusiness) return

    try {
      setIsApplying(true)
      const response = await fetch('/api/driver/apply-to-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          business_id: selectedBusiness.id,
          message: applicationMessage
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Application Submitted",
          description: data.message
        })
        setShowApplicationDialog(false)
        // Refresh the businesses list to update application status
        fetchBusinesses()
      } else {
        toast({
          variant: "destructive",
          title: "Application Failed",
          description: data.error
        })
      }
    } catch (error) {
      console.error('Error submitting application:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to submit application"
      })
    } finally {
      setIsApplying(false)
    }
  }

  const getStatusBadge = (business: Business) => {
    if (!business.application_status) {
      return business.can_apply ? (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
          <Building2 className="w-3 h-3 mr-1" />
          Can Apply
        </Badge>
      ) : null
    }

    switch (business.application_status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        )
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        )
      case 'banned':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Banned
          </Badge>
        )
      default:
        return null
    }
  }

  const filteredBusinesses = businesses.filter(business =>
    business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    business.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Directory</h1>
          <p className="text-gray-600">Apply to deliver for businesses across Jersey</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchBusinesses}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search businesses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Business Type</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  {businessTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Parish</label>
              <Select value={selectedParish} onValueChange={setSelectedParish}>
                <SelectTrigger>
                  <SelectValue placeholder="All parishes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All parishes</SelectItem>
                  {parishes.map(parish => (
                    <SelectItem key={parish} value={parish}>{parish}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Businesses Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredBusinesses.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No businesses found</h3>
            <p className="text-gray-500">Try adjusting your filters or search terms.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBusinesses.map((business) => (
            <Card key={business.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{business.name}</CardTitle>
                    <CardDescription className="flex items-center gap-1 mt-1">
                      <Building2 className="h-3 w-3" />
                      {business.business_types?.name || `Type ${business.business_type_id}`}
                    </CardDescription>
                  </div>
                  {business.logo_url && (
                    <img
                      src={business.logo_url}
                      alt={business.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(business)}
                  {!business.delivery_available && (
                    <Badge variant="secondary">Delivery Unavailable</Badge>
                  )}
                  {business.is_approved === false && (
                    <Badge variant="secondary">Not Approved</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600 line-clamp-2">
                  {business.description}
                </p>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{business.address}, {business.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{business.preparation_time_minutes} min prep time</span>
                  </div>
                  {business.phone && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <Phone className="h-4 w-4" />
                      <span>{business.phone}</span>
                    </div>
                  )}
                </div>

                <div className="pt-2 space-y-2">
                  <div className="flex gap-2">
                    <Link href={`/driver/businesses/${business.id}`} className="flex-1">
                      <Button variant="outline" className="w-full" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </Link>
                    <Link
                      href={`/connections-hub?business_id=${business.id}&business_name=${encodeURIComponent(business.name)}`}
                      className="flex-1"
                    >
                      <Button variant="outline" className="w-full" size="sm">
                        <MessageCircle className="h-4 w-4 mr-1" />
                        Connect
                      </Button>
                    </Link>
                  </div>
                  <div className="w-full">
                    {business.can_apply && business.delivery_available ? (
                      <Button
                        onClick={() => handleApplyToBusiness(business)}
                        className="w-full bg-green-600 hover:bg-green-700"
                        size="sm"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Apply
                      </Button>
                    ) : business.application_status === 'approved' ? (
                      <Button variant="outline" className="w-full" size="sm" disabled>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Already Approved
                      </Button>
                    ) : business.application_status === 'pending' ? (
                      <Button variant="outline" className="w-full" size="sm" disabled>
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Application Pending
                      </Button>
                    ) : !business.delivery_available ? (
                      <Button variant="outline" className="w-full" size="sm" disabled>
                        Delivery Unavailable
                      </Button>
                    ) : (
                      <Button variant="outline" className="w-full" size="sm" disabled>
                        Cannot Apply
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Application Dialog */}
      <Dialog open={showApplicationDialog} onOpenChange={setShowApplicationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Apply to {selectedBusiness?.name}</DialogTitle>
            <DialogDescription>
              Submit your application to deliver for this business. Include any relevant information about your experience or availability.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Message (Optional)</label>
              <Textarea
                placeholder="Tell the business why you'd be a great delivery driver for them..."
                value={applicationMessage}
                onChange={(e) => setApplicationMessage(e.target.value)}
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowApplicationDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={submitApplication}
                disabled={isApplying}
                className="flex-1"
              >
                {isApplying ? "Submitting..." : "Submit Application"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
