import { NextResponse } from "next/server"
import { adminClient } from "@/lib/supabase-admin"
import { createServerSupabase } from "@/lib/supabase-server"

// Helper function to get authenticated user
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

// GET - Search for users to connect with
export async function GET(request: Request) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const profile_type = url.searchParams.get('profile_type')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    // Validate limit
    if (limit > 100) {
      return NextResponse.json(
        { error: "Limit cannot exceed 100" },
        { status: 400 }
      )
    }

    // Build the search query
    let searchQuery = adminClient
      .from('connection_profiles')
      .select(`
        id,
        user_id,
        display_name,
        bio,
        avatar_url,
        average_rating,
        total_ratings,
        specialties,
        role_capabilities
      `)
      .eq('is_public', true)
      .neq('user_id', user.id) // Exclude current user

    // Note: We'll filter by profile type after fetching since it's stored in role_capabilities JSON

    // Apply text search if query provided
    if (query.trim()) {
      // Search in display_name and bio
      searchQuery = searchQuery.or(`display_name.ilike.%${query}%,bio.ilike.%${query}%`)
    }

    // Apply pagination and ordering
    searchQuery = searchQuery
      .order('average_rating', { ascending: false })
      .order('total_ratings', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: profiles, error: searchError } = await searchQuery

    if (searchError) {
      console.error("Error searching profiles:", searchError)
      return NextResponse.json(
        { error: "Failed to search profiles" },
        { status: 500 }
      )
    }

    // Get existing connections for these users to show connection status
    const userIds = profiles?.map(p => p.user_id) || []
    let existingConnections: any[] = []

    if (userIds.length > 0) {
      const { data: connections, error: connectionsError } = await adminClient
        .from('connections')
        .select('user1_id, user2_id, status, connection_type')
        .or(
          userIds.map(userId => {
            const user1 = user.id < userId ? user.id : userId
            const user2 = user.id < userId ? userId : user.id
            return `and(user1_id.eq.${user1},user2_id.eq.${user2})`
          }).join(',')
        )

      if (!connectionsError) {
        existingConnections = connections || []
      }
    }

    // Helper function to determine profile type from role_capabilities
    const getProfileType = (roleCapabilities: any) => {
      if (roleCapabilities?.owns_business) return 'business'
      if (roleCapabilities?.can_be_rider) return 'rider'
      return 'customer'
    }

    // Transform results to include connection status and filter by profile type
    let results = profiles?.map(profile => {
      // Find existing connection
      const connection = existingConnections.find(conn =>
        (conn.user1_id === user.id && conn.user2_id === profile.user_id) ||
        (conn.user2_id === user.id && conn.user1_id === profile.user_id)
      )

      const profileType = getProfileType(profile.role_capabilities)

      return {
        user_id: profile.user_id,
        profile_type: profileType,
        display_name: profile.display_name,
        bio: profile.bio,
        avatar_url: profile.avatar_url,
        average_rating: profile.average_rating,
        total_ratings: profile.total_ratings,
        specialties: profile.specialties,
        role_capabilities: profile.role_capabilities,
        connection_status: connection ? connection.status : null,
        connection_type: connection ? connection.connection_type : null
      }
    }) || []

    // Filter by profile type if specified
    if (profile_type) {
      const validTypes = ['customer', 'business', 'rider']
      if (!validTypes.includes(profile_type)) {
        return NextResponse.json(
          { error: "Invalid profile_type. Must be: customer, business, or rider" },
          { status: 400 }
        )
      }
      results = results.filter(result => result.profile_type === profile_type)
    }

    return NextResponse.json({
      data: results,
      pagination: {
        limit,
        offset,
        count: results.length,
        has_more: results.length === limit
      }
    })

  } catch (error) {
    console.error("Unexpected error in GET /api/connections-hub/users/search:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
