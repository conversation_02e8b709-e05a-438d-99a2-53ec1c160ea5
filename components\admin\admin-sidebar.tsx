"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  ChevronDown,
  Database,
  LayoutDashboard,
  LogOut,
  MapPin,
  Menu,
  Package,
  Settings,
  ShoppingBag,
  Store,
  Users,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

interface AdminSidebarProps {
  user: any
  userProfile: any
  stats?: {
    pendingBusinesses?: number
    newUsersThisMonth?: number
  }
}

export function AdminSidebar({ user, userProfile, stats = {} }: AdminSidebarProps) {
  const pathname = usePathname()
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)

  // Default stats if not provided
  const displayStats = {
    pendingBusinesses: stats.pendingBusinesses || 0,
    newUsersThisMonth: stats.newUsersThisMonth || 0
  }

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(`${path}/`)
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <aside
        className={`fixed inset-y-0 z-20 flex h-full flex-col border-r bg-background transition-all duration-300 ${isSidebarOpen ? "w-64" : "w-20"}`}
      >
        <div className="flex h-16 items-center justify-between border-b px-4">
          <div className={`flex items-center gap-2 ${!isSidebarOpen && "justify-center w-full"}`}>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-500 text-white">
              <Package className="h-4 w-4" />
            </div>
            {isSidebarOpen && <span className="font-semibold">Loop Jersey</span>}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className={!isSidebarOpen ? "hidden" : ""}
          >
            <ChevronDown className="h-4 w-4 rotate-90" />
          </Button>
        </div>
        <div className="flex-1 overflow-auto py-2">
          <nav className="grid gap-1 px-2">
            <Link href="/admin-new">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin-new") && pathname === "/admin-new" ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <LayoutDashboard className="h-4 w-4" />
                {isSidebarOpen && <span>Dashboard</span>}
              </Button>
            </Link>
            <Link href="/admin-new/businesses">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin-new/businesses") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Store className="h-4 w-4" />
                {isSidebarOpen && <span>Businesses</span>}
                {isSidebarOpen && displayStats.pendingBusinesses > 0 && (
                  <Badge className="ml-auto">{displayStats.pendingBusinesses}</Badge>
                )}
              </Button>
            </Link>
            <Link href="/admin/orders">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin/orders") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <ShoppingBag className="h-4 w-4" />
                {isSidebarOpen && <span>Orders</span>}
              </Button>
            </Link>
            <Link href="/admin-new/users">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin-new/users") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Users className="h-4 w-4" />
                {isSidebarOpen && <span>Users</span>}
                {isSidebarOpen && displayStats.newUsersThisMonth > 0 && (
                  <Badge className="ml-auto">{displayStats.newUsersThisMonth}</Badge>
                )}
              </Button>
            </Link>
            <Link href="/admin/update-coordinates">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin/update-coordinates") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <MapPin className="h-4 w-4" />
                {isSidebarOpen && <span>Coordinates</span>}
              </Button>
            </Link>
            <Link href="/admin-new/analytics">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin-new/analytics") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <BarChart3 className="h-4 w-4" />
                {isSidebarOpen && <span>Analytics</span>}
              </Button>
            </Link>
            <Link href="/admin-new/database-tools">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin-new/database-tools") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Database className="h-4 w-4" />
                {isSidebarOpen && <span>Database Tools</span>}
              </Button>
            </Link>
            <Link href="/admin/settings">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-${isSidebarOpen ? "start" : "center"} gap-2 px-3 py-2 ${
                  isActive("/admin/settings") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Settings className="h-4 w-4" />
                {isSidebarOpen && <span>Settings</span>}
              </Button>
            </Link>
          </nav>
        </div>
        <div className="border-t p-4">
          {isSidebarOpen ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={userProfile?.avatar_url || "/placeholder-user.jpg"} />
                <AvatarFallback>{userProfile?.full_name?.charAt(0) || "A"}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{userProfile?.full_name || userProfile?.name || "Admin"}</span>
                <span className="text-xs text-muted-foreground">{user?.email || "<EMAIL>"}</span>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="ml-auto">
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <Link href="/logout">
                    <DropdownMenuItem>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </Link>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            <Button variant="ghost" size="icon" onClick={() => setIsSidebarOpen(true)} className="w-full">
              <Menu className="h-4 w-4" />
            </Button>
          )}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="fixed left-4 top-4 z-40 lg:hidden">
            <Menu className="h-4 w-4" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <div className="flex h-16 items-center border-b px-6">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-500 text-white">
                <Package className="h-4 w-4" />
              </div>
              <span className="font-semibold">Loop Jersey</span>
            </div>
          </div>
          <div className="grid gap-1 p-2">
            <Link href="/admin-new">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin-new") && pathname === "/admin-new" ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <LayoutDashboard className="h-4 w-4" />
                <span>Dashboard</span>
              </Button>
            </Link>
            <Link href="/admin-new/businesses">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin-new/businesses") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Store className="h-4 w-4" />
                <span>Businesses</span>
                {displayStats.pendingBusinesses > 0 && (
                  <Badge className="ml-auto">{displayStats.pendingBusinesses}</Badge>
                )}
              </Button>
            </Link>
            <Link href="/admin/orders">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin/orders") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <ShoppingBag className="h-4 w-4" />
                <span>Orders</span>
              </Button>
            </Link>
            <Link href="/admin-new/users">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin-new/users") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Users className="h-4 w-4" />
                <span>Users</span>
                {displayStats.newUsersThisMonth > 0 && (
                  <Badge className="ml-auto">{displayStats.newUsersThisMonth}</Badge>
                )}
              </Button>
            </Link>
            <Link href="/admin/update-coordinates">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin/update-coordinates") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <MapPin className="h-4 w-4" />
                <span>Coordinates</span>
              </Button>
            </Link>
            <Link href="/admin-new/analytics">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin-new/analytics") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <BarChart3 className="h-4 w-4" />
                <span>Analytics</span>
              </Button>
            </Link>
            <Link href="/admin-new/database-tools">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin-new/database-tools") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Database className="h-4 w-4" />
                <span>Database Tools</span>
              </Button>
            </Link>
            <Link href="/admin/settings">
              <Button
                variant="ghost"
                className={`w-full flex items-center justify-start gap-2 px-3 py-2 ${
                  isActive("/admin/settings") ? "bg-emerald-50 text-emerald-600 font-medium" : ""
                }`}
              >
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Button>
            </Link>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
