import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    console.log("Business linking API called");

    const { businessId, userEmail, authUserId } = await request.json();

    if (!businessId || !userEmail || !authUserId) {
      return NextResponse.json(
        { error: "Missing required fields (businessId, userEmail, authUserId)" },
        { status: 400 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log("Linking business", businessId, "to user", userEmail, "with auth ID", authUserId);

    // 1. Find the user record by email
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("id, auth_id")
      .eq("email", userEmail)
      .single();

    if (userError || !user) {
      console.error("Error finding user:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    console.log("Found user:", user.id);

    // 2. Update user with auth_id if not already set
    if (!user.auth_id) {
      console.log("Updating user with auth_id:", authUserId);
      const { error: updateError } = await supabase
        .from("users")
        .update({
          auth_id: authUserId,
          updated_at: new Date().toISOString()
        })
        .eq("id", user.id);

      if (updateError) {
        console.error("Error updating user with auth_id:", updateError);
        return NextResponse.json(
          { error: "Failed to link auth user" },
          { status: 500 }
        );
      }
      console.log("Successfully updated user with auth_id");
    } else {
      console.log("User already has auth_id:", user.auth_id);
    }

    // 3. Check if business manager link already exists
    const { data: existingLink, error: linkCheckError } = await supabase
      .from("business_managers")
      .select("id")
      .eq("user_id", user.id)
      .eq("business_id", businessId)
      .single();

    if (linkCheckError && linkCheckError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error("Error checking existing business manager link:", linkCheckError);
      return NextResponse.json(
        { error: "Failed to check existing business link" },
        { status: 500 }
      );
    }

    if (existingLink) {
      console.log("Business manager link already exists");
      return NextResponse.json({
        success: true,
        message: "User already linked to business",
        alreadyLinked: true
      });
    }

    // 4. Create business manager link
    console.log("Creating business manager link");
    const { error: managerError } = await supabase
      .from("business_managers")
      .insert({
        user_id: user.id,
        business_id: businessId,
        is_primary: true,
        created_at: new Date().toISOString()
      });

    if (managerError) {
      console.error("Error creating business manager link:", managerError);
      return NextResponse.json(
        { error: "Failed to create business manager link" },
        { status: 500 }
      );
    }

    console.log("Successfully linked user to business");

    return NextResponse.json({
      success: true,
      message: "User successfully linked to business",
      userId: user.id,
      businessId: businessId
    });

  } catch (error: any) {
    console.error("Unexpected error in business linking:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
