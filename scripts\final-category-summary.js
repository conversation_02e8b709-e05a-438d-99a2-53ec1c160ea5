// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function showFinalCategorySummary() {
  try {
    console.log('=== FINAL CATEGORY SYSTEM SUMMARY ===\n');

    // Get final category count
    const { data: allCategories, error: countError } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        business_types!business_type_id (
          name
        )
      `)
      .eq('is_active', true)
      .order('business_type_id')
      .order('name');

    if (countError) {
      console.error('Error fetching categories:', countError);
      return;
    }

    console.log(`📊 FINAL RESULTS:`);
    console.log(`   • Total categories: ${allCategories.length}`);
    console.log(`   • Reduced from: 165 categories`);
    console.log(`   • Reduction: ${Math.round((165 - allCategories.length) / 165 * 100)}%`);

    // Group by business type
    const categoriesByType = {};
    allCategories.forEach(cat => {
      const typeName = cat.business_types?.name || 'Unknown';
      if (!categoriesByType[typeName]) {
        categoriesByType[typeName] = [];
      }
      categoriesByType[typeName].push(cat);
    });

    console.log('\n📋 CATEGORIES BY BUSINESS TYPE:');
    console.log('='.repeat(60));

    Object.keys(categoriesByType).forEach(typeName => {
      console.log(`\n🏢 ${typeName} (${categoriesByType[typeName].length} categories):`);
      categoriesByType[typeName].forEach(cat => {
        console.log(`   ✅ ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
      });
    });

    // Get business subscription summary
    console.log('\n\n📊 BUSINESS SUBSCRIPTION SUMMARY:');
    console.log('='.repeat(60));

    const { data: subscriptionSummary, error: subError } = await supabase
      .from('business_categories')
      .select(`
        business_id,
        category_id,
        is_primary
      `);

    if (subError) {
      console.error('Error fetching subscription summary:', subError);
    } else {
      const totalSubscriptions = subscriptionSummary.length;
      const businessesWithCategories = new Set(subscriptionSummary.map(s => s.business_id)).size;
      const categoriesInUse = new Set(subscriptionSummary.map(s => s.category_id)).size;

      console.log(`   • Total subscriptions: ${totalSubscriptions}`);
      console.log(`   • Businesses with categories: ${businessesWithCategories}`);
      console.log(`   • Categories in use: ${categoriesInUse}`);
      console.log(`   • Unused categories: ${allCategories.length - categoriesInUse}`);
    }

    // Show businesses by type with their categories
    console.log('\n\n🏢 BUSINESS CATEGORY ASSIGNMENTS:');
    console.log('='.repeat(60));

    const { data: businessesWithCategories, error: businessError } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        business_types!business_type_id (
          name
        )
      `)
      .eq('is_approved', true)
      .order('business_type_id')
      .order('name');

    if (businessError) {
      console.error('Error fetching businesses:', businessError);
    } else {
      const businessesByType = {};
      businessesWithCategories.forEach(business => {
        const typeName = business.business_types?.name || 'Unknown';
        if (!businessesByType[typeName]) {
          businessesByType[typeName] = [];
        }
        businessesByType[typeName].push(business);
      });

      for (const [typeName, businesses] of Object.entries(businessesByType)) {
        console.log(`\n🏢 ${typeName} businesses:`);
        
        for (const business of businesses) {
          // Get categories for this business
          const { data: businessCategories, error: catError } = await supabase
            .from('business_categories')
            .select(`
              category_id,
              is_primary,
              categories!category_id (
                name
              )
            `)
            .eq('business_id', business.id);

          if (catError) {
            console.error(`Error fetching categories for ${business.name}:`, catError);
            continue;
          }

          const categoryNames = businessCategories?.map(bc => bc.categories?.name).filter(Boolean) || [];
          const primaryCategory = businessCategories?.find(bc => bc.is_primary)?.categories?.name;

          console.log(`   📍 ${business.name}:`);
          if (categoryNames.length > 0) {
            categoryNames.forEach(catName => {
              const isPrimary = catName === primaryCategory;
              console.log(`      ${isPrimary ? '⭐' : '•'} ${catName}${isPrimary ? ' (primary)' : ''}`);
            });
          } else {
            console.log(`      ⚠️  No categories assigned`);
          }
        }
      }
    }

    console.log('\n\n✨ CHANGES COMPLETED:');
    console.log('='.repeat(60));
    console.log('✅ Removed 2 prescription categories (not needed)');
    console.log('✅ Removed airport runs category (service not provided)');
    console.log('✅ Updated British Cuisine icon to pie (🥧)');
    console.log('✅ All remaining categories are in active use');
    console.log('✅ Category filtering system is now optimized');

  } catch (error) {
    console.error('Error generating summary:', error);
  }
}

showFinalCategorySummary()
  .then(() => {
    console.log('\n🎉 Category system optimization complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Summary failed:', error);
    process.exit(1);
  });
