"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { AdminSidebar } from "./admin-sidebar"
import { useAdminDashboardStats } from "@/hooks/use-admin-dashboard-stats"

interface AdminLayoutWrapperProps {
  children: React.ReactNode
}

export function AdminLayoutWrapper({ children }: AdminLayoutWrapperProps) {
  const router = useRouter()
  const { user, userProfile, isAdmin, isLoading: authLoading } = useAuth()
  const [authChecked, setAuthChecked] = useState(false)

  // Use our real-time stats hook
  const {
    stats,
    pendingBusinesses,
    loading: statsLoading,
    error
  } = useAdminDashboardStats()

  // Fallback stats in case of error
  const fallbackStats = {
    totalBusinesses: 0,
    pendingBusinesses: 0,
    totalUsers: 0,
    totalOrders: 0,
    activeUsers: 0,
    newUsersThisMonth: 0,
    totalRevenue: 0,
    averageOrderValue: 0
  }

  // Use fallback stats if there's an error
  const displayStats = error ? fallbackStats : stats

  // First, check authentication before showing any data
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!authLoading && (!user || !isAdmin)) {
      console.log("AdminLayoutWrapper: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin-new")
      return
    }

    // If user is admin, mark auth as checked
    if (!authLoading && user && isAdmin) {
      console.log("AdminLayoutWrapper: User is authorized")
      setAuthChecked(true)
    }
  }, [user, isAdmin, authLoading, router])

  // Show loading state if auth is not checked
  if (!authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
          {!authLoading && !user && (
            <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md max-w-md">
              <p className="font-semibold">Authentication Error</p>
              <p className="text-sm mt-1">You need to be logged in as an admin to access this page.</p>
              <button
                className="mt-3 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
                onClick={() => router.push("/login?redirectTo=/admin-new")}
              >
                Go to Login
              </button>
            </div>
          )}
          {authLoading && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">
                If loading takes too long, you can try refreshing the page or go back to the classic dashboard.
              </p>
              <div className="mt-3 flex justify-center gap-3">
                <button
                  className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </button>
                <button
                  className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                  onClick={() => router.push("/admin")}
                >
                  Classic Dashboard
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-muted/40">
      <AdminSidebar 
        user={user} 
        userProfile={userProfile} 
        stats={{
          pendingBusinesses: displayStats.pendingBusinesses,
          newUsersThisMonth: displayStats.newUsersThisMonth
        }} 
      />
      
      {/* Main Content */}
      <main className="flex-1 lg:ml-64">
        {children}
      </main>
    </div>
  )
}
