'use client'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { Filter, X } from 'lucide-react'

interface OrderFiltersProps {
  statusFilter: string
  onStatusFilterChange: (status: string) => void
  businessFilter: string
  onBusinessFilterChange: (businessId: string) => void
}

export function OrderFilters({
  statusFilter,
  onStatusFilterChange,
  businessFilter,
  onBusinessFilterChange
}: OrderFiltersProps) {
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'preparing', label: 'Preparing' },
    { value: 'ready', label: 'Ready' },
    { value: 'out_for_delivery', label: 'On the way' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' }
  ]

  const hasActiveFilters = statusFilter || businessFilter

  const clearAllFilters = () => {
    onStatusFilterChange('')
    onBusinessFilterChange('')
  }

  return (
    <div className="flex items-center gap-3">
      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="h-12 px-6 rounded-xl border-gray-200 bg-white shadow-sm hover:bg-gray-50">
            <Filter className="h-5 w-5 mr-2" />
            Status
            {statusFilter && (
              <Badge variant="secondary" className="ml-2 h-5 bg-emerald-100 text-emerald-700 rounded-full">
                1
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 rounded-xl border-gray-200 shadow-lg">
          <DropdownMenuLabel className="text-gray-700 font-semibold">Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {statusOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => onStatusFilterChange(option.value)}
              className={`rounded-lg mx-1 ${statusFilter === option.value ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'}`}
            >
              {option.label}
              {statusFilter === option.value && (
                <span className="ml-auto text-emerald-600">✓</span>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <Button
          variant="ghost"
          onClick={clearAllFilters}
          className="h-12 px-4 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl"
        >
          <X className="h-4 w-4 mr-2" />
          Clear
        </Button>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2">
          {statusFilter && (
            <Badge variant="secondary" className="px-3 py-1.5 bg-emerald-100 text-emerald-700 rounded-full text-sm">
              Status: {statusOptions.find(s => s.value === statusFilter)?.label}
              <button
                onClick={() => onStatusFilterChange('')}
                className="ml-2 hover:bg-emerald-200 rounded-full p-1"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
