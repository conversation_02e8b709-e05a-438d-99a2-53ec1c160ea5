"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function MenuTest() {
  const [categoriesResponse, setCategoriesResponse] = useState<any>(null)
  const [productsResponse, setProductsResponse] = useState<any>(null)
  const [debugResponse, setDebugResponse] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testApis = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Initialize the API (create buckets if needed)
      await fetch("/api/init", {
        method: "GET",
        credentials: "include"
      })

      // Test debug API
      console.log("Testing debug API...")
      const debugResponse = await fetch("/api/debug", {
        method: "GET",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      const debugData = await debugResponse.json()
      setDebugResponse({
        status: debugResponse.status,
        data: debugData
      })

      // Test categories API
      console.log("Testing categories API...")
      const categoriesResponse = await fetch("/api/business-admin/categories", {
        method: "GET",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      const categoriesData = await categoriesResponse.json()
      setCategoriesResponse({
        status: categoriesResponse.status,
        data: categoriesData
      })

      // Test products API
      console.log("Testing products API...")
      const productsResponse = await fetch("/api/business-admin/products", {
        method: "GET",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      const productsData = await productsResponse.json()
      setProductsResponse({
        status: productsResponse.status,
        data: productsData
      })
    } catch (err: any) {
      console.error("Error testing APIs:", err)
      setError(err.message || "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Menu API Test</h1>
        <Button
          onClick={testApis}
          disabled={isLoading}
        >
          {isLoading ? "Testing..." : "Test APIs"}
        </Button>
      </div>

      {error && (
        <Card className="border-red-500">
          <CardHeader>
            <CardTitle className="text-red-500">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
          </CardContent>
        </Card>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Debug API</CardTitle>
        </CardHeader>
        <CardContent>
          {debugResponse ? (
            <div>
              <p>Status: {debugResponse.status}</p>
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">Authentication</h3>
                  <div className="p-2 bg-gray-100 rounded-md">
                    <p>Authenticated: {debugResponse.data.isAuthenticated ? "Yes" : "No"}</p>
                    {debugResponse.data.userInfo && (
                      <div className="mt-2">
                        <p>User ID: {debugResponse.data.userInfo.id}</p>
                        <p>Role: {debugResponse.data.userInfo.role}</p>
                        <p>Email: {debugResponse.data.userInfo.email}</p>
                        <p>Business ID: {debugResponse.data.userInfo.business_id || "None"}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Database Tables</h3>
                  <div className="p-2 bg-gray-100 rounded-md">
                    <p>Categories: {debugResponse.data.tables.categories ? "Exists" : "Missing"}</p>
                    <p>Products: {debugResponse.data.tables.products ? "Exists" : "Missing"}</p>
                    <p>Users: {debugResponse.data.tables.users ? "Exists" : "Missing"}</p>
                    <p>Businesses: {debugResponse.data.tables.businesses ? "Exists" : "Missing"}</p>
                    <p>Product Variants: {debugResponse.data.tables.product_variants ? "Exists" : "Missing"}</p>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-medium mb-2">Environment</h3>
                <div className="p-2 bg-gray-100 rounded-md">
                  <p>NODE_ENV: {debugResponse.data.environment.NODE_ENV}</p>
                  <p>NEXT_PUBLIC_SUPABASE_URL: {debugResponse.data.environment.NEXT_PUBLIC_SUPABASE_URL}</p>
                  <p>SUPABASE_SERVICE_ROLE_KEY: {debugResponse.data.environment.SUPABASE_SERVICE_ROLE_KEY}</p>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-medium mb-2">Full Response</h3>
                <pre className="p-4 bg-gray-100 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(debugResponse.data, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No data yet. Click "Test APIs" to fetch data.</p>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Categories API</CardTitle>
          </CardHeader>
          <CardContent>
            {categoriesResponse ? (
              <div>
                <p>Status: {categoriesResponse.status}</p>
                <pre className="mt-4 p-4 bg-gray-100 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(categoriesResponse.data, null, 2)}
                </pre>
              </div>
            ) : (
              <p className="text-gray-500">No data yet. Click "Test APIs" to fetch data.</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Products API</CardTitle>
          </CardHeader>
          <CardContent>
            {productsResponse ? (
              <div>
                <p>Status: {productsResponse.status}</p>
                <pre className="mt-4 p-4 bg-gray-100 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(productsResponse.data, null, 2)}
                </pre>
              </div>
            ) : (
              <p className="text-gray-500">No data yet. Click "Test APIs" to fetch data.</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
