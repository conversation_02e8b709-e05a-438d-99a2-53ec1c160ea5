import { NextResponse } from 'next/server';
import { adminClient } from '@/lib/supabase-admin';
import { formatPointString } from '@/lib/address-utils';

/**
 * API endpoint to update coordinates for all users
 * This is an admin-only endpoint that should be protected
 */
export async function POST(request: Request) {
  try {
    // Get all users that don't have coordinates
    const { data: users, error: fetchError } = await adminClient
      .from('users')
      .select('id, email, address, postcode, coordinates, latitude, longitude')
      .or('coordinates.is.null,latitude.is.null,longitude.is.null');

    if (fetchError) {
      console.error('Error fetching users:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch users', details: fetchError.message },
        { status: 500 }
      );
    }

    if (!users || users.length === 0) {
      return NextResponse.json({
        message: 'No users found that need coordinate updates',
        updated: 0,
        total: 0
      });
    }

    console.log(`Found ${users.length} users that need coordinate updates`);

    // Process each user
    const results = {
      success: 0,
      failed: 0,
      total: users.length,
      failures: [] as any[]
    };

    for (const user of users) {
      try {
        // Skip users without address or postcode
        if (!user.address && !user.postcode) {
          console.log(`Skipping user ${user.id} due to missing address and postcode`);
          results.failed++;
          results.failures.push({
            id: user.id,
            email: user.email,
            reason: 'Missing address and postcode'
          });
          continue;
        }

        // Construct the full address for geocoding
        const addressComponents = [
          user.address,
          user.postcode,
          'Jersey'
        ].filter(Boolean);

        const fullAddress = addressComponents.join(", ");
        console.log(`Geocoding address for user ${user.id}: "${fullAddress}"`);

        // Geocode the address
        const coordinates = await geocodeAddress(fullAddress);

        if (!coordinates) {
          console.error(`Could not geocode address: ${fullAddress}`);
          results.failed++;
          results.failures.push({
            id: user.id,
            email: user.email,
            reason: 'Geocoding failed'
          });
          continue;
        }

        console.log(`Successfully geocoded address to coordinates: [${coordinates[0]}, ${coordinates[1]}]`);

        // Format coordinates as PostgreSQL POINT type
        const pointString = formatPointString(coordinates[0], coordinates[1]);

        // Update the user record
        const { error: updateError } = await adminClient
          .from('users')
          .update({
            coordinates: pointString,
            latitude: coordinates[1],
            longitude: coordinates[0],
            coordinates_updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (updateError) {
          console.error(`Error updating user ${user.id}:`, updateError);
          results.failed++;
          results.failures.push({
            id: user.id,
            email: user.email,
            reason: updateError.message
          });
          continue;
        }

        console.log(`Successfully updated coordinates for user ${user.id}`);
        results.success++;
      } catch (error: any) {
        console.error(`Error processing user ${user.id}:`, error);
        results.failed++;
        results.failures.push({
          id: user.id,
          email: user.email,
          reason: error.message || 'Unknown error'
        });
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return NextResponse.json({
      message: `Processed ${results.total} users: ${results.success} updated, ${results.failed} failed`,
      ...results
    });
  } catch (error: any) {
    console.error('Error in update-user-coordinates endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
