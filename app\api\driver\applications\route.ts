import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single()

    if (driverError) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Get search parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // pending, approved, rejected, banned
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query for applications
    let applicationsQuery = supabase
      .from('driver_business_approvals')
      .select(`
        id,
        business_id,
        status,
        application_date,
        decision_date,
        rejection_reason,
        ban_reason,
        notes,
        businesses!inner (
          id,
          name,
          slug,
          business_type_id,
          logo_url,
          address,
          location,
          phone,
          is_approved,
          delivery_available,
          business_types(name)
        )
      `)
      .eq('driver_id', driverProfile.id)
      .order('application_date', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply status filter if provided
    if (status) {
      applicationsQuery = applicationsQuery.eq('status', status)
    }

    const { data: applications, error: applicationsError } = await applicationsQuery

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError)
      return NextResponse.json(
        { error: "Failed to fetch applications" },
        { status: 500 }
      )
    }

    // Get summary statistics
    const { data: summaryData, error: summaryError } = await supabase
      .from('driver_business_approvals')
      .select('status')
      .eq('driver_id', driverProfile.id)

    const summary = {
      total: summaryData?.length || 0,
      pending: summaryData?.filter(app => app.status === 'pending').length || 0,
      approved: summaryData?.filter(app => app.status === 'approved').length || 0,
      rejected: summaryData?.filter(app => app.status === 'rejected').length || 0,
      banned: summaryData?.filter(app => app.status === 'banned').length || 0
    }

    // Transform applications for response
    const transformedApplications = applications?.map(app => ({
      id: app.id,
      business: {
        id: app.businesses.id,
        name: app.businesses.name,
        slug: app.businesses.slug,
        type: app.businesses.business_types.name,
        logo_url: app.businesses.logo_url,
        address: app.businesses.address,
        parish: app.businesses.location,
        phone: app.businesses.phone,
        is_active: app.businesses.is_approved && app.businesses.delivery_available
      },
      status: app.status,
      application_date: app.application_date,
      decision_date: app.decision_date,
      rejection_reason: app.rejection_reason,
      ban_reason: app.ban_reason,
      notes: app.notes,
      can_reapply: app.status === 'rejected' && app.businesses.is_active
    }))

    return NextResponse.json({
      success: true,
      applications: transformedApplications,
      summary,
      pagination: {
        limit,
        offset,
        total: transformedApplications?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in driver applications GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
