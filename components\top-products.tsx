import { Package } from "lucide-react"

export function TopProducts() {
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
          <Package className="h-5 w-5 text-emerald-500" />
        </div>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Premium Headphones</p>
          <p className="text-sm text-muted-foreground">142 units sold</p>
        </div>
        <div className="ml-auto font-medium">£4,250</div>
      </div>
      <div className="flex items-center">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
          <Package className="h-5 w-5 text-emerald-500" />
        </div>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Wireless Charger</p>
          <p className="text-sm text-muted-foreground">98 units sold</p>
        </div>
        <div className="ml-auto font-medium">£2,940</div>
      </div>
      <div className="flex items-center">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
          <Package className="h-5 w-5 text-emerald-500" />
        </div>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Smart Watch</p>
          <p className="text-sm text-muted-foreground">76 units sold</p>
        </div>
        <div className="ml-auto font-medium">£2,280</div>
      </div>
      <div className="flex items-center">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
          <Package className="h-5 w-5 text-emerald-500" />
        </div>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Bluetooth Speaker</p>
          <p className="text-sm text-muted-foreground">64 units sold</p>
        </div>
        <div className="ml-auto font-medium">£1,920</div>
      </div>
      <div className="flex items-center">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
          <Package className="h-5 w-5 text-emerald-500" />
        </div>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Power Bank</p>
          <p className="text-sm text-muted-foreground">52 units sold</p>
        </div>
        <div className="ml-auto font-medium">£1,560</div>
      </div>
    </div>
  )
}
