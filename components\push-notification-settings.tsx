'use client'

import { useState, useEffect } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON>, BellOff, Settings } from 'lucide-react'
import { notificationService } from '@/services/notification-service'
import { NotificationPermission } from '@/components/notification-permission'

interface PushNotificationSettingsProps {
  className?: string
}

export function PushNotificationSettings({ className }: PushNotificationSettingsProps) {
  const [hasPermission, setHasPermission] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [preferences, setPreferences] = useState({
    orderUpdates: true,
    deliveryUpdates: true,
    promotions: false,
    messages: true
  })

  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Check if push notifications are supported
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
          console.log('Push notifications not supported')
          setIsLoading(false)
          return
        }

        // Check current permission status
        const permission = Notification.permission
        setHasPermission(permission === "granted")

        // Check if user has existing subscriptions via API
        await loadUserPreferences()

      } catch (error) {
        console.error('Error initializing notifications:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeNotifications()
  }, [])

  const loadUserPreferences = async () => {
    try {
      const response = await fetch('/api/notifications/subscribe')
      if (response.ok) {
        const data = await response.json()
        console.log('Subscription data:', data) // Debug log

        if (data.subscriptions && data.subscriptions.length > 0) {
          setIsSubscribed(true)
          const userPrefs = data.subscriptions[0].preferences
          if (userPrefs) {
            setPreferences({
              orderUpdates: userPrefs.order_updates ?? true,
              deliveryUpdates: userPrefs.delivery_updates ?? true,
              promotions: userPrefs.marketing ?? false,
              messages: userPrefs.messages ?? true
            })
          }
        } else {
          setIsSubscribed(false)
        }
      } else {
        console.log('API response not ok:', response.status, response.statusText)
        setIsSubscribed(false)
      }
    } catch (error) {
      console.error('Error loading preferences:', error)
      setIsSubscribed(false)
    }
  }

  const handlePermissionChange = (permission: NotificationPermission) => {
    setHasPermission(permission === "granted")
  }

  const handleSubscriptionToggle = async () => {
    setIsLoading(true)
    try {
      if (isSubscribed) {
        // Unsubscribe
        const response = await fetch('/api/notifications/subscribe', {
          method: 'DELETE'
        })
        if (response.ok) {
          setIsSubscribed(false)
        }
      } else {
        // Request permission first if not granted
        if (!hasPermission) {
          const permission = await Notification.requestPermission()
          setHasPermission(permission === 'granted')
          if (permission !== 'granted') {
            return
          }
        }

        // Create real service worker subscription for customers
        try {
          // Initialize notification service to get real subscription
          await notificationService.initialize()
          const subscription = await notificationService.subscribe()

          if (!subscription) {
            throw new Error('Failed to create push subscription')
          }

          console.log('🔧 Creating subscription with endpoint:', subscription.endpoint.substring(0, 50) + '...')

          const response = await fetch('/api/notifications/subscribe', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subscription: {
                endpoint: subscription.endpoint,
                keys: {
                  p256dh: subscription.keys?.p256dh || '',
                  auth: subscription.keys?.auth || ''
                }
              },
              deviceType: 'desktop',
              browserName: navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other',
              userAgent: navigator.userAgent,
              preferences: {
                order_updates: preferences.orderUpdates,
                delivery_updates: preferences.deliveryUpdates,
                marketing: preferences.promotions,
                messages: preferences.messages
              },
              forceActivate: false // Allow multiple device subscriptions to be active
            })
          })

          if (!response.ok) {
            const errorText = await response.text()
            console.error('Subscription API failed:', errorText)
            throw new Error(`Failed to save subscription: ${response.status}`)
          }

          console.log('✅ Subscription created successfully')
        } catch (subscriptionError) {
          console.error('Failed to create service worker subscription:', subscriptionError)
          throw new Error('Could not set up push notifications. Please try again.')
        }

        if (response.ok) {
          setIsSubscribed(true)
          await loadUserPreferences()
        }
      }
    } catch (error) {
      console.error('Error toggling subscription:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreferenceChange = async (key: keyof typeof preferences) => {
    const newPreferences = {
      ...preferences,
      [key]: !preferences[key]
    }
    setPreferences(newPreferences)

    // Save to server
    try {
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription: await notificationService.getSubscription(),
          preferences: {
            order_updates: newPreferences.orderUpdates,
            delivery_updates: newPreferences.deliveryUpdates,
            marketing: newPreferences.promotions,
            messages: newPreferences.messages
          }
        })
      })

      if (!response.ok) {
        console.error('Failed to save preferences')
        // Revert the change
        setPreferences(preferences)
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      // Revert the change
      setPreferences(preferences)
    }
  }

  const testNotification = async () => {
    if (isSubscribed && hasPermission) {
      try {
        // Use server-side test notification for proper Loop branding
        const response = await fetch('/api/test-notification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'customer_test'
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Test notification sent via server:', result)
          // Don't show browser notification - server notification should appear
        } else {
          const errorText = await response.text()
          console.error('❌ Server test notification failed:', response.status, errorText)
          // Fallback to browser notification
          new Notification('Test Notification (Fallback)', {
            body: 'Server notification failed - this is browser fallback',
            icon: '/android-chrome-192x192.png',
            badge: '/favicon-32x32.png'
          })
        }
      } catch (error) {
        console.error('❌ Error sending test notification:', error)
        // Fallback to browser notification
        new Notification('Test Notification (Error Fallback)', {
          body: 'Network error occurred - this is browser fallback',
          icon: '/android-chrome-192x192.png',
          badge: '/favicon-32x32.png'
        })
      }
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Push Notifications
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Permission Request */}
        <NotificationPermission onPermissionChange={handlePermissionChange} />

        {hasPermission && (
          <>
            {/* Main Subscription Toggle */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base font-medium">Enable Push Notifications</Label>
                <p className="text-sm text-gray-600">
                  {isSubscribed
                    ? "You'll receive real-time notifications about your orders"
                    : "Get instant updates about your orders and deliveries"
                  }
                </p>
              </div>
              <Switch
                checked={isSubscribed}
                onCheckedChange={handleSubscriptionToggle}
                disabled={isLoading}
              />
            </div>

            {/* Notification Preferences */}
            {isSubscribed && (
              <>
                <div className="border-t pt-6">
                  <h4 className="text-sm font-medium mb-4 flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Notification Types
                  </h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Order Updates</Label>
                        <p className="text-xs text-gray-500">
                          Status changes, confirmations, and ready notifications
                        </p>
                      </div>
                      <Switch
                        checked={preferences.orderUpdates}
                        onCheckedChange={() => handlePreferenceChange('orderUpdates')}
                        size="sm"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Delivery Updates</Label>
                        <p className="text-xs text-gray-500">
                          Driver assignment and delivery progress
                        </p>
                      </div>
                      <Switch
                        checked={preferences.deliveryUpdates}
                        onCheckedChange={() => handlePreferenceChange('deliveryUpdates')}
                        size="sm"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Messages</Label>
                        <p className="text-xs text-gray-500">
                          Messages from businesses and drivers
                        </p>
                      </div>
                      <Switch
                        checked={preferences.messages}
                        onCheckedChange={() => handlePreferenceChange('messages')}
                        size="sm"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Promotions & Offers</Label>
                        <p className="text-xs text-gray-500">
                          Special deals and discounts
                        </p>
                      </div>
                      <Switch
                        checked={preferences.promotions}
                        onCheckedChange={() => handlePreferenceChange('promotions')}
                        size="sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Test Notification */}
                <div className="border-t pt-6">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testNotification}
                    className="w-full"
                  >
                    Send Test Notification
                  </Button>
                </div>
              </>
            )}
          </>
        )}

        {!hasPermission && (
          <div className="text-center py-4">
            <BellOff className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              Enable browser notifications to receive real-time updates
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
