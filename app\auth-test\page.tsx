"use client"

import { useEffect, useState } from "react"

export default function AuthTestPage() {
  const [authState, setAuthState] = useState<any>({})
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const testAuth = async () => {
      try {
        // Test 1: Check if we can import the auth context
        console.log("Testing auth context import...")
        const { useAuth } = await import("@/context/unified-auth-context")
        console.log("Auth context imported successfully")

        // Test 2: Check if we can call the API directly
        console.log("Testing API call...")
        const response = await fetch('/api/user/profile', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })
        
        const result = await response.json()
        console.log("API response:", result)

        setAuthState({
          apiWorking: response.ok,
          apiData: result,
          timestamp: new Date().toISOString()
        })

      } catch (err: any) {
        console.error("Auth test error:", err)
        setError(err.message)
      }
    }

    testAuth()
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Auth System Test</h1>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(authState, null, 2)}
          </pre>
        </div>

        <div className="mt-6 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Manual Tests</h2>
          <div className="space-y-4">
            <button 
              onClick={() => window.location.href = '/api/user/profile'}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test API Endpoint Directly
            </button>
            
            <button 
              onClick={() => window.location.href = '/profile'}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 ml-4"
            >
              Go to Profile Page
            </button>

            <button 
              onClick={() => window.location.href = '/debug-auth'}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 ml-4"
            >
              Debug Auth Context
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
