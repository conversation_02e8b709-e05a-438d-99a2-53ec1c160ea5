"use client"

import { useState } from "react"
import { Book, ChevronRight, ChevronDown, CheckCircle, Clock, MapPin, DollarSign, Store, Image, Users, Phone, Mail } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"

interface SetupGuideProps {
  className?: string
}

interface GuideSection {
  id: string
  title: string
  icon: any
  description: string
  estimatedTime: string
  steps: GuideStep[]
  tips: string[]
  commonMistakes: string[]
}

interface GuideStep {
  title: string
  description: string
  action?: string
  link?: string
}

export default function SetupGuide({ className = "" }: SetupGuideProps) {
  const [expandedSection, setExpandedSection] = useState<string | null>('getting-started')

  const guideSections: GuideSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: Store,
      description: 'Essential first steps to set up your business profile',
      estimatedTime: '10-15 minutes',
      steps: [
        {
          title: 'Complete Business Information',
          description: 'Add your business name, description, phone, and email',
          action: 'Go to Basic Info',
          link: '/business-admin/settings?tab=basic'
        },
        {
          title: 'Set Your Location',
          description: 'Enter your business address and Jersey postcode',
          action: 'Set Location',
          link: '/business-admin/settings?tab=location'
        },
        {
          title: 'Configure Opening Hours',
          description: 'Set when customers can place orders',
          action: 'Set Hours',
          link: '/business-admin/settings?tab=hours'
        }
      ],
      tips: [
        'Use a clear, memorable business name that customers will recognize',
        'Write a compelling description that highlights what makes you special',
        'Ensure your phone number is always answered during business hours',
        'Use your actual business address for accurate delivery calculations'
      ],
      commonMistakes: [
        'Using a generic business description',
        'Setting unrealistic opening hours',
        'Forgetting to include contact information'
      ]
    },
    {
      id: 'pricing-delivery',
      title: 'Pricing & Delivery',
      icon: DollarSign,
      description: 'Set up your pricing structure and delivery options',
      estimatedTime: '5-10 minutes',
      steps: [
        {
          title: 'Set Minimum Order Amount',
          description: 'Choose a minimum order value that makes delivery worthwhile',
          action: 'Configure Pricing',
          link: '/business-admin/settings?tab=pricing'
        },
        {
          title: 'Configure Delivery Fees',
          description: 'Set your delivery fee structure (fixed, distance-based, or combination)',
          action: 'Set Delivery Fees',
          link: '/business-admin/settings?tab=pricing'
        },
        {
          title: 'Set Delivery Radius',
          description: 'Define how far you\'ll deliver from your location',
          action: 'Set Delivery Area',
          link: '/business-admin/settings?tab=location'
        }
      ],
      tips: [
        'Research competitor pricing in your area',
        'Consider your costs when setting delivery fees',
        'Distance-based pricing can be fairer for customers',
        'Start with a smaller delivery radius and expand as you grow'
      ],
      commonMistakes: [
        'Setting minimum orders too high or too low',
        'Not accounting for delivery costs in pricing',
        'Setting delivery radius too large for your capacity'
      ]
    },
    {
      id: 'branding-photos',
      title: 'Branding & Photos',
      icon: Image,
      description: 'Upload your logo and showcase your business',
      estimatedTime: '15-20 minutes',
      steps: [
        {
          title: 'Upload Your Logo',
          description: 'Add a high-quality logo that represents your brand',
          action: 'Upload Logo',
          link: '/business-admin/settings?tab=branding'
        },
        {
          title: 'Add Banner Image',
          description: 'Showcase your best dishes or restaurant interior',
          action: 'Add Banner',
          link: '/business-admin/settings?tab=branding'
        },
        {
          title: 'Add Product Photos',
          description: 'Upload appetizing photos of your menu items',
          action: 'Manage Products',
          link: '/business-admin/products'
        }
      ],
      tips: [
        'Use high-quality, well-lit photos',
        'Square logos work best for profile pictures',
        'Show your food at its most appetizing',
        'Keep file sizes under 2MB for faster loading'
      ],
      commonMistakes: [
        'Using low-quality or blurry images',
        'Photos that don\'t represent the actual food',
        'Forgetting to add photos to menu items'
      ]
    },
    {
      id: 'menu-products',
      title: 'Menu & Products',
      icon: Store,
      description: 'Add your menu items and organize your offerings',
      estimatedTime: '30-60 minutes',
      steps: [
        {
          title: 'Create Product Categories',
          description: 'Organize your menu into logical sections (starters, mains, desserts, etc.)',
          action: 'Manage Products',
          link: '/business-admin/products'
        },
        {
          title: 'Add Menu Items',
          description: 'Add your dishes with descriptions, prices, and photos',
          action: 'Add Products',
          link: '/business-admin/products'
        },
        {
          title: 'Set Availability',
          description: 'Mark items as available or out of stock',
          action: 'Update Availability',
          link: '/business-admin/products'
        }
      ],
      tips: [
        'Write appetizing descriptions that make customers hungry',
        'Include allergen information and dietary options',
        'Price competitively but don\'t undervalue your food',
        'Keep your menu updated with seasonal items'
      ],
      commonMistakes: [
        'Adding too many items at once',
        'Forgetting to include allergen information',
        'Not updating availability when items run out'
      ]
    },
    {
      id: 'team-management',
      title: 'Team Management',
      icon: Users,
      description: 'Add team members and manage access',
      estimatedTime: '5-10 minutes',
      steps: [
        {
          title: 'Add Team Members',
          description: 'Invite staff who need access to manage orders and settings',
          action: 'Manage Team',
          link: '/business-admin/settings?tab=team'
        },
        {
          title: 'Set Permissions',
          description: 'Choose what each team member can access',
          action: 'Set Permissions',
          link: '/business-admin/settings?tab=team'
        },
        {
          title: 'Train Your Team',
          description: 'Ensure everyone knows how to use the system',
          action: 'View Training',
          link: '/help/business-training'
        }
      ],
      tips: [
        'Only add team members who truly need admin access',
        'Train staff on order management procedures',
        'Regularly review team member access',
        'Have backup staff trained on the system'
      ],
      commonMistakes: [
        'Giving too many people admin access',
        'Not training staff properly',
        'Forgetting to remove access when staff leave'
      ]
    },
    {
      id: 'going-live',
      title: 'Going Live',
      icon: CheckCircle,
      description: 'Final steps before accepting your first orders',
      estimatedTime: '10-15 minutes',
      steps: [
        {
          title: 'Test Order Process',
          description: 'Place a test order to ensure everything works',
          action: 'Test System',
          link: '/business-admin/orders'
        },
        {
          title: 'Review Settings',
          description: 'Double-check all your settings are correct',
          action: 'Review Settings',
          link: '/business-admin/settings'
        },
        {
          title: 'Monitor First Orders',
          description: 'Stay close to the system for your first few orders',
          action: 'View Orders',
          link: '/business-admin/orders'
        }
      ],
      tips: [
        'Start with limited hours to test the system',
        'Have extra staff available for your first day',
        'Monitor order times and adjust preparation time if needed',
        'Ask for feedback from your first customers'
      ],
      commonMistakes: [
        'Not testing the system before going live',
        'Being unprepared for the first orders',
        'Not monitoring performance closely at first'
      ]
    }
  ]

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 rounded-full p-2">
            <Book className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <CardTitle>Business Setup Guide</CardTitle>
            <CardDescription>
              Step-by-step guide to get your business ready for orders
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {guideSections.map((section, index) => (
          <div key={section.id}>
            <Collapsible 
              open={expandedSection === section.id} 
              onOpenChange={() => setExpandedSection(expandedSection === section.id ? null : section.id)}
            >
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-4 h-auto border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 rounded-full p-2">
                      <section.icon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-left">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{section.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {section.estimatedTime}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">{section.description}</div>
                    </div>
                  </div>
                  {expandedSection === section.id ? (
                    <ChevronDown className="h-5 w-5" />
                  ) : (
                    <ChevronRight className="h-5 w-5" />
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="pt-4 space-y-4">
                {/* Steps */}
                <div className="space-y-3 pl-4">
                  <h4 className="font-medium text-sm text-gray-900">Steps to Complete:</h4>
                  {section.steps.map((step, stepIndex) => (
                    <div key={stepIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{step.title}</div>
                        <div className="text-sm text-gray-600">{step.description}</div>
                      </div>
                      {step.link && (
                        <Link href={step.link}>
                          <Button size="sm" variant="outline" className="ml-3">
                            {step.action}
                          </Button>
                        </Link>
                      )}
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Tips */}
                <div className="pl-4">
                  <h4 className="font-medium text-sm text-green-800 mb-2">💡 Pro Tips:</h4>
                  <ul className="space-y-1">
                    {section.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="text-sm text-green-700 pl-4 border-l-2 border-green-200">
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Common Mistakes */}
                <div className="pl-4">
                  <h4 className="font-medium text-sm text-orange-800 mb-2">⚠️ Avoid These Mistakes:</h4>
                  <ul className="space-y-1">
                    {section.commonMistakes.map((mistake, mistakeIndex) => (
                      <li key={mistakeIndex} className="text-sm text-orange-700 pl-4 border-l-2 border-orange-200">
                        {mistake}
                      </li>
                    ))}
                  </ul>
                </div>
              </CollapsibleContent>
            </Collapsible>
            
            {index < guideSections.length - 1 && <Separator className="my-4" />}
          </div>
        ))}

        {/* Quick Help */}
        <div className="pt-4 border-t border-gray-200">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Need Help?</h4>
            <p className="text-sm text-blue-700 mb-3">
              Our team is here to help you succeed. Get in touch if you need assistance with any part of the setup.
            </p>
            <div className="flex gap-2">
              <Link href="/messages">
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Support
                </Button>
              </Link>
              <Button size="sm" variant="outline">
                <Phone className="mr-2 h-4 w-4" />
                Call Us
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
