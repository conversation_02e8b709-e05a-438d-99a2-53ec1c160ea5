'use client'

import { useState, useEffect } from 'react'
import { supabase, supabaseAdmin } from '@/lib/supabase'

export default function TestConnectionPage() {
  const [apiResult, setApiResult] = useState<any>(null)
  const [directResult, setDirectResult] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      
      try {
        // Test API endpoint
        const apiResponse = await fetch('/api/test-connection')
        const apiData = await apiResponse.json()
        setApiResult(apiData)
        
        // Test direct Supabase connection
        try {
          const { data: businessTypes, error: typesError } = await supabase
            .from('business_types')
            .select('*')
            .limit(5)
            
          if (typesError) {
            throw typesError
          }
          
          const { data: businesses, error: businessesError } = await supabase
            .from('businesses')
            .select('*')
            .limit(5)
            
          if (businessesError) {
            throw businessesError
          }
          
          setDirectResult({
            success: true,
            businessTypes,
            businesses
          })
        } catch (directError) {
          setDirectResult({
            success: false,
            error: directError.message || 'Unknown error'
          })
        }
      } catch (err) {
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">API Test Result</h2>
          {apiResult ? (
            <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(apiResult, null, 2)}
            </pre>
          ) : (
            <p>No API result yet</p>
          )}
        </div>
        
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Direct Connection Test</h2>
          {directResult ? (
            <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(directResult, null, 2)}
            </pre>
          ) : (
            <p>No direct result yet</p>
          )}
        </div>
      </div>
    </div>
  )
}
