'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft, Search } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-[70vh] flex items-center justify-center bg-white px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-green-100 mb-4">
            <span className="text-5xl">🔍</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Page Not Found</h1>
          <p className="text-gray-600 mb-6">
            We couldn&apos;t find the page you&apos;re looking for. It might have been moved, deleted, or never existed.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="default"
            className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center gap-2"
            asChild
          >
            <Link href="/">
              <Home className="h-4 w-4" />
              Back to Home
            </Link>
          </Button>

          <Button
            variant="outline"
            className="border-emerald-600 text-emerald-600 hover:bg-emerald-50 flex items-center gap-2"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>

          <Button
            variant="outline"
            className="border-emerald-600 text-emerald-600 hover:bg-emerald-50 flex items-center gap-2"
            asChild
          >
            <Link href="/search">
              <Search className="h-4 w-4" />
              Search
            </Link>
          </Button>
        </div>

        <div className="mt-12">
          <h2 className="text-lg font-medium text-gray-700 mb-4">Popular Destinations</h2>
          <div className="grid grid-cols-2 gap-3">
            <Link
              href="/search?type=restaurant"
              className="p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 text-gray-800 hover:text-red-600"
            >
              Restaurants
            </Link>
            <Link
              href="/search?type=shop"
              className="p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 text-gray-800 hover:text-blue-600"
            >
              Shops
            </Link>
            <Link
              href="/search?type=pharmacy"
              className="p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 text-gray-800 hover:text-purple-600"
            >
              Pharmacies
            </Link>
            <Link
              href="/search?type=cafe"
              className="p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 text-gray-800 hover:text-amber-600"
            >
              Cafes
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
