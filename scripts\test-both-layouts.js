/**
 * Test script to verify both standard and aisle layouts are working
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testBothLayouts() {
  try {
    console.log('🧪 Testing both business layouts...\n');

    // Test Jersey Wings (standard layout)
    const { data: wings, error: wingsError } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-wings')
      .single();

    if (wingsError) {
      console.error('❌ Error fetching Jersey Wings:', wingsError);
    } else {
      console.log('🍗 Jersey Wings:');
      console.log(`   Layout: ${wings.page_layout || 'standard'}`);
      console.log(`   URL: http://localhost:3000/business/${wings.slug}`);
      console.log(`   Expected: Standard layout with categories in sidebar\n`);
    }

    // Test Jersey Co-op (aisle layout)
    const { data: coop, error: coopError } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-coop')
      .single();

    if (coopError) {
      console.error('❌ Error fetching Jersey Co-op:', coopError);
    } else {
      console.log('🏪 Jersey Co-op:');
      console.log(`   Layout: ${coop.page_layout}`);
      console.log(`   URL: http://localhost:3000/business/${coop.slug}`);
      console.log(`   Expected: Aisle layout with Level 0 categories as cards\n`);

      // Get some Level 0 categories for testing
      const { data: categories, error: catError } = await supabase
        .from('business_custom_categories')
        .select('name, slug')
        .eq('business_id', coop.id)
        .eq('level', 0)
        .limit(3);

      if (!catError && categories && categories.length > 0) {
        console.log('   Sample category URLs:');
        categories.forEach(cat => {
          console.log(`     http://localhost:3000/business/${coop.slug}?category=${cat.slug}`);
        });
      }
    }

    console.log('\n✅ Test URLs ready!');
    console.log('\n📋 What to verify:');
    console.log('   1. Jersey Wings shows standard layout (categories in sidebar)');
    console.log('   2. Jersey Co-op shows aisle layout (Level 0 categories as cards)');
    console.log('   3. Jersey Co-op category pages show Level 1 subcategories');
    console.log('   4. Search functionality works in both layouts');
    console.log('   5. Order forms work in both layouts');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testBothLayouts();
