import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Check if we have the required environment variables
if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables for Supabase client');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const productId = searchParams.get('productId');
  const attributeType = searchParams.get('attributeType');
  const businessId = searchParams.get('businessId');

  if (!productId && !businessId) {
    return NextResponse.json(
      { error: 'Either productId or businessId is required' },
      { status: 400 }
    );
  }

  try {
    console.log(`Fetching product attributes for ${productId ? `product ${productId}` : `business ${businessId}`}${attributeType ? ` with type ${attributeType}` : ''}`);
    
    let query = supabase
      .from('product_attributes')
      .select(`
        id,
        product_id,
        attribute_type,
        attribute_value,
        products:product_id (
          id,
          name,
          business_id
        )
      `);

    // Add filters if provided
    if (productId) {
      query = query.eq('product_id', productId);
    }

    if (attributeType) {
      query = query.eq('attribute_type', attributeType);
    }

    if (businessId && !productId) {
      query = query.eq('products.business_id', businessId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching product attributes:', error);
      
      return NextResponse.json(
        { error: 'Failed to fetch product attributes', details: error.message },
        { status: 500 }
      );
    }

    console.log(`Found ${data?.length || 0} attributes`);
    
    return NextResponse.json({ 
      attributes: data || [],
      message: `Found ${data?.length || 0} attributes`
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
