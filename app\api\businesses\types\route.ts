import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  // Create a Supabase client with the service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey)

  try {
    // Fetch business types
    const { data: businessTypes, error: typesError } = await supabase
      .from("business_types")
      .select("id, name, slug")
      .order("name")

    if (typesError) {
      console.error("Error fetching business types:", typesError)
      return NextResponse.json(
        { error: typesError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({ businessTypes })
  } catch (error) {
    console.error("Error fetching business types:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
