import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    console.log('🔍 Debug: Checking notifications for user:', user.id)

    // Check push subscriptions
    const { data: subscriptions, error: subError } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (subError) {
      console.error('Error fetching subscriptions:', subError)
      return NextResponse.json({ error: 'Failed to fetch subscriptions' }, { status: 500 })
    }

    // Check recent orders for this user
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('id, order_number, status, user_id, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5)

    if (ordersError) {
      console.error('Error fetching orders:', ordersError)
    }

    // Check recent notification logs
    const { data: notificationLogs, error: logsError } = await supabase
      .from('notification_log')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10)

    if (logsError) {
      console.error('Error fetching notification logs:', logsError)
    }

    // Check order status history for recent orders
    let statusHistory = null
    if (orders && orders.length > 0) {
      const { data: history, error: historyError } = await supabase
        .from('order_status_history')
        .select('*')
        .in('order_id', orders.map(o => o.id))
        .order('created_at', { ascending: false })
        .limit(10)

      if (!historyError) {
        statusHistory = history
      }
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email
      },
      subscriptions: {
        count: subscriptions?.length || 0,
        active: subscriptions?.filter(s => s.is_active).length || 0,
        details: subscriptions?.map(s => ({
          id: s.id,
          device_type: s.device_type,
          browser_name: s.browser_name,
          is_active: s.is_active,
          created_at: s.created_at,
          preferences: s.preferences,
          hasRealEndpoint: s.subscription_data?.endpoint && !s.subscription_data.endpoint.includes('placeholder')
        }))
      },
      orders: {
        count: orders?.length || 0,
        recent: orders?.map(o => ({
          id: o.id,
          order_number: o.order_number,
          status: o.status,
          created_at: o.created_at
        }))
      },
      notificationLogs: {
        count: notificationLogs?.length || 0,
        recent: notificationLogs?.map(l => ({
          id: l.id,
          title: l.title,
          status: l.status,
          error_message: l.error_message,
          created_at: l.created_at
        }))
      },
      statusHistory: {
        count: statusHistory?.length || 0,
        recent: statusHistory?.map(h => ({
          order_id: h.order_id,
          status: h.status,
          customer_notified: h.customer_notified,
          notification_sent: h.notification_sent,
          created_at: h.created_at
        }))
      }
    })

  } catch (error: any) {
    console.error('Debug API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
