"use client"

import { useEffect, useState } from "react"
import { Inter } from "next/font/google"
import type { ReactNode } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useA<PERSON>, AuthProvider } from "@/context/unified-auth-context"
import { SupabaseProvider } from "@/components/providers/supabase-provider"
import { SuperAdminGuard } from "@/components/auth/auth-guards"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import {
  BarChart3,
  Home,
  MenuIcon,
  LogOut,
  Shield,
  Database,
  Cog,
  ChevronDown,
  FileText,
  Settings,
  ShoppingBag,
  UserCircle,
  Store,
  Building2,
  ShieldCheck,
  Tags
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
// import UserMenu from "@/components/auth/user-menu" // Uses unified auth context, incompatible with AuthProviderDirect

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

interface SuperAdminLayoutProps {
  children: ReactNode
}

function SuperAdminLayoutContent({ children }: SuperAdminLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, userProfile, signOut } = useAuth()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Get page title based on pathname
  const getPageTitle = (path: string) => {
    const routes: Record<string, string> = {
      '/super-admin': 'Dashboard',
      '/super-admin/system-logs': 'System Logs',
      '/super-admin/platform-settings': 'Platform Settings',
      '/super-admin/data-audit': 'Data Audit'
    }
    return routes[path] || 'Super Admin'
  }

  return (
    <div className={`${inter.variable} font-sans min-h-screen flex flex-col md:flex-row bg-gray-50`}>
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 bg-white border-r">
        <div className="flex flex-col flex-grow pt-5 overflow-y-auto">
          <div className="flex items-center px-4 mb-6">
            <Link href="/" className="flex items-center group">
              <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
                <div className="wheel-logo mr-2 group-hover:animate-spin">
                  <WheelLogoIcon
                    size={24}
                    color="white"
                    className="text-white w-6 h-6"
                  />
                </div>
                <span className="text-lg font-bold text-white">Loop</span>
              </div>
            </Link>
          </div>

          <nav className="flex-1 p-4 space-y-1">
            <Link
              href="/super-admin"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                pathname === "/super-admin" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
              }`}
            >
              <Home className="w-5 h-5 mr-3 text-gray-500" />
              Dashboard
            </Link>

            <div className="pt-4 pb-2">
              <div className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Super Admin Only
              </div>
            </div>

            <Link
              href="/super-admin/system-logs"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                pathname === "/super-admin/system-logs" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
              }`}
            >
              <FileText className="w-5 h-5 mr-3 text-gray-500" />
              System Logs
            </Link>
            <Link
              href="/super-admin/platform-settings"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                pathname === "/super-admin/platform-settings" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
              }`}
            >
              <Settings className="w-5 h-5 mr-3 text-gray-500" />
              Platform Settings
            </Link>
            <Link
              href="/super-admin/data-audit"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                pathname === "/super-admin/data-audit" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
              }`}
            >
              <Database className="w-5 h-5 mr-3 text-gray-500" />
              Data Audit
            </Link>

            <Link
              href="/super-admin/categories"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                pathname === "/super-admin/categories" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
              }`}
            >
              <Tags className="w-5 h-5 mr-3 text-gray-500" />
              Categories
            </Link>

            <div className="pt-4 pb-2">
              <div className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Admin Functions
              </div>
            </div>

            <Link
              href="/admin"
              className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100`}
            >
              <BarChart3 className="w-5 h-5 mr-3 text-gray-500" />
              Admin Dashboard
            </Link>

          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col md:pl-64">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200 shadow-sm">
          <div className="container-fluid">
            <div className="flex items-center justify-end py-3">
              {/* Right side - Account Menu */}
              <div className="flex items-center">
                <div className="hidden md:block">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="lg" className="relative h-10 w-10 rounded-lg text-white hover:bg-emerald-700 border border-gray-300 bg-emerald-600 p-0 overflow-hidden">
                        <Avatar className="h-10 w-10 rounded-none">
                          <AvatarImage src="" alt={userProfile?.name || "Super Admin"} />
                          <AvatarFallback className="bg-emerald-600 text-white rounded-none">
                            {userProfile?.name?.substring(0, 2).toUpperCase() || "SA"}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{userProfile?.name || "Super Admin"}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/my-orders" className="cursor-pointer">
                      <ShoppingBag className="mr-2 h-4 w-4" />
                      <span>My Orders</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/profile" className="cursor-pointer">
                      <UserCircle className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/profile/settings" className="cursor-pointer">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                  <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                    Your Access
                  </DropdownMenuLabel>

                  {/* Business Admin */}
                  <DropdownMenuItem asChild>
                    <Link href="/business-admin" className="cursor-pointer">
                      <Store className="mr-2 h-4 w-4" />
                      <span>Business Admin</span>
                    </Link>
                  </DropdownMenuItem>

                  {/* Admin */}
                  <DropdownMenuItem asChild>
                    <Link href="/admin" className="cursor-pointer">
                      <Building2 className="mr-2 h-4 w-4" />
                      <span>Admin Dashboard</span>
                    </Link>
                  </DropdownMenuItem>

                  {/* Super Admin */}
                  <DropdownMenuItem asChild>
                    <Link href="/super-admin" className="cursor-pointer">
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      <span>Super Admin</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={async () => {
                      try {
                        await signOut()
                        router.push("/")
                      } catch (error) {
                        console.error("Error signing out:", error)
                      }
                    }}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Mobile Menu Button */}
                <div className="md:hidden">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 rounded-lg"
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  >
                    <MenuIcon className="h-6 w-6" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-b">
            <nav className="p-4 space-y-1">
              <Link
                href="/super-admin"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                  pathname === "/super-admin" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Home className="w-5 h-5 mr-3 text-gray-500" />
                Dashboard
              </Link>

              <div className="pt-4 pb-2">
                <div className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Super Admin Only
                </div>
              </div>

              <Link
                href="/super-admin/system-logs"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                  pathname === "/super-admin/system-logs" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <FileText className="w-5 h-5 mr-3 text-gray-500" />
                System Logs
              </Link>
              <Link
                href="/super-admin/platform-settings"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                  pathname === "/super-admin/platform-settings" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Settings className="w-5 h-5 mr-3 text-gray-500" />
                Platform Settings
              </Link>
              <Link
                href="/super-admin/data-audit"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                  pathname === "/super-admin/data-audit" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Database className="w-5 h-5 mr-3 text-gray-500" />
                Data Audit
              </Link>

              <Link
                href="/super-admin/categories"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                  pathname === "/super-admin/categories" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Tags className="w-5 h-5 mr-3 text-gray-500" />
                Categories
              </Link>

              <div className="pt-4 pb-2">
                <div className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Admin Functions
                </div>
              </div>

              <Link
                href="/admin"
                className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <BarChart3 className="w-5 h-5 mr-3 text-gray-500" />
                Admin Dashboard
              </Link>
            </nav>
          </div>
        )}

        {/* Page Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  return (
    <SupabaseProvider>
      <AuthProvider>
        <SuperAdminLayoutContent>{children}</SuperAdminLayoutContent>
      </AuthProvider>
    </SupabaseProvider>
  )
}
