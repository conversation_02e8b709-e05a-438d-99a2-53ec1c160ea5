import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// PATCH - Update business page layout
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { page_layout } = body

    if (!page_layout || !['standard', 'aisle'].includes(page_layout)) {
      return NextResponse.json(
        { error: 'Valid page_layout is required (standard or aisle)' },
        { status: 400 }
      )
    }

    // Get business ID from session/auth
    // For now, we'll use a placeholder - this should be replaced with proper auth
    const businessId = 1 // TODO: Get from authenticated user session

    const { data: updatedBusiness, error } = await adminClient
      .from('businesses')
      .update({
        page_layout,
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId)
      .select()
      .single()

    if (error) {
      console.error('Error updating business layout:', error)
      return NextResponse.json(
        { error: 'Failed to update business layout' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      business: updatedBusiness,
      message: `Business layout updated to ${page_layout}`
    })

  } catch (error) {
    console.error('Error in layout update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
