import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');

    console.log("Business data API called with businessId:", businessId);

    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    console.log("Auth check result:", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Session found for user:", userEmail);

    // Use the admin client to bypass RLS and check the user's role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", userEmail)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      console.error("User profile not found for email:", userEmail);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    console.log("Found user profile:", userProfile.name, "with role:", userProfile.role);

    // No special cases - use the role from the database

    // For admin users, check if a specific business ID was requested
    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // If a specific business ID was requested, fetch that business
      if (businessId) {
        console.log(`Admin user requesting specific business with ID: ${businessId}`);

        // Fetch the business with its type
        const { data: business, error: businessError } = await adminClient
          .from("businesses")
          .select("*, business_types(id, name, slug)")
          .eq("id", businessId)
          .single();

        if (businessError) {
          console.error("Error fetching specific business:", businessError);
          return NextResponse.json(
            { error: "Could not find the requested business" },
            { status: 404 }
          );
        }

        console.log("Found business:", business.name, "with type:", business.business_types?.name);

        return NextResponse.json({
          isAdminUser: true,
          business: {
            ...business,
            business_type: business.business_types?.name || "Business"
          },
          isPendingApproval: false
        });
      }

      // If no specific business ID was requested, return the default admin view
      return NextResponse.json({
        isAdminUser: true,
        business: {
          id: 0,
          name: "Admin View",
          business_type_id: 1,
          business_type: "Admin",
          logo_url: null
        },
        isPendingApproval: false
      });
    }

    // For business users, check if they have an approved business
    // First check if they're a business manager or staff
    if (userProfile.role === 'business_manager' || userProfile.role === 'business_staff') {
      console.log("User is a business user, checking business status");

      // No special cases - all businesses are treated consistently

      // Get the business managed by this user
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .maybeSingle();

      if (managerError && managerError.code !== 'PGRST116') {
        console.error("Error fetching business manager data:", managerError);

        // No special cases - consistent error handling for all users

        return NextResponse.json({
          error: "Error fetching business manager data",
          errorDetails: managerError
        }, { status: 500 });
      }

      // If no manager relationship found, check for pending registration
      if (!managerData) {
        console.log("No business manager relationship found, checking for pending registration");

        // Check if there's a pending business registration
        const { data: pendingBusiness, error: pendingError } = await adminClient
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (pendingError) {
          console.error("Error fetching pending business:", pendingError);
          return NextResponse.json({
            error: "Error fetching pending business",
            errorDetails: pendingError
          }, { status: 500 });
        }

        if (pendingBusiness) {
          console.log("Found pending business registration:", pendingBusiness);
          return NextResponse.json({
            isPendingApproval: true,
            pendingBusiness
          });
        }

        console.error("Could not find business information for user:", userProfile.id);
        return NextResponse.json({
          error: "Could not find your business information",
        }, { status: 404 });
      }
    }

    // Get business details including type
    // Make sure we have a valid business_id from the manager data
    // This is a scope issue - managerData is not defined in this scope
    // We need to get the manager data again
    const { data: managerData, error: managerDataError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    if (!managerData || !managerData.business_id) {
      console.error("No valid business_id found in manager data");

      // For admin users, return a default business
      if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        return NextResponse.json({
          isAdminUser: true,
          business: {
            id: 0,
            name: "Admin View",
            business_type_id: 1,
            business_type: "Admin",
            logo_url: null
          },
          isPendingApproval: false
        });
      }

      // No special cases - consistent error handling for all users

      // For business users with no business, show pending approval
      if (userProfile.role === 'business_manager' || userProfile.role === 'business_staff') {
        return NextResponse.json({
          isPendingApproval: true,
          message: "No business found for this user"
        });
      }

      return NextResponse.json({
        error: "No valid business ID found",
      }, { status: 404 });
    }

    console.log("Fetching business details for business_id:", managerData.business_id);

    const { data: businessData, error: businessError } = await adminClient
      .from("businesses")
      .select("*, business_types(name)")
      .eq("id", managerData.business_id)
      .single();

    if (businessError) {
      console.error("Could not load business details:", businessError);
      return NextResponse.json({
        error: "Could not load business details",
        errorDetails: businessError
      }, { status: 500 });
    }

    // Check if the business itself is approved
    if (businessData.is_approved === false) {
      console.log("Business exists but is not approved");
      return NextResponse.json({
        isPendingApproval: true,
        message: "Business exists but is not approved"
      });
    }

    console.log("Business is approved, returning business data");
    return NextResponse.json({
      business: {
        ...businessData,
        business_type: businessData.business_types?.name || "Business"
      },
      isPendingApproval: false
    });

  } catch (error: any) {
    console.error("Error in GET /api/business-admin/business-data:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
