import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET() {
  try {
    console.log('Direct businesses API called');

    // Get business types
    const { data: businessTypes, error: typesError } = await adminClient
      .from('business_types')
      .select('id, name, slug')
      .order('name');

    if (typesError) {
      console.error('Error fetching business types:', typesError);
      return NextResponse.json(
        { error: `Failed to fetch business types: ${typesError.message}` },
        { status: 500 }
      );
    }

    // Get restaurant type ID
    const restaurantTypeId = businessTypes.find(t => t.slug === 'restaurant')?.id;

    if (!restaurantTypeId) {
      console.error('Restaurant type not found');
      return NextResponse.json(
        {
          error: 'Restaurant type not found',
          businessTypes
        },
        { status: 404 }
      );
    }

    // Fetch restaurants - only approved ones for public display
    const { data: restaurants, error: restaurantsError } = await adminClient
      .from('businesses')
      .select('*')
      .eq('business_type_id', restaurantTypeId)
      .eq('is_approved', true)
      .limit(10);

    if (restaurantsError) {
      console.error('Error fetching restaurants:', restaurantsError);
      return NextResponse.json(
        { error: `Failed to fetch restaurants: ${restaurantsError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      businessTypes,
      restaurants,
      environment: {
        supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        nodeEnv: process.env.NODE_ENV
      }
    });
  } catch (error) {
    console.error('Unexpected error in direct-businesses API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
