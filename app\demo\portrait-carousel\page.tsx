'use client'

import { useEffect, useState } from 'react'
import PortraitBusinessCarousel from '@/components/portrait-business-carousel'

export default function PortraitCarouselDemo() {
  // Sample business data for demonstration
  const sampleBusinesses = [
    {
      id: '1',
      slug: 'mcdon<PERSON>s-jersey',
      name: '<PERSON>\'s Jersey',
      logo_url: '/images/businesses/mcdonalds.jpg',
      rating: 4.5,
      delivery_time_minutes: 25,
      delivery_fee: 2.50,
      location: 'St Helier',
      business_type: 'Restaurant',
      business_type_slug: 'restaurant'
    },
    {
      id: '2',
      slug: 'burger-king',
      name: 'Burger King',
      logo_url: '/images/businesses/burgerking.jpg',
      rating: 4.2,
      delivery_time_minutes: 30,
      delivery_fee: 3.00,
      location: 'St Brelade',
      business_type: 'Restaurant',
      business_type_slug: 'restaurant'
    },
    {
      id: '3',
      slug: 'harbour-coffee',
      name: 'Harbour Coffee',
      logo_url: '/images/businesses/coffee.jpg',
      rating: 4.8,
      delivery_time_minutes: 15,
      delivery_fee: 0,
      location: 'St Aubin',
      business_type: 'Cafe',
      business_type_slug: 'cafe'
    },
    {
      id: '4',
      slug: 'jersey-wraps',
      name: 'Jersey Wraps',
      logo_url: '/images/businesses/wraps.jpg',
      rating: 4.3,
      delivery_time_minutes: 20,
      delivery_fee: 2.00,
      location: 'St Helier',
      business_type: 'Restaurant',
      business_type_slug: 'restaurant'
    },
    {
      id: '5',
      slug: 'coastal-convenience',
      name: 'Coastal Convenience',
      logo_url: '/images/businesses/convenience.jpg',
      rating: 4.1,
      delivery_time_minutes: 35,
      delivery_fee: 3.50,
      location: 'St Clement',
      business_type: 'Shop',
      business_type_slug: 'shop'
    },
    {
      id: '6',
      slug: 'st-brelades-bistro',
      name: 'St Brelade\'s Bistro',
      logo_url: '/images/businesses/bistro.jpg',
      rating: 4.7,
      delivery_time_minutes: 40,
      delivery_fee: 4.00,
      location: 'St Brelade',
      business_type: 'Restaurant',
      business_type_slug: 'restaurant'
    }
  ]

  return (
    <div className="container-fluid py-8">
      <h1 className="text-2xl font-bold mb-6">Portrait Business Cards Carousel</h1>

      <div className="space-y-12">
        <PortraitBusinessCarousel
          businesses={sampleBusinesses}
          title="Featured Restaurants"
        />

        <PortraitBusinessCarousel
          businesses={sampleBusinesses.filter(b => b.delivery_fee === 0)}
          title="Free Delivery"
        />

        <PortraitBusinessCarousel
          businesses={sampleBusinesses.filter(b => b.delivery_time_minutes < 30)}
          title="Quick Delivery"
        />
      </div>
    </div>
  )
}
