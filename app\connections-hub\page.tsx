"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/context/unified-auth-context"
import { addAuthHeaders } from '@/utils/auth-token'
import { useRouter } from "next/navigation"
import { ConnectionsHub as ConnectionsHubComponent } from './components/ConnectionsHub'
import {
  Building2,
  ShieldCheck,
  Loader2,
  Truck,
  ShoppingBag,
  HelpCircle,
  Info,
  Bell
} from "lucide-react"

type UserRole = "customer" | "business" | "rider" | "admin"

interface BusinessData {
  id: string
  name: string
  business_type: string
}

export default function ConnectionsHub() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<UserRole>("customer")

  // Get business connection parameters from URL
  const businessId = searchParams.get('business_id')
  const businessName = searchParams.get('business_name')

  useEffect(() => {
    const initializeData = async () => {
      if (!user) {
        setIsLoading(false)
        return
      }

      try {
        // Determine user role
        let role: UserRole = "customer"
        if (isAdmin || isSuperAdmin) {
          role = "admin"
        } else if (userProfile?.role === "business_manager") {
          role = "business"
          // Fetch business data for business users
          const response = await fetch('/api/business/profile', {
            headers: addAuthHeaders({})
          })
          if (response.ok) {
            const businessData = await response.json()
            setBusiness(businessData)
          }
        } else if (userProfile?.role === "rider") {
          role = "rider"
        }

        setUserRole(role)
      } catch (error) {
        console.error('Error initializing connections hub:', error)
        setError('Failed to load user data')
      } finally {
        setIsLoading(false)
      }
    }

    initializeData()
  }, [user, userProfile, isAdmin, isSuperAdmin])

  const getUserDisplayName = () => {
    if (userProfile?.name) return userProfile.name
    if (userProfile?.first_name && userProfile?.last_name) {
      return `${userProfile.first_name} ${userProfile.last_name}`
    }
    if (user?.email) return user.email.split('@')[0]
    return 'User'
  }

  const getRoleIcon = () => {
    switch (userRole) {
      case "business":
        return <Building2 className="h-5 w-5 text-blue-600" />
      case "rider":
        return <Truck className="h-5 w-5 text-green-600" />
      case "admin":
        return <ShieldCheck className="h-5 w-5 text-purple-600" />
      default:
        return <ShoppingBag className="h-5 w-5 text-orange-600" />
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <main className="container-fluid py-6">
          <div className="flex items-center justify-center h-full min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
          </div>
        </main>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Custom Header for Connections Hub */}
      <header className="bg-white shadow-sm sticky top-0 z-50 border-b border-gray-100">
        <div className="container-fluid py-3">
          <div className="flex items-center justify-between">
            {/* Left: Logo and Title */}
            <div className="flex items-center gap-3">
              <Link href="/" className="flex items-center gap-2">
                <div className="h-8 w-8 sm:h-10 sm:w-10">
                  <svg viewBox="0 0 100 100" className="w-full h-full">
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#059669" strokeWidth="8"/>
                    <circle cx="50" cy="50" r="25" fill="none" stroke="#059669" strokeWidth="6"/>
                    <circle cx="50" cy="50" r="8" fill="#059669"/>
                    <path d="M 20 50 L 35 50 M 65 50 L 80 50 M 50 20 L 50 35 M 50 65 L 50 80" stroke="#059669" strokeWidth="4" strokeLinecap="round"/>
                  </svg>
                </div>
                <span className="text-lg sm:text-xl font-bold text-gray-900">Loop</span>
              </Link>
              <div className="hidden sm:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900">
                Connections Hub
              </h1>
            </div>

            {/* Right: User Actions */}
            <div className="flex items-center gap-2">
              {/* Messages */}
              {user && (
                <Link href="/messages">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-700 hover:bg-gray-100 flex items-center gap-2 px-3 h-10 border border-gray-300 rounded-md"
                  >
                    <Bell className="h-4 w-4" />
                    <span className="hidden sm:inline">Messages</span>
                  </Button>
                </Link>
              )}

              {/* Help */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/connections-hub/help')}
                className="text-gray-700 hover:bg-gray-100 flex items-center gap-2 px-3 h-10 border border-gray-300 rounded-md"
              >
                <HelpCircle className="h-4 w-4" />
                <span className="hidden sm:inline">Help</span>
              </Button>

              {/* User Menu */}
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {getRoleIcon()}
                  <span className="hidden sm:inline">{getUserDisplayName()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container-fluid py-3 sm:py-6">
        {/* Main Connections Hub Component */}
        <ConnectionsHubComponent
          businessId={businessId}
          businessName={businessName}
        />
      </main>
    </div>
  )
}
