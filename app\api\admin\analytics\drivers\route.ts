import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Phase 1: Driver Count Metrics
    const [
      activeDriversResult,
      totalDriversResult,
      pendingApplicationsResult,
      onlineDriversResult,
      onShiftDriversResult
    ] = await Promise.all([
      supabase.from('driver_profiles').select('*', { count: 'exact', head: true }).eq('is_verified', true).eq('is_active', true),
      supabase.from('driver_profiles').select('*', { count: 'exact', head: true }),
      supabase.from('driver_profiles').select('*', { count: 'exact', head: true }).eq('is_verified', false).eq('is_active', true),
      supabase.from('driver_status').select('*', { count: 'exact', head: true }).eq('is_online', true),
      supabase.from('driver_status').select('*', { count: 'exact', head: true }).eq('is_on_shift', true)
    ])

    // Phase 2: Activity Metrics (using raw SQL for complex queries)
    const { data: activityMetrics, error: activityError } = await supabase.rpc('get_driver_activity_metrics')

    if (activityError) {
      console.warn('Activity metrics not available (function may not exist):', activityError.message)
    }

    // Phase 3: Performance Metrics from Orders
    const { data: performanceData, error: performanceError } = await supabase
      .from('orders')
      .select(`
        id,
        status,
        driver_id,
        delivery_fee,
        delivery_distance_km,
        parish,
        offered_at,
        assigned_at,
        picked_up_at,
        delivered_at,
        cancelled_at,
        driver_response_time_seconds,
        pickup_delay_minutes,
        estimated_delivery_time
      `)
      .not('driver_id', 'is', null)

    // Phase 4: Response Time from Driver Shift Orders (more accurate)
    const { data: responseTimeData, error: responseTimeError } = await supabase
      .from('driver_shift_orders')
      .select(`
        offered_at,
        action_taken_at,
        action_taken
      `)
      .eq('action_taken', 'accepted')
      .not('action_taken_at', 'is', null)

    if (performanceError) {
      console.error('Error fetching performance data:', performanceError)
    }

    // Calculate metrics from performance data
    const metrics = calculateDriverMetrics(performanceData || [], responseTimeData || [])

    return NextResponse.json({
      success: true,
      data: {
        // Phase 1: Driver Count Metrics
        activeDrivers: activeDriversResult.count || 0,
        totalDrivers: totalDriversResult.count || 0,
        pendingApplications: pendingApplicationsResult.count || 0,
        onlineDrivers: onlineDriversResult.count || 0,
        onShiftDrivers: onShiftDriversResult.count || 0,

        // Phase 2: Activity Metrics
        ...metrics,

        // Phase 3: Additional metrics from activity function (if available)
        ...(activityMetrics?.[0] || {})
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in driver analytics API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to calculate metrics from order data
function calculateDriverMetrics(orders: any[], responseTimeData: any[]) {
  const totalOrders = orders.length
  const deliveredOrders = orders.filter(o => o.status === 'delivered')
  const pickedUpOrders = orders.filter(o => o.picked_up_at)
  const cancelledOrders = orders.filter(o => o.cancelled_at)

  // Response time metrics (calculated from driver_shift_orders)
  const avgResponseTime = responseTimeData.length > 0
    ? responseTimeData.reduce((sum, record) => {
        const offeredTime = new Date(record.offered_at).getTime()
        const acceptedTime = new Date(record.action_taken_at).getTime()
        const responseTimeSeconds = (acceptedTime - offeredTime) / 1000
        return sum + responseTimeSeconds
      }, 0) / responseTimeData.length
    : 0

  // Pickup to delivery time
  const ordersWithDeliveryTime = deliveredOrders.filter(o => o.picked_up_at && o.delivered_at)
  const avgDeliveryTime = ordersWithDeliveryTime.length > 0
    ? ordersWithDeliveryTime.reduce((sum, o) => {
        const pickupTime = new Date(o.picked_up_at).getTime()
        const deliveryTime = new Date(o.delivered_at).getTime()
        return sum + (deliveryTime - pickupTime) / (1000 * 60) // minutes
      }, 0) / ordersWithDeliveryTime.length
    : 0

  // Distance and fees
  const totalDistance = deliveredOrders.reduce((sum, o) => sum + (o.delivery_distance_km || 0), 0)
  const totalEarnings = deliveredOrders.reduce((sum, o) => sum + (o.delivery_fee || 0), 0)
  const avgFeesPerKm = totalDistance > 0 ? totalEarnings / totalDistance : 0

  // Parish distribution
  const parishEarnings = deliveredOrders.reduce((acc, o) => {
    const parish = o.parish || 'Unknown'
    acc[parish] = (acc[parish] || 0) + (o.delivery_fee || 0)
    return acc
  }, {} as Record<string, number>)

  return {
    // Core activity metrics
    totalOrdersAssigned: totalOrders,
    ordersDelivered: deliveredOrders.length,
    ordersPickedUp: pickedUpOrders.length,
    ordersCancelled: cancelledOrders.length,

    // Performance metrics
    avgResponseTimeSeconds: Math.round(avgResponseTime),
    avgDeliveryTimeMinutes: Math.round(avgDeliveryTime),

    // Financial metrics
    totalEarnings: Number(totalEarnings.toFixed(2)),
    totalDistanceKm: Number(totalDistance.toFixed(2)),
    avgFeesPerKm: Number(avgFeesPerKm.toFixed(2)),

    // Geographic distribution
    parishEarnings
  }
}
