"use client"

import { useState, useEffect, useRef } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface OSMRestaurantMapInnerSimpleProps {
  restaurantName: string
  restaurantLocation: string
  restaurantCoordinates: [number, number] // [longitude, latitude]
  deliveryRadius: number // in kilometers
}

export default function OSMRestaurantMapInnerSimple({
  restaurantName,
  restaurantLocation,
  restaurantCoordinates,
  deliveryRadius
}: OSMRestaurantMapInnerSimpleProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const circleRef = useRef<L.Circle | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    // Ensure coordinates are valid
    if (!restaurantCoordinates || !Array.isArray(restaurantCoordinates) || restaurantCoordinates.length !== 2) {
      console.error("Invalid restaurant coordinates:", restaurantCoordinates);
      return;
    }

    // Make sure coordinates are numbers
    const validCoordinates: [number, number] = [
      typeof restaurantCoordinates[0] === 'number' ? restaurantCoordinates[0] : -2.1053,
      typeof restaurantCoordinates[1] === 'number' ? restaurantCoordinates[1] : 49.1805
    ];

    // Create map instance - note that Leaflet uses [lat, lng] order, opposite of our [lng, lat]
    const map = L.map(mapContainer.current, {
      center: [validCoordinates[1], validCoordinates[0]], // [lat, lng]
      zoom: 13,
      zoomControl: true,
    })

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    // Create custom restaurant icon
    const restaurantIcon = L.divIcon({
      html: `<div style="background-color: #f97316; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                <path d="M12 21v-9"></path>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Add restaurant marker
    const restaurantMarker = L.marker([validCoordinates[1], validCoordinates[0]], { 
      icon: restaurantIcon 
    }).addTo(map)

    // Add popup to restaurant marker
    restaurantMarker.bindPopup(`
      <div class="p-2">
        <h3 class="font-bold">${restaurantName}</h3>
        <p class="text-sm">${restaurantLocation}</p>
      </div>
    `)

    // Add delivery radius circle
    const circle = L.circle([validCoordinates[1], validCoordinates[0]], {
      radius: deliveryRadius * 1000, // Convert km to meters
      color: '#10b981',
      fillColor: '#10b981',
      fillOpacity: 0.2,
      weight: 2
    }).addTo(map)
    
    circleRef.current = circle

    // Add a label for the delivery radius
    const radiusLabelPoint = [
      validCoordinates[1] + (deliveryRadius / 111), // Approximate conversion from km to degrees latitude
      validCoordinates[0]
    ]
    
    const radiusLabel = L.marker(radiusLabelPoint as [number, number], {
      icon: L.divIcon({
        html: `<div class="bg-white px-2 py-1 rounded-md shadow-md text-sm font-medium border border-emerald-200">${deliveryRadius} km delivery radius</div>`,
        className: '',
        iconSize: [120, 30],
        iconAnchor: [60, 15]
      })
    }).addTo(map)

    mapRef.current = map
    setMapLoaded(true)

    return () => {
      map.remove()
      mapRef.current = null
      circleRef.current = null
    }
  }, [restaurantCoordinates, restaurantName, restaurantLocation, deliveryRadius])

  return (
    <div ref={mapContainer} className="h-[400px]" />
  )
}
