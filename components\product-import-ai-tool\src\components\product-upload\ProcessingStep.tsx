import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { transformToProducts } from '@/utils/csvUtils';
import { useProductUpload } from '@/context/ProductUploadContext';
import { Check } from "lucide-react";

export const ProcessingStep: React.FC = () => {
  const { 
    csvData, 
    headers, 
    columnMapping, 
    setProducts, 
    setCurrentStep,
    generateProductDescription,
    generateProductCategories,
    generateProductAttributes,
    processingStatus,
    setProcessingStatus
  } = useProductUpload();
  
  const [progress, setProgress] = useState(0);
  const [currentTask, setCurrentTask] = useState('');
  const [processedCount, setProcessedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  
  // Start processing when this component mounts
  useEffect(() => {
    const processProducts = async () => {
      setProcessingStatus('processing');
      
      // First transform the CSV data to product objects
      const baseProducts = transformToProducts(csvData, headers, columnMapping);
      setTotalCount(baseProducts.length);
      
      // Process each product
      const enhancedProducts = [];
      
      for (let i = 0; i < baseProducts.length; i++) {
        const product = { ...baseProducts[i] };
        setCurrentTask(`Processing product ${i + 1} of ${baseProducts.length}`);
        
        try {
          // Add product ID if mapping is specified
          if (columnMapping.productId?.sourceColumn && columnMapping.productId.sourceColumn !== 'none') {
            const productIdIndex = headers.indexOf(columnMapping.productId.sourceColumn);
            if (productIdIndex !== -1) {
              const productIdValue = csvData[i + 1][productIdIndex];
              if (productIdValue) {
                product.productId = productIdValue.trim();
              }
            }
          }
          
          // Generate description with AI if selected
          if (columnMapping.description.useAI) {
            setCurrentTask(`Generating description for product ${i + 1}`);
            product.description = await generateProductDescription(product.name);
          }
          
          // Generate categories with AI if selected
          if (columnMapping.categories.useAI) {
            setCurrentTask(`Generating categories for product ${i + 1}`);
            product.categories = await generateProductCategories(product.name, product.description);
          } else if (columnMapping.categories.sourceColumn) {
            // Parse categories from CSV if they're provided as a comma-separated list
            const categoryIndex = headers.indexOf(columnMapping.categories.sourceColumn);
            if (categoryIndex !== -1) {
              const categoryValue = csvData[i + 1][categoryIndex];
              if (categoryValue) {
                product.categories = categoryValue.split(',').map(c => c.trim());
              }
            }
          }
          
          // Generate attributes with AI if selected
          if (columnMapping.attributes.useAI) {
            setCurrentTask(`Generating attributes for product ${i + 1}`);
            product.attributes = await generateProductAttributes(product.name, product.description);
          } else if (columnMapping.attributes.sourceColumn) {
            // Parse attributes from CSV if they're provided in a format like "key:value,key:value"
            const attrIndex = headers.indexOf(columnMapping.attributes.sourceColumn);
            if (attrIndex !== -1) {
              const attrValue = csvData[i + 1][attrIndex];
              if (attrValue) {
                const attributes: Record<string, string> = {};
                attrValue.split(',').forEach(pair => {
                  const [key, value] = pair.split(':').map(s => s.trim());
                  if (key && value) {
                    attributes[key] = value;
                  }
                });
                product.attributes = attributes;
              }
            }
          }
          
          enhancedProducts.push(product);
          setProcessedCount(i + 1);
          setProgress(Math.round(((i + 1) / baseProducts.length) * 100));
        } catch (error) {
          console.error(`Error processing product ${product.name}:`, error);
          // Still add the product even if some AI processing failed
          enhancedProducts.push(product);
          setProcessedCount(i + 1);
          setProgress(Math.round(((i + 1) / baseProducts.length) * 100));
        }
      }
      
      setProducts(enhancedProducts);
      setProcessingStatus('completed');
      setCurrentTask('Processing complete');
      setProgress(100);
    };
    
    processProducts();
  }, []);
  
  const handleBack = () => {
    setCurrentStep(2);
  };
  
  const handleContinue = () => {
    setCurrentStep(4);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Processing Products</CardTitle>
        <CardDescription>
          We're processing your products according to your mapping settings. This might take a few moments.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>{currentTask}</span>
            <span>{processedCount} of {totalCount} products</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
        
        <div className="space-y-4">
          {/* Processing stages */}
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-green-100 p-1">
              <Check className="h-4 w-4 text-green-600" />
            </div>
            <span>CSV data imported</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {processingStatus === 'completed' ? (
              <div className="rounded-full bg-green-100 p-1">
                <Check className="h-4 w-4 text-green-600" />
              </div>
            ) : (
              <div className={`rounded-full p-1 ${progress > 0 ? 'bg-app-blue bg-opacity-20' : 'bg-gray-100'}`}>
                <div className={`h-4 w-4 rounded-full ${progress > 0 && processingStatus === 'processing' ? 'animate-pulse-slow bg-app-blue' : 'bg-gray-300'}`} />
              </div>
            )}
            <span>Generating product descriptions {columnMapping.description.useAI ? 'with AI' : 'from CSV'}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {processingStatus === 'completed' ? (
              <div className="rounded-full bg-green-100 p-1">
                <Check className="h-4 w-4 text-green-600" />
              </div>
            ) : (
              <div className={`rounded-full p-1 ${progress > 30 ? 'bg-app-blue bg-opacity-20' : 'bg-gray-100'}`}>
                <div className={`h-4 w-4 rounded-full ${progress > 30 && processingStatus === 'processing' ? 'animate-pulse-slow bg-app-blue' : 'bg-gray-300'}`} />
              </div>
            )}
            <span>Categorizing products {columnMapping.categories.useAI ? 'with AI' : 'from CSV'}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {processingStatus === 'completed' ? (
              <div className="rounded-full bg-green-100 p-1">
                <Check className="h-4 w-4 text-green-600" />
              </div>
            ) : (
              <div className={`rounded-full p-1 ${progress > 60 ? 'bg-app-blue bg-opacity-20' : 'bg-gray-100'}`}>
                <div className={`h-4 w-4 rounded-full ${progress > 60 && processingStatus === 'processing' ? 'animate-pulse-slow bg-app-blue' : 'bg-gray-300'}`} />
              </div>
            )}
            <span>Generating product attributes {columnMapping.attributes.useAI ? 'with AI' : 'from CSV'}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {processingStatus === 'completed' ? (
              <div className="rounded-full bg-green-100 p-1">
                <Check className="h-4 w-4 text-green-600" />
              </div>
            ) : (
              <div className={`rounded-full p-1 ${progress === 100 ? 'bg-app-blue bg-opacity-20' : 'bg-gray-100'}`}>
                <div className={`h-4 w-4 rounded-full ${progress === 100 && processingStatus === 'processing' ? 'animate-pulse-slow bg-app-blue' : 'bg-gray-300'}`} />
              </div>
            )}
            <span>Finalizing product data</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleBack} disabled={processingStatus === 'processing'}>Back</Button>
        <Button onClick={handleContinue} disabled={processingStatus !== 'completed'}>
          Continue to Review
        </Button>
      </CardFooter>
    </Card>
  );
};
