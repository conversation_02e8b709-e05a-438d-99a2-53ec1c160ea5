"use client"

import React from 'react'
import { OrderWeightChart } from '@/components/driver/order-weight-chart'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestWeightChartPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-900">
              🧪 Weight Chart Testing Page
            </CardTitle>
            <p className="text-gray-600">
              Testing the OrderWeightChart component with real order data
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                This page tests the weight chart component with existing orders from the database.
              </p>

              <div className="grid gap-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Order #556 (LJ-25-985074) - FIXED Real Customer Order</h3>
                  <OrderWeightChart orderId={556} />
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded text-sm">
                    <strong>Real Order:</strong> 9 items from Jersey Wings - 7 hot items (0-1kg), 1 cold milkshake, 1 none thermal drink
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Order #550 (***********) - Test Data</h3>
                  <OrderWeightChart orderId={550} />
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                    <strong>Test Data:</strong> 9 items total - 2 undeclared weight, 1 cold (0kg), 2 hot (1kg), 3 none thermal (2kg), 1 hot (4kg)
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Order #556 - Compact Chart</h3>
                  <OrderWeightChart orderId={556} compact={true} />
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Order #550 - Compact Chart</h3>
                  <OrderWeightChart orderId={550} compact={true} />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>API Testing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">You can also test the API directly:</p>
              <ul className="text-sm space-y-1">
                <li>• <code className="bg-gray-100 px-2 py-1 rounded">/api/driver/order-analysis/550</code> (Test order with full data)</li>
                <li>• <code className="bg-gray-100 px-2 py-1 rounded">/api/driver/order-analysis/389</code> (Existing order)</li>
                <li>• <code className="bg-gray-100 px-2 py-1 rounded">/api/driver/order-analysis/390</code> (Existing order)</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Driver Pages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">View the charts in actual driver pages:</p>
              <ul className="text-sm space-y-1">
                <li>• <a href="/driver-mobile/deliveries/***********" className="text-blue-600 hover:underline">Order *********** (Test Order - Full View)</a></li>
                <li>• <a href="/driver-mobile/deliveries/***********" className="text-blue-600 hover:underline">Order *********** (Existing Order)</a></li>
                <li>• <a href="/driver-mobile/orders" className="text-blue-600 hover:underline">Driver Orders List (Compact Views)</a></li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
