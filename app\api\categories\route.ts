import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  // Create a Supabase client with the service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey)
  const { searchParams } = new URL(request.url)
  const businessTypeId = searchParams.get("businessTypeId")
  const purpose = searchParams.get("purpose") // 'specialization', 'menu', or null for all
  const level = searchParams.get("level") // 1 for parent categories, 2 for child categories
  const parentId = searchParams.get("parentId") // Get children of specific parent

  try {
    let query = supabase
      .from("categories")
      .select("id, name, slug, business_type_id, category_purpose, description, display_order, level, parent_id")
      .eq("is_active", true)
      .order("display_order", { ascending: true })

    // Filter by business type if provided
    if (businessTypeId) {
      query = query.eq("business_type_id", businessTypeId)
    }

    // Filter by purpose if provided
    if (purpose) {
      query = query.eq("category_purpose", purpose)
    }

    // Filter by level if provided
    if (level) {
      query = query.eq("level", parseInt(level))
    }

    // Filter by parent ID if provided
    if (parentId) {
      query = query.eq("parent_id", parseInt(parentId))
    }

    const { data: categories, error } = await query

    if (error) {
      console.error("Error fetching categories:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({ categories })
  } catch (error) {
    console.error("Error fetching categories:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
