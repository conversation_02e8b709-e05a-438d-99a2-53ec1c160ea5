import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { createServerClient } from "@/lib/supabase-server";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// GET handler to fetch user orders
export async function GET(request: Request) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('Authorization')
    let email = null;
    let user = null;

    // First try to get user from Authorization header if present
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      if (token) {
        try {
          // Use admin client to verify the token
          const { data: userData, error } = await adminClient.auth.getUser(token)

          if (!error && userData?.user) {
            user = userData.user
            email = userData.user.email
          }
        } catch (e) {
          // Continue to try cookies
        }
      }
    }

    // If we couldn't get user from token, try cookies
    if (!user) {
      try {
        // Get the user's session using the server client
        const supabase = await createServerClient()
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (!sessionError && session?.user) {
          user = session.user
          email = session.user.email
        }
      } catch (cookieError) {
        // Continue to development check
      }
    }

    // Skip auth check in development for easier testing
    if (!user && process.env.NODE_ENV === 'development') {
      // Extract email from query params for development testing
      const url = new URL(request.url)
      const devEmail = url.searchParams.get('dev_email')

      if (devEmail) {
        email = devEmail
      }
    } else if (!user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    // Get user's auth_id from the users table
    const { data: userRecord, error: userError } = await adminClient
      .from("users")
      .select("auth_id")
      .eq("email", email)
      .single();

    if (userError || !userRecord?.auth_id) {
      console.error("Error fetching user auth_id:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const userAuthId = userRecord.auth_id;

    // Get query parameters for filtering
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const offset = parseInt(url.searchParams.get('offset') || '0')
    const status = url.searchParams.get('status')

    console.log('🔍 USER ORDERS API: Fetching orders for user:', {
      email,
      userAuthId,
      limit,
      offset,
      status
    });

    // Build the query to fetch orders
    let query = adminClient
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        business_id,
        status,
        subtotal,
        delivery_fee,
        service_fee,
        total,
        delivery_method,
        delivery_type,
        estimated_delivery_time,
        delivery_address,
        customer_name,
        customer_phone,
        parish,
        postcode,
        created_at,
        updated_at,
        session_id,
        cart_id
      `)
      .eq('user_id', userAuthId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status) {
      query = query.eq('status', status);
    }

    const { data: orders, error } = await query;

    if (error) {
      console.error('❌ USER ORDERS API: Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders' },
        { status: 500 }
      );
    }

    console.log(`✅ USER ORDERS API: Found ${orders?.length || 0} orders`);

    // Fetch cart items for all orders
    const cartIds = (orders || []).filter(order => order.cart_id).map(order => order.cart_id)
    let allCartItems: any[] = []

    if (cartIds.length > 0) {
      const { data: cartItemsData, error: cartItemsError } = await adminClient
        .from('cart_items')
        .select(`
          id,
          cart_id,
          product_id,
          name,
          price,
          quantity,
          business_id,
          image_url,
          variant_id
        `)
        .in('cart_id', cartIds)

      if (cartItemsError) {
        console.warn('⚠️ USER ORDERS API: Error fetching cart items:', cartItemsError)
      } else {
        allCartItems = cartItemsData || []
        console.log(`✅ USER ORDERS API: Found ${allCartItems.length} cart items`)
      }
    }

    // Group cart items by cart_id for easy lookup
    const cartItemsByCartId = new Map<string, any[]>()
    allCartItems.forEach(item => {
      if (!cartItemsByCartId.has(item.cart_id)) {
        cartItemsByCartId.set(item.cart_id, [])
      }
      cartItemsByCartId.get(item.cart_id)!.push(item)
    })

    // Group orders by session_id and delivery_address for display
    const sessionGroups = new Map<string, {
      session_id: string
      session_date: Date
      customer_address: string
      customer_name: string
      customer_phone: string
      parish: string
      postcode: string
      business_count: number
      session_total: number
      business_orders: any[]
    }>();

    (orders || []).forEach(order => {
      // For orders without session_id, create a unique session key using order ID
      const sessionKey = order.session_id
        ? `${order.session_id}_${order.delivery_address}`
        : `legacy_${order.id}_${order.delivery_address}`;

      if (!sessionGroups.has(sessionKey)) {
        sessionGroups.set(sessionKey, {
          session_id: order.session_id || `legacy_${order.id}`, // Use order ID for legacy orders
          session_date: new Date(order.created_at),
          customer_address: order.delivery_address,
          customer_name: order.customer_name,
          customer_phone: order.customer_phone,
          parish: order.parish,
          postcode: order.postcode,
          business_count: 0,
          session_total: 0,
          business_orders: []
        });
      }

      const sessionGroup = sessionGroups.get(sessionKey)!;

      // Get cart items for this order
      const orderCartItems = order.cart_id ? cartItemsByCartId.get(order.cart_id) || [] : []

      // Add business order to the session group
      sessionGroup.business_orders.push({
        order_id: order.id,
        order_number: order.order_number,
        business_name: order.business_name,
        business_id: order.business_id,
        status: order.status,
        subtotal: order.subtotal,
        delivery_fee: order.delivery_fee,
        service_fee: order.service_fee,
        total: order.total,
        delivery_method: order.delivery_method,
        delivery_type: order.delivery_type,
        estimated_delivery_time: order.estimated_delivery_time,
        created_at: order.created_at,
        updated_at: order.updated_at,
        cart_id: order.cart_id,
        cart_items: orderCartItems
      });

      // Update session totals
      sessionGroup.business_count = sessionGroup.business_orders.length;
      sessionGroup.session_total = sessionGroup.business_orders.reduce(
        (total, businessOrder) => total + (businessOrder.total || 0),
        0
      );
    });

    // Convert map to array and sort by session date (newest first)
    const orderSessions = Array.from(sessionGroups.values())
      .sort((a, b) => b.session_date.getTime() - a.session_date.getTime());

    return NextResponse.json({
      success: true,
      orderSessions,
      totalCount: orderSessions.length,
      hasMore: (orders?.length || 0) === limit
    });

  } catch (error: any) {
    console.error("❌ USER ORDERS API: Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
