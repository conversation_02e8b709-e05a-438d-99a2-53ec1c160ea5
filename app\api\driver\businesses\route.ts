import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified) {
      return NextResponse.json(
        { error: "Driver not verified by platform" },
        { status: 403 }
      )
    }

    if (!driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver profile is inactive" },
        { status: 403 }
      )
    }

    // Get search parameters
    const { searchParams } = new URL(request.url)
    const businessType = searchParams.get('type')
    const location = searchParams.get('location')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query for businesses - only show approved businesses to drivers
    let businessQuery = supabase
      .from('businesses')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        description,
        address,
        location,
        logo_url,
        phone,
        delivery_radius,
        minimum_order_amount,
        delivery_fee,
        preparation_time_minutes,
        is_approved,
        delivery_available,
        business_types(name)
      `)
      .eq('delivery_available', true)
      .eq('is_approved', true)
      .order('name')
      .range(offset, offset + limit - 1)

    // Apply filters
    if (businessType && businessType !== 'all') {
      businessQuery = businessQuery.eq('business_type_id', businessType)
    }

    if (location && location !== 'all') {
      businessQuery = businessQuery.eq('location', location)
    }

    const { data: businesses, error: businessesError } = await businessQuery

    if (businessesError) {
      console.error('Error fetching businesses:', businessesError)
      return NextResponse.json(
        { error: "Failed to fetch businesses" },
        { status: 500 }
      )
    }

    // Get driver's existing applications/approvals for these businesses
    const businessIds = businesses?.map(b => b.id) || []

    const { data: existingApplications, error: applicationsError } = await supabase
      .from('driver_business_requests')
      .select('business_id, status, created_at')
      .eq('driver_id', driverProfile.id)
      .in('business_id', businessIds)

    if (applicationsError) {
      console.error('Error fetching existing applications:', applicationsError)
    }

    // Create a map of existing applications
    const applicationMap = new Map()
    existingApplications?.forEach(app => {
      applicationMap.set(app.business_id, app)
    })

    // Enhance businesses with application status
    const enhancedBusinesses = businesses?.map(business => {
      const existingApp = applicationMap.get(business.id)
      return {
        ...business,
        application_status: existingApp?.status || null,
        application_date: existingApp?.created_at || null,
        can_apply: !existingApp || existingApp.status === 'rejected'
      }
    })

    return NextResponse.json({
      success: true,
      businesses: enhancedBusinesses,
      pagination: {
        limit,
        offset,
        total: enhancedBusinesses?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in driver businesses GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
