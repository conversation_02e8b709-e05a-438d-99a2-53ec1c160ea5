"use client"

import React, { ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/unified-auth-context'
import { hasPermission, hasAnyRole, Permission, UserRole } from '@/lib/auth-utils'

interface AuthGuardProps {
  children: ReactNode
  fallback?: ReactNode
  redirectTo?: string
}

interface PermissionGuardProps extends AuthGuardProps {
  permission: Permission
}

interface RoleGuardProps extends AuthGuardProps {
  roles: UserRole[]
}

/**
 * Base authentication guard - requires user to be logged in
 */
export function AuthGuard({ children, fallback, redirectTo = '/login' }: AuthGuardProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  // Handle redirect in useEffect to avoid render-time side effects
  React.useEffect(() => {
    if (!isLoading && !user && redirectTo) {
      router.push(redirectTo)
    }
  }, [isLoading, user, redirectTo, router])

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!user) {
    if (redirectTo) {
      return <LoadingSpinner />
    }
    return fallback || <UnauthorizedMessage />
  }

  return <>{children}</>
}

/**
 * Permission-based guard - requires specific permission
 */
export function PermissionGuard({
  permission,
  children,
  fallback,
  redirectTo = '/'
}: PermissionGuardProps) {
  const { userProfile, isLoading } = useAuth()
  const router = useRouter()

  // Handle redirect in useEffect to avoid render-time side effects
  React.useEffect(() => {
    if (!isLoading && userProfile && !hasPermission(userProfile.role, permission) && redirectTo) {
      router.push(redirectTo)
    }
  }, [isLoading, userProfile, permission, redirectTo, router])

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!userProfile || !hasPermission(userProfile.role, permission)) {
    if (redirectTo) {
      return <LoadingSpinner />
    }
    return fallback || <ForbiddenMessage permission={permission} />
  }

  return <>{children}</>
}

/**
 * Role-based guard - requires one of the specified roles
 */
export function RoleGuard({
  roles,
  children,
  fallback,
  redirectTo = '/'
}: RoleGuardProps) {
  const { userProfile, isLoading } = useAuth()
  const router = useRouter()
  const [shouldRedirect, setShouldRedirect] = React.useState(false)

  // Handle redirect in useEffect to avoid render-time side effects
  React.useEffect(() => {
    if (!isLoading && userProfile && !hasAnyRole(userProfile.role, roles) && redirectTo) {
      setShouldRedirect(true)
    }
  }, [isLoading, userProfile, roles, redirectTo])

  // Separate useEffect for actual redirect
  React.useEffect(() => {
    if (shouldRedirect) {
      const timer = setTimeout(() => {
        router.push(redirectTo)
      }, 100) // Small delay to avoid render conflicts
      return () => clearTimeout(timer)
    }
  }, [shouldRedirect, redirectTo, router])

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!userProfile || !hasAnyRole(userProfile.role, roles)) {
    if (redirectTo) {
      return <LoadingSpinner />
    }
    return fallback || <ForbiddenMessage roles={roles} />
  }

  return <>{children}</>
}

/**
 * Super Admin guard - shorthand for super_admin role
 */
export function SuperAdminGuard(props: AuthGuardProps) {
  return <RoleGuard {...props} roles={['super_admin']} />
}

/**
 * Admin guard - shorthand for admin and super_admin roles
 */
export function AdminGuard(props: AuthGuardProps) {
  return <RoleGuard {...props} roles={['admin', 'super_admin']} />
}

/**
 * Business Admin guard - for business management access
 */
export function BusinessAdminGuard(props: AuthGuardProps) {
  return <RoleGuard {...props} roles={['business_manager', 'business_staff', 'admin', 'super_admin']} />
}

/**
 * Business Manager guard - for business owner access
 */
export function BusinessManagerGuard(props: AuthGuardProps) {
  return <RoleGuard {...props} roles={['business_manager', 'admin', 'super_admin']} />
}

/**
 * Conditional rendering based on permissions
 */
export function ShowForPermission({ 
  permission, 
  children, 
  fallback = null 
}: { 
  permission: Permission
  children: ReactNode
  fallback?: ReactNode 
}) {
  const { userProfile } = useAuth()

  if (!userProfile || !hasPermission(userProfile.role, permission)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Conditional rendering based on roles
 */
export function ShowForRoles({ 
  roles, 
  children, 
  fallback = null 
}: { 
  roles: UserRole[]
  children: ReactNode
  fallback?: ReactNode 
}) {
  const { userProfile } = useAuth()

  if (!userProfile || !hasAnyRole(userProfile.role, roles)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Loading spinner component
 */
function LoadingSpinner() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  )
}

/**
 * Unauthorized message component
 */
function UnauthorizedMessage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h1>
        <p className="text-gray-600 mb-6">Please log in to access this page.</p>
        <a 
          href="/login" 
          className="bg-emerald-600 text-white px-6 py-2 rounded-md hover:bg-emerald-700 transition-colors"
        >
          Go to Login
        </a>
      </div>
    </div>
  )
}

/**
 * Forbidden message component
 */
function ForbiddenMessage({ 
  permission, 
  roles 
}: { 
  permission?: Permission
  roles?: UserRole[] 
}) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="text-6xl mb-4">⛔</div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
        <p className="text-gray-600 mb-2">You don't have permission to access this page.</p>
        {permission && (
          <p className="text-sm text-gray-500 mb-6">Required permission: {permission}</p>
        )}
        {roles && (
          <p className="text-sm text-gray-500 mb-6">Required roles: {roles.join(', ')}</p>
        )}
        <a 
          href="/" 
          className="bg-emerald-600 text-white px-6 py-2 rounded-md hover:bg-emerald-700 transition-colors"
        >
          Go to Home
        </a>
      </div>
    </div>
  )
}

/**
 * Hook for checking permissions in components
 */
export function usePermissions() {
  const { userProfile } = useAuth()

  return {
    hasPermission: (permission: Permission) => 
      userProfile ? hasPermission(userProfile.role, permission) : false,
    hasRole: (role: UserRole) => 
      userProfile ? userProfile.role === role : false,
    hasAnyRole: (roles: UserRole[]) => 
      userProfile ? hasAnyRole(userProfile.role, roles) : false,
    isSuperAdmin: userProfile?.role === 'super_admin',
    isAdmin: userProfile?.role === 'admin' || userProfile?.role === 'super_admin',
    isBusinessManager: userProfile?.role === 'business_manager',
    isBusinessStaff: userProfile?.role === 'business_staff',
    isCustomer: userProfile?.role === 'customer'
  }
}
