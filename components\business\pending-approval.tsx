"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, Clock, ArrowLeft, RefreshCw, Info } from "lucide-react"
import Link from "next/link"
// Using server API instead of direct Supabase access
import { useAuth } from "@/context/unified-auth-context"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function PendingApprovalMessage() {
  const { user, userProfile } = useAuth()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [showDebug, setShowDebug] = useState(false)

  // Check if the business is actually approved but we're having issues detecting it
  useEffect(() => {
    let statusCheckTimer: NodeJS.Timeout;
    let statusCheckAttempts = 0;
    const MAX_ATTEMPTS = 3;

    const checkApprovalStatus = async () => {
      if (!user) {
        console.log("No user found, delaying status check");
        if (statusCheckAttempts < MAX_ATTEMPTS) {
          statusCheckAttempts++;
          statusCheckTimer = setTimeout(checkApprovalStatus, 1000);
        }
        return;
      }

      try {
        console.log("Checking registration status via API");

        // Use the server API to check registration status (bypasses RLS issues)
        const response = await fetch('/api/business-admin/registration-status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          cache: 'no-store',
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: response.statusText }));
          console.error("Error checking registration status:", errorData.error);

          // If we get a 401 or 403, try again a few times
          if ((response.status === 401 || response.status === 403) && statusCheckAttempts < MAX_ATTEMPTS) {
            console.log(`Auth error (${response.status}), attempt ${statusCheckAttempts + 1}. Trying again in 1 second`);
            statusCheckAttempts++;
            statusCheckTimer = setTimeout(checkApprovalStatus, 1000);
          }

          return;
        }

        const data = await response.json();
        console.log("Registration status check result:", data);

        // Set debug info from API response
        setDebugInfo(data.debugInfo);

        // If status is approved, refresh the page to show the dashboard
        if (data.status === 'approved') {
          console.log("Business is approved! Refreshing page...");
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          // If not approved yet, check again after a delay
          if (statusCheckAttempts < MAX_ATTEMPTS) {
            console.log(`Business not approved yet, attempt ${statusCheckAttempts + 1}. Checking again in 3 seconds`);
            statusCheckAttempts++;
            statusCheckTimer = setTimeout(checkApprovalStatus, 3000);
          }
        }
      } catch (err) {
        console.error("Error checking registration status:", err);

        // On error, try again a few times
        if (statusCheckAttempts < MAX_ATTEMPTS) {
          console.log(`Error checking status, attempt ${statusCheckAttempts + 1}. Trying again in 1 second`);
          statusCheckAttempts++;
          statusCheckTimer = setTimeout(checkApprovalStatus, 1000);
        }
      }
    }

    checkApprovalStatus();

    return () => {
      if (statusCheckTimer) clearTimeout(statusCheckTimer);
    };
  }, [user])

  const handleRefresh = () => {
    setIsRefreshing(true)
    // Force a page refresh
    window.location.reload()
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-amber-100 p-3 rounded-full">
              <Clock className="h-10 w-10 text-amber-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">Business Registration Pending</CardTitle>
          <CardDescription>
            Your business registration is awaiting approval
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-amber-50 border-amber-200">
            <AlertTitle className="flex items-center text-amber-800">
              <CheckCircle className="h-4 w-4 mr-2" />
              Registration Received
            </AlertTitle>
            <AlertDescription className="text-amber-700 mt-2">
              Thank you for registering your business with Loop Jersey. Your application has been received and is currently under review by our team.
            </AlertDescription>
          </Alert>

          <div className="space-y-4 text-center">
            <p className="text-gray-600">
              We typically process applications within 1-2 business days. You'll receive an email notification once your business has been approved.
            </p>

            <p className="text-gray-600">
              If you have any questions or need to update your application, please contact our support team.
            </p>
          </div>

          <div className="flex flex-col space-y-3 pt-4">
            <Link href="/" className="w-full">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Home
              </Button>
            </Link>
            <Link href="/contact" className="w-full">
              <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                Contact Support
              </Button>
            </Link>
            <Link href="/business-admin/debug/approval" className="w-full">
              <Button variant="ghost" className="w-full text-sm">
                Troubleshoot Approval Issues
              </Button>
            </Link>
          </div>

          {/* Debug information */}
          <Accordion type="single" collapsible className="mt-6">
            <AccordionItem value="debug">
              <AccordionTrigger className="text-sm text-gray-500">
                <div className="flex items-center">
                  <Info className="h-4 w-4 mr-2" />
                  Technical Information
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="text-xs text-gray-600 bg-gray-50 p-3 rounded overflow-auto max-h-40">
                  <p>If your business has been approved but you're still seeing this message, try refreshing the page.</p>

                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 text-xs"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Refreshing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Refresh Page
                      </>
                    )}
                  </Button>

                  {debugInfo && (
                    <div className="mt-3">
                      <p className="font-semibold">Status:</p>
                      <pre className="text-xs overflow-auto mt-1">
                        {debugInfo.business?.data?.is_approved === true ?
                          "Your business is approved! Try refreshing the page." :
                          "Your business is pending approval."}
                      </pre>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  )
}
