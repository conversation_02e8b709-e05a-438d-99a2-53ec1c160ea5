"use client"

import { useState } from "react"
import ProductItem from "@/components/product-item"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

// Mock product data for testing
const mockProduct = {
  id: "1",
  name: "Delicious Burger",
  description: "A juicy beef burger with lettuce, tomato, and our special sauce",
  price: 12.99,
  image: "/placeholder.svg",
  isPopular: true
}

const mockBusiness = {
  id: 1,
  slug: "test-business",
  name: "Test Restaurant",
  businessType: "restaurant" as const
}

export default function TestSlimMenuPage() {
  const [useSlimLayout, setUseSlimLayout] = useState(false)

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Slim Menu Test Page</h1>
        
        <div className="mb-6">
          <Button 
            onClick={() => setUseSlimLayout(!useSlimLayout)}
            variant={useSlimLayout ? "default" : "outline"}
          >
            {useSlimLayout ? "Switch to Full Layout" : "Switch to Slim Layout"}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>
                {useSlimLayout ? "Slim Layout" : "Full Layout"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <ProductItem
                  product={mockProduct}
                  businessId={mockBusiness.id}
                  businessSlug={mockBusiness.slug}
                  businessName={mockBusiness.name}
                  businessType={mockBusiness.businessType}
                  categoryId="1"
                  layout={useSlimLayout ? "slim" : "compact"}
                />
                
                <ProductItem
                  product={{
                    ...mockProduct,
                    id: "2",
                    name: "Chicken Caesar Salad",
                    description: "Fresh romaine lettuce with grilled chicken, parmesan cheese, and caesar dressing",
                    price: 9.99,
                    isPopular: false
                  }}
                  businessId={mockBusiness.id}
                  businessSlug={mockBusiness.slug}
                  businessName={mockBusiness.name}
                  businessType={mockBusiness.businessType}
                  categoryId="1"
                  layout={useSlimLayout ? "slim" : "compact"}
                />
                
                <ProductItem
                  product={{
                    ...mockProduct,
                    id: "3",
                    name: "Margherita Pizza",
                    description: "Classic pizza with tomato sauce, mozzarella cheese, and fresh basil",
                    price: 14.99,
                    isPopular: true
                  }}
                  businessId={mockBusiness.id}
                  businessSlug={mockBusiness.slug}
                  businessName={mockBusiness.name}
                  businessType={mockBusiness.businessType}
                  categoryId="1"
                  layout={useSlimLayout ? "slim" : "compact"}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Layout Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Full Layout Features:</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Product name</li>
                    <li>• Product description</li>
                    <li>• Product image</li>
                    <li>• Price</li>
                    <li>• Add to cart controls</li>
                    <li>• Popular badge</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Slim Layout Features:</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Product name (truncated if long)</li>
                    <li>• Price</li>
                    <li>• Add to cart controls</li>
                    <li>• Popular badge</li>
                    <li>• Compact horizontal design</li>
                  </ul>
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Current Layout:</strong> {useSlimLayout ? "Slim" : "Full"}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Toggle the button above to see the difference between layouts.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">Implementation Status</h3>
          <div className="text-sm text-green-700 space-y-1">
            <p>✅ Database: Added `slim_menu` column to businesses table</p>
            <p>✅ Backend: Updated business service to include slimMenu property</p>
            <p>✅ Frontend: Added slim layout to ProductItem component</p>
            <p>✅ Business Page: Conditional layout based on business.slimMenu</p>
            <p>✅ Testing: Turkish Delight business set to use slim menu</p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Test Instructions</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>1. Visit <code>/business/turkish-delight</code> to see slim menu in action</p>
            <p>2. Visit <code>/business/robin-hood</code> to see full menu layout</p>
            <p>3. Compare the product card styles between the two businesses</p>
            <p>4. Test add-to-cart functionality on both layouts</p>
          </div>
        </div>
      </div>
    </div>
  )
}
