"use client"

import React from 'react'
import WheelLogoIcon from '@/components/wheel-logo-icon'
import WheelLogoAltIcon from '@/components/wheel-logo-alt-icon'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function LogoDemo() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Logo Comparison</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Original Logo */}
        <Card>
          <CardHeader>
            <CardTitle>Original Logo</CardTitle>
            <CardDescription>Green background with white text and white wheel details</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-8">
            {/* Original logo with background */}
            <div className="flex items-center bg-emerald-600 rounded-full px-3 py-1.5 border border-emerald-500 shadow-sm h-11">
              <div className="wheel-logo mr-2">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop</span>
            </div>

            {/* Original wheel only */}
            <div>
              <h3 className="text-sm font-medium mb-2">Wheel Only</h3>
              <WheelLogoIcon size={64} color="white" className="bg-emerald-600 rounded-full p-2" />
            </div>

            {/* Original SVG */}
            <div>
              <h3 className="text-sm font-medium mb-2">SVG Reference</h3>
              <img src="/wheel-logo.svg" alt="Original Logo" className="w-16 h-16" />
            </div>
          </CardContent>
        </Card>

        {/* New Logo */}
        <Card>
          <CardHeader>
            <CardTitle>New Logo</CardTitle>
            <CardDescription>No background, green text, and inverted wheel colors</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-8">
            {/* New logo without background */}
            <div className="flex items-center h-11">
              <WheelLogoAltIcon
                size={24}
                wheelColor="#059669"
                textColor="#059669"
                className="w-6 h-6"
              />
            </div>

            {/* New wheel only */}
            <div>
              <h3 className="text-sm font-medium mb-2">Wheel Only</h3>
              <WheelLogoAltIcon size={64} showText={false} />
            </div>

            {/* New SVG */}
            <div>
              <h3 className="text-sm font-medium mb-2">Wheel SVG Reference</h3>
              <img src="/wheel-logo-alt.svg" alt="New Logo Wheel" className="w-16 h-16" />
            </div>

            {/* Combined SVG */}
            <div>
              <h3 className="text-sm font-medium mb-2">Combined SVG Reference</h3>
              <img src="/loop-logo-alt.svg" alt="New Combined Logo" className="w-48 h-16" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Larger display */}
      <div className="mt-12">
        <h2 className="text-xl font-bold mb-4">Larger Display</h2>
        <div className="flex flex-col md:flex-row items-center justify-center gap-12 p-8 bg-gray-50 rounded-lg">
          {/* Original logo */}
          <div className="flex flex-col items-center">
            <div className="flex items-center bg-emerald-600 rounded-full px-4 py-2 border border-emerald-500 shadow-sm h-16">
              <div className="wheel-logo mr-3">
                <WheelLogoIcon
                  size={36}
                  color="white"
                  className="text-white w-9 h-9"
                />
              </div>
              <span className="text-2xl font-bold text-white">Loop</span>
            </div>
            <p className="mt-4 text-sm text-gray-500">Original Logo</p>
          </div>

          {/* New logo */}
          <div className="flex flex-col items-center">
            <WheelLogoAltIcon
              size={36}
              wheelColor="#059669"
              textColor="#059669"
              className="w-9 h-9"
              textClassName="ml-3 text-2xl font-bold"
            />
            <p className="mt-4 text-sm text-gray-500">New Logo</p>
          </div>
        </div>
      </div>

      {/* Different sizes */}
      <div className="mt-12">
        <h2 className="text-xl font-bold mb-4">Different Sizes</h2>
        <div className="flex flex-wrap items-end justify-center gap-8 p-8 bg-gray-50 rounded-lg">
          {/* Small */}
          <div className="flex flex-col items-center">
            <WheelLogoAltIcon
              size={16}
              wheelColor="#059669"
              textColor="#059669"
              className="w-4 h-4"
              textClassName="ml-1.5 text-sm font-bold"
            />
            <p className="mt-2 text-xs text-gray-500">Small</p>
          </div>

          {/* Medium */}
          <div className="flex flex-col items-center">
            <WheelLogoAltIcon
              size={24}
              wheelColor="#059669"
              textColor="#059669"
              className="w-6 h-6"
              textClassName="ml-2 text-base font-bold"
            />
            <p className="mt-2 text-xs text-gray-500">Medium</p>
          </div>

          {/* Large */}
          <div className="flex flex-col items-center">
            <WheelLogoAltIcon
              size={36}
              wheelColor="#059669"
              textColor="#059669"
              className="w-9 h-9"
              textClassName="ml-3 text-xl font-bold"
            />
            <p className="mt-2 text-xs text-gray-500">Large</p>
          </div>

          {/* Extra Large */}
          <div className="flex flex-col items-center">
            <WheelLogoAltIcon
              size={48}
              wheelColor="#059669"
              textColor="#059669"
              className="w-12 h-12"
              textClassName="ml-4 text-2xl font-bold"
            />
            <p className="mt-2 text-xs text-gray-500">Extra Large</p>
          </div>
        </div>
      </div>
    </div>
  )
}
