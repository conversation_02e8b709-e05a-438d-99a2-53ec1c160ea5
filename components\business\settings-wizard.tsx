"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, ArrowRight, Save, CheckCircle, AlertCircle, HelpCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import WizardFieldDialog from "./wizard-field-dialog"

interface SettingsWizardProps {
  business?: any
  onSave?: (data: any) => Promise<void>
  onComplete?: () => void
}

export default function SettingsWizard({ business, onSave, onComplete }: SettingsWizardProps) {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [showFieldDialog, setShowFieldDialog] = useState(false)

  // Comprehensive field list
  const fields = [
    { id: 'name', label: 'Business Name', required: true, type: 'text', description: 'The official name of your business' },
    { id: 'description', label: 'Business Description', required: true, type: 'textarea', description: 'Tell customers what makes your business special' },
    { id: 'phone', label: 'Phone Number', required: true, type: 'tel', description: 'Your business contact number' },
    { id: 'address', label: 'Business Address', required: true, type: 'textarea', description: 'Your full business address' },
    { id: 'postcode', label: 'Jersey Postcode', required: true, type: 'text', description: 'Your Jersey postcode (JE format)' },
    { id: 'delivery_radius', label: 'Delivery Radius (km)', required: true, type: 'number', description: 'How far you deliver from your location' },
    { id: 'preparation_time_minutes', label: 'Preparation Time (minutes)', required: true, type: 'number', description: 'Average time to prepare orders' },
    { id: 'minimum_order_amount', label: 'Minimum Order Amount (£)', required: true, type: 'number', description: 'Minimum order value required' },
    { id: 'delivery_fee', label: 'Delivery Fee (£)', required: true, type: 'number', description: 'Standard delivery charge' }
  ]

  const currentField = fields[currentStep]
  const progress = ((currentStep + 1) / fields.length) * 100

  // Pre-populate form data when business data is loaded
  useEffect(() => {
    if (business) {
      console.log('Pre-populating form with business data:', business)
      setFormData({
        name: business.name || '',
        description: business.description || '',
        phone: business.phone || '',
        address: business.address || '',
        postcode: business.postcode || '',
        delivery_radius: business.delivery_radius || 5,
        preparation_time_minutes: business.preparation_time_minutes || 15,
        minimum_order_amount: business.minimum_order_amount || 15,
        delivery_fee: business.delivery_fee || 2.50
      })
    }
  }, [business])

  const handleNext = () => {
    if (currentStep < fields.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSave = async () => {
    if (onComplete) {
      onComplete()
    } else {
      router.push('/business-admin/dashboard')
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Business Settings Wizard</CardTitle>
          <CardDescription>Complete your business setup step by step</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={progress} className="h-2" />
            <div className="text-sm text-gray-600">
              Step {currentStep + 1} of {fields.length}: {currentField.label}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {currentField.label}
              {currentField.required && (
                <Badge variant="outline" className="bg-orange-100 text-orange-700 border-orange-300 text-xs">
                  Required
                </Badge>
              )}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFieldDialog(true)}
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor={currentField.id}>{currentField.label}</Label>
              <p className="text-sm text-gray-600 mt-1">{currentField.description}</p>
            </div>

            {currentField.type === 'textarea' ? (
              <Textarea
                id={currentField.id}
                value={formData[currentField.id] || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, [currentField.id]: e.target.value }))}
                rows={3}
              />
            ) : currentField.type === 'select' ? (
              <Select
                value={formData[currentField.id] || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, [currentField.id]: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={`Select ${currentField.label.toLowerCase()}`} />
                </SelectTrigger>
                <SelectContent>
                  {currentField.options?.map((option: any) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                id={currentField.id}
                type={currentField.type}
                value={formData[currentField.id] || ''}
                onChange={(e) => {
                  const value = currentField.type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value
                  setFormData(prev => ({ ...prev, [currentField.id]: value }))
                }}
                min={currentField.min}
                max={currentField.max}
                step={currentField.step}
              />
            )}

            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              {currentStep === fields.length - 1 ? (
                <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                  <Save className="h-4 w-4 mr-2" />
                  Complete Setup
                </Button>
              ) : (
                <Button onClick={handleNext}>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Field Help Dialog - Temporarily disabled */}
      {showFieldDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-2">{currentField.label}</h3>
            <p className="text-gray-600 mb-4">{currentField.description}</p>
            <div className="flex justify-end">
              <Button onClick={() => setShowFieldDialog(false)}>Close</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
