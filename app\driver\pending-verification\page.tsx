"use client"

import { useEffect, useState } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Clock, CheckCircle, XCircle, ArrowLeft, Mail, Phone } from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"

export default function PendingVerificationPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [driverStatus, setDriverStatus] = useState<{
    isDriver: boolean
    isVerified: boolean
    isActive: boolean
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkDriverStatus = async () => {
      if (!user) {
        // User is not logged in at all - redirect to main login
        router.push('/login?redirectTo=/driver/pending-verification')
        return
      }

      try {
        const response = await fetch(`/api/driver/verify?userId=${user.id}`)
        const status = await response.json()

        setDriverStatus(status)

        if (!status.isDriver) {
          // User is logged in but doesn't have a driver profile - redirect to application page
          router.push('/partners/riders/apply')
          return
        }

        if (status.isVerified) {
          // User is verified - redirect to dashboard
          router.push('/driver/dashboard')
          return
        }
      } catch (error) {
        console.error('Error checking driver status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkDriverStatus()
  }, [user, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking verification status...</p>
        </div>
      </div>
    )
  }

  if (!driverStatus?.isDriver) {
    return null // Will redirect
  }

  if (driverStatus.isVerified) {
    return null // Will redirect to dashboard
  }

  // Check if application was rejected
  const isRejected = !driverStatus.isActive

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="inline-flex items-center text-emerald-600 mb-6">
          <ArrowLeft size={16} className="mr-2" />
          Back to home
        </Link>

        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
            {isRejected ? (
              <XCircle className="h-6 w-6 text-red-600" />
            ) : (
              <Clock className="h-6 w-6 text-yellow-600" />
            )}
          </div>
          <h2 className="text-center text-3xl font-extrabold text-gray-900">
            {isRejected ? 'Application Not Approved' : 'Application Under Review'}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {isRejected
              ? 'Your driver application was not approved at this time'
              : 'Your driver application is being reviewed by our team'
            }
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">
              {isRejected ? 'Application Status' : 'What happens next?'}
            </CardTitle>
            <CardDescription className="text-center">
              {isRejected
                ? 'Information about your application status'
                : 'Here\'s what to expect during the review process'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isRejected ? (
              <>
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    Your driver application was not approved. This could be due to incomplete documentation,
                    verification issues, or other requirements not being met.
                  </AlertDescription>
                </Alert>

                <div className="space-y-4">
                  <h3 className="font-medium">What you can do:</h3>
                  <ul className="text-sm space-y-2 text-gray-600">
                    <li>• Contact our support team for specific feedback</li>
                    <li>• Review and update your documentation</li>
                    <li>• Submit a new application when ready</li>
                  </ul>
                </div>

                <div className="space-y-3">
                  <Link href="/partners/riders/apply">
                    <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                      Submit New Application
                    </Button>
                  </Link>
                  <Button variant="outline" className="w-full">
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Support
                  </Button>
                </div>
              </>
            ) : (
              <>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full bg-emerald-100">
                        <span className="text-emerald-600 text-sm font-medium">1</span>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Document Review</h3>
                      <p className="text-sm text-gray-600">
                        Our team is reviewing your submitted documents and information
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full bg-gray-100">
                        <span className="text-gray-600 text-sm font-medium">2</span>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Verification</h3>
                      <p className="text-sm text-gray-600">
                        We'll verify your identity and vehicle documentation
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full bg-gray-100">
                        <span className="text-gray-600 text-sm font-medium">3</span>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Approval & Email</h3>
                      <p className="text-sm text-gray-600">
                        You'll receive an email confirmation when approved
                      </p>
                    </div>
                  </div>
                </div>

                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Review time:</strong> Applications are typically reviewed within 48 hours.
                    You'll receive an email notification once your application has been processed.
                  </AlertDescription>
                </Alert>

                <div className="space-y-3">
                  <Button variant="outline" className="w-full">
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Support
                  </Button>
                  <div className="text-center">
                    <Link href="/" className="text-sm text-emerald-600 hover:underline">
                      Continue browsing as customer
                    </Link>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
