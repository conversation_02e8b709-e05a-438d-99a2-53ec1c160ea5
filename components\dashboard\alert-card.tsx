"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle,
  Clock,
  ExternalLink,
  X
} from "lucide-react"

export type AlertType = 'critical' | 'warning' | 'info' | 'success'
export type AlertPriority = 'high' | 'medium' | 'low'

interface AlertAction {
  label: string
  onClick: () => void
  variant?: 'default' | 'destructive' | 'outline' | 'secondary'
  external?: boolean
}

interface AlertCardProps {
  type: AlertType
  priority: AlertPriority
  title: string
  message: string
  count?: number
  timestamp?: Date
  actions?: AlertAction[]
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const alertConfig = {
  critical: {
    icon: AlertTriangle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    iconColor: 'text-red-600',
    titleColor: 'text-red-900',
    badgeColor: 'bg-red-100 text-red-800'
  },
  warning: {
    icon: AlertCircle,
    bgColor: 'bg-amber-50',
    borderColor: 'border-amber-200',
    iconColor: 'text-amber-600',
    titleColor: 'text-amber-900',
    badgeColor: 'bg-amber-100 text-amber-800'
  },
  info: {
    icon: Info,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    iconColor: 'text-blue-600',
    titleColor: 'text-blue-900',
    badgeColor: 'bg-blue-100 text-blue-800'
  },
  success: {
    icon: CheckCircle,
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    iconColor: 'text-green-600',
    titleColor: 'text-green-900',
    badgeColor: 'bg-green-100 text-green-800'
  }
}

export function AlertCard({
  type,
  priority,
  title,
  message,
  count,
  timestamp,
  actions = [],
  dismissible = false,
  onDismiss,
  className = ""
}: AlertCardProps) {
  const config = alertConfig[type]
  const Icon = config.icon

  const formatTimestamp = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <Card className={`${config.bgColor} ${config.borderColor} border-l-4 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-full ${config.bgColor}`}>
              <Icon className={`h-5 w-5 ${config.iconColor}`} />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <CardTitle className={`text-base font-semibold ${config.titleColor}`}>
                  {title}
                </CardTitle>
                {count !== undefined && (
                  <Badge className={`${config.badgeColor} text-xs font-bold`}>
                    {count}
                  </Badge>
                )}
                {priority === 'high' && (
                  <Badge variant="destructive" className="text-xs">
                    URGENT
                  </Badge>
                )}
              </div>
              {timestamp && (
                <div className="flex items-center space-x-1 mt-1">
                  <Clock className="h-3 w-3 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(timestamp)}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {dismissible && onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-gray-700 mb-4">
          {message}
        </p>

        {actions.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'default'}
                size="sm"
                onClick={action.onClick}
                className="text-xs"
              >
                {action.label}
                {action.external && (
                  <ExternalLink className="ml-1 h-3 w-3" />
                )}
              </Button>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Preset alert components for common scenarios
export function CriticalAlert({ title, message, count, actions, onDismiss }: Omit<AlertCardProps, 'type' | 'priority'>) {
  return (
    <AlertCard
      type="critical"
      priority="high"
      title={title}
      message={message}
      count={count}
      actions={actions}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      timestamp={new Date()}
    />
  )
}

export function WarningAlert({ title, message, count, actions, onDismiss }: Omit<AlertCardProps, 'type' | 'priority'>) {
  return (
    <AlertCard
      type="warning"
      priority="medium"
      title={title}
      message={message}
      count={count}
      actions={actions}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      timestamp={new Date()}
    />
  )
}

export function InfoAlert({ title, message, count, actions, onDismiss }: Omit<AlertCardProps, 'type' | 'priority'>) {
  return (
    <AlertCard
      type="info"
      priority="low"
      title={title}
      message={message}
      count={count}
      actions={actions}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      timestamp={new Date()}
    />
  )
}

export function SuccessAlert({ title, message, count, actions, onDismiss }: Omit<AlertCardProps, 'type' | 'priority'>) {
  return (
    <AlertCard
      type="success"
      priority="low"
      title={title}
      message={message}
      count={count}
      actions={actions}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      timestamp={new Date()}
    />
  )
}
