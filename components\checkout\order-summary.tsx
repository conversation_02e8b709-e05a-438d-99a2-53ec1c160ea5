"use client"

import { <PERSON><PERSON>ag, Truck, Info, <PERSON><PERSON><PERSON><PERSON>gle, MapPin } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Helper function to format variant and customization details
const formatItemDetails = (item: any): string[] => {
  const details: string[] = [];

  // Add variant information
  if (item.variantName) {
    details.push(`Size: ${item.variantName}`);
  }

  // Add customization information
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
}

interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  businessId: number | string // Allow both number and string for compatibility
  businessSlug?: string
  businessName?: string
  businessType?: string
  variantId?: number
  variantName?: string
  customizations?: any[]
  options?: string[]
}

interface OrderSummaryProps {
  items: Record<string, OrderItem[]>
  businessNames: Record<string, string> | ((businessId: string) => string)
  totalPrice: number
  deliveryFee: number
  serviceFee: number
  isSubmitting: boolean
  isFormComplete?: boolean
  onSubmit: (e: React.FormEvent) => void | Promise<void>
  getDeliveryMethod?: (businessId: string) => 'delivery' | 'pickup'
  getDeliveryFee?: (businessId: string) => number
}

export default function OrderSummary({
  items,
  businessNames,
  totalPrice,
  deliveryFee,
  serviceFee,
  isSubmitting,
  isFormComplete = true,
  onSubmit,
  getDeliveryMethod,
  getDeliveryFee
}: OrderSummaryProps) {
  // Calculate the grand total correctly
  const grandTotal = totalPrice + deliveryFee + serviceFee
  const businessCount = Object.keys(items).length

  // For debugging - log the values to ensure they're correct
  console.log("OrderSummary - totalPrice:", totalPrice)
  console.log("OrderSummary - deliveryFee:", deliveryFee)
  console.log("OrderSummary - serviceFee:", serviceFee)
  console.log("OrderSummary - grandTotal:", grandTotal)

  return (
    <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center">
          <ShoppingBag className="h-5 w-5 mr-2 text-emerald-600" />
          Order Summary
        </h2>
        {businessCount > 1 && (
          <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
            {businessCount} businesses
          </Badge>
        )}
      </div>

      <div className="max-h-[calc(100vh-350px)] overflow-y-auto pr-2 -mr-2">
        {Object.keys(items).map((businessId) => {
          // Calculate business subtotal
          const businessSubtotal = items[businessId]
            .reduce((total, item) => total + item.price * item.quantity, 0);

          // Get business name using the businessNames function
          let businessName;

          if (typeof businessNames === 'function') {
            businessName = businessNames(businessId);
          } else if (businessNames && businessNames[businessId]) {
            businessName = businessNames[businessId];
          } else {
            // Format the business ID as a last resort
            businessName = businessId
              .split('-')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ');
          }

          // Debug business names
          console.log(`Business ID: ${businessId}, Name: ${businessName}`);

          return (
            <div key={businessId} className="mb-4 rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-800">
                    {businessName}
                  </h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center text-xs text-gray-500">
                          {getDeliveryMethod && getDeliveryMethod(businessId) === 'delivery' ? (
                            <>
                              <Truck className="h-3 w-3 mr-1" />
                              <span>Separate delivery</span>
                            </>
                          ) : (
                            <>
                              <MapPin className="h-3 w-3 mr-1" />
                              <span>Pickup</span>
                            </>
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs max-w-[200px]">
                          {getDeliveryMethod && getDeliveryMethod(businessId) === 'delivery'
                            ? "Items from this business will be delivered separately"
                            : "Items from this business will be available for pickup"}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              <div className="p-4">
                <div className="space-y-2 mb-3">
                  {items[businessId].map((item) => {
                    const itemDetails = formatItemDetails(item);
                    return (
                      <div key={`${item.id}-${item.variantId || 'no-variant'}-${item.customizations?.map(c => c.groupId).join('-') || 'no-custom'}`} className="py-1 text-sm">
                        <div className="flex justify-between">
                          <div className="flex items-start">
                            <span className="font-medium text-emerald-700 mr-1.5">{item.quantity}×</span>
                            <span className="text-gray-800">{item.name}</span>
                          </div>
                          <div className="font-medium text-gray-800">£{(item.price * item.quantity).toFixed(2)}</div>
                        </div>
                        {itemDetails.length > 0 && (
                          <div className="text-xs text-gray-500 ml-6 mt-1">
                            {itemDetails.join(", ")}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>

                <div className="space-y-2 pt-2 border-t">
                  <div className="flex justify-between text-sm font-medium text-gray-700">
                    <div>Business Subtotal:</div>
                    <div>£{businessSubtotal.toFixed(2)}</div>
                  </div>

                  {/* Show delivery fee if getDeliveryMethod and getDeliveryFee are provided */}
                  {getDeliveryMethod && getDeliveryFee && getDeliveryMethod(businessId) === 'delivery' && (
                    <div className="flex justify-between text-sm text-gray-600">
                      <div>Delivery Fee:</div>
                      <div>
                        {getDeliveryFee(businessId) === 0
                          ? 'Free'
                          : `£${getDeliveryFee(businessId).toFixed(2)}`}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="border-t pt-4 space-y-2">
        <div className="flex justify-between text-sm text-gray-600">
          <div>Subtotal:</div>
          <div>£{totalPrice.toFixed(2)}</div>
        </div>
        <div className="flex justify-between text-sm text-gray-600">
          <div className="flex items-center">
            <span>Delivery Fee:</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-3 w-3 ml-1 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs max-w-[200px]">
                    Total delivery fee for all businesses in your order
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div>£{deliveryFee.toFixed(2)}</div>
        </div>
        <div className="flex justify-between text-sm text-gray-600">
          <div>Service Fee:</div>
          <div>£{serviceFee.toFixed(2)}</div>
        </div>
        <Separator className="my-2" />
        <div className="flex justify-between font-bold text-lg">
          <div>Total:</div>
          <div className="text-emerald-700">£{grandTotal.toFixed(2)}</div>
        </div>
      </div>

      <Button
        onClick={(e) => onSubmit(e as React.FormEvent)}
        className={`w-full mt-6 ${
          isSubmitting || !isFormComplete
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-emerald-600 hover:bg-emerald-700'
        } text-white relative overflow-hidden transition-all duration-300 group`}
        disabled={isSubmitting || !isFormComplete}
      >
        {isSubmitting ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </div>
        ) : !isFormComplete ? (
          <div className="flex items-center justify-center">
            <AlertTriangle className="h-4 w-4 mr-2 text-white opacity-80" />
            Complete all steps to place order
          </div>
        ) : (
          <>
            <span className="relative z-10">Place Order</span>
            <span className="absolute inset-0 bg-emerald-500 transform scale-x-0 origin-left transition-transform duration-300 group-hover:scale-x-100"></span>
          </>
        )}
      </Button>

      <p className="text-xs text-center text-gray-500 mt-4">
        By placing your order, you agree to our Terms of Service and Privacy Policy
      </p>
    </div>
  )
}
