/**
 * <PERSON><PERSON><PERSON> to set Jersey Co-op to aisle layout and revert Jersey Wings to standard
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupCoopAisleLayout() {
  try {
    console.log('🧪 Setting up aisle layout for Jersey Co-op...\n');

    // First, revert Jersey Wings back to standard layout
    console.log('🔄 Reverting Jersey Wings to standard layout...');
    const { error: revertError } = await supabase
      .from('businesses')
      .update({ page_layout: 'standard' })
      .eq('slug', 'jersey-wings');

    if (revertError) {
      console.error('❌ Error reverting Jersey Wings:', revertError);
    } else {
      console.log('✅ Jersey Wings reverted to standard layout\n');
    }

    // Find Jersey Co-op
    const { data: coop, error: coopError } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-coop')
      .single();

    if (coopError) {
      console.error('❌ Error finding Jersey Co-op:', coopError);
      return;
    }

    if (!coop) {
      console.error('❌ Jersey Co-op not found');
      return;
    }

    console.log('📋 Jersey Co-op info:');
    console.log(`   Name: ${coop.name}`);
    console.log(`   Slug: ${coop.slug}`);
    console.log(`   Current layout: ${coop.page_layout || 'standard'}\n`);

    // Update Jersey Co-op to aisle layout
    console.log('🔄 Setting Jersey Co-op to aisle layout...');
    const { data: updatedCoop, error: updateError } = await supabase
      .from('businesses')
      .update({ page_layout: 'aisle' })
      .eq('id', coop.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating Jersey Co-op:', updateError);
      return;
    }

    console.log('✅ Jersey Co-op updated successfully!');
    console.log(`   New layout: ${updatedCoop.page_layout}\n`);

    // Check Jersey Co-op categories
    const { data: categories, error: categoriesError } = await supabase
      .from('business_custom_categories')
      .select('id, name, level, parent_category')
      .eq('business_id', coop.id)
      .order('level', { ascending: true })
      .order('display_order', { ascending: true });

    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
      return;
    }

    console.log('📂 Jersey Co-op categories:');
    if (categories && categories.length > 0) {
      const level0Categories = categories.filter(cat => cat.level === 0);
      const level1Categories = categories.filter(cat => cat.level === 1);
      
      console.log(`   Level 0 categories (${level0Categories.length}):`);
      level0Categories.forEach(cat => {
        console.log(`     - ${cat.name} (ID: ${cat.id})`);
      });
      
      console.log(`   Level 1 categories (${level1Categories.length}):`);
      level1Categories.forEach(cat => {
        const parent = level0Categories.find(p => p.id === cat.parent_category);
        console.log(`     - ${cat.name} (ID: ${cat.id}) -> Parent: ${parent?.name || 'None'}`);
      });
    } else {
      console.log('   No custom categories found');
    }

    console.log('\n🎉 Setup complete!');
    console.log(`\n🌐 You can now test the aisle layout at:`);
    console.log(`   Standard page (should redirect): http://localhost:3000/business/${coop.slug}`);
    console.log(`   Aisle main page: http://localhost:3000/business/${coop.slug}/categories`);
    
    if (categories && categories.length > 0) {
      const level0Categories = categories.filter(cat => cat.level === 0);
      if (level0Categories.length > 0) {
        console.log(`   Example category page: http://localhost:3000/business/${coop.slug}/categories/${level0Categories[0].name.toLowerCase().replace(/\s+/g, '-')}`);
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the setup
setupCoopAisleLayout();
