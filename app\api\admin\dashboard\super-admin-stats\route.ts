import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { headers } from "next/headers"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting super admin dashboard stats API request")

    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      console.log("No authorization header found, using direct admin access")
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check")
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        )
      }
    } else {
      console.log("Found authorization header, attempting to verify")

      // Extract the token
      const token = authorization.replace('Bearer ', '');

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          console.error("Invalid token:", error)
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        console.log("Token verified for user:", user.email)

        // Check if the user has super admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          console.error("Error fetching user profile:", profileError)
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has super_admin role
        if (userProfile.role !== "super_admin") {
          console.error("Unauthorized access attempt by:", user.email, "with role:", userProfile.role)
          return NextResponse.json(
            { error: "Super admin access required" },
            { status: 403 }
          )
        }

        console.log("Super admin access verified for user:", user.email, "with role:", userProfile.role)
      } catch (authError) {
        console.error("Error verifying token:", authError)
        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          )
        } else {
          console.log("Development mode: Continuing despite auth error")
        }
      }
    }

    // Get total users
    let totalUsers = 0;
    try {
      const { count, error: usersError } = await adminClient
        .from("users")
        .select("*", { count: "exact", head: true })

      if (usersError) {
        console.error("Error fetching users count:", usersError)
      } else {
        totalUsers = count || 0;
        console.log("Total users count:", totalUsers)
      }
    } catch (err) {
      console.error("Exception fetching users count:", err)
    }

    // Get users by role
    let userRoles = [];
    try {
      const { data, error: rolesError } = await adminClient
        .from("users")
        .select("role")

      if (rolesError) {
        console.error("Error fetching user roles:", rolesError)
      } else {
        userRoles = data || [];
        console.log("User roles data count:", userRoles.length)
      }
    } catch (err) {
      console.error("Exception fetching user roles:", err)
    }

    // Calculate users by role
    const usersByRole = {
      customer: 0,
      business_staff: 0,
      business_manager: 0,
      admin: 0,
      super_admin: 0
    }

    if (userRoles && userRoles.length > 0) {
      console.log(`Processing ${userRoles.length} user roles`);
      userRoles.forEach(user => {
        const role = user.role || "customer"
        console.log(`User role: ${role}`);

        if (usersByRole[role] !== undefined) {
          usersByRole[role]++
          console.log(`Incremented count for ${role} to ${usersByRole[role]}`);
        } else {
          console.log(`Unknown user role: ${role}`);
        }
      })
    } else {
      console.log("No user roles found in the database");
    }

    console.log("Final usersByRole counts:", usersByRole);

    // Get total businesses
    let totalBusinesses = 0;
    try {
      const { count, error: businessError } = await adminClient
        .from("businesses")
        .select("*", { count: "exact", head: true })

      if (businessError) {
        console.error("Error fetching businesses count:", businessError)
      } else {
        totalBusinesses = count || 0;
        console.log("Total businesses count:", totalBusinesses)
      }
    } catch (err) {
      console.error("Exception fetching businesses count:", err)
    }

    // First, get all available business types
    let availableTypes = [];
    try {
      const { data, error: availableTypesError } = await adminClient
        .from("business_types")
        .select("id, slug, name")

      if (availableTypesError) {
        console.error("Error fetching available business types:", availableTypesError)
      } else {
        availableTypes = data || [];
        console.log("Available business types count:", availableTypes.length)
      }
    } catch (err) {
      console.error("Exception fetching available business types:", err)
    }

    // Get businesses by type
    let businessTypes = [];
    try {
      const { data, error: typesError } = await adminClient
        .from("businesses")
        .select("business_type_id")

      if (typesError) {
        console.error("Error fetching business types:", typesError)
      } else {
        businessTypes = data || [];
        console.log("Business types data count:", businessTypes.length)
      }
    } catch (err) {
      console.error("Exception fetching business types:", err)
    }

    // Initialize businessesByType with all available types
    const businessesByType = {
      restaurant: 0,
      shop: 0,
      pharmacy: 0,
      cafe: 0,
      errand: 0
    }

    // Create a map of business_type_id to slug for easier lookup
    const typeIdToSlug = {};
    if (availableTypes && availableTypes.length > 0) {
      availableTypes.forEach(type => {
        if (type.id && type.slug) {
          typeIdToSlug[type.id] = type.slug;
        }
      });
    }

    console.log("Type ID to slug mapping:", typeIdToSlug);

    // Count businesses by type
    if (businessTypes && businessTypes.length > 0) {
      businessTypes.forEach(business => {
        // Debug each business
        console.log("Processing business:", business);

        if (business.business_type_id) {
          const typeId = business.business_type_id;
          const slug = typeIdToSlug[typeId];

          if (slug && businessesByType[slug] !== undefined) {
            businessesByType[slug]++;
            console.log(`Incremented count for ${slug} (ID: ${typeId}) to ${businessesByType[slug]}`);
          } else {
            console.log(`Unknown business type ID: ${typeId}, slug: ${slug}`);
          }
        } else {
          console.log("Business has no business_type_id:", business);
        }
      });
    }

    console.log("Final businessesByType counts:", businessesByType);

    // Get total orders
    let totalOrders = 0;
    try {
      const { count, error: ordersError } = await adminClient
        .from("orders")
        .select("*", { count: "exact", head: true })

      if (ordersError) {
        console.error("Error fetching orders count:", ordersError)
        // Don't return an error for orders, just log it and continue
        // This allows the dashboard to still work even if orders table doesn't exist yet
        console.log("Setting totalOrders to 0 due to error");
      } else {
        totalOrders = count || 0;
        console.log("Total orders count:", totalOrders);
      }
    } catch (err) {
      console.error("Exception fetching orders count:", err)
    }

    // Log the stats we're returning
    const statsToReturn = {
      totalUsers: totalUsers || 0,
      totalBusinesses: totalBusinesses || 0,
      totalOrders: totalOrders || 0,
      usersByRole,
      businessesByType
    };

    console.log("Returning super admin stats:", JSON.stringify(statsToReturn, null, 2));

    // Return all stats
    return NextResponse.json(statsToReturn)
  } catch (error: any) {
    console.error("Unexpected error in super admin stats API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
