import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * GET endpoint to verify if an order exists in the database
 * This is a lightweight endpoint that only checks existence, not full details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🔍 ORDER VERIFY API: Verifying order existence:', params.id);

    // Validate the order ID
    const orderId = parseInt(params.id);
    if (isNaN(orderId)) {
      console.error('❌ ORDER VERIFY API: Invalid order ID format:', params.id);
      return NextResponse.json(
        { exists: false, error: 'Invalid order ID format' },
        { status: 400 }
      );
    }

    // Check if the order exists in the database
    // We only select the id and order_number fields to keep the query lightweight
    const { data, error } = await supabase
      .from('orders')
      .select('id, order_number, status, created_at')
      .eq('id', orderId)
      .single();

    if (error) {
      console.error('❌ ORDER VERIFY API: Error verifying order:', error);
      return NextResponse.json(
        { exists: false, error: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      console.warn('⚠️ ORDER VERIFY API: Order not found:', orderId);
      return NextResponse.json(
        { exists: false, message: 'Order not found' },
        { status: 404 }
      );
    }

    console.log('✅ ORDER VERIFY API: Order verified:', {
      id: data.id,
      orderNumber: data.order_number,
      status: data.status,
      createdAt: data.created_at
    });

    return NextResponse.json({
      exists: true,
      orderId: data.id,
      orderNumber: data.order_number,
      status: data.status,
      createdAt: data.created_at
    });
  } catch (error: any) {
    console.error('❌ ORDER VERIFY API: Unexpected error:', error);
    return NextResponse.json(
      { exists: false, error: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
