"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/unified-auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { refreshSession, getCurrentUser } from "@/utils/auth-utils"
import { synchronizeAuthState, forceSynchronizeAuthState, dispatchAuthSyncEvent } from "@/utils/auth-sync"
import { Loader2 } from "lucide-react"

export function AuthDebug() {
  const { user, session, isLoading, userProfile, signOut } = useAuth()
  const [debugInfo, setDebugInfo] = useState<string | null>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const [loadingDuration, setLoadingDuration] = useState(0)

  // Track how long the loading state has been active
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isLoading) {
      const startTime = Date.now();
      interval = setInterval(() => {
        setLoadingDuration(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    } else {
      setLoadingDuration(0);
      if (interval) clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLoading]);

  // Auto-check auth on component mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      setIsChecking(true);

      // Use our new utility to synchronize the authentication state
      await synchronizeAuthState(true); // Force sync

      // Check if we have a session active flag in localStorage
      const isSessionActive = localStorage.getItem('loop_jersey_session_active') === 'true';

      // Check for auth token in localStorage or cookies
      const authToken = localStorage.getItem('loop_jersey_auth_token') ||
                        document.cookie.split(';').find(c => c.trim().startsWith('loop_jersey_auth_token='))?.split('=')[1];

      const hasAuthToken = !!authToken;

      // Get current session and user
      let currentSession = await refreshSession();
      let currentUser = await getCurrentUser();

      // If we have a token but no session/user, suggest forcing a refresh
      if (hasAuthToken && (!currentSession || !currentUser)) {
        setDebugInfo("Auth token found but no session/user. Try clicking Force Refresh.");
      }

      // Dispatch an event to notify other components that auth has been checked
      dispatchAuthSyncEvent();

      // Get localStorage data
      let localStorageData = {};
      if (typeof window !== 'undefined') {
        try {
          localStorageData = {
            loop_jersey_session_active: localStorage.getItem('loop_jersey_session_active'),
            loop_jersey_user: localStorage.getItem('loop_jersey_user'),
            loop_jersey_user_profile: localStorage.getItem('loop_jersey_user_profile'),
            loop_jersey_auth_token: localStorage.getItem('loop_jersey_auth_token') ? 'Present' : 'Not found',
            auth_token_cookie: document.cookie.includes('loop_jersey_auth_token') ? 'Present' : 'Not found'
          };
        } catch (e) {
          localStorageData = { error: 'Error accessing localStorage' };
        }
      }

      setDebugInfo(JSON.stringify({
        contextUser: user ? {
          id: user.id,
          email: user.email,
          role: user.role
        } : null,
        contextSession: session ? {
          access_token: session.access_token ? `${session.access_token.substring(0, 10)}...` : null,
          expires_at: session.expires_at,
          refresh_token: session.refresh_token ? `${session.refresh_token.substring(0, 10)}...` : null,
        } : null,
        contextUserProfile: userProfile ? {
          id: userProfile.id,
          email: userProfile.email,
          role: userProfile.role
        } : null,
        refreshedSession: currentSession ? {
          access_token: currentSession.access_token ? `${currentSession.access_token.substring(0, 10)}...` : null,
          expires_at: currentSession.expires_at,
          refresh_token: currentSession.refresh_token ? `${currentSession.refresh_token.substring(0, 10)}...` : null,
        } : null,
        currentUser: currentUser ? {
          id: currentUser.id,
          email: currentUser.email,
          role: currentUser.role
        } : null,
        localStorage: localStorageData,
        isLoading,
        loadingDuration: loadingDuration + " seconds"
      }, null, 2))
    } catch (error) {
      setDebugInfo(`Error checking auth: ${error}`)
    } finally {
      setIsChecking(false);
    }
  }

  const forceRefreshAuth = async () => {
    try {
      setIsChecking(true);
      setDebugInfo("Forcing authentication synchronization...");

      // Use our new utility to force synchronize the authentication state
      await forceSynchronizeAuthState(true); // true = force reload

      // The page will reload, so we don't need to do anything else
    } catch (error) {
      setDebugInfo(`Error refreshing auth: ${error}`);
      setIsChecking(false);
    }
  }

  // Force clear the loading state without reloading the page
  const forceClearLoadingState = () => {
    try {
      setIsChecking(true);
      setDebugInfo("Forcing clear of loading state...");

      // Dispatch a custom event to notify the auth context to clear loading state
      window.dispatchEvent(new CustomEvent('auth-loading-clear'));

      // Manually update our component state
      setLoadingDuration(0);

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } catch (error) {
      setDebugInfo(`Error clearing loading state: ${error}`);
      setIsChecking(false);
    }
  }

  const clearStorageAndReload = async () => {
    try {
      setIsChecking(true);
      setDebugInfo("Signing out and clearing storage...");

      // Use the unified auth context's signOut method
      await signOut();

      setDebugInfo("Signed out successfully. Reloading page...");

      // Reload the page
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      setDebugInfo(`Error signing out: ${error}`);
      setIsChecking(false);
    }
  }

  return (
    <div className="bg-gray-100 border border-gray-300 rounded-md p-3 my-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium text-gray-700">Authentication Debug</h3>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={checkAuth}
            disabled={isChecking}
            className="text-xs"
          >
            {isChecking ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
            Check Auth
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={forceRefreshAuth}
            disabled={isChecking}
            className="text-xs"
          >
            {isChecking ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
            Force Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs"
          >
            {isExpanded ? "Hide" : "Show"} Details
          </Button>
          {isLoading && loadingDuration > 3 && (
            <Button
              variant="outline"
              size="sm"
              onClick={forceClearLoadingState}
              disabled={isChecking}
              className="text-xs bg-amber-100 text-amber-800 border-amber-300 hover:bg-amber-200"
            >
              {isChecking ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
              Force Clear Loading
            </Button>
          )}
          <Button
            variant="destructive"
            size="sm"
            onClick={clearStorageAndReload}
            disabled={isChecking}
            className="text-xs"
          >
            Clear Storage
          </Button>
        </div>
      </div>

      <div className="mt-2">
        <div className="text-xs">
          <span className="font-medium">Status:</span>
          {isLoading ? (
            <span className="text-amber-600">
              Loading... ({loadingDuration}s)
              {loadingDuration > 10 ? " - Loading stuck, try Force Refresh" : ""}
            </span>
          ) : user ? (
            <span className="text-green-600">Authenticated</span>
          ) : (
            <span className="text-red-600">Not authenticated</span>
          )}
        </div>
        {user && (
          <div className="text-xs mt-1">
            <span className="font-medium">User:</span> {user.email}
          </div>
        )}
        {userProfile && (
          <div className="text-xs mt-1">
            <span className="font-medium">Role:</span> {userProfile.role}
          </div>
        )}
        {session && (
          <div className="text-xs mt-1">
            <span className="font-medium">Session:</span> {session.expires_at ?
              `Expires ${new Date(session.expires_at * 1000).toLocaleTimeString()}` :
              'Present'}
          </div>
        )}
        {!user && !isLoading && (
          <div className="text-xs mt-1 text-red-600">
            No user detected. Please try logging in again.
          </div>
        )}
        <div className="text-xs mt-1">
          <span className="font-medium">Auth Method:</span> Unified Auth Context
        </div>
      </div>

      {isExpanded && debugInfo && (
        <div className="mt-3 bg-gray-800 text-gray-200 p-3 rounded-md overflow-auto max-h-60">
          <pre className="text-xs whitespace-pre-wrap">{debugInfo}</pre>
        </div>
      )}
    </div>
  )
}
