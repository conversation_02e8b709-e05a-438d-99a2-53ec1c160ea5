"use client"

import { useState } from "react"
import { Calendar, Download, RefreshCcw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/date-range-picker"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { format } from "date-fns"

interface DashboardHeaderProps {
  realtimeEnabled: boolean
  onRealtimeToggle: (enabled: boolean) => void
  dateRange: { from: Date; to: Date }
  onDateRangeChange: (range: { from: Date; to: Date }) => void
  onExport: () => void
  activeTab: string
  onTabChange: (tab: string) => void
  onRefresh: () => void
}

export function DashboardHeader({
  realtimeEnabled,
  onRealtimeToggle,
  dateRange,
  onDateRangeChange,
  onExport,
  activeTab,
  onTabChange,
  onRefresh
}: DashboardHeaderProps) {
  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your business performance and track key metrics
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row items-end sm:items-center gap-3">
          <div className="flex items-center gap-2 bg-muted/50 p-1.5 rounded-md">
            <div className={`flex items-center gap-1.5 ${realtimeEnabled ? 'text-foreground' : 'text-muted-foreground'}`}>
              <div className={`h-2 w-2 rounded-full ${realtimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
              <span className="text-xs whitespace-nowrap">Real-time updates</span>
            </div>
            <Switch 
              checked={realtimeEnabled} 
              onCheckedChange={onRealtimeToggle}
              aria-label="Toggle real-time updates"
            />
          </div>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon" 
                  className="h-9 w-9"
                  onClick={onRefresh}
                >
                  <RefreshCcw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex items-center gap-2">
            <div className="relative">
              <DatePickerWithRange
                className="w-auto min-w-[240px]"
                selected={{ from: dateRange.from, to: dateRange.to }}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    onDateRangeChange({ from: range.from, to: range.to })
                  }
                }}
              />
              <Badge 
                variant="outline" 
                className="absolute -top-2 -left-2 text-[10px] px-1 py-0 h-4 bg-background"
              >
                Date Range
              </Badge>
            </div>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-9"
                    onClick={onExport}
                  >
                    <Download className="mr-2 h-3.5 w-3.5" />
                    Export Dashboard
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Export current dashboard view as CSV</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="w-full sm:w-auto bg-muted/50">
          <TabsTrigger 
            value="overview" 
            className={`flex-1 sm:flex-initial ${activeTab === 'overview' ? 'bg-primary text-primary-foreground' : ''}`}
          >
            Overview
          </TabsTrigger>
          <TabsTrigger 
            value="analytics" 
            className={`flex-1 sm:flex-initial ${activeTab === 'analytics' ? 'bg-primary text-primary-foreground' : ''}`}
          >
            Analytics
          </TabsTrigger>
          <TabsTrigger 
            value="reports" 
            className={`flex-1 sm:flex-initial ${activeTab === 'reports' ? 'bg-primary text-primary-foreground' : ''}`}
          >
            Reports
            <Badge className="ml-2 bg-muted-foreground/20 text-foreground text-[10px]">
              Downloads
            </Badge>
          </TabsTrigger>
        </TabsList>
      </Tabs>
      
      {activeTab === "overview" && (
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold flex items-center">
            Today's Performance{" "}
            <span className="text-xs font-normal text-muted-foreground ml-2">(Last 24 hours)</span>
          </h2>
          <Badge variant="outline" className="text-xs">
            {format(new Date(), "EEEE, MMMM d, yyyy")}
          </Badge>
        </div>
      )}
    </div>
  )
}
