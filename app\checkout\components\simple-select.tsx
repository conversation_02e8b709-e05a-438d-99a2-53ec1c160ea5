"use client"

import React, { useState, useRef, useEffect } from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimpleSelectProps {
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  children: React.ReactNode
  className?: string
  id?: string
}

interface SimpleSelectItemProps {
  value: string
  children: React.ReactNode
  className?: string
}

export function SimpleSelect({
  value,
  onValueChange,
  placeholder = "Select an option",
  children,
  className,
  id
}: SimpleSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const selectRef = useRef<HTMLDivElement>(null)

  // Find the selected item's label
  const selectedItemLabel = React.Children.toArray(children).find(
    (child) => React.isValidElement(child) && child.props.value === value
  ) as React.ReactElement | undefined

  // For debugging
  console.log("SimpleSelect value:", value);
  console.log("SimpleSelect children:", React.Children.count(children));
  console.log("SimpleSelect selectedItemLabel:", selectedItemLabel);

  const displayValue = selectedItemLabel ? selectedItemLabel.props.children : (value || placeholder)

  // Close the dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div
      ref={selectRef}
      className={cn("relative", className)}
    >
      <button
        type="button"
        id={id}
        className={cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          "placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
        onClick={() => setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span className={cn(!value && "text-muted-foreground")}>
          {displayValue}
        </span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-popover text-popover-foreground shadow-md">
          <div role="listbox" className="p-1">
            {React.Children.map(children, (child) => {
              if (!React.isValidElement(child)) return null

              return React.cloneElement(child as React.ReactElement<SimpleSelectItemProps>, {
                onSelect: () => {
                  onValueChange(child.props.value)
                  setIsOpen(false)
                }
              })
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export function SimpleSelectItem({
  value,
  children,
  className,
  onSelect,
  ...props
}: SimpleSelectItemProps & { onSelect?: () => void }) {
  return (
    <div
      role="option"
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none",
        "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        className
      )}
      onClick={onSelect}
      data-value={value}
      aria-selected={false}
      {...props}
    >
      {children}
    </div>
  )
}
