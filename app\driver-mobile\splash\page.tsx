"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function SplashScreen() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to dashboard after 2 seconds
    const timer = setTimeout(() => {
      router.push("/driver-mobile/dashboard")
    }, 2000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-emerald-600 text-white">
      <div className="animate-pulse">
        <div className="flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="64"
            height="64"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white"
          >
            {/* Outer wheel circle */}
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />

            {/* Inner hub circle */}
            <circle cx="12" cy="12" r="2.5" stroke="currentColor" strokeWidth="1.5" fill="none" />

            {/* Wheel spokes */}
            <path d="M12 9.5V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M12 20V14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M9.5 12H4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M20 12H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />

            {/* Diagonal spokes */}
            <path d="M10.1 10.1L6.5 6.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M17.5 17.5L13.9 13.9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M10.1 13.9L6.5 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
            <path d="M17.5 6.5L13.9 10.1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
          </svg>
        </div>
        <h1 className="text-3xl font-bold text-center">Loop</h1>
        <p className="text-center mt-2">Driver App</p>
      </div>
      <div className="mt-12">
        <div className="w-12 h-1 bg-white/30 rounded-full overflow-hidden">
          <div className="h-full bg-white w-1/2 rounded-full animate-[loading_1.5s_ease-in-out_infinite]"></div>
        </div>
      </div>

      <style jsx>{`
        @keyframes loading {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
      `}</style>
    </div>
  )
}
