import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    // Create admin client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('search')

    if (!searchTerm) {
      return NextResponse.json({ error: 'Search term is required' }, { status: 400 })
    }

    // Build query to search across multiple fields
    let query = supabase
      .from('orders')
      .select(`
        id,
        created_at,
        updated_at,
        customer_name,
        customer_email,
        customer_phone,
        delivery_address,
        customer_coordinates,
        delivery_instructions,
        payment_method,
        payment_status,
        total,
        order_number,
        business_id,
        business_name,
        business_type,
        subtotal,
        delivery_fee,
        service_fee,
        status,
        preparation_time,
        estimated_delivery_time,
        business_slug,
        delivery_method,
        scheduled_time,
        delivery_type,
        cart_id,
        session_id,
        parish,
        postcode,
        user_id,
        ready_time,
        driver_id,
        delivery_distance_km,
        driver_pickup_confirmed,
        business_pickup_confirmed,
        delivery_fulfillment,
        offered_at,
        assigned_at,
        picked_up_at,
        out_for_delivery_at,
        delivered_at,
        cancelled_at,
        driver_response_time_seconds,
        expected_pickup_time,
        pickup_delay_minutes
      `)

    // Search across multiple fields using OR conditions
    const searchPattern = `%${searchTerm}%`

    // Use a comprehensive OR query to search across all relevant fields
    const { data: orders, error } = await query
      .or(`order_number.ilike.${searchPattern},customer_name.ilike.${searchPattern},customer_email.ilike.${searchPattern},delivery_address.ilike.${searchPattern},parish.ilike.${searchPattern},postcode.ilike.${searchPattern},business_name.ilike.${searchPattern}`)
      .order('created_at', { ascending: false })
      .limit(20)

    if (error) {
      console.error('Error searching orders:', error)
      return NextResponse.json({ error: 'Search failed' }, { status: 500 })
    }

    if (!orders || orders.length === 0) {
      return NextResponse.json({ error: 'No orders found' }, { status: 404 })
    }
    // Return the first match (most recent) or all matches
    return NextResponse.json({
      orders: orders,
      count: orders.length,
      primary: orders[0] // Most recent match
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
