import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useProductUpload } from '@/context/ProductUploadContext';
import { Badge } from '@/components/ui/badge';
import { Edit, Upload, Check, X } from 'lucide-react';

export const ReviewStep: React.FC = () => {
  const { products, updateProduct, requestImageGeneration, setCurrentStep } = useProductUpload();
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [imageGenerationPrompt, setImageGenerationPrompt] = useState('');
  const [editedProduct, setEditedProduct] = useState(products[currentProductIndex]);
  const [activeTab, setActiveTab] = useState('description');
  
  // Get current product
  const currentProduct = products[currentProductIndex];

  // Calculate progress
  const progress = {
    current: currentProductIndex + 1,
    total: products.length,
    percentage: Math.round(((currentProductIndex + 1) / products.length) * 100)
  };

  const handleSaveChanges = () => {
    updateProduct(currentProduct.id, editedProduct);
    
    // If there are more products to review, go to the next one
    if (currentProductIndex < products.length - 1) {
      setCurrentProductIndex(currentProductIndex + 1);
      setEditedProduct(products[currentProductIndex + 1]);
    }
  };

  const handlePrevious = () => {
    if (currentProductIndex > 0) {
      setCurrentProductIndex(currentProductIndex - 1);
      setEditedProduct(products[currentProductIndex - 1]);
    }
  };

  const handleNext = () => {
    // Save changes for the current product
    updateProduct(currentProduct.id, editedProduct);
    
    if (currentProductIndex < products.length - 1) {
      setCurrentProductIndex(currentProductIndex + 1);
      setEditedProduct(products[currentProductIndex + 1]);
    } else {
      // If this is the last product, go to the next step
      setCurrentStep(5);
    }
  };

  const handleImageGeneration = async () => {
    await requestImageGeneration(
      currentProduct.id, 
      imageGenerationPrompt || `High quality product image of ${currentProduct.name}`
    );
  };

  const handleAttributeChange = (key: string, value: string) => {
    setEditedProduct(prev => ({
      ...prev,
      attributes: {
        ...prev.attributes,
        [key]: value
      }
    }));
  };

  const renderImageStatus = () => {
    switch (currentProduct.imageGenerationStatus) {
      case 'generating':
        return (
          <div className="flex items-center space-x-2 text-yellow-600 bg-yellow-50 p-2 rounded">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
            <span className="text-sm">Generating image...</span>
          </div>
        );
      case 'completed':
        return (
          <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-2 rounded">
            <Check className="h-4 w-4" />
            <span className="text-sm">Image generated successfully</span>
          </div>
        );
      case 'failed':
        return (
          <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-2 rounded">
            <X className="h-4 w-4" />
            <span className="text-sm">Failed to generate image</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-2xl">Review & Edit Products</CardTitle>
            <CardDescription>
              Review and edit your products before finalizing. AI-generated content can be modified as needed.
            </CardDescription>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">
              Product {progress.current} of {progress.total}
            </div>
            <div className="w-24 h-1.5 bg-gray-200 rounded-full mt-1 overflow-hidden">
              <div 
                className="h-full bg-app-blue rounded-full" 
                style={{ width: `${progress.percentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Product ID field (new) */}
          {editedProduct.productId && (
            <div className="flex gap-4">
              <div className="flex-1">
                <label htmlFor="product-id" className="block text-sm font-medium text-gray-700 mb-1">
                  Product ID
                </label>
                <Input
                  id="product-id"
                  value={editedProduct.productId || ''}
                  onChange={(e) => setEditedProduct(prev => ({ ...prev, productId: e.target.value }))}
                  className="w-full"
                />
              </div>
            
              {/* Product Name field */}
              <div className="flex-1">
                <label htmlFor="product-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name
                </label>
                <Input
                  id="product-name"
                  value={editedProduct.name}
                  onChange={(e) => setEditedProduct(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full"
                />
              </div>
            </div>
          )}

          {/* Product Name field (full width if no product ID) */}
          {!editedProduct.productId && (
            <div>
              <label htmlFor="product-name" className="block text-sm font-medium text-gray-700 mb-1">
                Product Name
              </label>
              <Input
                id="product-name"
                value={editedProduct.name}
                onChange={(e) => setEditedProduct(prev => ({ ...prev, name: e.target.value }))}
                className="w-full"
              />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="description">Description</TabsTrigger>
                  <TabsTrigger value="categories">Categories</TabsTrigger>
                  <TabsTrigger value="attributes">Attributes</TabsTrigger>
                </TabsList>
                
                <TabsContent value="description" className="mt-4 space-y-2">
                  <label htmlFor="product-description" className="block text-sm font-medium text-gray-700">
                    Product Description
                  </label>
                  <Textarea
                    id="product-description"
                    value={editedProduct.description}
                    onChange={(e) => setEditedProduct(prev => ({ ...prev, description: e.target.value }))}
                    rows={8}
                    className="w-full"
                  />
                </TabsContent>
                
                <TabsContent value="categories" className="mt-4 space-y-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Product Categories
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {editedProduct.categories.map((category, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1 px-3 py-1">
                        {category}
                        <button 
                          type="button" 
                          onClick={() => setEditedProduct(prev => ({
                            ...prev,
                            categories: prev.categories.filter((_, i) => i !== index)
                          }))}
                          className="ml-1 text-gray-500 hover:text-gray-700"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    <Input
                      placeholder="Add new category"
                      value={editedProduct.newCategory || ''}
                      onChange={(e) => setEditedProduct(prev => ({ ...prev, newCategory: e.target.value }))}
                      className="flex-1"
                    />
                    <Button 
                      type="button" 
                      size="sm"
                      onClick={() => {
                        if (editedProduct.newCategory?.trim()) {
                          setEditedProduct(prev => ({
                            ...prev,
                            categories: [...prev.categories, prev.newCategory!.trim()],
                            newCategory: ''
                          }));
                        }
                      }}
                    >
                      Add
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="attributes" className="mt-4 space-y-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Product Attributes
                  </label>
                  
                  {Object.entries(editedProduct.attributes).map(([key, value], index) => (
                    <div key={index} className="grid grid-cols-12 gap-2">
                      <Input
                        className="col-span-4"
                        value={key}
                        onChange={(e) => {
                          const newAttributes = { ...editedProduct.attributes };
                          delete newAttributes[key];
                          newAttributes[e.target.value] = value;
                          setEditedProduct(prev => ({
                            ...prev,
                            attributes: newAttributes
                          }));
                        }}
                      />
                      <Input
                        className="col-span-7"
                        value={value}
                        onChange={(e) => handleAttributeChange(key, e.target.value)}
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="col-span-1"
                        onClick={() => {
                          const newAttributes = { ...editedProduct.attributes };
                          delete newAttributes[key];
                          setEditedProduct(prev => ({
                            ...prev,
                            attributes: newAttributes
                          }));
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <div className="grid grid-cols-12 gap-2 mt-4">
                    <Input
                      className="col-span-4"
                      placeholder="New attribute"
                      value={editedProduct.newAttributeKey || ''}
                      onChange={(e) => setEditedProduct(prev => ({
                        ...prev,
                        newAttributeKey: e.target.value
                      }))}
                    />
                    <Input
                      className="col-span-7"
                      placeholder="Value"
                      value={editedProduct.newAttributeValue || ''}
                      onChange={(e) => setEditedProduct(prev => ({
                        ...prev,
                        newAttributeValue: e.target.value
                      }))}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="col-span-1"
                      onClick={() => {
                        if (editedProduct.newAttributeKey?.trim()) {
                          setEditedProduct(prev => ({
                            ...prev,
                            attributes: {
                              ...prev.attributes,
                              [prev.newAttributeKey!.trim()]: prev.newAttributeValue || ''
                            },
                            newAttributeKey: '',
                            newAttributeValue: ''
                          }));
                        }
                      }}
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
              
              <div className="space-y-2">
                <label htmlFor="product-price" className="block text-sm font-medium text-gray-700">
                  Price
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">$</span>
                  </div>
                  <Input
                    id="product-price"
                    type="text"
                    value={editedProduct.price}
                    onChange={(e) => setEditedProduct(prev => ({ ...prev, price: e.target.value }))}
                    className="pl-7"
                  />
                </div>
              </div>

              <div className="pt-2">
                <Button 
                  onClick={handleSaveChanges} 
                  variant="outline" 
                  className="w-full flex items-center justify-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Images
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {editedProduct.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img 
                        src={image} 
                        alt={`Product ${index + 1}`} 
                        className="w-full h-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => setEditedProduct(prev => ({
                          ...prev,
                          images: prev.images.filter((_, i) => i !== index)
                        }))}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                  
                  {editedProduct.images.length === 0 && (
                    <div className="col-span-2 h-32 border-2 border-dashed rounded-md flex items-center justify-center text-gray-400">
                      No images yet
                    </div>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="image-prompt" className="block text-sm font-medium text-gray-700">
                  Generate Image with AI
                </label>
                <div className="flex gap-2">
                  <Input
                    id="image-prompt"
                    placeholder="e.g. Modern office chair, white background"
                    value={imageGenerationPrompt}
                    onChange={(e) => setImageGenerationPrompt(e.target.value)}
                    disabled={currentProduct.imageGenerationStatus === 'generating'}
                  />
                  <Button
                    onClick={handleImageGeneration}
                    disabled={currentProduct.imageGenerationStatus === 'generating'}
                    className="whitespace-nowrap"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Generate
                  </Button>
                </div>
                <div className="mt-2">
                  {renderImageStatus()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handlePrevious} 
          disabled={currentProductIndex === 0}
        >
          Previous Product
        </Button>
        <Button 
          onClick={handleNext}
        >
          {currentProductIndex < products.length - 1 ? 'Next Product' : 'Finish Review'}
        </Button>
      </CardFooter>
    </Card>
  );
};
