"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, Clock, ArrowLeft } from "lucide-react"
import Link from "next/link"
import PendingApprovalMessage from "@/components/business/pending-approval"

export default function RegistrationSuccessPage() {
  const router = useRouter()
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading for at least 1 second
    const loadingTimer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    // Get registration data from localStorage
    const data = localStorage.getItem('business_registration_success')
    if (data) {
      try {
        setRegistrationData(JSON.parse(data))
      } catch (err) {
        console.error("Error parsing registration data:", err)
      }
    } else {
      // If no data, redirect to dashboard after a delay
      setTimeout(() => {
        router.push("/business-admin/dashboard")
      }, 1000)
    }

    return () => clearTimeout(loadingTimer)

    // Clear the registration form data
    localStorage.removeItem('business_registration_form')

    // Clear the success data after 5 minutes
    setTimeout(() => {
      localStorage.removeItem('business_registration_success')
    }, 5 * 60 * 1000)
  }, [router])

  if (isLoading) {
    return (
      <div className="container max-w-4xl mx-auto py-8 px-4">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-emerald-600"></div>
          <p className="mt-6 text-lg text-gray-600">Loading your registration details...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Registration Successful</h1>
        <p className="text-gray-600 mt-2">Your business registration has been submitted</p>
      </div>

      {!registrationData && <PendingApprovalMessage />}

      {registrationData && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Registration Details</CardTitle>
            <CardDescription>Summary of your business registration</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Business Information</h3>
                <div className="space-y-2">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Business Name</p>
                    <p className="font-medium">{registrationData.businessName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Business Type</p>
                    <p>{registrationData.businessType || 'Business'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Address</p>
                    <p>{registrationData.businessAddress}</p>
                    <p>{registrationData.businessPostcode}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p>{registrationData.businessPhone}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Delivery Fee</p>
                    <p>£{registrationData.deliveryFee}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Minimum Order</p>
                    <p>£{registrationData.minimumOrderAmount}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Registration ID</p>
                    <p>{registrationData.registrationId}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Registration Date</p>
                    <p>{new Date(registrationData.registrationDate).toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-700 mb-2">Registration Status</h3>
                <Alert className="bg-amber-50 border-amber-200">
                  <Clock className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-800">Pending Approval</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    Your registration is awaiting review by our team. This typically takes 1-2 business days.
                  </AlertDescription>
                </Alert>

                <div className="mt-6 space-y-4">
                  <Button
                    onClick={() => router.push("/business-admin/dashboard")}
                    className="w-full bg-emerald-600 hover:bg-emerald-700"
                  >
                    Go to Business Dashboard
                  </Button>

                  <Link href="/" className="block">
                    <Button variant="outline" className="w-full">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Return to Home
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
