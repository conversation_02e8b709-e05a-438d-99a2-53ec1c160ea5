"use client"

import { useState, useEffect } from "react"
import { Clock, Truck, Store } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { useRealtimeCart } from "@/context/realtime-cart-context"

interface BusinessInfo {
  id: number
  name: string
  preparation_time_minutes: number
  delivery_time_minutes?: number
  distance?: number
}

interface MultiBusinessTimeEstimatorProps {
  businessIds: string[]
  preparationTimes: Record<string, number>
  deliveryTimes?: Record<string, number>
  className?: string
}

export default function DeliveryTimeEstimator({
  businessIds,
  preparationTimes,
  deliveryTimes = {},
  className = ""
}: MultiBusinessTimeEstimatorProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [businessInfo, setBusinessInfo] = useState<Record<string, BusinessInfo>>({})
  const { getDeliveryMethod } = useRealtimeCart()

  // Fetch business information from the database
  useEffect(() => {
    const fetchBusinessInfo = async () => {
      setIsLoading(true)

      try {
        console.log('MultiBusinessTimeEstimator - Raw business IDs:', businessIds)

        // First try to convert string IDs to numbers for direct ID lookup
        const numericIds = businessIds
          .map(id => parseInt(id))
          .filter(id => !isNaN(id))

        console.log('MultiBusinessTimeEstimator - Numeric business IDs:', numericIds)

        // Also collect any potential slug IDs (non-numeric strings)
        const slugIds = businessIds
          .filter(id => isNaN(parseInt(id)) && id.includes('-'))

        console.log('MultiBusinessTimeEstimator - Potential slug IDs:', slugIds)

        if (numericIds.length === 0 && slugIds.length === 0) {
          console.log('MultiBusinessTimeEstimator - No valid business IDs found')
          setIsLoading(false)
          return
        }

        // Create a map to store business info
        const businessInfoMap: Record<string, BusinessInfo> = {}

        // First fetch businesses by numeric ID
        if (numericIds.length > 0) {
          const { data, error } = await supabase
            .from('businesses')
            .select('id, name, preparation_time_minutes, delivery_time_minutes')
            .in('id', numericIds)

          if (error) {
            console.error('Error fetching business info by ID:', error)
          } else if (data) {
            console.log('MultiBusinessTimeEstimator - Fetched business data by ID:', data)

            // Add to the map
            data.forEach(business => {
              businessInfoMap[business.id.toString()] = business
            })
          }
        }

        // Then fetch businesses by slug if needed
        if (slugIds.length > 0) {
          const { data, error } = await supabase
            .from('businesses')
            .select('id, name, preparation_time_minutes, delivery_time_minutes, slug')
            .in('slug', slugIds)

          if (error) {
            console.error('Error fetching business info by slug:', error)
          } else if (data) {
            console.log('MultiBusinessTimeEstimator - Fetched business data by slug:', data)

            // Add to the map by both ID and slug for easy lookup
            data.forEach(business => {
              businessInfoMap[business.id.toString()] = business
              if (business.slug) {
                businessInfoMap[business.slug] = business
              }
            })
          }
        }

        console.log('MultiBusinessTimeEstimator - Final business info map:', businessInfoMap)
        setBusinessInfo(businessInfoMap)
      } catch (error) {
        console.error('Error in fetchBusinessInfo:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchBusinessInfo()
  }, [businessIds])

  // Format preparation time with "mins" suffix
  const formatTime = (minutes: number) => {
    return `${minutes} mins`;
  }

  // Get business name from the fetched data or use a fallback
  const getBusinessName = (businessId: string) => {
    return businessInfo[businessId]?.name || `Business ${businessId}`
  }

  // Get preparation time from the fetched data or use the provided time
  const getPreparationTime = (businessId: string) => {
    return businessInfo[businessId]?.preparation_time_minutes || preparationTimes[businessId] || 15
  }

  // Fallback implementation of getDeliveryMethod in case the cart context doesn't provide it
  const getDeliveryMethodFallback = (businessId: string): 'delivery' | 'pickup' => {
    // Default to delivery unless we know it's pickup only
    return 'delivery'
  }

  // Get delivery time from the provided time in cart context or use a standard value
  const getDeliveryTime = (businessId: string) => {
    // Use the cart context's getDeliveryMethod if available, otherwise use our fallback
    const deliveryMethod = getDeliveryMethod ? getDeliveryMethod(businessId) : getDeliveryMethodFallback(businessId);

    console.log(`DEBUG: Getting delivery time for business ${businessId}`, {
      fromContext: deliveryTimes[businessId],
      fromBusinessInfo: businessInfo[businessId]?.delivery_time_minutes,
      deliveryMethod: deliveryMethod,
      allDeliveryTimes: deliveryTimes,
      allBusinessInfo: businessInfo
    });

    // Check if this is a pickup order - if so, return 0 for delivery time
    if (deliveryMethod === 'pickup') {
      console.log(`Business ${businessId} is pickup only - no delivery time needed`);
      return 0;
    }

    // First try to get from deliveryTimes context (this is set when adding to cart)
    if (deliveryTimes[businessId] !== undefined && deliveryTimes[businessId] > 0) {
      console.log(`Using delivery time from context: ${deliveryTimes[businessId]}`);
      return deliveryTimes[businessId];
    }

    // If delivery time is 0 or undefined, check if we have business info with delivery_time_minutes
    if (businessInfo[businessId]?.delivery_time_minutes) {
      console.log(`Using delivery time from business info: ${businessInfo[businessId].delivery_time_minutes}`);
      return businessInfo[businessId].delivery_time_minutes;
    }

    // Calculate delivery time based on distance (approximately 2 minutes per km)
    // This matches the calculation in the business detail page
    if (businessInfo[businessId]?.distance) {
      const calculatedDeliveryTime = Math.round(businessInfo[businessId].distance * 2);
      console.log(`Calculated delivery time from distance: ${calculatedDeliveryTime} mins (distance: ${businessInfo[businessId].distance} km)`);
      return calculatedDeliveryTime;
    }

    // Use a standard delivery time of 20 minutes as a last resort
    console.log(`Using default delivery time: 20`);
    return 20;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-sm font-medium text-gray-700">Preparation Times</h3>

      {isLoading ? (
        <div className="text-gray-500 text-sm">Loading business information...</div>
      ) : businessIds.length === 0 ? (
        <div className="text-gray-500 text-sm">No businesses in your order</div>
      ) : (
        <div className="space-y-3">
          {businessIds.map((businessId) => {
            const prepTime = getPreparationTime(businessId);

            return (
              <div key={businessId} className="flex items-center p-3 bg-white rounded-md border border-gray-200">
                <div className="bg-emerald-100 p-2 rounded-full mr-3">
                  <Store className="h-5 w-5 text-emerald-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">{getBusinessName(businessId)}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1 text-emerald-500" />
                    <span>Preparation: {formatTime(prepTime)}</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="pt-3 border-t border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Delivery Times</h3>
        <div className="space-y-3">
          {businessIds.map((businessId) => {
            const prepTime = getPreparationTime(businessId);
            const deliveryTime = getDeliveryTime(businessId);
            const totalTime = prepTime + deliveryTime;

            return (
              <div key={`delivery-${businessId}`} className="flex items-center p-3 bg-white rounded-md border border-gray-200">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Truck className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">{getBusinessName(businessId)}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1 text-blue-500" />
                    <span>Delivery: {formatTime(deliveryTime)} <span className="text-xs text-gray-400">(after preparation)</span></span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Total estimated time: {formatTime(totalTime)}
                    {businessInfo[businessId]?.distance && (
                      <span className="ml-2">
                        (Distance: {businessInfo[businessId].distance.toFixed(1)} km)
                      </span>
                    )}
                  </div>
                  {process.env.NODE_ENV === 'development' && (
                    <div className="text-xs text-gray-400 mt-1 p-1 bg-gray-50 rounded">
                      Debug: Method: {getDeliveryMethod ? getDeliveryMethod(businessId) : getDeliveryMethodFallback(businessId)},
                      Context delivery time: {deliveryTimes[businessId] || 'not set'},
                      Business info: {businessInfo[businessId]?.delivery_time_minutes || 'not set'},
                      Distance: {businessInfo[businessId]?.distance ? `${businessInfo[businessId].distance.toFixed(1)} km` : 'not set'}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="text-sm text-gray-500 pt-2 space-y-1">
        <p>Following your Order being placed, you can track your Order status and location on a map in the Your Active Orders section.</p>
        <p>Actual delivery times may vary based on traffic and weather conditions.</p>
      </div>
    </div>
  )
}
