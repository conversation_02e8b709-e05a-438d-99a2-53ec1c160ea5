import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has admin permissions
    const authClient = await createServerSupabase()

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: profileError } = await authClient
      .from("users")
      .select("role")
      .eq("email", session.user.email)
      .single()

    if (profileError || !userProfile) {
      console.error("Error fetching user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", session.user.email, "with role:", userProfile.role)
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      )
    }

    console.log("Admin access verified for user:", session.user.email, "with role:", userProfile.role)

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const businessId = parseInt(params.id)
    console.log("API: Approving business with ID:", businessId)

    // Set user context for audit logging
    try {
      await supabase.rpc('set_config', {
        setting_name: 'app.current_user_id',
        setting_value: session.user.id,
        is_local: true
      })

      await supabase.rpc('set_config', {
        setting_name: 'app.current_user_email',
        setting_value: session.user.email,
        is_local: true
      })

      await supabase.rpc('set_config', {
        setting_name: 'app.current_user_role',
        setting_value: userProfile.role,
        is_local: true
      })
    } catch (configError) {
      console.warn("Could not set user context for audit logging:", configError)
    }

    // First, get the business details
    const { data: business, error: fetchError } = await supabase
      .from('businesses')
      .select('id, address, postcode')
      .eq('id', businessId)
      .single()

    if (fetchError) {
      console.error("API: Error fetching business details:", fetchError)
      return NextResponse.json(
        { error: fetchError.message },
        { status: 500 }
      )
    }

    // Update the business approval status
    const { error } = await supabase
      .from('businesses')
      .update({ is_approved: true })
      .eq('id', businessId)

    if (error) {
      console.error("API: Error approving business:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    // Generate coordinates for the business
    if (business.address) {
      try {
        const { geocodeAndUpdateBusinessCoordinates } = await import('@/lib/address-utils')
        const { success, coordinates } = await geocodeAndUpdateBusinessCoordinates(
          businessId.toString(),
          business.address,
          business.postcode
        )

        if (success) {
          console.log("API: Business coordinates updated successfully:", coordinates)
        } else {
          console.warn("API: Failed to update business coordinates, but business was approved")
        }
      } catch (geocodeError) {
        console.error("API: Error geocoding business address:", geocodeError)
        // Continue anyway, as the business was approved
      }
    }

    // Create admin notification for the approval action
    try {
      await supabase
        .from('admin_notifications')
        .insert({
          admin_user_id: session.user.id,
          type: 'business_approved',
          title: 'Business Approved',
          message: `You approved business "${business.name || 'Unknown'}" (ID: ${businessId})`,
          action_url: `/admin/businesses/${businessId}`,
          priority: 'medium',
          related_table: 'businesses',
          related_record_id: businessId,
          metadata: {
            business_name: business.name,
            approved_by: session.user.email,
            coordinates_updated: business.address ? true : false
          }
        })
    } catch (notificationError) {
      console.warn("Could not create admin notification:", notificationError)
    }

    console.log("API: Business approved successfully")
    return NextResponse.json({
      success: true,
      message: "Business approved successfully"
    })
  } catch (error: any) {
    console.error("API: Error in business approval:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
