"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  DeliveryTimeEstimator,
  JerseyAddressInput,
  DeliveryMap,
  DeliveryStatusTracker,
  DeliveryStatus
} from "@/components/delivery"


export default function DeliveryDemoPage() {
  // Address state
  const [restaurantAddress, setRestaurantAddress] = useState("St Helier, Jersey")
  const [customerAddress, setCustomerAddress] = useState("")

  // Coordinates state
  const [restaurantCoords, setRestaurantCoords] = useState<[number, number]>([-2.1037, 49.1805]) // Default to St Helier
  const [customerCoords, setCustomerCoords] = useState<[number, number] | null>(null)

  // Delivery status state
  const [deliveryStatus, setDeliveryStatus] = useState<DeliveryStatus>("order_placed")

  // Handle address geocoding
  const handleAddressSubmit = async () => {
    if (!customerAddress || !restaurantAddress) return

    // Geocode restaurant address if needed
    if (!restaurantCoords) {
      const restaurantResult = await geocodeAddress(restaurantAddress)
      if (restaurantResult) {
        setRestaurantCoords(restaurantResult)
      }
    }

    // Geocode customer address if needed
    if (!customerCoords) {
      const customerResult = await geocodeAddress(customerAddress)
      if (customerResult) {
        setCustomerCoords(customerResult)
      }
    }
  }

  // Advance delivery status
  const advanceStatus = () => {
    const statusOrder: DeliveryStatus[] = [
      "order_placed",
      "preparing",
      "ready_for_pickup",
      "out_for_delivery",
      "delivered"
    ]

    const currentIndex = statusOrder.indexOf(deliveryStatus)
    if (currentIndex < statusOrder.length - 1) {
      setDeliveryStatus(statusOrder[currentIndex + 1])
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Delivery System Demo</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Set Delivery Addresses</CardTitle>
            <CardDescription>Enter the pickup and delivery addresses</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Pickup Location</label>
              <JerseyAddressInput
                value={restaurantAddress}
                onChange={setRestaurantAddress}
                onCoordinatesChange={setRestaurantCoords}
                placeholder="Enter restaurant or shop address"
              />
            </div>

            <div>
              <div className="block text-sm font-medium mb-1">Delivery Address</div>
              <JerseyAddressInput
                value={customerAddress}
                onChange={setCustomerAddress}
                onCoordinatesChange={setCustomerCoords}
                placeholder="Enter delivery address in Jersey"
              />
            </div>

            <Button
              onClick={handleAddressSubmit}
              disabled={!restaurantAddress || !customerAddress}
              className="w-full mt-2"
            >
              Calculate Delivery
            </Button>
          </CardContent>
        </Card>

        {customerAddress && restaurantAddress && (
          <DeliveryTimeEstimator
            restaurantAddress={restaurantAddress}
            customerAddress={customerAddress}
            preparationTimeMinutes={15}
          />
        )}
      </div>

      {customerAddress && restaurantAddress && (
        <Tabs defaultValue="map" className="mb-8">
          <TabsList>
            <TabsTrigger value="map">Delivery Map</TabsTrigger>
            <TabsTrigger value="tracker">Delivery Tracker</TabsTrigger>
          </TabsList>

          <TabsContent value="map" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Delivery Route</CardTitle>
                <CardDescription>Map showing the delivery route</CardDescription>
              </CardHeader>
              <CardContent>
                {restaurantCoords && (
                  <DeliveryMap
                    restaurantLng={restaurantCoords[0]}
                    restaurantLat={restaurantCoords[1]}
                    customerLng={customerCoords ? customerCoords[0] : -2.1037}
                    customerLat={customerCoords ? customerCoords[1] : 49.1805}
                    height="400px"
                    showRoute={true}
                  />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tracker" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <DeliveryStatusTracker
                  status={deliveryStatus}
                  estimatedDeliveryTime="12:30 PM"
                  restaurantName="Jersey Business"
                  restaurantAddress={restaurantAddress || "Pickup Address"}
                  customerAddress={customerAddress || "Customer Address"}
                  restaurantCoordinates={restaurantCoords || [-2.1037, 49.1805]}
                  customerCoordinates={customerCoords || [-2.1037, 49.1805]}
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Demo Controls</CardTitle>
                  <CardDescription>Simulate delivery progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium mb-1">Current Status:</p>
                      <p className="text-lg font-bold capitalize">{deliveryStatus.replace(/_/g, " ")}</p>
                    </div>

                    <Button
                      onClick={advanceStatus}
                      disabled={deliveryStatus === "delivered"}
                      className="w-full"
                    >
                      Advance to Next Status
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => setDeliveryStatus("order_placed")}
                      className="w-full"
                    >
                      Reset Status
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
