import { NextResponse } from 'next/server';
import { standardizeJerseyAddress, standardizeAndGeocodeAddress } from '@/lib/address-standardizer';

/**
 * API endpoint to standardize a Jersey address
 * @param request - The request object containing the address to standardize
 * @returns Standardized address components
 */
export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    const { address, parish, postcode, geocode = false } = body;

    // Validate required fields
    if (!address) {
      return NextResponse.json(
        { error: "Address is required" },
        { status: 400 }
      );
    }

    // Standardize the address
    const standardized = geocode
      ? await standardizeAndGeocodeAddress(address, parish, postcode)
      : await standardizeJerseyAddress(address, parish, postcode);

    if (!standardized) {
      return NextResponse.json(
        { error: "Address standardization failed" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: standardized.is_valid ? "Address standardized successfully" : "Address standardization completed with warnings",
      address: standardized
    });
  } catch (error) {
    console.error('Error in address standardization API:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
