import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to get the current user from request
async function getCurrentUser(request: NextRequest) {
  let user = null;
  
  // First try to get the user from the Authorization header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    
    // Create a Supabase client with the token
    const supabaseClientWithToken = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );
    
    // Get the user from the token
    const { data: userData, error: userError } = await supabaseClientWithToken.auth.getUser();
    
    if (!userError && userData?.user) {
      user = userData.user;
    }
  }
  
  // If we couldn't get the user from the Authorization header, try cookies
  if (!user) {
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: userData } = await supabaseClient.auth.getUser();
    user = userData?.user;
  }
  
  return user;
}

// POST endpoint to validate a user's cart
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const deliveryAddress = body.deliveryAddress;

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get the user's cart
    const { data: userCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('user_id', userData.id)
      .single();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch user cart', details: cartError.message },
        { status: 500 }
      );
    }

    // If no cart exists, return invalid
    if (!userCart) {
      return NextResponse.json({
        valid: false,
        message: 'No cart found'
      });
    }

    // Get the cart items
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', userCart.id);

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart items', details: itemsError.message },
        { status: 500 }
      );
    }

    // Check if the cart is empty
    if (!cartItems || cartItems.length === 0) {
      return NextResponse.json({
        valid: false,
        message: 'Cart is empty'
      });
    }

    // Perform any additional validation here
    // For example, check if all items are in stock, if delivery is available to the address, etc.

    // For now, we'll just return valid if there are items in the cart
    return NextResponse.json({
      valid: true,
      message: 'Cart is valid',
      itemCount: cartItems.length
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
