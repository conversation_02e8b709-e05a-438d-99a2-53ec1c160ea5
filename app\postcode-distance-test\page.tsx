'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { MapPin, Navigation, Truck } from 'lucide-react';

interface DistanceResult {
  postcode1: string;
  postcode2: string;
  distance_meters: number;
  distance_km: number;
  method?: string;
  coordinates1?: [number, number];
  coordinates2?: [number, number];
}

export default function PostcodeDistanceTestPage() {
  const [postcode1, setPostcode1] = useState<string>('');
  const [postcode2, setPostcode2] = useState<string>('');
  const [result, setResult] = useState<DistanceResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [coordinatesResult, setCoordinatesResult] = useState<any>(null);

  // Sample postcodes for testing
  const samplePostcodes = [
    { name: 'St Helier', postcode: 'JE2 4UE' },
    { name: 'St John', postcode: 'JE3 4EQ' },
    { name: 'St Brelade', postcode: 'JE3 8BS' },
    { name: 'Trinity', postcode: 'JE4 9FL' },
  ];

  const handleGetCoordinates = async () => {
    if (!postcode1) {
      setError('Please enter a postcode');
      return;
    }

    setIsLoading(true);
    setError(null);
    setCoordinatesResult(null);

    try {
      const response = await fetch(`/api/postcodes?action=coordinates&postcode=${encodeURIComponent(postcode1)}`);
      const data = await response.json();

      if (response.ok) {
        setCoordinatesResult(data);
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError('An error occurred while processing the request');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCalculateDistance = async () => {
    if (!postcode1 || !postcode2) {
      setError('Please enter both postcodes');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch(
        `/api/postcodes?action=distance&postcode=${encodeURIComponent(postcode1)}&postcode2=${encodeURIComponent(postcode2)}`
      );
      const data = await response.json();

      if (response.ok) {
        // Get coordinates for both postcodes to display on map
        const coords1Response = await fetch(`/api/postcodes?action=coordinates&postcode=${encodeURIComponent(postcode1)}`);
        const coords2Response = await fetch(`/api/postcodes?action=coordinates&postcode=${encodeURIComponent(postcode2)}`);
        
        const coords1Data = await coords1Response.json();
        const coords2Data = await coords2Response.json();

        setResult({
          ...data,
          coordinates1: coords1Data.coordinates,
          coordinates2: coords2Data.coordinates
        });
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError('An error occurred while processing the request');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Jersey Postcode Distance Calculator</h1>
      <p className="mb-6 text-gray-600">
        This page tests the distance calculation between two Jersey postcodes using the Haversine formula and OpenStreetMap data.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Postcode to Coordinates</CardTitle>
            <CardDescription>
              Test converting a Jersey postcode to coordinates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex space-x-2">
                <div className="relative flex-grow">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Enter a postcode (e.g., JE2 3NG)"
                    className="pl-10"
                    value={postcode1}
                    onChange={(e) => setPostcode1(e.target.value)}
                  />
                </div>
                <Button 
                  onClick={handleGetCoordinates}
                  disabled={isLoading}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  Get Coordinates
                </Button>
              </div>

              <div className="mt-2">
                <h3 className="text-sm font-medium mb-2">Sample Postcodes:</h3>
                <div className="flex flex-wrap gap-2">
                  {samplePostcodes.map((sample) => (
                    <Button
                      key={sample.postcode}
                      variant="outline"
                      size="sm"
                      onClick={() => setPostcode1(sample.postcode)}
                    >
                      {sample.name}: {sample.postcode}
                    </Button>
                  ))}
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              {coordinatesResult && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium mb-2">Result:</h3>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium">Postcode:</div>
                      <div className="text-sm">{coordinatesResult.postcode}</div>
                      
                      <div className="text-sm font-medium">Coordinates:</div>
                      <div className="text-sm">[{coordinatesResult.coordinates[0]}, {coordinatesResult.coordinates[1]}]</div>
                      
                      <div className="text-sm font-medium">Parish:</div>
                      <div className="text-sm">{coordinatesResult.parish || 'Unknown'}</div>
                      
                      <div className="text-sm font-medium">Source:</div>
                      <div className="text-sm">{coordinatesResult.source}</div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">Map Preview</h4>
                      <div className="aspect-video bg-gray-200 rounded-md overflow-hidden">
                        <iframe
                          width="100%"
                          height="100%"
                          frameBorder="0"
                          src={`https://www.openstreetmap.org/export/embed.html?bbox=${coordinatesResult.coordinates[0] - 0.01},${coordinatesResult.coordinates[1] - 0.01},${coordinatesResult.coordinates[0] + 0.01},${coordinatesResult.coordinates[1] + 0.01}&marker=${coordinatesResult.coordinates[1]},${coordinatesResult.coordinates[0]}`}
                          style={{ border: 0 }}
                        ></iframe>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Distance Calculator</CardTitle>
            <CardDescription>
              Calculate the distance between two Jersey postcodes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex space-x-2">
                <div className="relative flex-grow">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="First postcode (e.g., JE2 3NG)"
                    className="pl-10"
                    value={postcode1}
                    onChange={(e) => setPostcode1(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex space-x-2">
                <div className="relative flex-grow">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Second postcode (e.g., JE3 4EQ)"
                    className="pl-10"
                    value={postcode2}
                    onChange={(e) => setPostcode2(e.target.value)}
                  />
                </div>
              </div>

              <Button 
                onClick={handleCalculateDistance}
                disabled={isLoading}
                className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                <Navigation className="mr-2 h-4 w-4" />
                Calculate Distance
              </Button>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              {result && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium mb-2">Result:</h3>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium">From:</div>
                      <div className="text-sm">{result.postcode1}</div>
                      
                      <div className="text-sm font-medium">To:</div>
                      <div className="text-sm">{result.postcode2}</div>
                      
                      <div className="text-sm font-medium">Distance:</div>
                      <div className="text-sm">{result.distance_km.toFixed(2)} km ({result.distance_meters.toFixed(0)} meters)</div>
                      
                      <div className="text-sm font-medium">Method:</div>
                      <div className="text-sm">{result.method || 'Haversine'}</div>
                    </div>

                    {result.coordinates1 && result.coordinates2 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Map Preview</h4>
                        <div className="aspect-video bg-gray-200 rounded-md overflow-hidden">
                          <iframe
                            width="100%"
                            height="100%"
                            frameBorder="0"
                            src={`https://www.openstreetmap.org/directions?engine=graphhopper_car&route=${result.coordinates1[1]},${result.coordinates1[0]};${result.coordinates2[1]},${result.coordinates2[0]}#map=12/49.2000/-2.1200`}
                            style={{ border: 0 }}
                          ></iframe>
                        </div>
                      </div>
                    )}

                    <div className="mt-4 p-3 bg-blue-50 rounded-md">
                      <div className="flex items-center">
                        <Truck className="h-5 w-5 text-blue-500 mr-2" />
                        <span className="text-sm font-medium text-blue-700">Estimated Delivery Time:</span>
                      </div>
                      <p className="text-sm text-blue-600 mt-1">
                        {Math.ceil(result.distance_km * 2)} minutes travel time
                        (assuming average speed of 30 km/h)
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
        <h2 className="text-lg font-medium text-blue-800 mb-2">About This Service</h2>
        <p className="text-blue-700 mb-4">
          This service uses the following methods to calculate distances between postcodes:
        </p>
        <ol className="list-decimal list-inside space-y-2 text-blue-700 ml-4">
          <li>
            <strong>Database Lookup:</strong> First tries to get coordinates from the Supabase database
          </li>
          <li>
            <strong>OpenStreetMap/Nominatim:</strong> If database lookup fails, uses OpenStreetMap's Nominatim service
            (free, with rate limit of 1 request per second)
          </li>
          <li>
            <strong>Hardcoded Mapping:</strong> Falls back to hardcoded postcode area mappings if both previous methods fail
          </li>
          <li>
            <strong>Parish-Based Coordinates:</strong> As a final fallback, uses parish center coordinates
          </li>
          <li>
            <strong>Haversine Formula:</strong> Calculates "as the crow flies" distance between coordinates
          </li>
        </ol>
      </div>
    </div>
  );
}
