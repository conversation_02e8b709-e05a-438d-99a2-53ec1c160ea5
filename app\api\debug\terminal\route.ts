import { NextResponse } from 'next/server';

// In-memory log storage
let terminalLogs: string[] = [];

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { message } = body;

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Add timestamp to message
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    
    // Add to in-memory storage
    terminalLogs.push(logEntry);
    
    // Keep only the last 100 logs
    if (terminalLogs.length > 100) {
      terminalLogs = terminalLogs.slice(-100);
    }

    // Log to server console as well
    console.log(logEntry);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error logging to terminal:', error);
    return NextResponse.json({ error: 'Failed to log message' }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ logs: terminalLogs });
}

// Clear logs
export async function DELETE() {
  terminalLogs = [];
  return NextResponse.json({ success: true });
}
