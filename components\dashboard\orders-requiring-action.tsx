"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Truck,
  Eye,
  MoreHorizontal,
  Phone,
  MessageSquare,
  MapPin
} from "lucide-react"
import Link from "next/link"

interface Order {
  id: string
  orderNumber: string
  customerName: string
  items: string[]
  totalAmount: number
  placedAt: Date
  estimatedReady: Date
  status: 'new' | 'acknowledged' | 'preparing' | 'ready' | 'overdue'
  deliveryMethod: 'pickup' | 'delivery'
  deliveryAddress?: string
  customerPhone?: string
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
}

interface OrdersRequiringActionProps {
  orders: Order[]
  onAcknowledgeOrder?: (orderId: string) => void
  onMarkReady?: (orderId: string) => void
  onContactCustomer?: (orderId: string) => void
  className?: string
}

function OrderCard({ 
  order, 
  onAcknowledgeOrder, 
  onMarkReady, 
  onContactCustomer 
}: { 
  order: Order
  onAcknowledgeOrder?: (orderId: string) => void
  onMarkReady?: (orderId: string) => void
  onContactCustomer?: (orderId: string) => void
}) {
  const getTimeSince = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    return `${Math.floor(diffMins / 60)}h ${diffMins % 60}m ago`
  }

  const getTimeUntilReady = (date: Date) => {
    const now = new Date()
    const diffMs = date.getTime() - now.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 0) return `${Math.abs(diffMins)}m overdue`
    if (diffMins < 60) return `${diffMins}m remaining`
    return `${Math.floor(diffMins / 60)}h ${diffMins % 60}m remaining`
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800'
      case 'acknowledged': return 'bg-green-100 text-green-800'
      case 'preparing': return 'bg-yellow-100 text-yellow-800'
      case 'ready': return 'bg-emerald-100 text-emerald-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  return (
    <Card className={`border-l-4 ${
      order.urgencyLevel === 'critical' ? 'border-red-500' :
      order.urgencyLevel === 'high' ? 'border-orange-500' :
      order.urgencyLevel === 'medium' ? 'border-yellow-500' :
      'border-gray-300'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-gray-900">
                Order #{order.orderNumber}
              </h4>
              <Badge className={getStatusColor(order.status)}>
                {order.status}
              </Badge>
              {order.urgencyLevel !== 'low' && (
                <Badge variant="outline" className={getUrgencyColor(order.urgencyLevel)}>
                  {order.urgencyLevel}
                </Badge>
              )}
            </div>
            
            <p className="text-sm text-gray-600 mb-1">
              {order.customerName} • {formatCurrency(order.totalAmount)}
            </p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
              <span className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                Placed {getTimeSince(order.placedAt)}
              </span>
              <span className="flex items-center">
                {order.deliveryMethod === 'delivery' ? (
                  <Truck className="h-3 w-3 mr-1" />
                ) : (
                  <MapPin className="h-3 w-3 mr-1" />
                )}
                {order.deliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'}
              </span>
            </div>

            <div className="text-xs text-gray-600 mb-3">
              <strong>Items:</strong> {order.items.join(', ')}
            </div>

            {order.deliveryAddress && (
              <div className="text-xs text-gray-600 mb-3">
                <strong>Address:</strong> {order.deliveryAddress}
              </div>
            )}

            <div className="flex items-center space-x-1 text-xs">
              {order.status === 'overdue' ? (
                <span className="text-red-600 font-medium">
                  <AlertTriangle className="h-3 w-3 inline mr-1" />
                  {getTimeUntilReady(order.estimatedReady)}
                </span>
              ) : (
                <span className="text-gray-600">
                  Ready in: {getTimeUntilReady(order.estimatedReady)}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {order.status === 'new' && onAcknowledgeOrder && (
            <Button 
              size="sm" 
              onClick={() => onAcknowledgeOrder(order.id)}
              className="text-xs"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Acknowledge
            </Button>
          )}
          
          {(order.status === 'acknowledged' || order.status === 'preparing') && onMarkReady && (
            <Button 
              size="sm" 
              onClick={() => onMarkReady(order.id)}
              className="text-xs"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Mark Ready
            </Button>
          )}

          {order.customerPhone && onContactCustomer && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onContactCustomer(order.id)}
              className="text-xs"
            >
              <Phone className="h-3 w-3 mr-1" />
              Call
            </Button>
          )}

          <Link href={`/business-admin/orders/${order.id}`}>
            <Button variant="outline" size="sm" className="text-xs">
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}

export function OrdersRequiringAction({
  orders,
  onAcknowledgeOrder,
  onMarkReady,
  onContactCustomer,
  className = ""
}: OrdersRequiringActionProps) {
  const [filter, setFilter] = useState<'all' | 'new' | 'overdue' | 'ready'>('all')

  const filteredOrders = orders.filter(order => {
    switch (filter) {
      case 'new': return order.status === 'new'
      case 'overdue': return order.status === 'overdue'
      case 'ready': return order.status === 'ready'
      default: return true
    }
  })

  const getFilterCount = (filterType: string) => {
    switch (filterType) {
      case 'new': return orders.filter(o => o.status === 'new').length
      case 'overdue': return orders.filter(o => o.status === 'overdue').length
      case 'ready': return orders.filter(o => o.status === 'ready').length
      default: return orders.length
    }
  }

  if (orders.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            All Caught Up!
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">No orders requiring immediate action.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
            Orders Requiring Action
            <Badge className="ml-2 bg-orange-100 text-orange-800">
              {orders.length}
            </Badge>
          </CardTitle>
          
          <div className="flex space-x-1">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
              className="text-xs"
            >
              All ({getFilterCount('all')})
            </Button>
            <Button
              variant={filter === 'new' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('new')}
              className="text-xs"
            >
              New ({getFilterCount('new')})
            </Button>
            <Button
              variant={filter === 'overdue' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('overdue')}
              className="text-xs"
            >
              Overdue ({getFilterCount('overdue')})
            </Button>
            <Button
              variant={filter === 'ready' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('ready')}
              className="text-xs"
            >
              Ready ({getFilterCount('ready')})
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {filteredOrders.map(order => (
          <OrderCard
            key={order.id}
            order={order}
            onAcknowledgeOrder={onAcknowledgeOrder}
            onMarkReady={onMarkReady}
            onContactCustomer={onContactCustomer}
          />
        ))}
        
        {filteredOrders.length === 0 && (
          <p className="text-center text-gray-500 py-4">
            No {filter} orders found.
          </p>
        )}
      </CardContent>
    </Card>
  )
}
