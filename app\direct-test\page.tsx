'use client'

import { useState, useEffect } from 'react'
import { supabaseAdmin } from '@/lib/supabase'

export default function DirectTestPage() {
  const [businesses, setBusinesses] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)

      try {
        console.log('Fetching business types...')
        const { data: businessTypes, error: typesError } = await supabaseAdmin
          .from('business_types')
          .select('id, name, slug')
          .order('name')

        if (typesError) {
          throw new Error(`Error fetching business types: ${typesError.message}`)
        }

        console.log('Business types:', businessTypes)

        // Get restaurant type ID
        const restaurantTypeId = businessTypes.find(t => t.slug === 'restaurant')?.id

        if (!restaurantTypeId) {
          throw new Error('Restaurant type not found')
        }

        console.log('Fetching restaurants...')
        const { data: restaurantsData, error: restaurantsError } = await supabaseAdmin
          .from('businesses')
          .select('*')
          .eq('business_type_id', restaurantTypeId)
          .limit(10)

        if (restaurantsError) {
          throw new Error(`Error fetching restaurants: ${restaurantsError.message}`)
        }

        console.log('Restaurants:', restaurantsData)
        setBusinesses(restaurantsData || [])
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="container-fluid py-4">
      <h1 className="text-2xl font-bold mb-4">Direct Supabase Admin Test</h1>

      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}

      <div className="mt-4">
        <h2 className="text-xl font-semibold mb-2">Restaurants ({businesses.length})</h2>

        {businesses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {businesses.map(business => (
              <div key={business.id} className="border p-4 rounded">
                <h3 className="font-bold">{business.name}</h3>
                <p className="text-sm text-gray-600">{business.location}</p>
                <p className="text-sm">Rating: {business.rating || 'N/A'}</p>
              </div>
            ))}
          </div>
        ) : (
          <p>No restaurants found</p>
        )}
      </div>
    </div>
  )
}
