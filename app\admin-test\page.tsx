'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@supabase/supabase-js'

export default function AdminTestPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString().split('T')[1].split('.')[0]} - ${message}`])
  }

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      addLog('Starting data fetch')
      
      try {
        // Get Supabase URL and keys
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
        
        addLog(`Supabase URL: ${supabaseUrl ? 'Set' : 'Not set'}`)
        addLog(`Supabase Service Key: ${supabaseServiceKey ? 'Set' : 'Not set'}`)
        
        if (!supabaseUrl || !supabaseServiceKey) {
          throw new Error('Supabase environment variables not set')
        }
        
        // Create Supabase admin client
        addLog('Creating Supabase admin client')
        const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
          auth: {
            persistSession: false,
            autoRefreshToken: false
          }
        })
        
        // Fetch business types
        addLog('Fetching business types')
        const { data: businessTypes, error: typesError } = await adminClient
          .from('business_types')
          .select('*')
          .limit(5)
        
        if (typesError) {
          addLog(`Error fetching business types: ${typesError.message}`)
          throw new Error(`Error fetching business types: ${typesError.message}`)
        }
        
        addLog(`Fetched ${businessTypes?.length || 0} business types`)
        
        // Fetch businesses
        addLog('Fetching businesses')
        const { data: businesses, error: businessesError } = await adminClient
          .from('businesses')
          .select('*')
          .limit(5)
        
        if (businessesError) {
          addLog(`Error fetching businesses: ${businessesError.message}`)
          throw new Error(`Error fetching businesses: ${businessesError.message}`)
        }
        
        addLog(`Fetched ${businesses?.length || 0} businesses`)
        
        // Try to fetch users
        addLog('Fetching users')
        const { data: users, error: usersError } = await adminClient
          .from('users')
          .select('*')
          .limit(5)
        
        if (usersError) {
          addLog(`Error fetching users: ${usersError.message}`)
        } else {
          addLog(`Fetched ${users?.length || 0} users`)
        }
        
        setData({
          businessTypes,
          businesses,
          users: usersError ? null : users
        })
      } catch (err) {
        console.error('Error fetching data:', err)
        addLog(`Error: ${err.message}`)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
        addLog('Fetch completed')
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Supabase Admin Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Status</h2>
          <p>Loading: {loading ? 'Yes' : 'No'}</p>
          {error && <p className="text-red-500">Error: {error}</p>}
          
          {data && (
            <>
              <h3 className="font-semibold mt-4">Business Types ({data.businessTypes?.length || 0})</h3>
              {data.businessTypes && data.businessTypes.length > 0 ? (
                <ul className="list-disc pl-5">
                  {data.businessTypes.map((type: any) => (
                    <li key={type.id}>{type.name}</li>
                  ))}
                </ul>
              ) : (
                <p>No business types found</p>
              )}
              
              <h3 className="font-semibold mt-4">Businesses ({data.businesses?.length || 0})</h3>
              {data.businesses && data.businesses.length > 0 ? (
                <ul className="list-disc pl-5">
                  {data.businesses.map((business: any) => (
                    <li key={business.id}>{business.name}</li>
                  ))}
                </ul>
              ) : (
                <p>No businesses found</p>
              )}
              
              <h3 className="font-semibold mt-4">Users ({data.users?.length || 0})</h3>
              {data.users && data.users.length > 0 ? (
                <ul className="list-disc pl-5">
                  {data.users.map((user: any) => (
                    <li key={user.id}>{user.name || user.email}</li>
                  ))}
                </ul>
              ) : (
                <p>No users found</p>
              )}
            </>
          )}
        </div>
        
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Logs</h2>
          <div className="bg-black text-green-400 p-2 rounded font-mono text-sm h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
