"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import SettingsWizard from "@/components/business/settings-wizard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"

export default function SettingsWizardPage() {
  const router = useRouter()
  const { user, userProfile, isLoading: authLoading } = useAuth()
  const [business, setBusiness] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Add timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log("Loading timeout reached, checking state...")
        console.log("Auth loading:", authLoading, "User:", !!user, "Business:", !!business)
        if (!authLoading && !business) {
          setError("Loading timeout - please refresh the page or contact support")
          setLoading(false)
        }
      }
    }, 10000) // 10 second timeout

    return () => clearTimeout(timeout)
  }, [loading, authLoading, user, business])

  // Fetch business data - using same approach as settings page
  useEffect(() => {
    const fetchBusiness = async () => {
      console.log("fetchBusiness called - authLoading:", authLoading, "user:", !!user)

      if (authLoading) {
        console.log("Still loading auth, waiting...")
        return
      }

      if (!user) {
        console.log("No user found, stopping loading")
        setLoading(false)
        return
      }

      try {
        setLoading(true)

        console.log("Current user:", user)
        console.log("User profile:", userProfile)
        console.log("Fetching business data from server API")

        // Add more debugging
        console.log("Auth loading:", authLoading)
        console.log("User email:", user?.email)

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';
        console.log("Token found:", !!token)

        // Use the server API to fetch business data (same as settings page)
        console.log("Making API call to /api/business-admin/settings-data")
        const response = await fetch('/api/business-admin/settings-data', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        console.log("API response status:", response.status)
        console.log("API response ok:", response.ok)

        if (!response.ok) {
          // If we get a 401 or 403, redirect to login
          if (response.status === 401 || response.status === 403) {
            console.log("Authentication error, redirecting to login")
            router.push("/login?redirectTo=/business-admin/settings-wizard")
            return
          }

          const errorData = await response.json()
          console.error("API error response:", errorData)
          setError(errorData.error || "Could not load business data")
          return
        }

        const data = await response.json()
        console.log("Business data from API:", data)

        if (data.business) {
          setBusiness(data.business)
        } else {
          setError("We couldn't find your business information. Please contact support for assistance.")
        }
      } catch (err) {
        console.error("Error in fetchBusiness:", err)
        setError("An unexpected error occurred. Please try again.")
      } finally {
        setLoading(false)
      }
    }

    fetchBusiness()
  }, [user, userProfile, router, authLoading])

  const handleSave = async (formData: any) => {
    if (!business?.id) {
      throw new Error('No business ID found')
    }

    const supabase = createClientComponentClient()

    // Prepare the data for update
    const updateData = {
      name: formData.name,
      description: formData.description,
      phone: formData.phone,
      address: formData.address,
      postcode: formData.postcode,
      delivery_radius: parseFloat(formData.delivery_radius),
      preparation_time_minutes: parseInt(formData.preparation_time_minutes),
      minimum_order_amount: parseFloat(formData.minimum_order_amount),
      delivery_fee: parseFloat(formData.delivery_fee),
      // Build opening hours object
      opening_hours: {
        monday: {
          open: formData.monday_open || '09:00',
          close: formData.monday_close || '21:00'
        },
        tuesday: {
          open: formData.tuesday_open || '09:00',
          close: formData.tuesday_close || '21:00'
        },
        wednesday: {
          open: formData.wednesday_open || '09:00',
          close: formData.wednesday_close || '21:00'
        },
        thursday: {
          open: formData.thursday_open || '09:00',
          close: formData.thursday_close || '21:00'
        },
        friday: {
          open: formData.friday_open || '09:00',
          close: formData.friday_close || '21:00'
        },
        saturday: {
          open: formData.saturday_open || '09:00',
          close: formData.saturday_close || '21:00'
        },
        sunday: {
          open: formData.sunday_open || '09:00',
          close: formData.sunday_close || '21:00'
        }
      },
      updated_at: new Date().toISOString()
    }

    const { error } = await supabase
      .from('businesses')
      .update(updateData)
      .eq('id', business.id)

    if (error) {
      console.error('Error updating business:', error)
      throw new Error('Failed to save business settings')
    }
  }

  const handleComplete = () => {
    router.push('/business-admin/dashboard')
  }

  if (authLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
              <p className="text-gray-600">Loading your business settings...</p>
              <p className="text-xs text-gray-400">Auth loading: {authLoading ? 'true' : 'false'}, Data loading: {loading ? 'true' : 'false'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You must be logged in to access the settings wizard.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!business && !loading && !error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No business found for your account. Please contact support.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <SettingsWizard
        business={business}
        onSave={handleSave}
        onComplete={handleComplete}
      />
    </div>
  )
}
