'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export default function SuperAdminTestPage() {
  return (
    <div className="container-fluid py-10">
      <h1 className="text-3xl font-bold mb-6">Super Admin Test Page</h1>
      <p className="mb-6">This page has no client-side protection and should only be accessible if the middleware is working correctly.</p>

      <Card>
        <CardHeader>
          <CardTitle>Super Admin Access Test</CardTitle>
          <CardDescription>You have successfully accessed a super admin page</CardDescription>
        </CardHeader>
        <CardContent>
          <p>If you can see this page, it means:</p>
          <ul className="list-disc pl-5 mt-2 space-y-1">
            <li>You are logged in</li>
            <li>You have super_admin role</li>
            <li>The middleware is not blocking your access</li>
          </ul>
        </CardContent>
        <CardFooter>
          <Link href="/auth-debug/admin-access">
            <Button variant="outline">
              Back to Debug Page
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  )
}
