// PHASE 6 STEP 12: Driver Capabilities API
// API endpoint to manage driver capabilities

import { NextRequest, NextResponse } from 'next/server';
import { 
  getDriverCapabilities, 
  updateDriverCapabilities,
  getAllDriverCapabilities,
  getDriverCapabilitiesStats,
  findCapableDrivers
} from '@/services/driver-capabilities-service';

// GET endpoint to fetch driver capabilities
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const driverId = searchParams.get('driverId');
    const action = searchParams.get('action');

    // If driverId is provided, get capabilities for specific driver
    if (driverId) {
      const capabilities = await getDriverCapabilities(driverId);
      
      if (!capabilities) {
        return NextResponse.json(
          { error: 'Driver capabilities not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: capabilities
      });
    }

    // Handle different actions
    switch (action) {
      case 'all':
        const allCapabilities = await getAllDriverCapabilities();
        return NextResponse.json({
          success: true,
          data: allCapabilities
        });

      case 'stats':
        const stats = await getDriverCapabilitiesStats();
        if (!stats) {
          return NextResponse.json(
            { error: 'Failed to fetch driver capabilities statistics' },
            { status: 500 }
          );
        }
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'find-capable':
        // Parse requirements from query parameters
        const requiresThermal = searchParams.get('requiresThermal') === 'true';
        const isLargeOrder = searchParams.get('isLargeOrder') === 'true';
        const totalItems = parseInt(searchParams.get('totalItems') || '0');
        const orderValue = parseFloat(searchParams.get('orderValue') || '0');

        const capableDrivers = await findCapableDrivers({
          requiresThermal,
          isLargeOrder,
          totalItems,
          orderValue
        });

        return NextResponse.json({
          success: true,
          data: capableDrivers
        });

      default:
        return NextResponse.json(
          { error: 'Missing required parameters. Provide driverId or action (all/stats/find-capable)' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Error in driver capabilities GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST endpoint to update driver capabilities
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.driverId) {
      return NextResponse.json(
        { error: 'Driver ID is required' },
        { status: 400 }
      );
    }

    // Validate vehicle type
    const validVehicleTypes = ['bicycle', 'motorbike', 'car', 'van', 'truck'];
    if (body.vehicleType && !validVehicleTypes.includes(body.vehicleType)) {
      return NextResponse.json(
        { error: `Invalid vehicle type. Must be one of: ${validVehicleTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate thermal bag size if provided
    const validThermalBagSizes = ['small', 'medium', 'large', 'extra_large'];
    if (body.thermalBagSize && !validThermalBagSizes.includes(body.thermalBagSize)) {
      return NextResponse.json(
        { error: `Invalid thermal bag size. Must be one of: ${validThermalBagSizes.join(', ')}` },
        { status: 400 }
      );
    }

    // Create capabilities object
    const capabilities = {
      driverId: body.driverId,
      hasThermalBag: body.hasThermalBag || false,
      vehicleType: body.vehicleType || 'bicycle',
      canCarryLargeOrders: body.canCarryLargeOrders || false,
      thermalBagSize: body.thermalBagSize,
      maxOrderValue: body.maxOrderValue || 50.00,
      maxItemsCapacity: body.maxItemsCapacity || 5
    };

    const success = await updateDriverCapabilities(capabilities);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update driver capabilities' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Driver capabilities updated successfully'
    });

  } catch (error) {
    console.error('❌ Error in driver capabilities POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
