import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      )
    }

    // Check if user exists in the public users table
    const { data: existingUser, error: lookupError } = await supabase
      .from('users')
      .select('id, name, first_name, last_name, email, phone')
      .eq('email', email.toLowerCase())
      .single()

    if (lookupError && lookupError.code !== 'PGRST116') {
      console.error('Error looking up user:', lookupError)
      return NextResponse.json(
        { error: 'Failed to check user' },
        { status: 500 }
      )
    }

    if (existingUser) {
      // Check if they already have a driver profile
      const { data: existingDriver, error: driverLookupError } = await supabase
        .from('driver_profiles')
        .select('id')
        .eq('user_id', existingUser.id)
        .single()

      return NextResponse.json({
        exists: true,
        user: existingUser,
        hasDriverProfile: !!existingDriver
      })
    } else {
      return NextResponse.json({
        exists: false,
        user: null,
        hasDriverProfile: false
      })
    }

  } catch (error) {
    console.error('Error in check email API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
