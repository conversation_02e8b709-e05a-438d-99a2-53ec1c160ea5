"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function CafesPage() {
  const router = useRouter()
  
  // Redirect to the search page with cafe filter
  useEffect(() => {
    router.replace('/search?type=cafe')
  }, [])

  return (
    <div className="container-fluid py-8">
      <h1 className="text-3xl font-bold mb-6">Redirecting to Search...</h1>
      <p className="text-gray-600">Please wait while we redirect you to the cafes search page.</p>
    </div>
  )
}
