"use client"

import { <PERSON>actN<PERSON>, useState, useEffect } from "react"
import { LucideIcon, ChevronDown, ChevronUp, CheckCircle, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface CollapsibleCheckoutSectionProps {
  title: string
  icon: LucideIcon
  children: ReactNode
  className?: string
  isActive: boolean
  isCompleted: boolean
  onActivate: () => void
  stepNumber: number
}

export default function CollapsibleCheckoutSection({
  title,
  icon: Icon,
  children,
  className,
  isActive,
  isCompleted,
  onActivate,
  stepNumber
}: CollapsibleCheckoutSectionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // When isActive changes, update the expanded state
  useEffect(() => {
    setIsExpanded(isActive);
  }, [isActive]);

  const handleToggle = () => {
    if (!isActive) {
      onActivate();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div className={cn(
      "bg-white rounded-lg shadow-md mb-6 transition-all duration-200 overflow-hidden",
      isActive ? "border-l-4 border-emerald-500" : isCompleted ? "border-l-4 border-emerald-300" : "border border-gray-200",
      className
    )}>
      <div
        className={cn(
          "flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors",
          isActive ? "bg-white" : isCompleted ? "bg-emerald-50" : "bg-gray-50"
        )}
        onClick={handleToggle}
      >
        <div className="flex items-center">
          <div className={cn(
            "flex items-center justify-center w-8 h-8 rounded-full mr-3",
            isCompleted ? "bg-emerald-100 text-emerald-600" :
            isActive ? "bg-emerald-100 text-emerald-600" : "bg-gray-100 text-gray-500"
          )}>
            {isCompleted ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <div className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-200 text-gray-700 text-xs font-medium">
                {stepNumber}
              </div>
            )}
          </div>
          <div>
            <span className={cn(
              "font-semibold",
              isActive ? "text-emerald-800" : isCompleted ? "text-emerald-700" : "text-gray-700"
            )}>
              {title}
            </span>
            {isCompleted && !isActive && (
              <span className="ml-2 text-sm text-emerald-600 bg-emerald-50 px-2 py-0.5 rounded-full">Completed</span>
            )}
            {!isCompleted && !isActive && (
              <span className="ml-2 text-sm text-amber-600 bg-amber-50 px-2 py-0.5 rounded-full">Incomplete</span>
            )}
          </div>
        </div>
        <div className="flex items-center">
          {isCompleted && !isActive && (
            <CheckCircle className="h-5 w-5 text-emerald-500 mr-2" />
          )}
          {!isCompleted && !isActive && (
            <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
          )}
          <div className={cn(
            "flex items-center justify-center w-6 h-6 rounded-full",
            isActive ? "bg-emerald-100" : "bg-gray-100"
          )}>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-gray-600" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-600" />
            )}
          </div>
        </div>
      </div>

      <div className={cn(
        "transition-all duration-300 overflow-hidden",
        isExpanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="p-6 border-t border-gray-100">
          {!isCompleted && isActive && (
            <div className="mb-4 p-3 bg-amber-50 border border-amber-100 rounded-md text-amber-700 text-sm flex items-start">
              <AlertCircle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Please complete all required fields</p>
                <p className="text-amber-600 mt-1">Required fields are marked with an asterisk (*)</p>
              </div>
            </div>
          )}
          {children}
        </div>
      </div>
    </div>
  )
}
