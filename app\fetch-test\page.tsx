'use client'

import { useState, useEffect } from 'react'

export default function FetchTestPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      
      try {
        // Fetch data from a public API
        const response = await fetch('https://jsonplaceholder.typicode.com/users')
        
        if (!response.ok) {
          throw new Error(`API returned ${response.status}`)
        }
        
        const apiData = await response.json()
        setData(apiData)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Public API Fetch Test</h1>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      
      {data && (
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Users ({data.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.map((user: any) => (
              <div key={user.id} className="border p-4 rounded">
                <h3 className="font-bold">{user.name}</h3>
                <p className="text-sm text-gray-600">{user.email}</p>
                <p className="text-sm">{user.company.name}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
