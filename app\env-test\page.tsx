'use client'

import { useState, useEffect } from 'react'

export default function EnvTestPage() {
  const [envVars, setEnvVars] = useState<any>(null)

  useEffect(() => {
    // Get environment variables
    const vars = {
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
      NEXT_PUBLIC_SUPABASE_URL_LENGTH: process.env.NEXT_PUBLIC_SUPABASE_URL?.length || 0,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
        `Set (${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10)}...)` : 'Not set',
      NEXT_PUBLIC_SUPABASE_ANON_KEY_LENGTH: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
      NODE_ENV: process.env.NODE_ENV || 'Not set'
    }
    
    setEnvVars(vars)
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Environment Variables Test</h1>
      
      {envVars ? (
        <div className="border p-4 rounded bg-gray-50">
          <h2 className="text-xl font-semibold mb-2">Client-Side Environment Variables</h2>
          <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(envVars, null, 2)}
          </pre>
          
          <div className="mt-4">
            <h3 className="font-semibold">Fetch API Test</h3>
            <button 
              onClick={async () => {
                try {
                  const response = await fetch('/api/check-env')
                  const data = await response.json()
                  alert(JSON.stringify(data, null, 2))
                } catch (error) {
                  alert(`Error: ${error.message}`)
                }
              }}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Test Server Environment
            </button>
          </div>
        </div>
      ) : (
        <p>Loading environment variables...</p>
      )}
    </div>
  )
}
