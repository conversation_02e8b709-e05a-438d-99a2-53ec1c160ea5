"use client"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Check,
  ChevronDown,
  Clock,
  CreditCard,
  HelpCircle,
  Menu,
  Package,
  Printer,
  RefreshCcw,
  Settings,
  ShoppingCart,
  Truck,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { MultiBusinessStatus } from "@/components/orders/multi-business-status"
import { BusinessStatusBadge } from "@/components/orders/business-status-badge"
import { useRealtimeOrderStatus } from "@/hooks/use-realtime-order-status"

export default function OrderDetailsPage({ params }: { params: { id: string } }) {
  const { toast } = useToast()

  // Use our real-time hook to get order data with live updates
  const {
    order,
    loading: isLoading,
    error: orderError,
    refetch
  } = useRealtimeOrderStatus({
    orderId: params.id,
    onStatusChange: (updatedOrder) => {
      toast({
        title: "Order Updated",
        description: `Order status changed to ${updatedOrder.status}`,
        duration: 3000
      })
    }
  })

  // Set local error state from the hook's error
  const [error, setError] = useState<string | null>(orderError ? orderError.message : null)

  const handleBusinessStatusChange = async (businessId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/orders/${params.id}/business/${businessId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update status')
      }

      // No need to manually update the order data - the real-time subscription will handle it

      toast({
        title: "Status update sent",
        description: `Business status update to ${newStatus} is being processed`,
      })
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error updating status",
        description: error.message || "An unexpected error occurred",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <RefreshCcw className="h-8 w-8 animate-spin text-emerald-500 mb-4" />
        <p>Loading order details...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6 bg-red-50 rounded-lg">
          <h2 className="text-xl font-bold text-red-700 mb-2">Error Loading Order</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button asChild>
            <Link href="/orders">Back to Orders</Link>
          </Button>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6 bg-amber-50 rounded-lg">
          <h2 className="text-xl font-bold text-amber-700 mb-2">Order Not Found</h2>
          <p className="text-amber-600 mb-4">The requested order could not be found.</p>
          <Button asChild>
            <Link href="/orders">Back to Orders</Link>
          </Button>
        </div>
      </div>
    )
  }
  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold">
            <Package className="h-6 w-6 text-emerald-500" />
            <span className="text-lg">Loop</span>
          </div>
          <Button variant="outline" size="icon" className="ml-auto h-8 w-8 lg:hidden">
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
          <div className="ml-auto hidden items-center gap-4 lg:flex">
            <Button variant="outline" size="icon" className="rounded-full">
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>
            <Button variant="outline" size="icon" className="rounded-full">
              <HelpCircle className="h-4 w-4" />
              <span className="sr-only">Help</span>
            </Button>
            <Button variant="outline" size="sm" className="rounded-full">
              <img src="/placeholder-user.jpg" alt="Avatar" className="mr-2 h-5 w-5 rounded-full" />
              John Doe
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          <div className="flex items-center gap-4">
            <Link href="/orders">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Order #{params.id}</h1>
              <p className="text-muted-foreground">View and manage order details</p>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <Button variant="outline" size="sm" className="h-8">
                <Printer className="mr-2 h-3.5 w-3.5" />
                Print Invoice
              </Button>
              <Select defaultValue="delivered">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Update status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Order Status</SelectLabel>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="out-for-delivery">Out for Delivery</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Order Status</CardTitle>
                <Truck className="h-4 w-4 text-emerald-500" />
              </CardHeader>
              <CardContent>
                <BusinessStatusBadge status={order.status} />
                <p className="mt-2 text-xs text-muted-foreground">
                  {order.status === 'delivered'
                    ? `Delivered on ${new Date(order.updated_at).toLocaleDateString()} at ${new Date(order.updated_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
                    : `Last updated: ${new Date(order.updated_at).toLocaleDateString()}`
                  }
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-emerald-500"
                >
                  <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                </svg>
              </CardHeader>
              <CardContent>
                <Badge
                  variant="outline"
                  className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                >
                  Paid
                </Badge>
                <p className="mt-2 text-xs text-muted-foreground">Paid via Credit Card on June 12, 2023</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Delivery Time</CardTitle>
                <Clock className="h-4 w-4 text-emerald-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">25 min</div>
                <p className="text-xs text-muted-foreground">From order to delivery</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Order Total</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-emerald-500"
                >
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <path d="M2 10h20" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">£39.50</div>
                <p className="text-xs text-muted-foreground">3 items</p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="details" className="space-y-4">
            <TabsList>
              <TabsTrigger value="details">Order Details</TabsTrigger>
              <TabsTrigger value="items">Items</TabsTrigger>
              <TabsTrigger value="customer">Customer</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              {/* Note: order_businesses table has been removed */}

              <Card>
                <CardHeader>
                  <CardTitle>Order Information</CardTitle>
                  <CardDescription>Complete details about this order</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Order ID</h3>
                      <p className="mt-1">{order.order_id || order.id}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Date Placed</h3>
                      <p className="mt-1">{new Date(order.created_at).toLocaleDateString()} at {new Date(order.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Payment Method</h3>
                      <p className="mt-1">{order.payment_method}</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="mb-2 text-sm font-medium">Delivery Address</h3>
                    <div className="rounded-md border p-3">
                      <p className="font-medium">Jackson Davis</p>
                      <p className="text-sm text-muted-foreground">123 Main St, Apt 4B</p>
                      <p className="text-sm text-muted-foreground">London, SW1A 1AA</p>
                      <p className="text-sm text-muted-foreground">United Kingdom</p>
                      <p className="mt-2 text-sm">Phone: +44 20 1234 5678</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="mb-2 text-sm font-medium">Order Summary</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Subtotal</span>
                        <span>£35.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Delivery Fee</span>
                        <span>£2.50</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Tax</span>
                        <span>£2.00</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>Total</span>
                        <span>£39.50</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="items" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Order Items</CardTitle>
                  <CardDescription>Items included in this order</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between border-b pb-4">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-md bg-gray-100"></div>
                        <div>
                          <p className="font-medium">Premium Headphones</p>
                          <p className="text-sm text-muted-foreground">1 x £19.99</p>
                        </div>
                      </div>
                      <div className="font-medium">£19.99</div>
                    </div>
                    <div className="flex items-center justify-between border-b pb-4">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-md bg-gray-100"></div>
                        <div>
                          <p className="font-medium">Wireless Charger</p>
                          <p className="text-sm text-muted-foreground">1 x £9.99</p>
                        </div>
                      </div>
                      <div className="font-medium">£9.99</div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-md bg-gray-100"></div>
                        <div>
                          <p className="font-medium">USB-C Cable Pack</p>
                          <p className="text-sm text-muted-foreground">1 x £5.02</p>
                        </div>
                      </div>
                      <div className="font-medium">£5.02</div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">3 items</div>
                  <div className="font-medium">Subtotal: £35.00</div>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="customer" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>Details about the customer who placed this order</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Customer Name</h3>
                      <p className="mt-1">Jackson Davis</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Email Address</h3>
                      <p className="mt-1"><EMAIL></p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Phone Number</h3>
                      <p className="mt-1">+44 20 1234 5678</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Customer Since</h3>
                      <p className="mt-1">March 15, 2022</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="mb-2 text-sm font-medium">Order History</h3>
                    <div className="text-sm text-muted-foreground">
                      <p>Total Orders: 12</p>
                      <p>Total Spent: £345.67</p>
                      <p>Average Order Value: £28.81</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    View Customer Profile
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Order History</CardTitle>
                  <CardDescription>Timeline of events for this order</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex">
                      <div className="mr-4 flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-100">
                          <Check className="h-4 w-4 text-emerald-600" />
                        </div>
                        <div className="h-full w-px bg-border"></div>
                      </div>
                      <div className="pb-8">
                        <p className="font-medium">Order Delivered</p>
                        <p className="text-sm text-muted-foreground">June 12, 2023 at 11:15 AM</p>
                        <p className="mt-2 text-sm">Order was delivered successfully to the customer.</p>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="mr-4 flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                          <Truck className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="h-full w-px bg-border"></div>
                      </div>
                      <div className="pb-8">
                        <p className="font-medium">Out for Delivery</p>
                        <p className="text-sm text-muted-foreground">June 12, 2023 at 10:50 AM</p>
                        <p className="mt-2 text-sm">Order is out for delivery with courier.</p>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="mr-4 flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100">
                          <Package className="h-4 w-4 text-purple-600" />
                        </div>
                        <div className="h-full w-px bg-border"></div>
                      </div>
                      <div className="pb-8">
                        <p className="font-medium">Order Processed</p>
                        <p className="text-sm text-muted-foreground">June 12, 2023 at 10:40 AM</p>
                        <p className="mt-2 text-sm">Order has been processed and is ready for delivery.</p>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="mr-4 flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
                          <CreditCard className="h-4 w-4 text-yellow-600" />
                        </div>
                        <div className="h-full w-px bg-border"></div>
                      </div>
                      <div className="pb-8">
                        <p className="font-medium">Payment Confirmed</p>
                        <p className="text-sm text-muted-foreground">June 12, 2023 at 10:32 AM</p>
                        <p className="mt-2 text-sm">Payment of £39.50 has been confirmed.</p>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="mr-4 flex flex-col items-center">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                          <ShoppingCart className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium">Order Placed</p>
                        <p className="text-sm text-muted-foreground">June 12, 2023 at 10:30 AM</p>
                        <p className="mt-2 text-sm">Order #ORD-45678 was placed by Jackson Davis.</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
