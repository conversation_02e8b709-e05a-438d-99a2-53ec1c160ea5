"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import ProductItem from "@/components/product-item"
import type { ProductCategory as ProductCategoryType } from "@/types/shop"

interface ProductCategoryProps {
  category: ProductCategoryType
  shopId: string
}

export default function ProductCategory({ category, shopId }: ProductCategoryProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <h2 className="text-xl font-bold">{category.name}</h2>
          <Badge variant="outline" className="ml-2">
            {category.products.length} items
          </Badge>
        </div>
        <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </Button>
      </div>

      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {category.products.map((product) => (
            <ProductItem key={product.id} product={product} shopId={shopId} categoryId={category.id} />
          ))}
        </div>
      )}
    </div>
  )
}
