import { NextResponse } from "next/server"
import { adminClient } from "@/lib/supabase-admin"
import { createServerSupabase } from "@/lib/supabase-server"

// Types for our API
interface ConnectionUpdate {
  status?: 'pending' | 'active' | 'blocked' | 'archived'
  is_favorite?: boolean
  notes?: string
}

// Helper function to get authenticated user
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

// GET - Retrieve specific connection
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const connectionId = params.id

    // Fetch connection with user verification
    const { data: connection, error } = await adminClient
      .from('connections')
      .select(`
        id,
        connection_type,
        status,
        user1_favorite,
        user2_favorite,
        created_at,
        updated_at,
        notes,
        user1_id,
        user2_id
      `)
      .eq('id', connectionId)
      .or(`user1_id.eq.${user.id},user2_id.eq.${user.id}`)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: "Connection not found" },
          { status: 404 }
        )
      }
      console.error("Error fetching connection:", error)
      return NextResponse.json(
        { error: "Failed to fetch connection" },
        { status: 500 }
      )
    }

    // Transform connection data
    const isUser1 = connection.user1_id === user.id
    const response = {
      id: connection.id,
      connection_type: connection.connection_type,
      status: connection.status,
      other_user_id: isUser1 ? connection.user2_id : connection.user1_id,
      is_favorite: isUser1 ? connection.user1_favorite : connection.user2_favorite,
      created_at: connection.created_at,
      updated_at: connection.updated_at,
      notes: connection.notes
    }

    return NextResponse.json({ data: response })

  } catch (error) {
    console.error("Unexpected error in GET /api/connections-hub/connections/[id]:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT - Update connection
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const connectionId = params.id
    const body: ConnectionUpdate = await request.json()
    const { status, is_favorite, notes } = body

    // First, fetch the connection to verify ownership and get current state
    const { data: connection, error: fetchError } = await adminClient
      .from('connections')
      .select('*')
      .eq('id', connectionId)
      .or(`user1_id.eq.${user.id},user2_id.eq.${user.id}`)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: "Connection not found" },
          { status: 404 }
        )
      }
      console.error("Error fetching connection for update:", fetchError)
      return NextResponse.json(
        { error: "Failed to fetch connection" },
        { status: 500 }
      )
    }

    // Determine which user is making the update
    const isUser1 = connection.user1_id === user.id

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    // Handle status updates (both users can update status)
    if (status !== undefined) {
      const validStatuses = ['pending', 'active', 'blocked', 'archived']
      if (!validStatuses.includes(status)) {
        return NextResponse.json(
          { error: "Invalid status value" },
          { status: 400 }
        )
      }
      updateData.status = status
    }

    // Handle favorite updates (user-specific)
    if (is_favorite !== undefined) {
      if (isUser1) {
        updateData.user1_favorite = is_favorite
      } else {
        updateData.user2_favorite = is_favorite
      }
    }

    // Handle notes updates (both users can update notes)
    if (notes !== undefined) {
      updateData.notes = notes
    }

    // Update the connection
    const { data: updatedConnection, error: updateError } = await adminClient
      .from('connections')
      .update(updateData)
      .eq('id', connectionId)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating connection:", updateError)
      return NextResponse.json(
        { error: "Failed to update connection" },
        { status: 500 }
      )
    }

    // Transform response
    const response = {
      id: updatedConnection.id,
      connection_type: updatedConnection.connection_type,
      status: updatedConnection.status,
      other_user_id: isUser1 ? updatedConnection.user2_id : updatedConnection.user1_id,
      is_favorite: isUser1 ? updatedConnection.user1_favorite : updatedConnection.user2_favorite,
      created_at: updatedConnection.created_at,
      updated_at: updatedConnection.updated_at,
      notes: updatedConnection.notes
    }

    return NextResponse.json({
      data: response,
      message: "Connection updated successfully"
    })

  } catch (error) {
    console.error("Unexpected error in PUT /api/connections-hub/connections/[id]:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE - Remove connection
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const connectionId = params.id

    // Verify the connection exists and user has access
    const { data: connection, error: fetchError } = await adminClient
      .from('connections')
      .select('id, user1_id, user2_id')
      .eq('id', connectionId)
      .or(`user1_id.eq.${user.id},user2_id.eq.${user.id}`)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: "Connection not found" },
          { status: 404 }
        )
      }
      console.error("Error fetching connection for deletion:", fetchError)
      return NextResponse.json(
        { error: "Failed to fetch connection" },
        { status: 500 }
      )
    }

    // Delete the connection
    const { error: deleteError } = await adminClient
      .from('connections')
      .delete()
      .eq('id', connectionId)

    if (deleteError) {
      console.error("Error deleting connection:", deleteError)
      return NextResponse.json(
        { error: "Failed to delete connection" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: "Connection deleted successfully"
    })

  } catch (error) {
    console.error("Unexpected error in DELETE /api/connections-hub/connections/[id]:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
