-- Migration: Add hierarchical columns to business_custom_categories table
-- This migration adds level and parent_category columns to support aisle layout hierarchy

-- Step 1: Add the hierarchical columns to the business_custom_categories table
DO $$
BEGIN
  -- Add level column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'business_custom_categories' AND column_name = 'level'
  ) THEN
    ALTER TABLE business_custom_categories ADD COLUMN level INTEGER NOT NULL DEFAULT 0;
    COMMENT ON COLUMN business_custom_categories.level IS 'Category hierarchy level: 0 = top level (aisle), 1 = subcategory';
  END IF;

  -- Add parent_category column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'business_custom_categories' AND column_name = 'parent_category'
  ) THEN
    ALTER TABLE business_custom_categories ADD COLUMN parent_category VARCHAR(255) NULL;
    COMMENT ON COLUMN business_custom_categories.parent_category IS 'Name of parent category for level 1 categories (references name field of level 0 category)';
  END IF;
END $$;

-- Step 2: Add constraint to ensure level 1 categories have parent_category
ALTER TABLE business_custom_categories 
DROP CONSTRAINT IF EXISTS business_custom_categories_level_parent_check;

ALTER TABLE business_custom_categories 
ADD CONSTRAINT business_custom_categories_level_parent_check 
CHECK (
  (level = 0 AND parent_category IS NULL) OR 
  (level = 1 AND parent_category IS NOT NULL)
);

-- Step 3: Add constraint to ensure valid level values
ALTER TABLE business_custom_categories 
DROP CONSTRAINT IF EXISTS business_custom_categories_level_check;

ALTER TABLE business_custom_categories 
ADD CONSTRAINT business_custom_categories_level_check 
CHECK (level IN (0, 1));

-- Step 4: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_level ON business_custom_categories(level);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_parent_category ON business_custom_categories(parent_category);
CREATE INDEX IF NOT EXISTS idx_business_custom_categories_business_level ON business_custom_categories(business_id, level);

-- Step 5: Verify the updates
SELECT 
  table_name, 
  column_name, 
  data_type, 
  column_default, 
  is_nullable
FROM 
  information_schema.columns
WHERE 
  table_name = 'business_custom_categories' 
  AND column_name IN ('level', 'parent_category')
ORDER BY 
  table_name, 
  column_name;
