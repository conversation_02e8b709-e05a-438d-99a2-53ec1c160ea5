import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Get the category ID from the URL params
    const categoryId = parseInt(params.id)

    // Fetch the category
    const { data: category, error: categoryError } = await adminClient
      .from("categories")
      .select(`
        id,
        name,
        description,
        display_order,
        business_id,
        created_at,
        updated_at,
        products (
          id,
          name,
          description,
          price,
          image_url,
          is_available,
          is_featured,
          created_at,
          updated_at
        )
      `)
      .eq("id", categoryId)
      .single()

    if (categoryError) {
      console.error("Error fetching category:", categoryError)
      return NextResponse.json(
        { error: "Failed to fetch category" },
        { status: 500 }
      )
    }

    // Check if the category belongs to the user's business (for business managers)
    if (isBusinessManager && category.business_id !== businessId) {
      console.error("Category does not belong to the user's business")
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ category })
  } catch (error: any) {
    console.error(`Error in GET /api/business-admin/categories/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Get the category ID from the URL params
    const categoryId = parseInt(params.id)

    // Check if the category exists and belongs to the user's business (for business managers)
    if (isBusinessManager) {
      const { data: existingCategory, error: existingCategoryError } = await adminClient
        .from("categories")
        .select("id, business_id")
        .eq("id", categoryId)
        .single()

      if (existingCategoryError || !existingCategory) {
        console.error("Category not found:", existingCategoryError)
        return NextResponse.json(
          { error: "Category not found" },
          { status: 404 }
        )
      }

      if (existingCategory.business_id !== businessId) {
        console.error("Category does not belong to the user's business")
        return NextResponse.json(
          { error: "Category not found" },
          { status: 404 }
        )
      }
    }

    // Parse the request body
    const requestData = await request.json()
    const { name, description, display_order } = requestData

    // Update the category
    const { data: updatedCategory, error: updateError } = await adminClient
      .from("categories")
      .update({
        name,
        description,
        display_order,
        updated_at: new Date().toISOString()
      })
      .eq("id", categoryId)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating category:", updateError)
      return NextResponse.json(
        { error: "Failed to update category" },
        { status: 500 }
      )
    }

    return NextResponse.json({ category: updatedCategory })
  } catch (error: any) {
    console.error(`Error in PATCH /api/business-admin/categories/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Get the category ID from the URL params
    const categoryId = parseInt(params.id)

    // Check if the category exists and belongs to the user's business (for business managers)
    if (isBusinessManager) {
      const { data: existingCategory, error: existingCategoryError } = await adminClient
        .from("categories")
        .select("id, business_id")
        .eq("id", categoryId)
        .single()

      if (existingCategoryError || !existingCategory) {
        console.error("Category not found:", existingCategoryError)
        return NextResponse.json(
          { error: "Category not found" },
          { status: 404 }
        )
      }

      if (existingCategory.business_id !== businessId) {
        console.error("Category does not belong to the user's business")
        return NextResponse.json(
          { error: "Category not found" },
          { status: 404 }
        )
      }
    }

    // First, update any products in this category to have no category
    await adminClient
      .from("products")
      .update({ category_id: null })
      .eq("category_id", categoryId)

    // Delete the category
    const { error: deleteError } = await adminClient
      .from("categories")
      .delete()
      .eq("id", categoryId)

    if (deleteError) {
      console.error("Error deleting category:", deleteError)
      return NextResponse.json(
        { error: "Failed to delete category" },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error(`Error in DELETE /api/business-admin/categories/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
