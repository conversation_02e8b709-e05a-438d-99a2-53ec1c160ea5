"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Search,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Mail,
  Phone,
  ExternalLink,
  Star,
  Calendar,
  DollarSign,
  ShoppingBag,
  UserCircle,
} from "lucide-react"
import type { Customer } from "@/types/customer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { customers } from "./data/mock-customers"

export default function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recent")

  // Filter customers based on search query and status filter
  const filteredCustomers = customers.filter((customer) => {
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        customer.name.toLowerCase().includes(query) ||
        customer.email.toLowerCase().includes(query) ||
        customer.phone.includes(query) ||
        customer.address.toLowerCase().includes(query)
      )
    }

    // Filter by status
    if (statusFilter !== "all" && customer.status !== statusFilter) {
      return false
    }

    return true
  })

  // Sort customers
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    switch (sortBy) {
      case "recent":
        // Sort by most recent customer (higher ID is more recent in our mock data)
        return b.id - a.id
      case "name":
        return a.name.localeCompare(b.name)
      case "orders":
        return b.totalOrders - a.totalOrders
      case "spent":
        return b.totalSpent - a.totalSpent
      default:
        return 0
    }
  })

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "loyal":
        return <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100">Loyal</Badge>
      case "regular":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Regular</Badge>
      case "occasional":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Occasional</Badge>
      case "at-risk":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">At Risk</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Customers</h1>
          <p className="text-gray-500">Manage and view your customer information</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline">Export Data</Button>
          <Button className="bg-emerald-600 hover:bg-emerald-700">Send Bulk Email</Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search customers..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>Status</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Customers</SelectItem>
              <SelectItem value="loyal">Loyal</SelectItem>
              <SelectItem value="regular">Regular</SelectItem>
              <SelectItem value="occasional">Occasional</SelectItem>
              <SelectItem value="at-risk">At Risk</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center">
                <ChevronDown className="mr-2 h-4 w-4" />
                <span>Sort By</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="orders">Most Orders</SelectItem>
              <SelectItem value="spent">Highest Spend</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Customer List */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left font-medium py-3 px-4">Customer</th>
                  <th className="text-left font-medium py-3 px-4 hidden md:table-cell">Contact</th>
                  <th className="text-center font-medium py-3 px-4 hidden lg:table-cell">Status</th>
                  <th className="text-center font-medium py-3 px-4 hidden md:table-cell">Orders</th>
                  <th className="text-center font-medium py-3 px-4 hidden lg:table-cell">Total Spent</th>
                  <th className="text-center font-medium py-3 px-4">Last Order</th>
                  <th className="text-right font-medium py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedCustomers.map((customer) => (
                  <tr key={customer.id} className="border-b last:border-0 hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8 mr-3">
                          <AvatarImage src={customer.avatar} alt={customer.name} />
                          <AvatarFallback>
                            <UserCircle className="h-5 w-5 text-gray-500" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Link href={`/restaurant-admin/customers/${customer.id}`} className="hover:underline">
                            <p className="font-medium">{customer.name}</p>
                          </Link>
                          <p className="text-xs text-gray-500">Since {customer.customerSince}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 hidden md:table-cell">
                      <div>
                        <p className="flex items-center text-xs">
                          <Mail className="h-3 w-3 mr-1" />
                          {customer.email}
                        </p>
                        <p className="flex items-center text-xs mt-1">
                          <Phone className="h-3 w-3 mr-1" />
                          {customer.phone}
                        </p>
                      </div>
                    </td>
                    <td className="text-center py-3 px-4 hidden lg:table-cell">{getStatusBadge(customer.status)}</td>
                    <td className="text-center py-3 px-4 hidden md:table-cell">
                      <div className="flex items-center justify-center">
                        <ShoppingBag className="h-3 w-3 mr-1 text-gray-500" />
                        {customer.totalOrders}
                      </div>
                    </td>
                    <td className="text-center py-3 px-4 hidden lg:table-cell">
                      <div className="flex items-center justify-center">
                        <DollarSign className="h-3 w-3 mr-1 text-gray-500" />
                        £{customer.totalSpent.toFixed(2)}
                      </div>
                    </td>
                    <td className="text-center py-3 px-4">
                      <div className="flex items-center justify-center">
                        <Calendar className="h-3 w-3 mr-1 text-gray-500" />
                        {customer.lastOrder}
                      </div>
                    </td>
                    <td className="text-right py-3 px-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/restaurant-admin/customers/${customer.id}`} className="flex items-center w-full">
                              <ExternalLink className="mr-2 h-4 w-4" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <ShoppingBag className="mr-2 h-4 w-4" />
                            View Orders
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-6">
        <div className="text-sm text-gray-500">
          Showing <span className="font-medium">{sortedCustomers.length}</span> of{" "}
          <span className="font-medium">{customers.length}</span> customers
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" disabled>
            Previous
          </Button>
          <Button variant="outline" size="sm" disabled>
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
