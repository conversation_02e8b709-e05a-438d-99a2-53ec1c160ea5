"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import {
  ArrowLeft,
  MapPin,
  Clock,
  Phone,
  Star,
  Building2,
  Users,
  TrendingUp,
  DollarSign,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle,
  Send,
  Timer,
  Target,
  BarChart3,
  PieChart,
  Calendar,
  Truck,
  Award,
  Activity,
  MessageCircle
} from "lucide-react"

interface BusinessStats {
  approvedDrivers: number
  totalDrivers: number
  onTimePreparation: number
  averagePreparationTime: number
  averageOrderValue: number
  totalOrders: number
  monthlyOrders: number
  weeklyOrders: number
  ordersByValue: {
    under15: number
    between15and30: number
    between30and50: number
    over50: number
  }
  ordersByParish: {
    [parish: string]: number
  }
  peakHours: {
    [hour: string]: number
  }
  driverEarnings: {
    averagePerOrder: number
    averagePerHour: number
    topEarningDay: string
  }
  businessRating: number
  driverSatisfaction: number
  repeatCustomerRate: number
}

interface Business {
  id: number
  name: string
  slug: string
  business_type_id: number
  description: string
  address: string
  location: string
  logo_url: string
  phone: string
  delivery_radius: number
  minimum_order_amount: number
  delivery_fee: number
  preparation_time_minutes: number
  is_approved: boolean
  delivery_available: boolean
  business_types: {
    name: string
  }
  application_status: string | null
  application_date: string | null
  can_apply: boolean
}

// Mock data for demonstration
const mockStats: BusinessStats = {
  approvedDrivers: 12,
  totalDrivers: 18,
  onTimePreparation: 87,
  averagePreparationTime: 18,
  averageOrderValue: 28.50,
  totalOrders: 1247,
  monthlyOrders: 156,
  weeklyOrders: 38,
  ordersByValue: {
    under15: 25,
    between15and30: 45,
    between30and50: 22,
    over50: 8
  },
  ordersByParish: {
    "St Helier": 42,
    "St Brelade": 18,
    "St Clement": 15,
    "St Saviour": 12,
    "St Lawrence": 8,
    "Other": 5
  },
  peakHours: {
    "12:00": 15,
    "13:00": 22,
    "18:00": 28,
    "19:00": 35,
    "20:00": 25,
    "21:00": 18
  },
  driverEarnings: {
    averagePerOrder: 4.25,
    averagePerHour: 16.80,
    topEarningDay: "Friday"
  },
  businessRating: 4.3,
  driverSatisfaction: 4.1,
  repeatCustomerRate: 68
}

export default function BusinessDetailPage() {
  const params = useParams()
  const router = useRouter()
  const businessId = params.businessId as string
  const [business, setBusiness] = useState<Business | null>(null)
  const [stats, setStats] = useState<BusinessStats>(mockStats)
  const [loading, setLoading] = useState(true)
  const [showApplicationDialog, setShowApplicationDialog] = useState(false)
  const [applicationMessage, setApplicationMessage] = useState("")
  const [isApplying, setIsApplying] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchBusinessDetails()
  }, [businessId])

  const fetchBusinessDetails = async () => {
    try {
      setLoading(true)
      // For now, we'll fetch from the businesses list and find the matching one
      const response = await fetch('/api/driver/businesses?limit=100')
      const data = await response.json()

      if (data.success) {
        const foundBusiness = data.businesses.find((b: Business) => b.id.toString() === businessId)
        if (foundBusiness) {
          setBusiness(foundBusiness)
        } else {
          toast({
            variant: "destructive",
            title: "Business Not Found",
            description: "The requested business could not be found."
          })
          router.push('/driver/businesses')
        }
      }
    } catch (error) {
      console.error('Error fetching business details:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load business details"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApplyToBusiness = () => {
    setApplicationMessage("")
    setShowApplicationDialog(true)
  }

  const submitApplication = async () => {
    if (!business) return

    try {
      setIsApplying(true)
      const response = await fetch('/api/driver/apply-to-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          business_id: business.id,
          message: applicationMessage
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Application Submitted",
          description: data.message
        })
        setShowApplicationDialog(false)
        // Refresh business details to update application status
        fetchBusinessDetails()
      } else {
        toast({
          variant: "destructive",
          title: "Application Failed",
          description: data.error
        })
      }
    } catch (error) {
      console.error('Error submitting application:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to submit application"
      })
    } finally {
      setIsApplying(false)
    }
  }

  const getStatusBadge = () => {
    if (!business?.application_status) {
      return business?.can_apply ? (
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          <Building2 className="w-3 h-3 mr-1" />
          Can Apply
        </Badge>
      ) : null
    }

    switch (business.application_status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        )
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        )
      case 'banned':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Banned
          </Badge>
        )
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!business) {
    return (
      <div className="text-center py-12">
        <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Business not found</h3>
        <Button onClick={() => router.push('/driver/businesses')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Businesses
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push('/driver/businesses')}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-4">
            {business.logo_url && (
              <img
                src={business.logo_url}
                alt={business.name}
                className="w-16 h-16 rounded-lg object-cover"
              />
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{business.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">
                  <Building2 className="w-3 h-3 mr-1" />
                  {business.business_types?.name}
                </Badge>
                {getStatusBadge()}
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-3">
          <Link href={`/connections-hub?business_id=${business.id}&business_name=${encodeURIComponent(business.name)}`}>
            <Button variant="outline">
              <MessageCircle className="h-4 w-4 mr-2" />
              Connect
            </Button>
          </Link>
          {business.can_apply && business.delivery_available && (
            <Button onClick={handleApplyToBusiness} className="bg-green-600 hover:bg-green-700">
              <Send className="h-4 w-4 mr-2" />
              Apply
            </Button>
          )}
        </div>
      </div>

      {/* Business Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Business Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="h-4 w-4" />
                <span className="text-sm">Location</span>
              </div>
              <p className="font-medium">{business.address}</p>
              <p className="text-sm text-gray-500">{business.location}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Clock className="h-4 w-4" />
                <span className="text-sm">Preparation Time</span>
              </div>
              <p className="font-medium">{business.preparation_time_minutes} minutes</p>
              <p className="text-sm text-gray-500">Average: {stats.averagePreparationTime} min</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <DollarSign className="h-4 w-4" />
                <span className="text-sm">Order Details</span>
              </div>
              <p className="font-medium">£{business.minimum_order_amount} minimum</p>
              <p className="text-sm text-gray-500">Avg: £{stats.averageOrderValue}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Truck className="h-4 w-4" />
                <span className="text-sm">Delivery</span>
              </div>
              <p className="font-medium">{business.delivery_radius}km radius</p>
              <p className="text-sm text-gray-500">£{business.delivery_fee} fee</p>
            </div>
          </div>
          {business.description && (
            <div className="mt-6">
              <h4 className="font-medium mb-2">About</h4>
              <p className="text-gray-600">{business.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Driver Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              Driver Network
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.approvedDrivers}</div>
            <p className="text-sm text-gray-500">Approved drivers</p>
            <div className="mt-2">
              <Progress value={(stats.approvedDrivers / stats.totalDrivers) * 100} className="h-2" />
              <p className="text-xs text-gray-500 mt-1">{stats.totalDrivers} total applications</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Timer className="h-4 w-4 text-green-600" />
              On-Time Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.onTimePreparation}%</div>
            <p className="text-sm text-gray-500">Orders ready on time</p>
            <div className="mt-2">
              <Progress value={stats.onTimePreparation} className="h-2" />
              <p className="text-xs text-gray-500 mt-1">Based on {stats.monthlyOrders} monthly orders</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-emerald-600" />
              Driver Earnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">£{stats.driverEarnings.averagePerOrder}</div>
            <p className="text-sm text-gray-500">Average per order</p>
            <div className="mt-2 text-sm">
              <p className="text-gray-600">£{stats.driverEarnings.averagePerHour}/hour</p>
              <p className="text-xs text-gray-500">Best day: {stats.driverEarnings.topEarningDay}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-600" />
              Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.driverSatisfaction}</div>
            <p className="text-sm text-gray-500">Driver rating</p>
            <div className="mt-2 text-sm">
              <p className="text-gray-600">{stats.businessRating}★ customer rating</p>
              <p className="text-xs text-gray-500">{stats.repeatCustomerRate}% repeat customers</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Orders by Value
            </CardTitle>
            <CardDescription>Distribution of order values (last 30 days)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Under £15</span>
                <div className="flex items-center gap-2">
                  <Progress value={stats.ordersByValue.under15} className="w-20 h-2" />
                  <span className="text-sm font-medium">{stats.ordersByValue.under15}%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">£15 - £30</span>
                <div className="flex items-center gap-2">
                  <Progress value={stats.ordersByValue.between15and30} className="w-20 h-2" />
                  <span className="text-sm font-medium">{stats.ordersByValue.between15and30}%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">£30 - £50</span>
                <div className="flex items-center gap-2">
                  <Progress value={stats.ordersByValue.between30and50} className="w-20 h-2" />
                  <span className="text-sm font-medium">{stats.ordersByValue.between30and50}%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Over £50</span>
                <div className="flex items-center gap-2">
                  <Progress value={stats.ordersByValue.over50} className="w-20 h-2" />
                  <span className="text-sm font-medium">{stats.ordersByValue.over50}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Orders by Parish
            </CardTitle>
            <CardDescription>Delivery distribution across Jersey</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stats.ordersByParish).map(([parish, percentage]) => (
                <div key={parish} className="flex items-center justify-between">
                  <span className="text-sm">{parish}</span>
                  <div className="flex items-center gap-2">
                    <Progress value={percentage} className="w-20 h-2" />
                    <span className="text-sm font-medium">{percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Peak Hours & Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Peak Hours & Activity
          </CardTitle>
          <CardDescription>Order volume throughout the day - plan your schedule</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(stats.peakHours).map(([hour, orders]) => (
              <div key={hour} className="text-center">
                <div className="text-sm font-medium">{hour}</div>
                <div className="mt-1">
                  <Progress value={(orders / 35) * 100} className="h-2" />
                </div>
                <div className="text-xs text-gray-500 mt-1">{orders} orders</div>
              </div>
            ))}
          </div>
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.weeklyOrders}</div>
              <div className="text-sm text-gray-600">Orders this week</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.monthlyOrders}</div>
              <div className="text-sm text-gray-600">Orders this month</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.totalOrders}</div>
              <div className="text-sm text-gray-600">Total orders</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Application Dialog */}
      <Dialog open={showApplicationDialog} onOpenChange={setShowApplicationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Apply to {business.name}</DialogTitle>
            <DialogDescription>
              Submit your application to deliver for this business. Include any relevant information about your experience or availability.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Message (Optional)</label>
              <Textarea
                placeholder="Tell the business why you'd be a great delivery driver for them..."
                value={applicationMessage}
                onChange={(e) => setApplicationMessage(e.target.value)}
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowApplicationDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={submitApplication}
                disabled={isApplying}
                className="flex-1"
              >
                {isApplying ? "Submitting..." : "Submit Application"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
