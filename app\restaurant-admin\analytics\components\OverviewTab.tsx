"use client"

import {
  <PERSON><PERSON>pR<PERSON>,
  ArrowDownRight,
  DollarSign,
  ShoppingBag,
  Clock,
  Users,
  TrendingUp,
  Utensils,
  Star,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { salesData, orderTimeData } from "../data/mock-data"

export default function OverviewTab() {
  return (
    <>
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                8%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <h3 className="text-2xl font-bold">£3,426.00</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£246 from last period</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <ShoppingBag className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                12%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <h3 className="text-2xl font-bold">246</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+28 from last period</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                5%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">New Customers</p>
              <h3 className="text-2xl font-bold">38</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+6 from last period</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-amber-100 p-2 rounded-lg">
                <Utensils className="h-6 w-6 text-amber-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                3%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Avg. Order Value</p>
              <h3 className="text-2xl font-bold">£13.93</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£0.42 from last period</div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Trend Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Daily revenue for the past 30 days</CardDescription>
              </div>
              <Select defaultValue="revenue">
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="orders">Orders</SelectItem>
                  <SelectItem value="aov">Avg. Order Value</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for area chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[150px]">
                <svg width="100%" height="100%" viewBox="0 0 100 20" preserveAspectRatio="none">
                  <path
                    d="M0,10 L5,12 L10,8 L15,14 L20,9 L25,11 L30,7 L35,10 L40,5 L45,8 L50,4 L55,9 L60,6 L65,12 L70,7 L75,13 L80,5 L85,10 L90,6 L95,11 L100,3"
                    fill="none"
                    stroke="#10b981"
                    strokeWidth="0.5"
                  />
                  <path
                    d="M0,10 L5,12 L10,8 L15,14 L20,9 L25,11 L30,7 L35,10 L40,5 L45,8 L50,4 L55,9 L60,6 L65,12 L70,7 L75,13 L80,5 L85,10 L90,6 L95,11 L100,3 L100,20 L0,20 Z"
                    fill="url(#salesGradient)"
                    fillOpacity="0.5"
                  />
                  <defs>
                    <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#10b981" stopOpacity="0.8" />
                      <stop offset="100%" stopColor="#10b981" stopOpacity="0.1" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-4 text-xs text-gray-400">
                <span>Apr 1</span>
                <span>Apr 15</span>
                <span>Apr 30</span>
              </div>
              <div className="absolute top-2 right-2 text-xs text-gray-400">
                <span>Daily revenue trend</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>Key performance indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Order Acceptance Rate</p>
                  <p className="text-sm font-medium">98%</p>
                </div>
                <Progress value={98} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">On-Time Delivery</p>
                  <p className="text-sm font-medium">92%</p>
                </div>
                <Progress value={92} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Customer Satisfaction</p>
                  <p className="text-sm font-medium">4.8/5</p>
                </div>
                <Progress value={96} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Menu Item Availability</p>
                  <p className="text-sm font-medium">95%</p>
                </div>
                <Progress value={95} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Average Prep Time</p>
                  <p className="text-sm font-medium">18 min</p>
                </div>
                <Progress value={85} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Time Distribution & Top Menu Items */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Order Time Distribution</CardTitle>
            <CardDescription>When your customers are ordering</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for bar chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <Clock className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[150px] flex items-end justify-around px-4">
                {orderTimeData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div
                      className="w-6 bg-blue-500 rounded-t-sm"
                      style={{ height: `${(item.orders / 42) * 100}px` }}
                    />
                    {index % 2 === 0 && (
                      <div className="text-xs text-gray-400 mt-1">{item.hour}</div>
                    )}
                  </div>
                ))}
              </div>
              <div className="absolute top-2 right-2 text-xs text-gray-400">
                <span>Peak hours: 12-2 PM, 6-8 PM</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Top Menu Items</CardTitle>
              <CardDescription>Your best-selling dishes</CardDescription>
            </div>
            <Select defaultValue="quantity">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Select metric" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="quantity">By Quantity</SelectItem>
                <SelectItem value="revenue">By Revenue</SelectItem>
                <SelectItem value="rating">By Rating</SelectItem>
              </SelectContent>
            </Select>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-emerald-700 font-medium text-sm">1</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Jersey Crab Cakes</h4>
                    <p className="text-sm font-medium">32 orders</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                    </div>
                    <p className="text-xs text-gray-500">£9.95 · £318.40 total</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-emerald-700 font-medium text-sm">2</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Grilled Sea Bass</h4>
                    <p className="text-sm font-medium">28 orders</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current opacity-30" />
                    </div>
                    <p className="text-xs text-gray-500">£22.95 · £642.60 total</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-emerald-700 font-medium text-sm">3</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Jersey Beef Burger</h4>
                    <p className="text-sm font-medium">24 orders</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current opacity-30" />
                    </div>
                    <p className="text-xs text-gray-500">£14.95 · £358.80 total</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-emerald-700 font-medium text-sm">4</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Jersey Cream Brûlée</h4>
                    <p className="text-sm font-medium">22 orders</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                    </div>
                    <p className="text-xs text-gray-500">£7.95 · £174.90 total</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-emerald-700 font-medium text-sm">5</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Sautéed Jersey Scallops</h4>
                    <p className="text-sm font-medium">18 orders</p>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-amber-500 mr-2">
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current" />
                      <Star className="h-3 w-3 fill-current opacity-30" />
                    </div>
                    <p className="text-xs text-gray-500">£12.95 · £233.10 total</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Insights & Delivery Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Customer Insights</CardTitle>
            <CardDescription>Understanding your customer base</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium mb-3">Customer Retention</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-emerald-100 flex items-center justify-center">
                    <span className="text-emerald-700 font-bold text-xl">68%</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Returning customers</p>
                    <p className="text-xs text-emerald-600 flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 4% from last month
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-3">Average Rating</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-amber-100 flex items-center justify-center">
                    <span className="text-amber-700 font-bold text-xl">4.8</span>
                  </div>
                  <div>
                    <div className="flex items-center text-amber-500">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current opacity-30" />
                    </div>
                    <p className="text-xs text-emerald-600 flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 0.2 from last month
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-3">New vs. Returning</h4>
                <div className="h-[120px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
                  {/* Visual placeholder for pie chart */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative w-20 h-20">
                      <svg viewBox="0 0 36 36" className="w-full h-full">
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="3"
                          strokeDasharray="68, 100"
                        />
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#3b82f6"
                          strokeWidth="3"
                          strokeDasharray="32, 100"
                          strokeDashoffset="-68"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="absolute bottom-2 left-0 right-0 flex justify-around px-4 text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-1"></div>
                      <span>Returning (68%)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                      <span>New (32%)</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-3">Order Frequency</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span>Once</span>
                    <span>40%</span>
                  </div>
                  <Progress value={40} className="h-1.5" />

                  <div className="flex items-center justify-between text-xs">
                    <span>2-3 times</span>
                    <span>30%</span>
                  </div>
                  <Progress value={30} className="h-1.5" />

                  <div className="flex items-center justify-between text-xs">
                    <span>4-6 times</span>
                    <span>20%</span>
                  </div>
                  <Progress value={20} className="h-1.5" />

                  <div className="flex items-center justify-between text-xs">
                    <span>7+ times</span>
                    <span>10%</span>
                  </div>
                  <Progress value={10} className="h-1.5" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Delivery Performance</CardTitle>
            <CardDescription>Tracking delivery metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-3">Average Delivery Time</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-blue-700 font-bold text-xl">32<span className="text-sm">min</span></span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">From order to delivery</p>
                    <p className="text-xs text-red-600 flex items-center mt-1">
                      <ArrowDownRight className="h-3 w-3 mr-1" /> 2 min slower than target
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-100">
                <h4 className="text-sm font-medium mb-3">Delivery Status</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-emerald-50 p-3 rounded-lg">
                    <p className="text-emerald-700 font-bold text-xl">92%</p>
                    <p className="text-xs text-gray-500 mt-1">On Time</p>
                  </div>
                  <div className="bg-amber-50 p-3 rounded-lg">
                    <p className="text-amber-700 font-bold text-xl">5%</p>
                    <p className="text-xs text-gray-500 mt-1">Delayed</p>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg">
                    <p className="text-red-700 font-bold text-xl">3%</p>
                    <p className="text-xs text-gray-500 mt-1">Failed</p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-100">
                <h4 className="text-sm font-medium mb-3">Delivery Feedback</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
                      <span className="text-sm">Positive</span>
                    </div>
                    <span className="text-sm font-medium">78%</span>
                  </div>
                  <Progress value={78} className="h-2" />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                      <span className="text-sm">Neutral</span>
                    </div>
                    <span className="text-sm font-medium">15%</span>
                  </div>
                  <Progress value={15} className="h-2" />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-sm">Negative</span>
                    </div>
                    <span className="text-sm font-medium">7%</span>
                  </div>
                  <Progress value={7} className="h-2" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
