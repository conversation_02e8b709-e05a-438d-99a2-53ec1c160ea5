import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json(
    {
      error: 'Supabase not configured',
      message: 'The Supabase environment variables are not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.',
      documentation: '/supabase-setup',
    },
    { status: 503 }
  );
}

export async function POST() {
  return NextResponse.json(
    {
      error: 'Supabase not configured',
      message: 'The Supabase environment variables are not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.',
      documentation: '/supabase-setup',
    },
    { status: 503 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      error: 'Supabase not configured',
      message: 'The Supabase environment variables are not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.',
      documentation: '/supabase-setup',
    },
    { status: 503 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      error: 'Supabase not configured',
      message: 'The Supabase environment variables are not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.',
      documentation: '/supabase-setup',
    },
    { status: 503 }
  );
}
