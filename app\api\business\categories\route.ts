import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in business/categories API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in business/categories API")
      return { authorized: true };
    } else {
      return { 
        authorized: false, 
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in business/categories API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in business/categories API:", error)
      return { 
        authorized: false, 
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in business/categories API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in business/categories API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return { 
        authorized: false, 
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in business/categories API")
      return { authorized: true };
    }
  }
}

// GET endpoint to fetch categories
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the URL parameters
    const url = new URL(request.url);
    const businessTypeId = url.searchParams.get('businessTypeId');

    // Create a query builder
    let query = supabase
      .from("categories")
      .select("id, name, business_type_id");

    // Apply filters
    if (businessTypeId) {
      query = query.eq('business_type_id', businessTypeId);
    }

    // Order by name
    query = query.order('name');

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      return NextResponse.json(
        { error: 'Failed to fetch categories', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: data || [],
      success: true
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
