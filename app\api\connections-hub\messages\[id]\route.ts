import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface UpdateMessageRequest {
  is_read?: boolean
  content?: string
  subject?: string
}

// GET - Get specific message
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { id: messageId } = await params

    // Fetch message with user verification
    const { data: message, error } = await supabase
      .from('communications')
      .select(`
        id,
        sender_id,
        recipient_id,
        connection_id,
        order_id,
        business_id,
        channel_type,
        message_type,
        subject,
        content,
        thread_id,
        parent_message_id,
        is_read,
        is_urgent,
        is_automated,
        priority,
        created_at,
        read_at,
        expires_at
      `)
      .eq('id', messageId)
      .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Message not found' },
          { status: 404 }
        )
      }
      console.error('Error fetching message:', error)
      return NextResponse.json(
        { error: 'Failed to fetch message' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data: message })

  } catch (error: any) {
    console.error('Error in message GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT - Update message (mainly for marking as read)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      console.error('PUT /api/connections-hub/messages/[id] - Access denied:', {
        error: accessCheck.error,
        status: accessCheck.status,
        authHeader: request.headers.get('authorization') ? 'present' : 'missing'
      })
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { id: messageId } = await params
    const body: UpdateMessageRequest = await request.json()

    // First verify the message exists and user has access
    const { data: existingMessage, error: fetchError } = await supabase
      .from('communications')
      .select('id, sender_id, recipient_id, is_read')
      .eq('id', messageId)
      .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Message not found' },
          { status: 404 }
        )
      }
      console.error('Error fetching message for update:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch message' },
        { status: 500 }
      )
    }

    // Prepare update data
    const updateData: any = {}

    if (body.is_read !== undefined) {
      // Only recipient can mark as read
      if (existingMessage.recipient_id !== user.id) {
        return NextResponse.json(
          { error: 'Only the recipient can mark messages as read' },
          { status: 403 }
        )
      }
      updateData.is_read = body.is_read
      if (body.is_read) {
        updateData.read_at = new Date().toISOString()
      }
    }

    if (body.content !== undefined) {
      // Only sender can edit content (within reasonable time limit)
      if (existingMessage.sender_id !== user.id) {
        return NextResponse.json(
          { error: 'Only the sender can edit message content' },
          { status: 403 }
        )
      }
      updateData.content = body.content
      updateData.updated_at = new Date().toISOString()
    }

    if (body.subject !== undefined) {
      // Only sender can edit subject
      if (existingMessage.sender_id !== user.id) {
        return NextResponse.json(
          { error: 'Only the sender can edit message subject' },
          { status: 403 }
        )
      }
      updateData.subject = body.subject
      updateData.updated_at = new Date().toISOString()
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    // Update the message
    const { data: updatedMessage, error: updateError } = await supabase
      .from('communications')
      .update(updateData)
      .eq('id', messageId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating message:', updateError)
      return NextResponse.json(
        { error: 'Failed to update message' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Message updated successfully',
      data: updatedMessage
    })

  } catch (error: any) {
    console.error('Error in message PUT:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete message (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { id: messageId } = await params

    // First verify the message exists and user is the sender
    const { data: existingMessage, error: fetchError } = await supabase
      .from('communications')
      .select('id, sender_id, deleted_at')
      .eq('id', messageId)
      .eq('sender_id', user.id) // Only sender can delete
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Message not found or you do not have permission to delete it' },
          { status: 404 }
        )
      }
      console.error('Error fetching message for deletion:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch message' },
        { status: 500 }
      )
    }

    if (existingMessage.deleted_at) {
      return NextResponse.json(
        { error: 'Message is already deleted' },
        { status: 400 }
      )
    }

    // Soft delete the message
    const { error: deleteError } = await supabase
      .from('communications')
      .update({
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', messageId)

    if (deleteError) {
      console.error('Error deleting message:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete message' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Message deleted successfully'
    })

  } catch (error: any) {
    console.error('Error in message DELETE:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
