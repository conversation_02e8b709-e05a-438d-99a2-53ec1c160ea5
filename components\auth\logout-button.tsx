"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/context/unified-auth-context"

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  children?: React.ReactNode
}

export default function LogoutButton({
  variant = "default",
  size = "default",
  className = "",
  children,
}: LogoutButtonProps) {
  const router = useRouter()
  const { signOut } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  const handleLogout = async () => {
    setIsLoading(true)
    try {
      console.log("Logout button: Starting sign-out process")

      // Use both the context signOut and our direct API signOut for redundancy
      try {
        await signOut()
        console.log("Logout button: Context signOut completed")
      } catch (contextError) {
        console.warn("Logout button: Error with context signOut:", contextError)
      }

      // Always use our direct API signOut as a backup
      await directSignOut()
      console.log("Logout button: Direct API signOut completed")

      // Redirect to home page
      console.log("Logout button: Sign-out completed, redirecting to home page")
      window.location.href = "/"
    } catch (error) {
      console.error("Logout button: Error signing out:", error)
      setIsLoading(false)

      // Try to redirect anyway in case of error
      window.location.href = "/"
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleLogout}
      disabled={isLoading}
    >
      {isLoading ? (
        "Signing out..."
      ) : (
        <>
          {children || (
            <>
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </>
          )}
        </>
      )}
    </Button>
  )
}
