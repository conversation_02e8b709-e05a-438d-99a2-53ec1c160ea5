"use client"

import React, { useState, useEffect, use } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Check, X, AlertTriangle, MapPin, Phone, Mail, Calendar, Clock, DollarSign, Tag } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/components/ui/use-toast"
import Link from "next/link"

interface BusinessDetails {
  id: number
  name: string
  description: string
  address: string
  postcode: string
  phone: string
  business_type_id: number
  business_type: string
  created_at: string
  updated_at: string
  slug: string
  logo_url: string | null
  banner_url: string | null
  delivery_fee: number | null
  minimum_order_amount: number | null
  is_approved: boolean | null
  categories: Array<{
    id: number
    name: string
    slug: string
    is_primary: boolean
  }> | null
  managers: Array<{
    id: number
    user_id: number
    is_primary: boolean
    name: string
    email: string
  }> | null
  staff: Array<{
    id: number
    user_id: number
    role: string
    permissions: any
    is_active: boolean
    name: string
    email: string
  }> | null
  product_count: number
  average_rating: number | null
  review_count: number
}

export default function BusinessDetailsPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const id = unwrappedParams.id;
  const businessId = parseInt(id, 10);

  const router = useRouter()
  const { toast } = useToast()
  const [business, setBusiness] = useState<BusinessDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isApproving, setIsApproving] = useState(false)
  const [isRejecting, setIsRejecting] = useState(false)

  // Fetch business details on component mount
  useEffect(() => {
    if (businessId) {
      console.log("Fetching business details for ID:", businessId);
      fetchBusinessDetails();
    }
  }, [])

  const fetchBusinessDetails = async () => {
    console.log("Starting fetchBusinessDetails for ID:", businessId);
    setLoading(true)
    setError(null)

    try {
      if (isNaN(businessId)) {
        throw new Error(`Invalid business ID: ${id}`);
      }

      console.log("Fetching business with ID:", businessId);

      // Use the server-side API endpoint to fetch business details
      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        // Add cache: 'no-store' to prevent caching issues
        cache: 'no-store',
        // Add credentials to ensure cookies are sent
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));

        // Handle specific error cases
        if (response.status === 401) {
          // Redirect to login if unauthorized
          router.push('/login');
          throw new Error("Authentication required. Please log in.");
        } else if (response.status === 403) {
          // Handle forbidden errors (likely permission issues)
          throw new Error(errorData.error || "You don't have permission to view this business");
        } else {
          throw new Error(errorData.error || `Failed to fetch business details: ${response.statusText}`);
        }
      }

      const businessData = await response.json();

      if (!businessData) {
        throw new Error(`Business with ID ${businessId} not found`);
      }

      console.log("Business data from API:", businessData);

      // Set the business data
      setBusiness(businessData);
    } catch (err: any) {
      console.error("Error fetching business details:", err)
      setError(err.message || "Failed to load business details")
    } finally {
      setLoading(false)
    }
  }



  const approveBusiness = async () => {
    if (!business) return

    // Validate that the business has at least one manager
    if (!business.managers || business.managers.length === 0) {
      toast({
        title: "Approval Failed",
        description: "Business must have at least one manager before it can be approved.",
        variant: "destructive",
      });
      return;
    }

    // Check if business has all required fields
    const requiredFields = [
      { field: 'name', label: 'Business Name' },
      { field: 'address', label: 'Address' },
      { field: 'postcode', label: 'Postcode' },
      { field: 'phone', label: 'Phone Number' },
      { field: 'business_type_id', label: 'Business Type' }
    ];

    const missingFields = requiredFields.filter(
      ({ field }) => !business[field as keyof BusinessDetails]
    );

    if (missingFields.length > 0) {
      toast({
        title: "Approval Failed",
        description: `Business is missing required fields: ${missingFields.map(f => f.label).join(', ')}`,
        variant: "destructive",
      });
      return;
    }

    setIsApproving(true)
    try {
      console.log("Approving business with ID:", businessId);

      // Use the server-side API endpoint to approve the business
      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_approved: true
        }),
        cache: 'no-store',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to approve business: ${response.statusText}`);
      }

      const result = await response.json();

      toast({
        title: "Business Approved",
        description: `${business.name} has been approved successfully.`,
        variant: "default",
      })

      // Update local state
      setBusiness({
        ...business,
        is_approved: true
      })

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh()
      }, 1500)
    } catch (err: any) {
      console.error("Error approving business:", err)
      toast({
        title: "Approval Failed",
        description: err.message || "An error occurred while approving the business.",
        variant: "destructive",
      })
    } finally {
      setIsApproving(false)
    }
  }

  const rejectBusiness = async () => {
    if (!business) return

    if (!confirm(`Are you sure you want to reject ${business.name}?`)) {
      return
    }

    setIsRejecting(true)
    try {
      console.log("Rejecting business with ID:", businessId);

      // Use the server-side API endpoint to reject the business
      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_approved: false
        }),
        cache: 'no-store',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to reject business: ${response.statusText}`);
      }

      const result = await response.json();

      toast({
        title: "Business Rejected",
        description: `${business.name} has been rejected.`,
        variant: "default",
      })

      // Update local state
      setBusiness({
        ...business,
        is_approved: false
      })

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh()
      }, 1500)
    } catch (err: any) {
      console.error("Error rejecting business:", err)
      toast({
        title: "Rejection Failed",
        description: err.message || "An error occurred while rejecting the business.",
        variant: "destructive",
      })
    } finally {
      setIsRejecting(false)
    }
  }

  if (loading) {
    return <div className="p-8 text-center">Loading business details...</div>
  }

  if (error) {
    return <div className="p-8 text-center text-red-500">Error: {error}</div>
  }

  if (!business) {
    return <div className="p-8 text-center">Business not found</div>
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link href="/admin/businesses" className="mr-4">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">{business.name}</h1>
          <Badge
            className="ml-4"
            variant={
              business.is_approved === true
                ? "success"
                : business.is_approved === false
                  ? "destructive"
                  : "outline"
            }
          >
            {business.is_approved === true
              ? "Approved"
              : business.is_approved === false
                ? "Rejected"
                : "Pending"}
          </Badge>
        </div>

        <div className="flex space-x-2">
          {business.is_approved !== true && (
            <Button
              onClick={approveBusiness}
              disabled={isApproving}
              className="bg-green-600 hover:bg-green-700"
            >
              <Check className="mr-2 h-4 w-4" />
              {isApproving ? "Approving..." : "Approve Business"}
            </Button>
          )}

          {business.is_approved !== false && (
            <Button
              onClick={rejectBusiness}
              disabled={isRejecting}
              variant="destructive"
            >
              <X className="mr-2 h-4 w-4" />
              {isRejecting ? "Rejecting..." : "Reject Business"}
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="mb-6">
          <TabsTrigger value="details">Business Details</TabsTrigger>
          <TabsTrigger value="managers">Managers & Staff</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
                <CardDescription>Basic details about the business</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Description</h3>
                  <p className="mt-1">{business.description || "No description provided"}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <MapPin className="mr-1 h-4 w-4" /> Address
                    </h3>
                    <p className="mt-1">{business.address}</p>
                    <p>{business.postcode}</p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <Phone className="mr-1 h-4 w-4" /> Contact
                    </h3>
                    <p className="mt-1">{business.phone}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <Tag className="mr-1 h-4 w-4" /> Business Type
                    </h3>
                    <p className="mt-1">{business.business_type}</p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <Calendar className="mr-1 h-4 w-4" /> Created
                    </h3>
                    <p className="mt-1">{new Date(business.created_at).toLocaleDateString('en-GB')}</p>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <DollarSign className="mr-1 h-4 w-4" /> Delivery Fee
                    </h3>
                    <p className="mt-1">£{business.delivery_fee?.toFixed(2) || "0.00"}</p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm text-gray-500 flex items-center">
                      <DollarSign className="mr-1 h-4 w-4" /> Minimum Order
                    </h3>
                    <p className="mt-1">£{business.minimum_order_amount?.toFixed(2) || "0.00"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Business Stats</CardTitle>
                <CardDescription>Performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Products</h3>
                  <p className="text-2xl font-bold mt-1">{business.product_count || 0}</p>
                </div>

                <div>
                  <h3 className="font-medium text-sm text-gray-500">Reviews</h3>
                  <p className="text-2xl font-bold mt-1">{business.review_count || 0}</p>
                </div>

                <div>
                  <h3 className="font-medium text-sm text-gray-500">Average Rating</h3>
                  <p className="text-2xl font-bold mt-1">
                    {business.average_rating ? business.average_rating.toFixed(1) : "N/A"}
                  </p>
                </div>

                <div>
                  <h3 className="font-medium text-sm text-gray-500">Slug</h3>
                  <p className="mt-1 text-sm font-mono bg-gray-100 p-2 rounded">{business.slug}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="managers">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Managers</CardTitle>
                <CardDescription>People who manage this business</CardDescription>
              </CardHeader>
              <CardContent>
                {business.managers && business.managers.length > 0 ? (
                  <div className="space-y-4">
                    {business.managers.map((manager) => (
                      <div key={manager.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <div>
                          <p className="font-medium">{manager.name}</p>
                          <p className="text-sm text-gray-500">{manager.email}</p>
                        </div>
                        {manager.is_primary && (
                          <Badge>Primary</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No managers found</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Business Staff</CardTitle>
                <CardDescription>Staff members for this business</CardDescription>
              </CardHeader>
              <CardContent>
                {business.staff && business.staff.length > 0 ? (
                  <div className="space-y-4">
                    {business.staff.map((staff) => (
                      <div key={staff.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <div>
                          <p className="font-medium">{staff.name}</p>
                          <p className="text-sm text-gray-500">{staff.email}</p>
                          <p className="text-xs text-gray-400">Role: {staff.role}</p>
                        </div>
                        {!staff.is_active && (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No staff members found</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <CardTitle>Business Categories</CardTitle>
              <CardDescription>Categories this business belongs to</CardDescription>
            </CardHeader>
            <CardContent>
              {business.categories && business.categories.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {business.categories.map((category) => (
                    <div key={category.id} className="flex items-center">
                      <Badge variant={category.is_primary ? "default" : "outline"} className="mr-2">
                        {category.name}
                      </Badge>
                      {category.is_primary && (
                        <span className="text-xs text-gray-500">(Primary)</span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No categories assigned</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
