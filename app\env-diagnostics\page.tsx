'use client'

import { useState, useEffect } from 'react'
import { checkSupabaseEnv } from '@/lib/env-checker'
import { createClient } from '@supabase/supabase-js'

export default function EnvDiagnosticsPage() {
  const [serverEnv, setServerEnv] = useState<any>(null)
  const [clientEnv, setClientEnv] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [testResults, setTestResults] = useState<any>(null)

  useEffect(() => {
    async function fetchServerEnv() {
      try {
        const response = await fetch('/api/debug-env')
        const data = await response.json()
        return data
      } catch (error) {
        console.error('Error fetching server environment:', error)
        return { error: 'Failed to fetch server environment' }
      }
    }

    function getClientEnv() {
      return {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
          `Set (${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10)}...)` : 'Not set',
        NEXT_PUBLIC_SUPABASE_ANON_KEY_LENGTH: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
        NODE_ENV: process.env.NODE_ENV || 'Not set',
        RUNTIME: typeof window === 'undefined' ? 'server' : 'client',
        ...checkSupabaseEnv()
      }
    }

    async function testSupabaseClient() {
      const results = {
        anonClient: { success: false, error: '' },
        serviceClient: { success: false, error: '' }
      }

      // Test anon client
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
        
        if (supabaseUrl && supabaseAnonKey) {
          const anonClient = createClient(supabaseUrl, supabaseAnonKey)
          await anonClient.from('businesses').select('count(*)').limit(1)
          results.anonClient.success = true
        } else {
          results.anonClient.error = 'Missing URL or anon key'
        }
      } catch (error: any) {
        results.anonClient.error = error.message || 'Unknown error'
      }

      // Get server-side test results for service role client
      try {
        const response = await fetch('/api/debug-env')
        const data = await response.json()
        results.serviceClient.success = !data.clientCreationError
        results.serviceClient.error = data.clientCreationError || ''
      } catch (error: any) {
        results.serviceClient.error = error.message || 'Failed to test service client'
      }

      return results
    }

    async function loadData() {
      setIsLoading(true)
      
      const [serverData, testData] = await Promise.all([
        fetchServerEnv(),
        testSupabaseClient()
      ])
      
      setServerEnv(serverData)
      setClientEnv(getClientEnv())
      setTestResults(testData)
      setIsLoading(false)
    }
    
    loadData()
  }, [])
  
  if (isLoading) {
    return <div className="p-8">Loading environment diagnostics...</div>
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Environment Variables Diagnostics</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Client-Side Environment</h2>
          <pre className="bg-white p-4 rounded overflow-auto text-sm h-64">
            {JSON.stringify(clientEnv, null, 2)}
          </pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Server-Side Environment</h2>
          <pre className="bg-white p-4 rounded overflow-auto text-sm h-64">
            {JSON.stringify(serverEnv, null, 2)}
          </pre>
        </div>
      </div>
      
      <div className="bg-gray-100 p-4 rounded mb-8">
        <h2 className="text-xl font-semibold mb-2">Supabase Client Tests</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={`p-4 rounded ${testResults?.anonClient.success ? 'bg-green-100' : 'bg-red-100'}`}>
            <h3 className="font-semibold">Anon Client Test</h3>
            <p>{testResults?.anonClient.success ? 'Success' : `Failed: ${testResults?.anonClient.error}`}</p>
          </div>
          
          <div className={`p-4 rounded ${testResults?.serviceClient.success ? 'bg-green-100' : 'bg-red-100'}`}>
            <h3 className="font-semibold">Service Role Client Test</h3>
            <p>{testResults?.serviceClient.success ? 'Success' : `Failed: ${testResults?.serviceClient.error}`}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl font-semibold mb-2">Troubleshooting Steps</h2>
        
        <ol className="list-decimal pl-6 space-y-2">
          <li>Check that your <code>.env.local</code> file exists and contains the required variables</li>
          <li>Verify that the environment variables have the correct values from your Supabase project</li>
          <li>Restart the development server completely</li>
          <li>Try clearing your browser cache or using incognito mode</li>
          <li>Check if your Supabase project is active and accessible</li>
        </ol>
      </div>
    </div>
  )
}
