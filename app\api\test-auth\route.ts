import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    const { email, password, name } = await request.json();

    console.log("Testing auth user creation with:", { email, name });
    console.log("Supabase URL:", supabaseUrl);
    console.log("Service key exists:", !!supabaseServiceKey);
    console.log("Service key length:", supabaseServiceKey?.length);

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Test 1: Try admin.createUser
    console.log("Test 1: Trying admin.createUser...");
    try {
      const { data: adminData, error: adminError } = await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: { name, role: "business_manager" }
      });

      if (adminError) {
        console.log("Admin createUser failed:", adminError);
        console.log("Admin error details:", JSON.stringify(adminError, null, 2));
      } else {
        console.log("Admin createUser succeeded:", adminData?.user?.id);
        return NextResponse.json({
          success: true,
          method: "admin.createUser",
          userId: adminData?.user?.id
        });
      }
    } catch (adminException: any) {
      console.log("Admin createUser exception:", adminException);
      console.log("Admin exception details:", JSON.stringify(adminException, null, 2));
    }

    // Test 2: Try regular signUp
    console.log("Test 2: Trying regular signUp...");
    try {
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { name, role: "business_manager" }
        }
      });

      if (signUpError) {
        console.log("SignUp failed:", signUpError);
        console.log("SignUp error details:", JSON.stringify(signUpError, null, 2));
      } else {
        console.log("SignUp succeeded:", signUpData?.user?.id);
        return NextResponse.json({
          success: true,
          method: "signUp",
          userId: signUpData?.user?.id
        });
      }
    } catch (signUpException: any) {
      console.log("SignUp exception:", signUpException);
      console.log("SignUp exception details:", JSON.stringify(signUpException, null, 2));
    }

    return NextResponse.json({
      success: false,
      error: "Both admin.createUser and signUp failed",
      details: "Check server logs for detailed error information"
    }, { status: 500 });

  } catch (error: any) {
    console.error("Test auth error:", error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
