import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Get Supabase URL and keys from environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    // Check if environment variables are set
    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
      return NextResponse.json({
        error: 'Supabase environment variables not set',
        environment: {
          supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
          supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Not set',
          supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        }
      }, { status: 500 });
    }
    
    // Create Supabase clients
    const anonClient = createClient(supabaseUrl, supabaseAnonKey);
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test anon client connection
    const { data: anonData, error: anonError } = await anonClient
      .from('business_types')
      .select('count(*)', { count: 'exact', head: true });
    
    // Test admin client connection
    const { data: adminData, error: adminError } = await adminClient
      .from('business_types')
      .select('count(*)', { count: 'exact', head: true });
    
    // Return results
    return NextResponse.json({
      status: 'Supabase connection check',
      timestamp: new Date().toISOString(),
      environment: {
        supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 15)}...` : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 5)}...` : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? `${supabaseServiceKey.substring(0, 5)}...` : 'Not set',
        nodeEnv: process.env.NODE_ENV,
      },
      anonClient: {
        success: !anonError,
        error: anonError ? anonError.message : null,
        data: anonData,
      },
      adminClient: {
        success: !adminError,
        error: adminError ? adminError.message : null,
        data: adminData,
      }
    });
  } catch (error) {
    console.error('Error in check-supabase API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
