"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import {
  ArrowDown,
  ArrowUp,
  BarChart3,
  Calendar,
  CreditCard,
  Download,
  LineChart,
  Package2,
  PieChart,
  Search,
  ShoppingBag,
  Store,
  TrendingUp,
  Users,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/context/unified-auth-context"

// Driver Analytics Component
function DriverAnalyticsTab() {
  const [driverAnalytics, setDriverAnalytics] = useState<{ activeDrivers: number } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDriverAnalytics = async () => {
      try {
        const response = await fetch('/api/admin/analytics/drivers')

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.success) {
          setDriverAnalytics(result.data)
        } else {
          throw new Error(result.error || 'Failed to fetch driver analytics')
        }
      } catch (err) {
        console.error('Error fetching driver analytics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load driver analytics')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDriverAnalytics()
  }, [])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Driver Analytics</CardTitle>
          <CardDescription>Performance metrics for drivers on the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Driver Analytics</CardTitle>
          <CardDescription>Performance metrics for drivers on the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">Error: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Driver Analytics</CardTitle>
        <CardDescription>Performance metrics for drivers on the platform</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* Active Drivers Card */}
          <div className="rounded-lg border p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Active Drivers</span>
            </div>
            <div className="mt-2 text-2xl font-bold">{driverAnalytics?.activeDrivers || 0}</div>
            <div className="mt-1 text-xs text-muted-foreground">
              Verified and active drivers
            </div>
          </div>

          {/* Placeholder for future metrics */}
          <div className="rounded-lg border p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Coming Soon</span>
            </div>
            <div className="mt-2 text-2xl font-bold">-</div>
            <div className="mt-1 text-xs text-muted-foreground">
              More metrics coming soon
            </div>
          </div>

          <div className="rounded-lg border p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Coming Soon</span>
            </div>
            <div className="mt-2 text-2xl font-bold">-</div>
            <div className="mt-1 text-xs text-muted-foreground">
              More metrics coming soon
            </div>
          </div>
        </div>

        <div className="mt-6 text-center py-4">
          <p className="text-sm text-muted-foreground">
            Driver performance analytics and shift tracking metrics will be added here.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export default function AdminAnalyticsPage() {
  const router = useRouter()
  const { user, isAdmin, isLoading: authLoading } = useAuth()
  const [authChecked, setAuthChecked] = useState(false)
  const [dateRange, setDateRange] = useState("7d")

  // First, check authentication before showing any data
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!authLoading && (!user || !isAdmin)) {
      console.log("AdminAnalyticsPage: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin-new/analytics")
      return
    }

    // If user is admin, mark auth as checked
    if (!authLoading && user && isAdmin) {
      console.log("AdminAnalyticsPage: User is authorized, proceeding to fetch data")
      setAuthChecked(true)
    }
  }, [user, isAdmin, authLoading, router])

  // Show loading state if auth is not checked
  if (!authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    )
  }

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  return (
    <>
      {/* Header */}
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background px-4 sm:px-6">
        <div className="flex flex-1 items-center gap-4">
          <h1 className="text-xl font-semibold">Analytics Dashboard</h1>
        </div>
        <div className="flex items-center gap-4">
          <Select defaultValue={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Date Range</SelectLabel>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="12m">Last 12 months</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" className="hidden md:flex">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Key Metrics */}
        <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(24680.50)}</div>
              <div className="flex items-center pt-1 text-xs text-emerald-500">
                <ArrowUp className="mr-1 h-3 w-3" />
                <span>12% from previous period</span>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <ShoppingBag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,284</div>
              <div className="flex items-center pt-1 text-xs text-emerald-500">
                <ArrowUp className="mr-1 h-3 w-3" />
                <span>8% from previous period</span>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Businesses</CardTitle>
              <Store className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">42</div>
              <div className="flex items-center pt-1 text-xs text-emerald-500">
                <ArrowUp className="mr-1 h-3 w-3" />
                <span>4 new this period</span>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">856</div>
              <div className="flex items-center pt-1 text-xs text-emerald-500">
                <ArrowUp className="mr-1 h-3 w-3" />
                <span>24 new this period</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="mb-6 grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Over Time</CardTitle>
              <CardDescription>Daily revenue for the selected period</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <LineChart className="mx-auto h-16 w-16 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Revenue chart will be displayed here
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Real-time data will be integrated with Supabase
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Orders by Business Type</CardTitle>
              <CardDescription>Distribution of orders across business categories</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <PieChart className="mx-auto h-16 w-16 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Orders distribution chart will be displayed here
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Real-time data will be integrated with Supabase
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for Different Analytics Views */}
        <Tabs defaultValue="orders" className="mb-6">
          <TabsList className="grid w-full grid-cols-5 lg:w-auto">
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="businesses">Businesses</TabsTrigger>
            <TabsTrigger value="drivers">Drivers</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Analytics</CardTitle>
                <CardDescription>Detailed breakdown of order metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div className="rounded-lg border p-4">
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Average Order Value</span>
                    </div>
                    <div className="mt-2 text-2xl font-bold">{formatCurrency(19.22)}</div>
                    <div className="mt-1 flex items-center text-xs text-emerald-500">
                      <ArrowUp className="mr-1 h-3 w-3" />
                      <span>5.2% from previous period</span>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Orders per Day</span>
                    </div>
                    <div className="mt-2 text-2xl font-bold">42</div>
                    <div className="mt-1 flex items-center text-xs text-emerald-500">
                      <ArrowUp className="mr-1 h-3 w-3" />
                      <span>3.8% from previous period</span>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Conversion Rate</span>
                    </div>
                    <div className="mt-2 text-2xl font-bold">3.2%</div>
                    <div className="mt-1 flex items-center text-xs text-red-500">
                      <ArrowDown className="mr-1 h-3 w-3" />
                      <span>0.5% from previous period</span>
                    </div>
                  </div>
                </div>
                <div className="mt-6 h-80">
                  <div className="flex h-full items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="mx-auto h-16 w-16 text-muted-foreground" />
                      <p className="mt-2 text-sm text-muted-foreground">
                        Order trends chart will be displayed here
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Real-time data will be integrated with Supabase
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="businesses" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Analytics</CardTitle>
                <CardDescription>Performance metrics for businesses on the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Store className="mx-auto h-16 w-16 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Business analytics will be displayed here
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Real-time data will be integrated with Supabase
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="drivers" className="mt-6">
            <DriverAnalyticsTab />
          </TabsContent>

          <TabsContent value="users" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>User Analytics</CardTitle>
                <CardDescription>User growth and engagement metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="mx-auto h-16 w-16 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    User analytics will be displayed here
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Real-time data will be integrated with Supabase
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analytics</CardTitle>
                <CardDescription>Detailed revenue breakdown and projections</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <CreditCard className="mx-auto h-16 w-16 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Revenue analytics will be displayed here
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Real-time data will be integrated with Supabase
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground">
            This is a preview of the new analytics dashboard with real-time data capabilities.
            <br />
            Charts and visualizations will be implemented with real-time Supabase data.
          </p>
          <div className="mt-4">
            <Link href="/admin-new">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
