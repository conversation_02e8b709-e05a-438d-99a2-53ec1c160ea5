"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ian<PERSON><PERSON>, <PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON>, XAxis, Y<PERSON><PERSON><PERSON> } from "recharts"

const data = [
  {
    name: "<PERSON>",
    revenue: 4000,
    orders: 240,
  },
  {
    name: "Feb",
    revenue: 3000,
    orders: 198,
  },
  {
    name: "<PERSON>",
    revenue: 2000,
    orders: 120,
  },
  {
    name: "Apr",
    revenue: 2780,
    orders: 160,
  },
  {
    name: "May",
    revenue: 1890,
    orders: 110,
  },
  {
    name: "Jun",
    revenue: 2390,
    orders: 140,
  },
  {
    name: "Jul",
    revenue: 3490,
    orders: 210,
  },
  {
    name: "Aug",
    revenue: 4000,
    orders: 240,
  },
  {
    name: "Sep",
    revenue: 4500,
    orders: 270,
  },
  {
    name: "Oct",
    revenue: 5200,
    orders: 310,
  },
  {
    name: "Nov",
    revenue: 4800,
    orders: 290,
  },
  {
    name: "Dec",
    revenue: 6100,
    orders: 370,
  },
]

export function Overview() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis yAxisId="left" orientation="left" stroke="#10b981" />
        <YAxis yAxisId="right" orientation="right" stroke="#94a3b8" />
        <Tooltip />
        <Legend />
        <Bar yAxisId="left" dataKey="revenue" fill="#10b981" name="Revenue ($)" />
        <Bar yAxisId="right" dataKey="orders" fill="#94a3b8" name="Orders" />
      </BarChart>
    </ResponsiveContainer>
  )
}
