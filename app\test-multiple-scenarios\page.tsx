"use client"

import React from 'react'
import { OrderWeightChart } from '@/components/driver/order-weight-chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const testScenarios = [
  {
    id: 550,
    name: 'Original Test Order',
    description: 'Mixed order with all attribute types',
    orderNumber: '***********',
    expectedItems: 9,
    highlights: ['Undeclared weight items', 'All thermal types', 'Good variety']
  },
  {
    id: 551,
    name: 'Light Takeaway Order',
    description: 'Small restaurant order with mostly light items',
    orderNumber: '***********',
    expectedItems: 5,
    highlights: ['All light items (0kg)', 'Hot and cold thermal', 'Quick delivery']
  },
  {
    id: 552,
    name: 'Heavy Grocery Order',
    description: 'Grocery delivery with heavy items',
    orderNumber: '***********',
    expectedItems: 9,
    highlights: ['Heavy items (2kg, 5kg)', 'Cold thermal required', 'Vehicle capacity planning']
  },
  {
    id: 553,
    name: 'Mixed Unknown Items',
    description: 'Order with many undeclared attributes',
    orderNumber: 'MI-93066908',
    expectedItems: 6,
    highlights: ['Many undeclared items', 'High uncertainty', 'Contact business needed']
  },
  {
    id: 554,
    name: 'Pharmacy/Medical Order',
    description: 'Temperature-sensitive medical supplies',
    orderNumber: 'PH-93067059',
    expectedItems: 6,
    highlights: ['Cold chain required', 'Light items', 'Medical priority']
  },
  {
    id: 555,
    name: 'Hardware Store Order',
    description: 'Large, heavy items from hardware store',
    orderNumber: '***********',
    expectedItems: 6,
    highlights: ['Very heavy items', 'Large sizes', 'No thermal needed']
  }
]

export default function TestMultipleScenariosPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-gray-900">
              📊 Driver Weight Chart - Multiple Scenarios
            </CardTitle>
            <p className="text-gray-600 text-lg">
              Comprehensive testing of the order weight analysis chart across different business types and order compositions
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-semibold text-blue-800">Weight Classes</h4>
                <p className="text-blue-700">Undeclared, 0kg, 1kg, 2kg, 3kg, 4kg, 5kg+</p>
              </div>
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <h4 className="font-semibold text-green-800">Thermal Requirements</h4>
                <p className="text-green-700">🔴 Hot, 🔵 Cold, 🟢 None, ⚫ Undeclared</p>
              </div>
              <div className="p-3 bg-amber-50 border border-amber-200 rounded">
                <h4 className="font-semibold text-amber-800">Driver Benefits</h4>
                <p className="text-amber-700">Equipment planning, risk assessment, time estimation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Scenarios */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {testScenarios.map((scenario) => (
            <Card key={scenario.id} className="border-2 border-gray-200 hover:border-blue-300 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900">
                      {scenario.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {scenario.expectedItems} items
                  </Badge>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {scenario.highlights.map((highlight, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {highlight}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <OrderWeightChart orderId={scenario.id} />
                <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                  <strong>Order:</strong> #{scenario.orderNumber} | 
                  <strong> API:</strong> /api/driver/order-analysis/{scenario.id}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Compact View Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Compact View Comparison</CardTitle>
            <p className="text-sm text-gray-600">
              How the charts appear in the driver orders list (compact mode)
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testScenarios.slice(0, 6).map((scenario) => (
                <div key={`compact-${scenario.id}`} className="border rounded-lg p-3">
                  <h4 className="font-medium text-sm mb-2">{scenario.name}</h4>
                  <OrderWeightChart orderId={scenario.id} compact={true} />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Analysis Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Chart Analysis & Driver Decision Making</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">What Drivers Can Learn:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600">•</span>
                    <span><strong>Equipment Needed:</strong> Hot bags, cold bags, thermal containers</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-600">•</span>
                    <span><strong>Physical Effort:</strong> Light vs heavy items, vehicle capacity</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-amber-600">•</span>
                    <span><strong>Risk Assessment:</strong> Undeclared items need clarification</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-purple-600">•</span>
                    <span><strong>Time Planning:</strong> Complex orders take longer</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Business Benefits:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <span className="text-red-600">•</span>
                    <span><strong>Better Matching:</strong> Right driver for the right order</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600">•</span>
                    <span><strong>Fewer Rejections:</strong> Drivers know what to expect</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-600">•</span>
                    <span><strong>Quality Delivery:</strong> Proper equipment preparation</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-orange-600">•</span>
                    <span><strong>Faster Service:</strong> Reduced confusion and delays</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Details */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Implementation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Database Schema</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• weight_class_kg (nullable integer)</li>
                  <li>• thermal_requirement (nullable enum)</li>
                  <li>• size_category (nullable enum)</li>
                  <li>• Cached in cart_items table</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">API Endpoint</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• /api/driver/order-analysis/[orderId]</li>
                  <li>• Groups by weight & thermal</li>
                  <li>• Returns structured chart data</li>
                  <li>• Handles nullable attributes</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">React Component</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Full and compact modes</li>
                  <li>• Loading and error states</li>
                  <li>• Responsive design</li>
                  <li>• Accessibility features</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
