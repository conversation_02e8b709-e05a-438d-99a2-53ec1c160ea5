"use client"

// Import the order confirmation component
import OrderConfirmationContent from './order-confirmation-content'

// Define the props interface for the client component
interface OrderConfirmationClientProps {
  orderIdentifier: string
  verifiedOrder: {
    id: number
    order_number: string | null
    status: string | null
    created_at: string | null
  }
  isMultiBusiness?: boolean
}

/**
 * Client component for order confirmation
 * This is a wrapper around the existing order confirmation page
 * that passes the verified order data from the server component
 */
export default function OrderConfirmationClient({
  orderIdentifier,
  verifiedOrder,
  isMultiBusiness = false
}: OrderConfirmationClientProps) {
  // Store the verified order data in sessionStorage for the client component to use
  if (typeof window !== 'undefined') {
    // Save the verified order data to sessionStorage
    sessionStorage.setItem('verifiedOrder', JSON.stringify({
      id: verifiedOrder.id,
      orderNumber: verifiedOrder.order_number,
      status: verifiedOrder.status,
      createdAt: verifiedOrder.created_at,
      isMultiBusiness: isMultiBusiness
    }));

    console.log('✅ ORDER CONFIRMATION CLIENT: Stored verified order data in sessionStorage', { isMultiBusiness });
  }

  // Render the existing order confirmation page
  return <OrderConfirmationContent />
}
