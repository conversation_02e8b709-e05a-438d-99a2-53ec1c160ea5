"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { MapPin, Home, AlertTriangle } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"


interface JerseyAddressInputProps {
  value: string
  onChange: (value: string, coordinates?: [number, number] | null, parish?: string) => void
  onCoordinatesChange?: (coordinates: [number, number] | null) => void
  onParishChange?: ((parish: string) => void) | undefined
  placeholder?: string
  className?: string
  required?: boolean
  parish?: string // External parish value that can be set from parent
}

// List of Jersey parishes
const JERSEY_PARISHES = [
  "St Helier",
  "St Brelade",
  "St Clement",
  "Grouville",
  "St John",
  "St Lawrence",
  "St Martin",
  "St Mary",
  "St Ouen",
  "St Peter",
  "St Saviour",
  "Trinity"
];

export default function JerseyAddressInput({
  value,
  onChange,
  onCoordinatesChange = undefined,
  onParishChange = undefined,
  placeholder = "Enter your Jersey address",
  className = "",
  required = false,
  parish: externalParish = ""
}: JerseyAddressInputProps) {
  const [street, setStreet] = useState("")
  const [parish, setParish] = useState("")
  const [postcode, setPostcode] = useState("")
  const [isInitialized, setIsInitialized] = useState(false)

  // Memoize the update function to prevent infinite loops
  const updateAddress = useCallback((newStreet: string, newParish: string, newPostcode: string) => {
    const components = [
      newStreet.trim(),
      newParish ? `${newParish}, Jersey` : ""
    ].filter(Boolean);

    const fullAddress = components.join(", ");

    // Call onParishChange if provided
    if (typeof onParishChange === 'function') {
      onParishChange(newParish);
    }

    // Don't do any geocoding here - let the parent component handle coordinate lookup
    // via the Jersey postcode system for consistency with business page calculations
    onChange(fullAddress, null, newParish);
  }, [onChange, onParishChange]);

  // Effect to handle external parish changes
  useEffect(() => {
    if (externalParish && externalParish !== parish) {
      console.log('JerseyAddressInput: External parish changed from', parish, 'to:', externalParish);
      setParish(externalParish);

      // Update the address with the new parish
      if (street) {
        updateAddress(street, externalParish, postcode);
      }
    }
  }, [externalParish, parish, street, postcode, updateAddress]);

  // Handle street input change
  const handleStreetChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newStreet = e.target.value;
    setStreet(newStreet);

    // Only update if we have a meaningful change
    if (newStreet.trim() !== street.trim()) {
      updateAddress(newStreet, parish, postcode);
    }
  }, [street, parish, postcode, updateAddress]);

  // Handle parish selection change
  const handleParishChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newParish = e.target.value;

    // Only update if there's a real change
    if (newParish !== parish) {
      setParish(newParish);

      // Call onParishChange directly to ensure it's updated immediately
      if (typeof onParishChange === 'function') {
        onParishChange(newParish);
      }

      // Update the full address
      updateAddress(street, newParish, postcode);
    }
  }, [street, parish, postcode, updateAddress, onParishChange]);

  // Handle postcode input change
  const handlePostcodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newPostcode = e.target.value;

    // Only update if there's a real change
    if (newPostcode !== postcode) {
      setPostcode(newPostcode);
      updateAddress(street, parish, newPostcode);
    }
  }, [street, parish, postcode, updateAddress]);

  // Parse the value into components when the value changes
  // Only run this effect on initial mount with a value
  useEffect(() => {
    // Skip if already initialized or no value
    if (!value || isInitialized) return;

    // Try to extract parish and postcode from the value
    const parts = value.split(",").map(p => p.trim());

    // Last part might be postcode
    const postcodeRegex = /\b(JE\d\s?\d[A-Z]{2})\b/i;
    const postcodeMatch = value.match(postcodeRegex);

    let newPostcode = "";
    if (postcodeMatch) {
      newPostcode = postcodeMatch[1];
      setPostcode(newPostcode);
    }

    // Look for parish names
    let foundParish = "";
    let streetPart = parts[0];

    for (const part of parts) {
      for (const parishName of JERSEY_PARISHES) {
        if (part.toLowerCase().includes(parishName.toLowerCase())) {
          foundParish = parishName;
          // If parish is in the first part, extract the street
          if (part === parts[0] && part.toLowerCase() !== parishName.toLowerCase()) {
            streetPart = part.replace(new RegExp(parishName, "i"), "").trim();
          }
          break;
        }
      }
      if (foundParish) break;
    }

    if (foundParish) {
      setParish(foundParish);
      // Don't call onParishChange during initialization to avoid loops
    }

    setStreet(streetPart || parts[0] || "");

    // If we have a street value but no parish was found, use the first part as street
    if (!streetPart && parts.length > 0) {
      setStreet(parts[0]);
    }

    setIsInitialized(true);

    // Call onChange directly once at initialization to update the parent
    // This is safer than calling updateAddress which might trigger more updates
    if (onChange && foundParish) {
      const fullAddress = `${streetPart || parts[0] || ""}, ${foundParish}, Jersey`;
      onChange(fullAddress, null, foundParish);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, isInitialized]);

  // Generate the display address for the UI
  const displayAddress = useMemo(() => {
    if (street && parish) {
      return `${street}, ${parish}, Jersey`;
    }
    return "Enter your Jersey address";
  }, [street, parish]);

  return (
    <div className={className}>
      <div className="space-y-3">
        <div>
          <div className="relative">
            <Home className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="street"
              value={street}
              onChange={handleStreetChange}
              placeholder="House number and street name"
              className="pl-10"
              required={required}
            />
          </div>
        </div>

        <div>
          <div className="mb-1 text-sm font-medium flex items-center">
            <span>Parish</span>
            {required && <span className="text-red-500 ml-1">*</span>}
          </div>
          <select
            id="parish"
            value={parish}
            onChange={handleParishChange}
            className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
            required={required}
          >
            <option value="">Select parish</option>
            {JERSEY_PARISHES.map((parishName) => (
              <option key={parishName} value={parishName}>
                {parishName}
              </option>
            ))}
          </select>
        </div>

        <div className="text-xs text-gray-500 mt-1">
          <MapPin className="inline-block h-3 w-3 mr-1" />
          {displayAddress}
        </div>


      </div>
    </div>
  )
}
