'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { StandardizedAddress } from '@/lib/address-standardizer';

interface StandardizedAddressInputProps {
  initialAddress?: string;
  initialParish?: string;
  initialPostcode?: string;
  onAddressChange?: (standardizedAddress: StandardizedAddress) => void;
  onValidationError?: (error: string) => void;
}

export function StandardizedAddressInput({
  initialAddress = '',
  initialParish = '',
  initialPostcode = '',
  onAddressChange,
  onValidationError
}: StandardizedAddressInputProps) {
  const [address, setAddress] = useState(initialAddress);
  const [parish, setParish] = useState(initialParish);
  const [postcode, setPostcode] = useState(initialPostcode);
  const [isStandardizing, setIsStandardizing] = useState(false);
  const [standardizedAddress, setStandardizedAddress] = useState<StandardizedAddress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAutoStandardizing, setIsAutoStandardizing] = useState(false);

  // Auto-standardize when inputs change (with debounce)
  useEffect(() => {
    if (!address && !parish && !postcode) return;
    
    const timer = setTimeout(() => {
      if (address || parish || postcode) {
        setIsAutoStandardizing(true);
        standardizeAddress(false);
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [address, parish, postcode]);

  const standardizeAddress = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsStandardizing(true);
      }
      setError(null);

      const response = await fetch('/api/address/standardize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address,
          parish: parish || undefined,
          postcode: postcode || undefined,
          geocode: true,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error || 'Address standardization failed';
        setError(errorMessage);
        if (onValidationError) {
          onValidationError(errorMessage);
        }
        return;
      }

      setStandardizedAddress(data.address);
      
      // Update input fields with standardized values
      if (data.address.address_line1) {
        setAddress(data.address.address_line1 + (data.address.address_line2 ? `, ${data.address.address_line2}` : ''));
      }
      if (data.address.parish) {
        setParish(data.address.parish);
      }
      if (data.address.postcode) {
        setPostcode(data.address.postcode);
      }

      if (onAddressChange) {
        onAddressChange(data.address);
      }

      if (!data.address.is_valid && onValidationError) {
        onValidationError(data.address.validation_message || 'Invalid address');
      }
    } catch (err) {
      console.error('Error standardizing address:', err);
      setError('An error occurred while standardizing the address');
      if (onValidationError) {
        onValidationError('An error occurred while standardizing the address');
      }
    } finally {
      setIsStandardizing(false);
      setIsAutoStandardizing(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          placeholder="e.g., 123 Main St"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="parish">Parish</Label>
          <Input
            id="parish"
            placeholder="e.g., St Helier"
            value={parish}
            onChange={(e) => setParish(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="postcode">Postcode</Label>
          <Input
            id="postcode"
            placeholder="e.g., JE2 4UE"
            value={postcode}
            onChange={(e) => setPostcode(e.target.value)}
          />
        </div>
      </div>

      <Button 
        onClick={() => standardizeAddress()} 
        disabled={isStandardizing || (!address && !parish && !postcode)}
        className="w-full"
      >
        {isStandardizing ? (
          <>
            <Spinner className="mr-2 h-4 w-4" />
            Standardizing...
          </>
        ) : (
          'Standardize Address'
        )}
      </Button>

      {isAutoStandardizing && (
        <div className="flex items-center justify-center text-sm text-gray-500">
          <Spinner className="mr-2 h-3 w-3" />
          Auto-standardizing...
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {standardizedAddress && (
        <Card>
          <CardContent className="pt-4">
            <div className="space-y-2">
              <h3 className="font-semibold">Standardized Address</h3>
              <div className="grid grid-cols-3 gap-2 text-sm">
                <div className="font-medium">Address Line 1:</div>
                <div className="col-span-2">{standardizedAddress.address_line1 || 'N/A'}</div>

                <div className="font-medium">Address Line 2:</div>
                <div className="col-span-2">{standardizedAddress.address_line2 || 'N/A'}</div>

                <div className="font-medium">Parish:</div>
                <div className="col-span-2">{standardizedAddress.parish || 'N/A'}</div>

                <div className="font-medium">Postcode:</div>
                <div className="col-span-2">{standardizedAddress.postcode || 'N/A'}</div>

                {standardizedAddress.coordinates && (
                  <>
                    <div className="font-medium">Coordinates:</div>
                    <div className="col-span-2">
                      [{standardizedAddress.coordinates[0].toFixed(6)}, {standardizedAddress.coordinates[1].toFixed(6)}]
                    </div>
                  </>
                )}

                <div className="font-medium">Valid:</div>
                <div className="col-span-2">
                  {standardizedAddress.is_valid ? (
                    <span className="text-green-600">Yes</span>
                  ) : (
                    <span className="text-red-600">No - {standardizedAddress.validation_message}</span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
