import { CheckCircle } from "lucide-react"
import WheelLogoIcon from "@/components/wheel-logo-icon"

interface OrderHeaderProps {
  businessCount: number
}

export function OrderHeader({ businessCount }: OrderHeaderProps) {
  return (
    <div className="flex flex-col items-center text-center mb-10">
      <div className="relative">
        <div className="h-24 w-24 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center mb-6 shadow-lg">
          <div className="animate-wheel-celebration">
            <WheelLogoIcon size={48} color="white" className="text-white" />
          </div>
        </div>
        <div className="absolute -top-1 -right-1 h-6 w-6 rounded-full bg-white flex items-center justify-center shadow-md">
          <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
        </div>
      </div>
      <h1 className="text-4xl font-bold text-gray-900 mb-3">Order Confirmed!</h1>
      <p className="text-lg text-gray-600 max-w-2xl leading-relaxed">
        Your {businessCount > 1 ? 'orders have' : 'order has'} been received and {businessCount > 1 ? 'are' : 'is'} being prepared.
      </p>
    </div>
  )
}
