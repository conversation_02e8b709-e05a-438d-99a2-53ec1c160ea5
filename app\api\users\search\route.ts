import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerSupabase } from '@/lib/supabase-server'

const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper function to get authenticated user
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        users: [],
        message: 'Query must be at least 2 characters'
      })
    }

    // Search connection profiles
    const { data: profiles, error } = await adminClient
      .from('connection_profiles')
      .select('*')
      .neq('user_id', user.id) // Exclude current user
      .eq('is_public', true) // Only public profiles
      .or(`display_name.ilike.%${query}%,bio.ilike.%${query}%`)
      .limit(20)

    if (error) {
      console.error('Error searching users:', error)
      return NextResponse.json(
        { error: 'Failed to search users' },
        { status: 500 }
      )
    }

    // Transform profiles to match UserProfile interface
    const users = (profiles || []).map(profile => ({
      id: profile.id,
      user_id: profile.user_id,
      display_name: profile.display_name,
      bio: profile.bio,
      avatar_url: profile.avatar_url,
      role_capabilities: {
        can_be_customer: true,
        can_be_rider: false,
        owns_business: false
      },
      specialties: {},
      average_rating: 4.5,
      total_ratings: 0,
      is_public: profile.is_public,
      allow_direct_messages: profile.allow_direct_messages
    }))

    return NextResponse.json({
      users,
      total: users.length
    })

  } catch (error) {
    console.error('Error in users search API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
