'use client'

import { useState } from 'react'

export default function SuperSimpleTest() {
  const [status, setStatus] = useState<string>('')

  const testBasicNotification = async () => {
    setStatus('🔔 Testing basic browser notification...')

    try {
      // Request permission
      const permission = await Notification.requestPermission()
      setStatus(prev => prev + `\nPermission: ${permission}`)

      if (permission === 'granted') {
        // Create the simplest possible persistent notification
        const notification = new Notification('🚨 BROWSER TEST', {
          body: 'This should stay visible until you click it',
          requireInteraction: true // Only persistence feature
        })

        notification.onshow = () => {
          setStatus(prev => prev + '\n✅ Browser notification shown!')
        }

        notification.onclick = () => {
          setStatus(prev => prev + '\n🖱️ Browser notification clicked!')
          notification.close()
        }

        notification.onerror = (error) => {
          setStatus(prev => prev + `\n❌ Error: ${error}`)
        }

        setStatus(prev => prev + '\n📤 Browser notification created')
      } else {
        setStatus(prev => prev + '\n❌ Permission denied')
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
    }
  }

  const testServiceWorkerNotification = async () => {
    setStatus('🔔 Testing service worker notification...')

    try {
      // Request permission
      const permission = await Notification.requestPermission()
      setStatus(prev => prev + `\nPermission: ${permission}`)

      if (permission === 'granted') {
        // Check if service worker is available
        if ('serviceWorker' in navigator) {
          setStatus(prev => prev + '\n🔍 Checking service worker...')

          // Get all registrations
          const registrations = await navigator.serviceWorker.getRegistrations()
          setStatus(prev => prev + `\n📊 Found ${registrations.length} service worker(s)`)

          if (registrations.length === 0) {
            setStatus(prev => prev + '\n❌ No service workers registered')
            setStatus(prev => prev + '\n🔧 Registering service worker...')

            // Register service worker
            const registration = await navigator.serviceWorker.register('/sw.js')
            setStatus(prev => prev + '\n✅ Service worker registered')

            // Wait for it to be ready
            await navigator.serviceWorker.ready
            setStatus(prev => prev + '\n✅ Service worker ready')
          } else {
            setStatus(prev => prev + '\n✅ Service worker already registered')
          }

          const registration = await navigator.serviceWorker.ready
          setStatus(prev => prev + `\n📋 SW State: ${registration.active?.state || 'unknown'}`)

          // Use service worker to show notification
          setStatus(prev => prev + '\n📤 Calling showNotification...')

          await registration.showNotification('🔧 SERVICE WORKER TEST', {
            body: 'This uses service worker - should work repeatedly!',
            requireInteraction: true,
            icon: '/android-chrome-192x192.png',
            badge: '/favicon-32x32.png',
            tag: `test-${Date.now()}`,
            vibrate: [200, 100, 200]
            // Removed actions to simplify
          })

          setStatus(prev => prev + '\n✅ Service worker notification created successfully!')

          // Check if notification actually appeared
          setTimeout(async () => {
            const notifications = await registration.getNotifications()
            setStatus(prev => prev + `\n📊 Active notifications: ${notifications.length}`)
          }, 1000)

        } else {
          setStatus(prev => prev + '\n❌ Service worker not supported')
        }
      } else {
        setStatus(prev => prev + '\n❌ Permission denied')
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
      setStatus(prev => prev + `\n🔍 Error details: ${error.stack}`)
    }
  }

  const testWithoutPersistence = async () => {
    setStatus('🔔 Testing normal (non-persistent) notification...')

    try {
      const permission = await Notification.requestPermission()
      setStatus(prev => prev + `\nPermission: ${permission}`)

      if (permission === 'granted') {
        // Normal notification without requireInteraction
        const notification = new Notification('⏰ NORMAL TEST', {
          body: 'This should disappear after a few seconds'
          // No requireInteraction - should auto-disappear
        })

        notification.onshow = () => {
          setStatus(prev => prev + '\n✅ Normal notification shown!')
        }

        notification.onclick = () => {
          setStatus(prev => prev + '\n🖱️ Normal notification clicked!')
          notification.close()
        }

        setStatus(prev => prev + '\n📤 Normal notification created')
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
    }
  }

  const checkBrowserSupport = async () => {
    setStatus('🔍 Checking browser support...')
    setStatus(prev => prev + `\nNotifications supported: ${'Notification' in window}`)
    setStatus(prev => prev + `\nPermission: ${Notification.permission}`)
    setStatus(prev => prev + `\nUser Agent: ${navigator.userAgent.substring(0, 50)}...`)
    setStatus(prev => prev + `\nService Workers: ${'serviceWorker' in navigator}`)

    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations()
        setStatus(prev => prev + `\nSW Registrations: ${registrations.length}`)

        if (registrations.length > 0) {
          const reg = registrations[0]
          setStatus(prev => prev + `\nSW Scope: ${reg.scope}`)
          setStatus(prev => prev + `\nSW State: ${reg.active?.state || 'no active worker'}`)
          setStatus(prev => prev + `\nSW Script URL: ${reg.active?.scriptURL || 'unknown'}`)
        }

        // Check if ready
        const ready = await navigator.serviceWorker.ready
        setStatus(prev => prev + `\nSW Ready: ${ready ? 'Yes' : 'No'}`)
      } catch (error) {
        setStatus(prev => prev + `\nSW Error: ${error.message}`)
      }
    }
  }

  const resetPermissions = () => {
    setStatus('🔄 To reset permissions:')
    setStatus(prev => prev + '\n1. Go to edge://settings/content/notifications')
    setStatus(prev => prev + '\n2. Find localhost:3000 and delete it')
    setStatus(prev => prev + '\n3. Refresh this page and try again')
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          🚨 Super Simple Notification Test
        </h1>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h3 className="font-semibold text-red-800 mb-2">Simple Test:</h3>
            <p className="text-red-700 text-sm">
              This test uses the absolute minimum code to show notifications. 
              No service workers, no complex features, just basic browser notifications.
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={testBasicNotification}
              className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 font-semibold"
            >
              🚨 Test Browser Notification (Limited)
            </button>

            <button
              onClick={testServiceWorkerNotification}
              className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 font-semibold"
            >
              🔧 Test Service Worker Notification (Unlimited)
            </button>

            <button
              onClick={testWithoutPersistence}
              className="w-full bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700"
            >
              ⏰ Test Normal (Auto-Disappearing) Notification
            </button>

            <button
              onClick={checkBrowserSupport}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              🔍 Check Browser Support
            </button>

            <button
              onClick={resetPermissions}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700"
            >
              🔄 How to Reset Permissions
            </button>
          </div>

          {status && (
            <div className="mt-4">
              <h3 className="font-semibold text-gray-700 mb-2">Status:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm whitespace-pre-wrap">
                {status}
              </pre>
            </div>
          )}

          <div className="mt-6 p-4 bg-yellow-50 rounded">
            <h4 className="font-semibold text-yellow-800 mb-2">What Should Happen:</h4>
            <ul className="text-sm text-yellow-700 space-y-1 list-disc list-inside">
              <li><strong>Browser Test:</strong> Works once, then stops working until browser restart</li>
              <li><strong>Service Worker Test:</strong> Should work repeatedly without restart!</li>
              <li><strong>Normal Test:</strong> Notification appears and disappears after 3-5 seconds</li>
              <li>If none work, there's a browser permission issue</li>
            </ul>
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-800 mb-2">Troubleshooting:</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Make sure you click "Allow" when asked for permission</li>
              <li>Check edge://settings/content/notifications</li>
              <li>Try a different browser (Chrome, Firefox)</li>
              <li>Try incognito/private mode</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
