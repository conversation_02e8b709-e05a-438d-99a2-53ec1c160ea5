
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, Download, RefreshCw } from "lucide-react";
import { useProductUpload } from '@/context/ProductUploadContext';

export const CompletionStep: React.FC = () => {
  const { products, setCurrentStep } = useProductUpload();
  
  const handleDownload = () => {
    // Create CSV content
    const headers = ['Name', 'Description', 'Categories', 'Price', 'Attributes', 'Images'];
    
    const rows = products.map(product => [
      product.name,
      product.description,
      product.categories.join(', '),
      product.price,
      Object.entries(product.attributes).map(([key, value]) => `${key}: ${value}`).join('; '),
      product.images.join(', ')
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(','))
    ].join('\n');
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'enhanced_products.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleStartOver = () => {
    // Reset to the first step
    setCurrentStep(1);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Products Enhanced Successfully!</CardTitle>
        <CardDescription>
          Your products have been successfully processed and enhanced with AI-generated content.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-green-50 border border-green-100 rounded-lg p-6 text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Check className="h-6 w-6 text-green-600" />
          </div>
          
          <h3 className="text-lg font-medium text-green-800 mb-2">All Products Processed</h3>
          <p className="text-green-700">
            {products.length} product{products.length !== 1 ? 's' : ''} successfully enhanced with AI-generated content!
          </p>
        </div>
        
        <div className="rounded-lg border p-6 space-y-4">
          <h3 className="text-lg font-medium">Summary</h3>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Products</span>
              <span className="font-medium">{products.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Products with AI Descriptions</span>
              <span className="font-medium">{products.filter(p => p.description).length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Products with Categories</span>
              <span className="font-medium">{products.filter(p => p.categories.length > 0).length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Products with Attributes</span>
              <span className="font-medium">
                {products.filter(p => Object.keys(p.attributes).length > 0).length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Products with Images</span>
              <span className="font-medium">{products.filter(p => p.images.length > 0).length}</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col space-y-4">
          <Button onClick={handleDownload} className="w-full flex items-center justify-center gap-2">
            <Download className="h-4 w-4" />
            Download Enhanced Products CSV
          </Button>
          
          <Button variant="outline" onClick={handleStartOver} className="w-full flex items-center justify-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Start Over with New File
          </Button>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <p className="text-sm text-gray-500 italic">
          Your AI-enhanced products are ready for your e-commerce store
        </p>
      </CardFooter>
    </Card>
  );
};
