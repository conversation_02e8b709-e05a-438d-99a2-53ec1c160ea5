"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"
import { supabase } from "@/lib/supabase"

export default function AuthCallbackPage() {
  const router = useRouter()
  const { user, session } = useAuth()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(true)

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setIsProcessing(true)
        const urlParams = new URLSearchParams(window.location.search)
        const code = urlParams.get('code')
        const error_code = urlParams.get('error_code')
        const error_description = urlParams.get('error_description')

        console.log("Auth callback: Processing callback with code:", !!code)

        // Handle error from Supabase
        if (error_code) {
          console.error("Auth callback: Supabase error:", error_code, error_description)
          setError(`Authentication error: ${error_description || error_code}`)
          setIsProcessing(false)
          return
        }

        // Handle email confirmation or auth code
        if (code) {
          console.log("Auth callback: Exchanging code for session")

          // Clear any existing session first to avoid conflicts
          try {
            await supabase.auth.signOut()
            console.log("Auth callback: Cleared existing session")
          } catch (signOutError) {
            console.log("Auth callback: No existing session to clear")
          }

          // Exchange the code for a session
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)

          if (exchangeError) {
            console.error("Auth callback: Exchange error:", exchangeError)
            setError(`Authentication error: ${exchangeError.message}`)
            setIsProcessing(false)
            return
          }

          if (data.session) {
            console.log("Auth callback: Session established successfully")

            // Check if this is email confirmation
            if (data.user?.email_confirmed_at) {
              setSuccess("Email confirmed successfully! You can now access all features.")
            } else {
              setSuccess("Authentication successful!")
            }

            // Wait a moment for the auth context to update
            setTimeout(() => {
              // Determine redirect destination
              const pendingBusinessLink = localStorage.getItem('pending_business_link')
              const authRedirect = localStorage.getItem('auth_redirect')

              let redirectTo = '/account/profile' // Default

              // Check URL params for redirect
              const urlParams = new URLSearchParams(window.location.search)
              const returnUrl = urlParams.get('returnUrl')

              if (returnUrl && returnUrl.startsWith('/')) {
                redirectTo = returnUrl
              } else if (authRedirect && authRedirect.startsWith('/')) {
                redirectTo = authRedirect
              } else if (pendingBusinessLink) {
                redirectTo = '/business-admin/dashboard'
              }

              console.log("Auth callback: Redirecting to:", redirectTo)
              console.log("Auth callback: Pending business link:", !!pendingBusinessLink)

              // Clear stored redirect
              localStorage.removeItem('auth_redirect')

              // Redirect
              router.push(redirectTo)
            }, 1500)

            setIsProcessing(false)
            return
          }
        }

        // No code parameter - check if user is already authenticated
        if (session && user) {
          console.log("Auth callback: User already authenticated, redirecting")
          const redirectTo = localStorage.getItem('auth_redirect') || '/account/profile'
          localStorage.removeItem('auth_redirect')
          router.push(redirectTo)
          return
        }

        // No code and no session - something went wrong
        console.error("Auth callback: No code parameter and no existing session")
        setError("Invalid authentication callback. Please try logging in again.")
        setIsProcessing(false)

      } catch (err: any) {
        console.error("Auth callback error:", err)
        setError(err.message || "An unexpected error occurred during authentication")
        setIsProcessing(false)
      }
    }

    handleCallback()
  }, [router, session, user])

  return (
    <div className="flex min-h-[calc(100vh-4rem)] items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            {error ? (
              <AlertCircle className="w-8 h-8 text-red-600" />
            ) : success ? (
              <CheckCircle className="w-8 h-8 text-green-600" />
            ) : (
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            )}
          </div>
          <CardTitle>
            {error ? "Authentication Error" : success ? "Success!" : "Processing Authentication"}
          </CardTitle>
          <CardDescription>
            {error
              ? "There was a problem with your authentication"
              : success
                ? "Redirecting you to your account..."
                : "Please wait while we complete your authentication..."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="text-center space-y-4">
              <div className="text-red-600 text-sm bg-red-50 p-3 rounded border border-red-200">
                {error}
              </div>
              <div className="space-y-2">
                <Button
                  onClick={() => router.push('/login')}
                  className="w-full bg-emerald-600 hover:bg-emerald-700"
                >
                  Return to Login
                </Button>
                <Button
                  onClick={() => router.push('/register')}
                  variant="outline"
                  className="w-full"
                >
                  Create Account
                </Button>
              </div>
            </div>
          )}

          {success && (
            <div className="text-center">
              <div className="text-green-600 text-sm bg-green-50 p-3 rounded border border-green-200">
                {success}
              </div>
            </div>
          )}

          {isProcessing && !error && !success && (
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="text-sm">Processing authentication...</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
