'use client'

import { useState, useEffect } from 'react'

export default function BusinessesTestPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      
      try {
        // Fetch data from our businesses API
        const response = await fetch('/api/businesses')
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `API returned ${response.status}`)
        }
        
        const apiData = await response.json()
        console.log('API data:', apiData)
        setData(apiData)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Businesses API Test</h1>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      
      {data && (
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">
            Businesses ({data.businesses?.length || 0})
            {data.pagination && (
              <span className="text-sm font-normal ml-2">
                Page {data.pagination.page} of {Math.ceil(data.pagination.total / data.pagination.limit)}
                (Total: {data.pagination.total})
              </span>
            )}
          </h2>
          
          {data.businesses && data.businesses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.businesses.map((business: any) => (
                <div key={business.id} className="border p-4 rounded">
                  <h3 className="font-bold">{business.name}</h3>
                  <p className="text-sm text-gray-600">{business.location}</p>
                  <p className="text-sm">Type: {business.business_types?.name || 'Unknown'}</p>
                  <p className="text-sm">Rating: {business.rating || 'N/A'}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>No businesses found</p>
          )}
        </div>
      )}
    </div>
  )
}
