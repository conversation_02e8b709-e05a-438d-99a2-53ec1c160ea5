import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting business statistics data audit API request")

    // Simple query to get all businesses
    const { data: businesses, error: businessesError } = await adminClient
      .from('businesses')
      .select(`
        id, 
        name, 
        business_type_id,
        business_types (
          id,
          name
        )
      `);
    
    if (businessesError) {
      console.error("Error fetching businesses:", businessesError);
      return NextResponse.json(
        { error: "Failed to fetch businesses" },
        { status: 500 }
      );
    }
    
    if (!businesses || businesses.length === 0) {
      return NextResponse.json([]);
    }
    
    // Get stats for each business
    const businessStats = [];
    
    for (const business of businesses) {
      // Get business type name
      const businessTypeName = business.business_types?.name || "Unknown";
      
      // Get approval status
      const approvalStatus = business.approval_status || "Approved";
      
      // Get product count
      const { count: productCount, error: productError } = await adminClient
        .from("products")
        .select("*", { count: "exact", head: true })
        .eq("business_id", business.id);
        
      if (productError) {
        console.error(`Error counting products for business ${business.id}:`, productError);
        continue;
      }
      
      // Get category counts
      const { count: allocatedCategoriesCount, error: categoriesError } = await adminClient
        .from("business_categories")
        .select("*", { count: "exact", head: true })
        .eq("business_id", business.id);
        
      if (categoriesError) {
        console.error(`Error counting categories for business ${business.id}:`, categoriesError);
      }
      
      // Get used categories
      const { data: productCategories, error: productCategoriesError } = await adminClient
        .from("products")
        .select("category_id")
        .eq("business_id", business.id)
        .not("category_id", "is", null);
        
      if (productCategoriesError) {
        console.error(`Error getting product categories for business ${business.id}:`, productCategoriesError);
      }
      
      // Count unique categories
      const uniqueCategories = new Set();
      if (productCategories) {
        productCategories.forEach(p => {
          if (p.category_id) uniqueCategories.add(p.category_id);
        });
      }
      const usedCategoriesCount = uniqueCategories.size;
      
      // Get null counts for business
      let businessNullCount = 0;
      
      // Get columns for business table
      const { data: businessColumns, error: businessColumnsError } = await adminClient
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'businesses');
        
      if (businessColumnsError) {
        console.error(`Error getting columns for businesses table:`, businessColumnsError);
      } else if (businessColumns && businessColumns.length > 0) {
        // Build query to count nulls
        const nullQuery = `
          SELECT 
            ${businessColumns.map(col => `CASE WHEN "${col.column_name}" IS NULL THEN 1 ELSE 0 END`).join(' + ')} as null_count
          FROM businesses
          WHERE id = ${business.id}
        `;
        
        const { data: nullData, error: nullError } = await adminClient.rpc("exec_sql", {
          sql_query: nullQuery
        });
        
        if (nullError) {
          console.error(`Error counting nulls for business ${business.id}:`, nullError);
        } else if (nullData && nullData.length > 0) {
          businessNullCount = parseInt(nullData[0].null_count) || 0;
        }
      }
      
      // Get null counts for products
      let productNullCount = 0;
      
      if (productCount > 0) {
        // Get columns for products table
        const { data: productColumns, error: productColumnsError } = await adminClient
          .from('information_schema.columns')
          .select('column_name')
          .eq('table_schema', 'public')
          .eq('table_name', 'products');
          
        if (productColumnsError) {
          console.error(`Error getting columns for products table:`, productColumnsError);
        } else if (productColumns && productColumns.length > 0) {
          // Build query to count nulls
          const nullQuery = `
            SELECT 
              ${productColumns.map(col => `SUM(CASE WHEN "${col.column_name}" IS NULL THEN 1 ELSE 0 END)`).join(' + ')} as null_count
            FROM products
            WHERE business_id = ${business.id}
          `;
          
          const { data: nullData, error: nullError } = await adminClient.rpc("exec_sql", {
            sql_query: nullQuery
          });
          
          if (nullError) {
            console.error(`Error counting nulls for products of business ${business.id}:`, nullError);
          } else if (nullData && nullData.length > 0) {
            productNullCount = parseInt(nullData[0].null_count) || 0;
          }
        }
      }
      
      // Get order count and null counts
      let orderCount = 0;
      let orderNullCount = 0;
      
      // Check if orders table has business_id column
      const { count: directOrderCount, error: directOrderError } = await adminClient
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', business.id);
        
      if (!directOrderError) {
        orderCount = directOrderCount || 0;
        
        if (orderCount > 0) {
          // Get columns for orders table
          const { data: orderColumns, error: orderColumnsError } = await adminClient
            .from('information_schema.columns')
            .select('column_name')
            .eq('table_schema', 'public')
            .eq('table_name', 'orders');
            
          if (orderColumnsError) {
            console.error(`Error getting columns for orders table:`, orderColumnsError);
          } else if (orderColumns && orderColumns.length > 0) {
            // Build query to count nulls
            const nullQuery = `
              SELECT 
                ${orderColumns.map(col => `SUM(CASE WHEN "${col.column_name}" IS NULL THEN 1 ELSE 0 END)`).join(' + ')} as null_count
              FROM orders
              WHERE business_id = ${business.id}
            `;
            
            const { data: nullData, error: nullError } = await adminClient.rpc("exec_sql", {
              sql_query: nullQuery
            });
            
            if (nullError) {
              console.error(`Error counting nulls for orders of business ${business.id}:`, nullError);
            } else if (nullData && nullData.length > 0) {
              orderNullCount = parseInt(nullData[0].null_count) || 0;
            }
          }
        }
      } else {
        // Try through order_businesses table if it exists
        try {
          const { count: relatedOrderCount, error: relatedOrderError } = await adminClient
            .from('order_businesses')
            .select('*', { count: 'exact', head: true })
            .eq('business_id', business.id);
            
          if (!relatedOrderError) {
            orderCount = relatedOrderCount || 0;
          }
        } catch (err) {
          console.error(`Error counting orders through order_businesses for business ${business.id}:`, err);
        }
      }
      
      // Add business stats
      businessStats.push({
        business_name: business.name,
        business_type: businessTypeName,
        approval_status: approvalStatus,
        product_count: productCount || 0,
        allocated_categories_count: allocatedCategoriesCount || 0,
        used_categories_count: usedCategoriesCount,
        null_counts: {
          businesses: businessNullCount,
          products: productNullCount,
          users: {
            customer: 0,
            business_staff: 0,
            business_manager: 0,
            admin: 0,
            super_admin: 0
          },
          orders: orderNullCount
        },
        row_counts: {
          businesses: 1, // Each business has 1 row
          products: productCount || 0,
          users: {
            customer: 0,
            business_staff: 0,
            business_manager: 0,
            admin: 0,
            super_admin: 0
          },
          orders: orderCount
        }
      });
    }
    
    return NextResponse.json(businessStats);
  } catch (error: any) {
    console.error("Unexpected error in business statistics API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
