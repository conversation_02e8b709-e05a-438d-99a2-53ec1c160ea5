"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import {
  ShoppingCart,
  Clock,
  Truck,
  CheckCircle2,
  AlertCircle,
  ChefHat,
  Package
} from "lucide-react"

interface OrderStats {
  total: number
  pending: number
  confirmed: number
  preparing: number
  ready: number
  outForDelivery: number
  completed: number
  cancelled: number
  averageDeliveryTime: string
  overdueOrders: number
  highPriorityOrders: number
}

interface EnhancedOrderStatsProps {
  stats: OrderStats
}

export function EnhancedOrderStats({ stats }: EnhancedOrderStatsProps) {
  // Helper function to safely render numbers, converting NaN to 0
  const safeNumber = (value: number): number => {
    return isNaN(value) || !isFinite(value) ? 0 : value
  }

  // Helper function to safely render strings
  const safeString = (value: string): string => {
    return value || "0 min"
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="border-l-4 border-l-emerald-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
          <div className="h-8 w-8 rounded-full bg-emerald-50 flex items-center justify-center">
            <ShoppingCart className="h-4 w-4 text-emerald-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.total)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            {safeNumber(stats.total) > 0 ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3 mr-1 text-emerald-500">
                  <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                </svg>
                <span>{`${safeNumber(stats.pending) + safeNumber(stats.confirmed)} need attention`}</span>
              </>
            ) : (
              "No orders yet"
            )}
          </p>
        </CardContent>
      </Card>
      
      <Card className="border-l-4 border-l-yellow-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Needs Attention</CardTitle>
          <div className="h-8 w-8 rounded-full bg-yellow-50 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.pending) + safeNumber(stats.confirmed)}</div>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
              <span>New: {safeNumber(stats.pending)}</span>
            </div>
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-amber-500 mr-1"></span>
              <span>Confirmed: {safeNumber(stats.confirmed)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="border-l-4 border-l-blue-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">In Progress</CardTitle>
          <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
            <ChefHat className="h-4 w-4 text-blue-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.preparing) + safeNumber(stats.ready) + safeNumber(stats.outForDelivery)}</div>
          <div className="grid grid-cols-3 gap-1 text-xs text-muted-foreground">
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
              <span>Prep: {safeNumber(stats.preparing)}</span>
            </div>
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-indigo-500 mr-1"></span>
              <span>Ready: {safeNumber(stats.ready)}</span>
            </div>
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-purple-500 mr-1"></span>
              <span>Out: {safeNumber(stats.outForDelivery)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="border-l-4 border-l-emerald-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Delivery Time</CardTitle>
          <div className="h-8 w-8 rounded-full bg-emerald-50 flex items-center justify-center">
            <Clock className="h-4 w-4 text-emerald-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeString(stats.averageDeliveryTime)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3 mr-1 text-emerald-500">
              <path fillRule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z" clipRule="evenodd" />
            </svg>
            <span>-2 min from last week</span>
          </p>
        </CardContent>
      </Card>
      
      {/* Additional Stats Cards */}
      <Card className="border-l-4 border-l-red-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Overdue Orders</CardTitle>
          <div className="h-8 w-8 rounded-full bg-red-50 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-red-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.overdueOrders)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            <span className="inline-block w-2 h-2 rounded-full bg-red-500 mr-1"></span>
            Require immediate attention
          </p>
        </CardContent>
      </Card>

      <Card className="border-l-4 border-l-orange-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">High Priority</CardTitle>
          <div className="h-8 w-8 rounded-full bg-orange-50 flex items-center justify-center">
            <Package className="h-4 w-4 text-orange-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.highPriorityOrders)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            <span className="inline-block w-2 h-2 rounded-full bg-orange-500 mr-1"></span>
            Orders marked as high priority
          </p>
        </CardContent>
      </Card>

      <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
          <div className="h-8 w-8 rounded-full bg-green-50 flex items-center justify-center">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.completed)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
            Successfully delivered orders
          </p>
        </CardContent>
      </Card>

      <Card className="border-l-4 border-l-gray-500 shadow-sm hover:shadow transition-shadow stats-card business-admin-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
          <div className="h-8 w-8 rounded-full bg-gray-50 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-gray-500" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeNumber(stats.cancelled)}</div>
          <p className="text-xs text-muted-foreground flex items-center">
            <span className="inline-block w-2 h-2 rounded-full bg-gray-500 mr-1"></span>
            Orders that were cancelled
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
