'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import {
  User,
  ShoppingBag,
  MapPin,
  Settings,
  LayoutDashboard
} from 'lucide-react'

const accountNavItems = [
  {
    href: '/account',
    label: 'Dashboard',
    icon: LayoutDashboard,
    description: 'Account overview'
  },
  {
    href: '/account/profile',
    label: 'Profile',
    icon: User,
    description: 'Personal information'
  },
  {
    href: '/account/orders',
    label: 'Order History',
    icon: ShoppingBag,
    description: 'View past orders'
  },
  {
    href: '/account/addresses',
    label: 'Addresses',
    icon: MapPin,
    description: 'Saved addresses'
  },
  {
    href: '/account/settings',
    label: 'Settings',
    icon: Settings,
    description: 'Account preferences'
  }
]

export function AccountNavigation() {
  const pathname = usePathname()

  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-lg font-semibold mb-4">Account</h2>
        <nav className="space-y-2">
          {accountNavItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors',
                  isActive
                    ? 'bg-emerald-600 text-white shadow-md'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <Icon className="h-4 w-4" />
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className={cn(
                    'text-xs',
                    isActive ? 'text-white/80' : 'text-gray-500'
                  )}>
                    {item.description}
                  </div>
                </div>
              </Link>
            )
          })}
        </nav>
      </CardContent>
    </Card>
  )
}
