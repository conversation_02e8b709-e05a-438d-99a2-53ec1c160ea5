import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface PushSubscriptionData {
  endpoint: string
  expirationTime?: number | null
  keys: {
    p256dh: string
    auth: string
  }
}

interface DriverSubscribeRequest {
  subscription: PushSubscriptionData
  deviceType?: string
  browserName?: string
  userAgent?: string
}

// POST - Subscribe driver to push notifications (simplified for testing)
export async function POST(request: NextRequest) {
  try {
    const body: DriverSubscribeRequest = await request.json()

    if (!body.subscription || !body.subscription.endpoint) {
      return NextResponse.json(
        { error: 'Valid push subscription is required' },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Use the auth_id for push subscriptions
    const authUserId = user.auth_id

    // Default preferences for drivers
    const driverPreferences = {
      order_updates: true,
      delivery_updates: true,
      messages: true,
      marketing: false,
      quiet_hours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      }
    }

    // First, deactivate any existing subscriptions for this user and device type
    await supabase
      .from('push_subscriptions')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', authUserId)
      .eq('device_type', body.deviceType || 'mobile')

    // Create new subscription record
    const subscriptionRecord = {
      user_id: authUserId,
      subscription_data: body.subscription,
      device_type: body.deviceType || 'mobile',
      browser_name: body.browserName || 'Unknown',
      user_agent: body.userAgent || 'Unknown',
      preferences: driverPreferences,
      is_active: true,
      last_used_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Try to insert, if it fails due to unique constraint, update instead
    let { data, error } = await supabase
      .from('push_subscriptions')
      .insert(subscriptionRecord)
      .select()
      .single()

    if (error && error.code === '23505') {
      // Unique constraint violation - update existing record
      console.log('Driver subscription exists, updating instead...')
      const result = await supabase
        .from('push_subscriptions')
        .update({
          subscription_data: body.subscription,
          device_type: body.deviceType || 'mobile',
          browser_name: body.browserName || 'Unknown',
          user_agent: body.userAgent || 'Unknown',
          preferences: driverPreferences,
          is_active: true,
          last_used_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', authUserId)
        .eq('subscription_data->endpoint', body.subscription.endpoint)
        .select()
        .single()

      data = result.data
      error = result.error
    }

    if (error) {
      console.error('Error creating/updating driver push subscription:', error)
      return NextResponse.json(
        { error: 'Failed to create push subscription', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Driver push subscription created successfully',
      subscription: data
    })

  } catch (error: any) {
    console.error('Error in driver push subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
