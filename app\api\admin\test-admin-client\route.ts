import { NextResponse } from 'next/server';
import { adminClient } from '@/lib/supabase-admin';

/**
 * API endpoint to test the adminClient
 * This is an admin-only endpoint that should be protected
 */
export async function GET(request: Request) {
  try {
    // Test the adminClient by fetching a small amount of data
    const { data, error } = await adminClient
      .from('users')
      .select('id, email')
      .limit(1);

    if (error) {
      console.error('Error testing adminClient:', error);
      return NextResponse.json(
        { error: 'Failed to test adminClient', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'adminClient is working correctly',
      data
    });
  } catch (error: any) {
    console.error('Error in test-admin-client endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
