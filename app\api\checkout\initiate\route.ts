import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * CHECKOUT VALIDATION API
 *
 * Validates cart data when customer navigates to checkout.
 * Does NOT create orders - orders are created only when Place Order is clicked.
 *
 * POST /api/checkout/initiate
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, sessionId } = body;

    console.log('🛒 Validating checkout for:', { userId, sessionId });

    // Validate input
    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'Either userId or sessionId is required' },
        { status: 400 }
      );
    }

    // Determine cart identifier and type
    const isUserCart = !!userId;
    const cartIdentifier = isUserCart ? userId : sessionId;

    console.log(`🔍 Looking for carts for ${isUserCart ? 'user' : 'session'}: ${cartIdentifier}`);

    // Get all user_carts using is_anonymous flag to determine which identifier to use
    let cartsQuery = supabase
      .from('user_carts')
      .select(`
        id,
        business_id,
        delivery_method,
        delivery_fee,
        is_anonymous,
        user_id,
        session_id,
        created_at,
        updated_at
      `);

    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      cartsQuery = cartsQuery.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      cartsQuery = cartsQuery.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: carts, error: cartsError } = await cartsQuery;

    if (cartsError) {
      console.error('Error fetching carts:', cartsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart data' },
        { status: 500 }
      );
    }

    if (!carts || carts.length === 0) {
      return NextResponse.json(
        { error: 'No cart data found' },
        { status: 404 }
      );
    }

    console.log(`📦 Found ${carts.length} business carts`);

    // Get all cart items for these carts
    const cartIds = carts.map(cart => cart.id);
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select(`
        id,
        cart_id,
        product_id,
        variant_id,
        quantity,
        business_id,
        name,
        price,
        image_url,
        session_id
      `)
      .in('cart_id', cartIds);

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart items' },
        { status: 500 }
      );
    }

    console.log(`📋 Found ${cartItems?.length || 0} cart items`);

    // Group items by business
    const itemsByBusiness: Record<number, any[]> = {};
    cartItems?.forEach(item => {
      if (!itemsByBusiness[item.business_id]) {
        itemsByBusiness[item.business_id] = [];
      }
      itemsByBusiness[item.business_id].push(item);
    });

    // Get business details for all businesses
    const businessIds = carts.map(cart => cart.business_id).filter(Boolean);
    const { data: businesses, error: businessError } = await supabase
      .from('businesses')
      .select('id, name')
      .in('id', businessIds);

    if (businessError) {
      console.error('Error fetching businesses:', businessError);
      return NextResponse.json(
        { error: 'Failed to fetch business data' },
        { status: 500 }
      );
    }

    // Create business lookup
    const businessLookup: Record<number, any> = {};
    businesses?.forEach(business => {
      businessLookup[business.id] = business;
    });

    // Validate cart data for each business (NO ORDER CREATION)
    const validatedBusinesses = [];

    for (const cart of carts) {
      const business = businessLookup[cart.business_id];
      const items = itemsByBusiness[cart.business_id] || [];

      if (!business || items.length === 0) {
        console.warn(`⚠️ Skipping business ${cart.business_id}: no business data or items`);
        continue;
      }

      // Calculate totals for validation
      const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const deliveryFee = cart.delivery_fee || 0;
      const total = subtotal + deliveryFee;

      console.log(`💰 Business ${business.name}: subtotal=${subtotal}, delivery=${deliveryFee}, total=${total}`);

      // Validate business data (no order creation)
      validatedBusinesses.push({
        businessId: cart.business_id,
        businessName: business.name,
        subtotal,
        deliveryFee,
        total,
        itemCount: items.length,
        cartId: cart.id,
        deliveryMethod: cart.delivery_method || 'delivery'
      });
    }

    console.log(`✅ Successfully validated ${validatedBusinesses.length} business carts`);
    console.log('📝 No orders created - orders will be created when Place Order is clicked');

    return NextResponse.json({
      success: true,
      message: `Validated ${validatedBusinesses.length} business carts for checkout`,
      businesses: validatedBusinesses,
      totalBusinesses: validatedBusinesses.length,
      totalAmount: validatedBusinesses.reduce((sum, business) => sum + business.total, 0),
      ordersCreated: false // Explicitly indicate no orders were created
    });

  } catch (error: any) {
    console.error('Checkout validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error during checkout validation' },
      { status: 500 }
    );
  }
}
