"use client"

import { useState, useEffect } from "react"
import { Clock, MapPin, Truck, AlertCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {calculateDeliveryTime, DeliveryTimeEstimate} from "@/lib/distance-calculator"

interface DeliveryTimeEstimatorProps {
  restaurantAddress: string // This can be a restaurant or shop address
  customerAddress: string
  preparationTimeMinutes?: number
  className?: string
  onEstimateChange?: (estimate: DeliveryTimeEstimate | null) => void
}

export default function DeliveryTimeEstimator({
  restaurantAddress,
  customerAddress,
  preparationTimeMinutes = 15,
  className = "",
  onEstimateChange = undefined,
}: DeliveryTimeEstimatorProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deliveryEstimate, setDeliveryEstimate] = useState<DeliveryTimeEstimate | null>(null)

  useEffect(() => {
    const calculateEstimate = async () => {
      setIsLoading(true)
      setError(null)
      setDeliveryEstimate(null)

      try {
        // Default coordinates for Jersey (St Helier)
        const defaultPickupCoords: [number, number] = [-2.1037, 49.1805]

        // Validate addresses
        if (!restaurantAddress || restaurantAddress.trim().length < 3) {
          setError("Please enter a valid pickup location")
          setIsLoading(false)
          return
        }

        if (!customerAddress || customerAddress.trim().length < 3) {
          setError("Please enter a valid delivery address")
          setIsLoading(false)
          return
        }

        // Geocode the pickup location address
        let pickupCoords: [number, number] | null = null
        try {
          pickupCoords = await geocodeAddress(restaurantAddress)
          if (!pickupCoords) {
            console.warn("Could not geocode pickup location address, using default")
            pickupCoords = defaultPickupCoords
          }
        } catch (err) {
          console.warn("Error geocoding pickup location address:", err)
          pickupCoords = defaultPickupCoords
        }

        // Geocode the customer address
        let customerCoords: [number, number] | null = null
        try {
          customerCoords = await geocodeAddress(customerAddress)
          if (!customerCoords) {
            setError("Could not find your delivery location. Please try a different address.")
            setIsLoading(false)
            return
          }
        } catch (err) {
          console.error("Error geocoding customer address:", err)
          setError("Could not find your delivery location. Please try a different address.")
          setIsLoading(false)
          return
        }

        // Calculate delivery time
        let estimate: DeliveryTimeEstimate | null = null
        try {
          estimate = await calculateDeliveryTime(
            pickupCoords[0],
            pickupCoords[1],
            customerCoords[0],
            customerCoords[1],
            preparationTimeMinutes
          )
        } catch (err) {
          console.error("Error calculating delivery time:", err)
        }

        if (!estimate) {
          // Create a fallback estimate
          const durationMinutes = 20;
          const totalDeliveryTimeMinutes = durationMinutes + preparationTimeMinutes;

          const now = new Date();
          const deliveryTime = new Date(now.getTime() + totalDeliveryTimeMinutes * 60 * 1000);
          const earliestDelivery = new Date(deliveryTime.getTime() - 10 * 60 * 1000);
          const latestDelivery = new Date(deliveryTime.getTime() + 10 * 60 * 1000);

          estimate = {
            distance: 2.0,
            duration: durationMinutes,
            formattedDistance: "~2.0 km",
            formattedDuration: `${durationMinutes} min`,
            estimatedDeliveryTime: formatTime(deliveryTime),
            estimatedDeliveryRange: `${formatTime(earliestDelivery)} - ${formatTime(latestDelivery)}`,
          };
        }

        setDeliveryEstimate(estimate)

        // Call the callback if provided
        if (onEstimateChange) {
          onEstimateChange(estimate)
        }


      } catch (err) {
        console.error("Error calculating delivery time:", err)
        setError("An error occurred while calculating delivery time. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    if (restaurantAddress && customerAddress) {
      calculateEstimate()
    } else {
      // Reset state when addresses are not provided
      setDeliveryEstimate(null)
      setIsLoading(false)
      setError(null)
    }
  }, [restaurantAddress, customerAddress, preparationTimeMinutes])

  // Helper function to format time
  function formatTime(date: Date): string {
    return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Delivery Estimate</CardTitle>
        <CardDescription>Estimated time for your order to arrive</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <Skeleton className="h-[150px] w-full rounded-md" />
          </>
        ) : (
          <>
            <div className="grid gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-emerald-600" />
                <div>
                  <p className="font-medium">Estimated Delivery Time</p>
                  <p className="text-sm text-gray-500">
                    {deliveryEstimate?.estimatedDeliveryRange || "Unknown"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-emerald-600" />
                <div>
                  <p className="font-medium">Delivery Distance</p>
                  <p className="text-sm text-gray-500">
                    {deliveryEstimate?.distance ? `${deliveryEstimate.distance.toFixed(1)}km` : "Unknown"} ({deliveryEstimate?.formattedDuration || "Unknown"} drive)
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-emerald-600" />
                <div>
                  <p className="font-medium">Delivery Address</p>
                  <p className="text-sm text-gray-500">{customerAddress}</p>
                </div>
              </div>
            </div>


          </>
        )}
      </CardContent>
      <CardFooter className="text-xs text-gray-500">
        Delivery times are estimates and may vary based on traffic and weather conditions.
      </CardFooter>
    </Card>
  )
}
