import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// POST handler to create a user profile during registration
export async function POST(request: Request) {
  try {
    // Get request body
    const { email, name, phone, address, role } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      )
    }

    console.log("Creating user profile for registration:", email)

    // Check if user already exists
    const { data: existingUser, error: checkError } = await adminClient
      .from("users")
      .select("id")
      .ilike("email", email)
      .maybeSingle()

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Error checking user existence:", checkError)
      return NextResponse.json(
        { error: `Error checking user existence: ${checkError.message}` },
        { status: 500 }
      )
    }

    let userData

    if (existingUser) {
      console.log("User already exists, updating profile:", existingUser.id)
      
      // Update existing user
      const { data, error } = await adminClient
        .from("users")
        .update({
          name: name || email.split('@')[0],
          phone: phone || null,
          address: address || null,
          role: role || 'customer',
          updated_at: new Date().toISOString()
        })
        .eq("id", existingUser.id)
        .select()
        .single()

      if (error) {
        console.error("Error updating user profile:", error)
        return NextResponse.json(
          { error: `Error updating user profile: ${error.message}` },
          { status: 500 }
        )
      }

      userData = data
    } else {
      console.log("Creating new user profile")
      
      // Create new user
      const { data, error } = await adminClient
        .from("users")
        .insert([{
          name: name || email.split('@')[0],
          email,
          phone: phone || null,
          address: address || null,
          role: role || 'customer',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) {
        console.error("Error creating user profile:", error)
        return NextResponse.json(
          { error: `Error creating user profile: ${error.message}` },
          { status: 500 }
        )
      }

      userData = data
    }

    return NextResponse.json({
      data: userData,
      message: "User profile created successfully"
    })
  } catch (error: any) {
    console.error("Unexpected error in POST /api/auth/register-user:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
