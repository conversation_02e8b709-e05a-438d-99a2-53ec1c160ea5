"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"
import BusinessManagerRegistration from "@/components/auth/business-manager-registration"

export default function RegisterBusinessPage() {
  const router = useRouter()
  const { user, isBusinessManager } = useAuth()

  // Redirect if already logged in as a business manager
  useEffect(() => {
    if (user && isBusinessManager) {
      router.push("/business-admin/dashboard")
    }
  }, [user, isBusinessManager, router])

  return (
    <div className="min-h-screen bg-white">
      <div className="container-fluid py-12">
        <div className="max-w-3xl mx-auto">
          <BusinessManagerRegistration />

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              By registering, you agree to our{" "}
              <Link href="/terms" className="text-emerald-600 hover:underline">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link href="/privacy" className="text-emerald-600 hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
