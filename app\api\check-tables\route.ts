import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET() {
  try {
    console.log('Check Tables API called');
    
    // Check if the business_types table exists
    const { data: businessTypesData, error: businessTypesError } = await adminClient
      .from('business_types')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the businesses table exists
    const { data: businessesData, error: businessesError } = await adminClient
      .from('businesses')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the users table exists
    const { data: usersData, error: usersError } = await adminClient
      .from('users')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the categories table exists
    const { data: categoriesData, error: categoriesError } = await adminClient
      .from('categories')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the products table exists
    const { data: productsData, error: productsError } = await adminClient
      .from('products')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the business_categories table exists
    const { data: businessCategoriesData, error: businessCategoriesError } = await adminClient
      .from('business_categories')
      .select('count(*)', { count: 'exact', head: true });
    
    // Check if the business_attributes table exists
    const { data: businessAttributesData, error: businessAttributesError } = await adminClient
      .from('business_attributes')
      .select('count(*)', { count: 'exact', head: true });
    
    // Try to get the list of tables from Postgres
    const { data: tablesData, error: tablesError } = await adminClient.rpc('get_tables');
    
    return NextResponse.json({
      tables: {
        business_types: {
          exists: !businessTypesError,
          count: businessTypesData,
          error: businessTypesError ? businessTypesError.message : null
        },
        businesses: {
          exists: !businessesError,
          count: businessesData,
          error: businessesError ? businessesError.message : null
        },
        users: {
          exists: !usersError,
          count: usersData,
          error: usersError ? usersError.message : null
        },
        categories: {
          exists: !categoriesError,
          count: categoriesData,
          error: categoriesError ? categoriesError.message : null
        },
        products: {
          exists: !productsError,
          count: productsData,
          error: productsError ? productsError.message : null
        },
        business_categories: {
          exists: !businessCategoriesError,
          count: businessCategoriesData,
          error: businessCategoriesError ? businessCategoriesError.message : null
        },
        business_attributes: {
          exists: !businessAttributesError,
          count: businessAttributesData,
          error: businessAttributesError ? businessAttributesError.message : null
        }
      },
      postgres_tables: {
        data: tablesData,
        error: tablesError ? tablesError.message : null
      }
    });
  } catch (error) {
    console.error('Error in check-tables API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
