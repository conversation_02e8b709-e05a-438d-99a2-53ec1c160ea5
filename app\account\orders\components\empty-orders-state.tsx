'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ShoppingBag, Search, Filter } from 'lucide-react'

interface EmptyOrdersStateProps {
  hasFilters: boolean
  onClearFilters: () => void
}

export function EmptyOrdersState({ hasFilters, onClearFilters }: EmptyOrdersStateProps) {
  if (hasFilters) {
    return (
      <Card className="border-0 shadow-sm rounded-2xl bg-white">
        <CardContent className="py-16 text-center">
          <div className="p-6 bg-gray-100 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
            <Search className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-3">
            No orders found
          </h3>
          <p className="text-gray-600 mb-8 text-lg max-w-md mx-auto">
            No orders match your current search or filter criteria. Try adjusting your filters or search terms.
          </p>
          <Button
            onClick={onClearFilters}
            variant="outline"
            className="bg-white hover:bg-gray-50 border-gray-200 rounded-xl px-8 py-3 text-base"
          >
            <Filter className="h-5 w-5 mr-2" />
            Clear All Filters
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 shadow-sm rounded-2xl bg-white">
      <CardContent className="py-16 text-center">
        <div className="p-6 bg-emerald-100 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
          <ShoppingBag className="h-12 w-12 text-emerald-600" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900 mb-3">
          No orders yet
        </h3>
        <p className="text-gray-600 mb-8 text-lg max-w-md mx-auto">
          You haven't placed any orders yet. Start browsing businesses to place your first order and discover amazing local food!
        </p>
        <Button
          asChild
          className="bg-emerald-600 hover:bg-emerald-700 text-white rounded-xl px-8 py-3 text-base"
        >
          <a href="/businesses">
            Browse Businesses
          </a>
        </Button>
      </CardContent>
    </Card>
  )
}
