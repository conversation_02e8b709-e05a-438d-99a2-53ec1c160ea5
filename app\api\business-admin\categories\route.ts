import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("Categories API called")

    // Parse URL to get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')

    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    console.log("Auth check result:", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;
    let userId = session?.user?.id;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found")
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    console.log("User authenticated:", userEmail)

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, business_id")
      .eq("email", userEmail)
      .single()

    if (userError) {
      console.error("Error fetching user profile:", userError)

      // Check if the table exists
      const { data: tableExists } = await adminClient
        .from("pg_tables")
        .select("tablename")
        .eq("tablename", "users")
        .eq("schemaname", "public")
        .single()

      if (!tableExists) {
        console.log("Users table doesn't exist, returning default categories")
        // Return default categories for development
        return NextResponse.json({
          categories: [
            {
              id: 1,
              name: "Starters",
              description: "Appetizers and small plates",
              display_order: 1,
              business_id: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product_count: 0
            },
            {
              id: 2,
              name: "Main Courses",
              description: "Main dishes and entrees",
              display_order: 2,
              business_id: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product_count: 0
            },
            {
              id: 3,
              name: "Desserts",
              description: "Sweet treats and desserts",
              display_order: 3,
              business_id: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product_count: 0
            }
          ]
        })
      }

      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    if (!userProfile) {
      console.error("User profile not found")
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    console.log("User profile:", userProfile)

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Determine which business ID to use
    let businessId: number | null = null

    if (businessIdParam && (isAdmin || isSuperAdmin)) {
      // Admin users can specify a business ID via query parameter
      businessId = parseInt(businessIdParam)
      console.log(`Admin user requesting categories for business ID: ${businessId}`)
    } else if (userProfile.business_id) {
      // Regular business managers use their associated business ID
      businessId = userProfile.business_id
    } else if (!isAdmin && !isSuperAdmin) {
      console.error("Business manager without a business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    // Check if business_custom_categories table exists
    const { data: categoriesTableExists } = await adminClient
      .from("pg_tables")
      .select("tablename")
      .eq("tablename", "business_custom_categories")
      .eq("schemaname", "public")
      .single()

    if (!categoriesTableExists) {
      console.log("Categories table doesn't exist, returning default categories")
      // Return default categories for development
      return NextResponse.json({
        categories: [
          {
            id: 1,
            name: "Starters",
            description: "Appetizers and small plates",
            display_order: 1,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 2,
            name: "Main Courses",
            description: "Main dishes and entrees",
            display_order: 2,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 3,
            name: "Desserts",
            description: "Sweet treats and desserts",
            display_order: 3,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        ]
      })
    }

    // Build the query based on user role and business ID
    let query = adminClient
      .from("business_custom_categories")
      .select(`
        id,
        name,
        description,
        display_order,
        business_id,
        created_at,
        updated_at
      `)
      .order("display_order", { ascending: true })
      .order("name", { ascending: true })

    // Apply business filter based on user role and business ID
    if (businessId) {
      query = query.eq("business_id", businessId)
    } else if (isBusinessManager) {
      // Business managers must have a business ID
      console.error("Business manager without business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    const { data: categories, error: categoriesError } = await query

    if (categoriesError) {
      console.error("Error fetching categories:", categoriesError)

      // Return default categories for development
      return NextResponse.json({
        categories: [
          {
            id: 1,
            name: "Starters",
            description: "Appetizers and small plates",
            display_order: 1,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 2,
            name: "Main Courses",
            description: "Main dishes and entrees",
            display_order: 2,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 3,
            name: "Desserts",
            description: "Sweet treats and desserts",
            display_order: 3,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        ]
      })
    }

    // If no categories found, return default categories
    if (!categories || categories.length === 0) {
      console.log("No categories found, returning default categories")
      return NextResponse.json({
        categories: [
          {
            id: 1,
            name: "Starters",
            description: "Appetizers and small plates",
            display_order: 1,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 2,
            name: "Main Courses",
            description: "Main dishes and entrees",
            display_order: 2,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 3,
            name: "Desserts",
            description: "Sweet treats and desserts",
            display_order: 3,
            business_id: businessId || 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        ]
      })
    }

    return NextResponse.json({ categories })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/categories:", error)

    // Return default categories for development
    return NextResponse.json({
      categories: [
        {
          id: 1,
          name: "Starters",
          description: "Appetizers and small plates",
          display_order: 1,
          business_id: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          product_count: 0
        },
        {
          id: 2,
          name: "Main Courses",
          description: "Main dishes and entrees",
          display_order: 2,
          business_id: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          product_count: 0
        },
        {
          id: 3,
          name: "Desserts",
          description: "Sweet treats and desserts",
          display_order: 3,
          business_id: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          product_count: 0
        }
      ]
    })
  }
}

export async function POST(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found")
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, business_id")
      .eq("email", userEmail)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    if (!businessId && !isAdmin && !isSuperAdmin) {
      console.error("Business manager without a business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    // Parse the request body
    const requestData = await request.json()
    const { name, description, display_order } = requestData

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      )
    }

    // Create the category
    const { data: category, error: categoryError } = await adminClient
      .from("categories")
      .insert({
        name,
        description,
        display_order: display_order || 0,
        business_id: businessId
      })
      .select()
      .single()

    if (categoryError) {
      console.error("Error creating category:", categoryError)
      return NextResponse.json(
        { error: "Failed to create category" },
        { status: 500 }
      )
    }

    return NextResponse.json({ category })
  } catch (error: any) {
    console.error("Error in POST /api/business-admin/categories:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
