import Link from "next/link"
import {
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  ShoppingBag,
  Clock,
  Users,
  BarChart3,
  ChevronRight,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export default function RestaurantAdminDashboard() {
  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Restaurant Dashboard</h1>
          <p className="text-gray-500">Welcome back, Jersey Grill</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline">Download Report</Button>
          <Button className="bg-emerald-600 hover:bg-emerald-700">View Live Orders</Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <ShoppingBag className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                12%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <h3 className="text-2xl font-bold">246</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+28 from yesterday</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                8%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <h3 className="text-2xl font-bold">£3,426.00</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+£246 from yesterday</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-orange-100 p-2 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
                <ArrowDownRight className="mr-1 h-3 w-3" />
                3%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Avg. Delivery Time</p>
              <h3 className="text-2xl font-bold">32 min</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+2 min from yesterday</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                5%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">New Customers</p>
              <h3 className="text-2xl font-bold">38</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+6 from yesterday</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders & Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>You have 12 orders in progress</CardDescription>
            </div>
            <Link href="/restaurant-admin/orders">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Order 1 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="bg-emerald-100 text-emerald-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      New Order
                    </div>
                    <span className="ml-2 text-sm font-medium">Order #JE-5289</span>
                  </div>
                  <span className="text-sm text-gray-500">2 mins ago</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Sarah Johnson</p>
                    <p className="text-sm text-gray-500">2× Jersey Crab Cakes, 1× Grilled Sea Bass</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">£42.90</p>
                    <Link href="/restaurant-admin/orders/5289" className="text-emerald-600 text-sm flex items-center">
                      Details <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>

              {/* Order 2 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      Preparing
                    </div>
                    <span className="ml-2 text-sm font-medium">Order #JE-5288</span>
                  </div>
                  <span className="text-sm text-gray-500">15 mins ago</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Michael Brown</p>
                    <p className="text-sm text-gray-500">1× Jersey Beef Burger, 1× Apple Crumble</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">£23.90</p>
                    <Link href="/restaurant-admin/orders/5288" className="text-emerald-600 text-sm flex items-center">
                      Details <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>

              {/* Order 3 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      Out for Delivery
                    </div>
                    <span className="ml-2 text-sm font-medium">Order #JE-5287</span>
                  </div>
                  <span className="text-sm text-gray-500">32 mins ago</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Emma Wilson</p>
                    <p className="text-sm text-gray-500">2× Seared Jersey Scallops, 1× Jersey Lobster</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">£56.40</p>
                    <Link href="/restaurant-admin/orders/5287" className="text-emerald-600 text-sm flex items-center">
                      Details <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>

              {/* Order 4 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      Delivered
                    </div>
                    <span className="ml-2 text-sm font-medium">Order #JE-5286</span>
                  </div>
                  <span className="text-sm text-gray-500">1 hour ago</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">James Taylor</p>
                    <p className="text-sm text-gray-500">1× Jersey Cream Brûlée, 1× Classic Prawn Cocktail</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">£18.90</p>
                    <Link href="/restaurant-admin/orders/5286" className="text-emerald-600 text-sm flex items-center">
                      Details <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance</CardTitle>
            <CardDescription>Your restaurant metrics for today</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Order Acceptance Rate</p>
                  <p className="text-sm font-medium">98%</p>
                </div>
                <Progress value={98} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">On-Time Delivery</p>
                  <p className="text-sm font-medium">92%</p>
                </div>
                <Progress value={92} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Customer Satisfaction</p>
                  <p className="text-sm font-medium">4.8/5</p>
                </div>
                <Progress value={96} className="h-2" />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Menu Item Performance</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Jersey Crab Cakes</span>
                    <span className="font-medium">32 orders</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Grilled Sea Bass</span>
                    <span className="font-medium">28 orders</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Jersey Beef Burger</span>
                    <span className="font-medium">24 orders</span>
                  </div>
                </div>
              </div>

              <div className="pt-4">
                <Link href="/restaurant-admin/analytics">
                  <Button variant="outline" className="w-full">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    View Detailed Analytics
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
