"use client"

import { useState, useEffect } from "react"
import React from "react"
import Link from "next/link"
import {
  MapPin,
  Phone,
  MessageSquare,
  CheckCircle,
  Package,
  Navigation,
  AlertTriangle,
  Map,
  RefreshCw,
  Clock,
  ArrowLeft,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import DriverLocationTracker from "@/components/driver-location-tracker"
import { OrderWeightChart } from "@/components/driver/order-weight-chart"
import { simulateDriverMovement } from "@/services/location-service"

interface OrderData {
  id: number
  order_number: string
  business_name: string
  customer_name: string
  customer_phone: string
  delivery_address: string
  postcode: string
  parish: string
  delivery_type: string
  total: number
  delivery_fee?: number
  status: string
  delivery_distance_km?: number
  estimated_delivery_time?: string | number
  assigned_at?: string
  businesses: {
    id: number
    name: string
    address: string
    phone: string
    latitude?: number
    coordinates?: string
  }
  cart_items?: Array<{
    id: string
    name: string
    product_id: number
    quantity: number
    price: number
    business_id: number
    image_url?: string
  }>
  created_at: string
  ready_time?: string
}

export default function MobileDeliveryDetailsPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)
  const [orderData, setOrderData] = useState<OrderData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [reportReason, setReportReason] = useState("")
  const [isSimulating, setIsSimulating] = useState(false)
  const [showPickupConfirmation, setShowPickupConfirmation] = useState(false)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Fetch order data
  const fetchOrderData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Use the dedicated driver order details API
      const response = await fetch(`/api/driver/orders/${unwrappedParams.id}`)

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.order) {
          setOrderData(data.order)
          setIsLoading(false)
          return
        }
      }

      // If the dedicated API fails, fall back to the previous method
      // First try to get from available orders
      const availableResponse = await fetch('/api/driver/available-orders')
      if (availableResponse.ok) {
        const availableData = await availableResponse.json()

        // Check if this is the current order
        if (availableData.currentOrder && availableData.currentOrder.order_number === unwrappedParams.id) {
          setOrderData(availableData.currentOrder)
          setIsLoading(false)
          return
        }

        // Check if it's in available orders
        const foundOrder = availableData.availableOrders?.find((order: any) =>
          order.order_number === unwrappedParams.id
        )
        if (foundOrder) {
          setOrderData(foundOrder)
          setIsLoading(false)
          return
        }
      }

      // If not found in available orders, try dashboard for current order
      const dashboardResponse = await fetch('/api/driver/dashboard')
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json()
        if (dashboardData.currentOrder && dashboardData.currentOrder.order_number === unwrappedParams.id) {
          setOrderData(dashboardData.currentOrder)
          setIsLoading(false)
          return
        }
      }

      // If still not found, show error
      setError('Order not found or not assigned to you')

    } catch (err) {
      console.error('Error fetching order data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load order data')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle pickup confirmation
  const handlePickupConfirmation = () => {
    setShowPickupConfirmation(true)
  }

  // Confirm pickup with verification
  const confirmPickup = async () => {
    if (!orderData) return

    setShowPickupConfirmation(false)
    await updateDeliveryStatus("out_for_delivery", "Order picked up from business")
  }

  // Update delivery status
  const updateDeliveryStatus = async (newStatus: string, notes?: string) => {
    if (!orderData) return

    setIsUpdatingStatus(true)
    setError(null) // Clear any previous errors

    try {
      const response = await fetch('/api/driver/update-delivery-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: orderData.id,
          status: newStatus,
          notes: notes || undefined
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`)
      }

      const result = await response.json()

      // Handle pickup confirmation success
      if (newStatus === "out_for_delivery" && result.success) {
        // Show success message for pickup confirmation
        setSuccessMessage("✅ Pickup confirmed! Order is now out for delivery.")
        setTimeout(() => setSuccessMessage(null), 4000)
      }

      // Update local order data for full status changes
      setOrderData(prev => prev ? { ...prev, status: newStatus } : null)

      // Show success message
      const statusMessages = {
        assigned: "Order accepted successfully!",
        out_for_delivery: "🚚 Pickup confirmed! You're now on the way to deliver.",
        delivered: "🎉 Congratulations! Delivery completed successfully! You earned " + formatCurrency(orderData.delivery_fee || 3.50) + "!"
      }
      setSuccessMessage(statusMessages[newStatus as keyof typeof statusMessages] || "Status updated successfully!")

      // Clear success message after different durations based on status
      const messageDuration = newStatus === 'delivered' ? 8000 : 3000 // 8 seconds for delivery completion
      setTimeout(() => setSuccessMessage(null), messageDuration)

      // Start location simulation when driver picks up the order
      if (newStatus === "picked_up" && !isSimulating && orderData.businesses?.coordinates) {
        setIsSimulating(true)

        // Extract coordinates from PostGIS point format
        const coordsMatch = orderData.businesses.coordinates.match(/\(([^,]+),([^)]+)\)/)
        if (coordsMatch) {
          const businessLng = parseFloat(coordsMatch[1])
          const businessLat = parseFloat(coordsMatch[2])

          // For demo, simulate movement to a nearby location
          simulateDriverMovement(
            "driver-1", // Driver ID
            businessLat, // Start latitude
            businessLng, // Start longitude
            businessLat + 0.01, // End latitude (slightly north)
            businessLng + 0.01, // End longitude (slightly east)
            5, // Duration in minutes (shortened for demo)
          )
        }
      }

      console.log('Status updated successfully:', result)

    } catch (err) {
      console.error('Error updating delivery status:', err)
      setError(err instanceof Error ? err.message : 'Failed to update status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Load order data on mount
  useEffect(() => {
    fetchOrderData()
  }, [unwrappedParams.id])

  // Auto-refresh order data every 2 minutes, and only for active orders
  useEffect(() => {
    // Only auto-refresh for orders that might change status
    const shouldAutoRefresh = orderData && !['delivered', 'cancelled'].includes(orderData.status)

    if (!shouldAutoRefresh) {
      return
    }

    const interval = setInterval(() => {
      if (!isLoading && !isUpdatingStatus) {
        fetchOrderData()
      }
    }, 120000) // 2 minutes instead of 30 seconds

    return () => clearInterval(interval)
  }, [isLoading, isUpdatingStatus, orderData?.status])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const getStatusBadge = () => {
    if (!orderData) return <Badge className="bg-gray-100 text-gray-800">Loading...</Badge>

    switch (orderData.status) {
      case "offered":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Offered</Badge>
      case "assigned":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Assigned</Badge>
      case "picked_up":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Picked Up</Badge>
      case "out_for_delivery":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Out for Delivery</Badge>
      case "delivered":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Delivered</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">{orderData.status}</Badge>
    }
  }

  const getProgressValue = () => {
    if (!orderData) return 0

    switch (orderData.status) {
      case "offered":
        return 10
      case "assigned":
        return 25
      case "picked_up":
        return 50
      case "out_for_delivery":
        return 75
      case "delivered":
        return 100
      default:
        return 0
    }
  }

  const getNextActionButton = () => {
    if (!orderData) return null

    switch (orderData.status) {
      case "offered":
        return (
          <Button
            className="w-full bg-emerald-600 hover:bg-emerald-700 h-14 text-lg font-semibold"
            onClick={() => updateDeliveryStatus("assigned")}
            disabled={isUpdatingStatus}
          >
            {isUpdatingStatus ? (
              <>
                <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
                Accepting Offer...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-5 w-5" />
                Accept Offer
              </>
            )}
          </Button>
        )
      case "assigned":
        return (
          <Button
            className="w-full bg-emerald-600 hover:bg-emerald-700 h-14 text-lg font-semibold"
            onClick={handlePickupConfirmation}
            disabled={isUpdatingStatus}
          >
            {isUpdatingStatus ? (
              <>
                <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
                Confirming Pickup...
              </>
            ) : (
              <>
                <Package className="mr-2 h-5 w-5" />
                Confirm Pickup & Start Delivery
              </>
            )}
          </Button>
        )
      case "out_for_delivery":
        return (
          <Button
            className="w-full bg-emerald-600 hover:bg-emerald-700 h-14 text-lg font-semibold"
            onClick={() => updateDeliveryStatus("delivered")}
            disabled={isUpdatingStatus}
          >
            {isUpdatingStatus ? (
              <>
                <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
                Completing Delivery...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-5 w-5" />
                Complete Delivery
              </>
            )}
          </Button>
        )
      case "delivered":
        return (
          <Link href="/driver-mobile/dashboard">
            <Button className="w-full bg-emerald-600 hover:bg-emerald-700 h-14 text-lg font-semibold text-white">
              <CheckCircle className="mr-2 h-5 w-5" />
              Return to Dashboard
            </Button>
          </Link>
        )
      default:
        return null
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="pb-20">
        <div className="sticky top-0 z-30 bg-white border-b shadow-sm p-4">
          <div className="flex items-center">
            <Link href="/driver-mobile/dashboard" className="mr-3">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-lg font-bold">Loading Order...</h1>
            </div>
          </div>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Loading order details...</span>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !orderData) {
    return (
      <div className="pb-20">
        <div className="sticky top-0 z-30 bg-white border-b shadow-sm p-4">
          <div className="flex items-center">
            <Link href="/driver-mobile/dashboard" className="mr-3">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-lg font-bold">Order Not Found</h1>
            </div>
          </div>
        </div>
        <div className="p-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error || 'Order not found'}</AlertDescription>
          </Alert>
          <Button onClick={fetchOrderData} className="w-full mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      {/* Error Alert */}
      {error && (
        <div className="p-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      )}

      <div className="p-4 space-y-4">
        {/* Delivery Celebration - Only show when delivered */}
        {orderData.status === 'delivered' && (
          <Card className="border-2 border-emerald-200 bg-gradient-to-br from-emerald-50 to-green-50 shadow-lg">
            <CardContent className="p-8 text-center">
              <div className="space-y-6">
                {/* Celebration Icon */}
                <div className="relative">
                  <div className="mx-auto w-20 h-20 bg-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                    <CheckCircle className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 text-2xl animate-bounce">🎉</div>
                  <div className="absolute -bottom-2 -left-2 text-2xl animate-bounce delay-150">✨</div>
                </div>

                {/* Celebration Message */}
                <div className="space-y-3">
                  <h2 className="text-2xl font-bold text-emerald-800">
                    🎉 Delivery Completed!
                  </h2>
                  <p className="text-lg text-emerald-700 font-medium">
                    Great job! Order #{orderData.order_number} has been successfully delivered.
                  </p>
                  <div className="bg-white/60 rounded-lg p-4 border border-emerald-200">
                    <p className="text-emerald-800 font-semibold">
                      You earned {formatCurrency(orderData.delivery_fee || 3.50)} for this delivery! 💰
                    </p>
                  </div>
                </div>

                {/* Delivery Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="bg-white/60 rounded-lg p-3 border border-emerald-200">
                    <div className="flex items-center justify-center space-x-2">
                      <MapPin className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm font-medium text-emerald-800">Distance</span>
                    </div>
                    <p className="text-lg font-bold text-emerald-900 mt-1">
                      {orderData.delivery_distance_km ? `${orderData.delivery_distance_km.toFixed(1)} km` : 'N/A'}
                    </p>
                  </div>
                  <div className="bg-white/60 rounded-lg p-3 border border-emerald-200">
                    <div className="flex items-center justify-center space-x-2">
                      <Clock className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm font-medium text-emerald-800">Business</span>
                    </div>
                    <p className="text-lg font-bold text-emerald-900 mt-1">
                      {orderData.business_name}
                    </p>
                  </div>
                </div>

                {/* Thank You Message */}
                <div className="pt-4 border-t border-emerald-200">
                  <p className="text-emerald-700 font-medium">
                    Thank you for providing excellent service to our Jersey community! 🏝️
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Delivery Progress */}
      <div className="p-4">
        <Card className="border-2 border-emerald-100 bg-emerald-50/50">
          <CardContent className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-gray-900">Delivery Progress</h3>
                <span className="text-sm text-emerald-600 font-medium capitalize">
                  {orderData.status === 'offered' ? 'Offer Available' :
                   orderData.status === 'assigned' ? 'Ready for Pickup' :
                   orderData.status === 'picked_up' ? 'Order Collected' :
                   orderData.status === 'out_for_delivery' ? 'On the Way' :
                   orderData.status === 'delivered' ? 'Completed' :
                   orderData.status.replace('_', ' ')}
                </span>
              </div>
              <Progress value={getProgressValue()} className="h-3 bg-gray-200 [&>div]:bg-emerald-500" />
            </div>
            <div className="grid grid-cols-4 gap-2">
              <div className="text-center space-y-2">
                <div
                  className={`mx-auto h-10 w-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    ["assigned", "picked_up", "out_for_delivery", "delivered"].includes(orderData.status)
                      ? "bg-emerald-500 border-emerald-500 text-white shadow-lg"
                      : "bg-gray-100 border-gray-300 text-gray-400"
                  }`}
                >
                  <CheckCircle className="h-5 w-5" />
                </div>
                <p className={`text-xs font-medium ${
                  ["assigned", "picked_up", "out_for_delivery", "delivered"].includes(orderData.status)
                    ? "text-emerald-600"
                    : "text-gray-500"
                }`}>Assigned</p>
              </div>
              <div className="text-center space-y-2">
                <div
                  className={`mx-auto h-10 w-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    ["picked_up", "out_for_delivery", "delivered"].includes(orderData.status)
                      ? "bg-emerald-500 border-emerald-500 text-white shadow-lg"
                      : "bg-gray-100 border-gray-300 text-gray-400"
                  }`}
                >
                  <Package className="h-5 w-5" />
                </div>
                <p className={`text-xs font-medium ${
                  ["picked_up", "out_for_delivery", "delivered"].includes(orderData.status)
                    ? "text-emerald-600"
                    : "text-gray-500"
                }`}>Picked Up</p>
              </div>
              <div className="text-center space-y-2">
                <div
                  className={`mx-auto h-10 w-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    ["out_for_delivery", "delivered"].includes(orderData.status)
                      ? "bg-emerald-500 border-emerald-500 text-white shadow-lg"
                      : "bg-gray-100 border-gray-300 text-gray-400"
                  }`}
                >
                  <Navigation className="h-5 w-5" />
                </div>
                <p className={`text-xs font-medium ${
                  ["out_for_delivery", "delivered"].includes(orderData.status)
                    ? "text-emerald-600"
                    : "text-gray-500"
                }`}>On Way</p>
              </div>
              <div className="text-center space-y-2">
                <div
                  className={`mx-auto h-10 w-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    orderData.status === "delivered"
                      ? "bg-emerald-500 border-emerald-500 text-white shadow-lg"
                      : "bg-gray-100 border-gray-300 text-gray-400"
                  }`}
                >
                  <CheckCircle className="h-5 w-5" />
                </div>
                <p className={`text-xs font-medium ${
                  orderData.status === "delivered"
                    ? "text-emerald-600"
                    : "text-gray-500"
                }`}>Delivered</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <div className="grid grid-cols-2 gap-3">
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                className="h-14 flex-col space-y-1.5 border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
              >
                <Map className="h-5 w-5 text-blue-600" />
                <span className="text-xs font-medium text-gray-700">View Map</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh] p-0 rounded-t-xl">
              <div className="p-4 border-b bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-xl">
                <h3 className="font-bold text-lg">Delivery Route</h3>
                <p className="text-blue-100 text-sm">Navigate to pickup and delivery locations</p>
              </div>
              <div className="h-full p-4">
                {/* We'll use the LiveDeliveryMap component here */}
                <div className="text-center py-12">
                  <Map className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Loading interactive map...</p>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                className="h-14 flex-col space-y-1.5 border-2 border-amber-200 hover:border-amber-300 hover:bg-amber-50 transition-all duration-200"
              >
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                <span className="text-xs font-medium text-gray-700">Report Issue</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh] p-0 rounded-t-xl">
              <SheetHeader className="p-4 border-b bg-gradient-to-r from-amber-600 to-amber-700 text-white rounded-t-xl">
                <SheetTitle className="text-white text-lg">Report Delivery Issue</SheetTitle>
                <p className="text-amber-100 text-sm">Let us know if you encounter any problems</p>
              </SheetHeader>
              <div className="p-4 space-y-4">
                <RadioGroup value={reportReason} onValueChange={setReportReason}>
                  <div className="flex items-center space-x-3 py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="restaurant_closed" id="restaurant_closed" />
                    <Label htmlFor="restaurant_closed" className="font-medium">Restaurant is closed</Label>
                  </div>
                  <div className="flex items-center space-x-3 py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="order_not_ready" id="order_not_ready" />
                    <Label htmlFor="order_not_ready" className="font-medium">Order not ready</Label>
                  </div>
                  <div className="flex items-center space-x-3 py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="customer_unavailable" id="customer_unavailable" />
                    <Label htmlFor="customer_unavailable" className="font-medium">Customer unavailable</Label>
                  </div>
                  <div className="flex items-center space-x-3 py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="address_issue" id="address_issue" />
                    <Label htmlFor="address_issue" className="font-medium">Address issue</Label>
                  </div>
                  <div className="flex items-center space-x-3 py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="other" id="other" />
                    <Label htmlFor="other" className="font-medium">Other</Label>
                  </div>
                </RadioGroup>
                <Textarea
                  placeholder="Please provide additional details about the issue..."
                  className="min-h-[100px] border-2 border-gray-200 focus:border-amber-300"
                />
                <div className="flex space-x-3 pt-4">
                  <Button variant="outline" className="flex-1 h-12 border-2">
                    Cancel
                  </Button>
                  <Button className="flex-1 h-12 bg-amber-600 hover:bg-amber-700">
                    Submit Report
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Delivery Information */}
      <div className="px-4">
        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6 h-12 bg-gray-100 rounded-xl p-1">
            <TabsTrigger value="details" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Details
            </TabsTrigger>
            <TabsTrigger value="restaurant" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Pickup
            </TabsTrigger>
            <TabsTrigger value="customer" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Delivery
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            {/* Order Summary */}
            <Card className="border-2 border-gray-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900 text-lg">Order Summary</h3>
                  <div className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium">
                    {orderData.delivery_type === 'delivery' ? '🚚 Delivery' : '🏪 Pickup'}
                  </div>
                </div>
                <div className="space-y-4">
                  {/* Pickup Location - Most Important */}
                  <div className="bg-orange-50 p-4 rounded-lg border-2 border-orange-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <Package className="h-5 w-5 text-orange-600" />
                      <p className="text-sm text-orange-600 font-bold uppercase tracking-wide">Pickup Location</p>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{orderData.business_name}</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {orderData.businesses?.address || 'Address not available'}
                    </p>
                  </div>

                  {/* Delivery Parish - Secondary */}
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <p className="text-xs text-blue-600 font-medium uppercase tracking-wide">Delivery Parish</p>
                    <p className="text-lg font-bold text-blue-900 mt-1">{orderData.parish}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-xs text-gray-500 font-medium uppercase tracking-wide">Order Number</p>
                      <p className="text-lg font-bold text-gray-900 mt-1">{orderData.order_number}</p>
                    </div>
                    {orderData.ready_time && (
                      <div className="bg-green-50 p-3 rounded-lg">
                        <p className="text-xs text-green-600 font-medium uppercase tracking-wide">Ready Time</p>
                        <p className="text-sm font-semibold text-green-900 mt-1">
                          {new Date(orderData.ready_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Distance and Delivery Time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-blue-600" />
                        <p className="text-xs text-blue-600 font-medium uppercase tracking-wide">Distance</p>
                      </div>
                      <p className="text-lg font-bold text-blue-900 mt-1">
                        {orderData.delivery_distance_km ?
                          `${orderData.delivery_distance_km.toFixed(1)} km` :
                          'Calculating...'}
                      </p>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-purple-600" />
                        <p className="text-xs text-purple-600 font-medium uppercase tracking-wide">Est. Time</p>
                      </div>
                      <p className="text-lg font-bold text-purple-900 mt-1">
                        {orderData.estimated_delivery_time ?
                          (typeof orderData.estimated_delivery_time === 'string' ?
                            orderData.estimated_delivery_time :
                            `${orderData.estimated_delivery_time} min`) :
                          'Calculating...'}
                      </p>
                    </div>
                  </div>

                  <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                    <div className="flex justify-between items-center">
                      <span className="text-emerald-700 font-medium">Your Delivery Fee</span>
                      <span className="text-2xl font-bold text-emerald-600">
                        {formatCurrency(orderData.delivery_fee || 3.50)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Weight Analysis */}
            <OrderWeightChart
              orderId={orderData.id}
              className="border-2 border-gray-100"
            />

            {/* Order Items */}
            <Card className="border-2 border-gray-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900 text-lg">Order Items</h3>
                  {orderData.cart_items && orderData.cart_items.length > 0 && (
                    <span className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-bold">
                      {orderData.cart_items.reduce((total, item) => total + item.quantity, 0)} items
                    </span>
                  )}
                </div>
                {orderData.cart_items && orderData.cart_items.length > 0 ? (
                  <div className="space-y-4">
                    {orderData.cart_items.map((item, index) => (
                      <div key={item.id || index} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full text-sm font-bold">
                                {item.quantity}×
                              </span>
                              <span className="font-semibold text-gray-900">{item.name || item.product_name}</span>
                            </div>

                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">Order items not available in this view</p>
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <p className="text-sm text-blue-800 font-medium">
                        📞 Contact the business for order details if needed
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Location Tracking */}
            {["picked_up", "out_for_delivery"].includes(orderData.status) && orderData.businesses?.coordinates && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-medium mb-3">Location Tracking</h3>
                  <p className="text-sm text-gray-500 mb-3">
                    Your location is being tracked for this delivery
                  </p>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      // Update driver location
                      if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition((position) => {
                          fetch('/api/driver/location', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                              latitude: position.coords.latitude,
                              longitude: position.coords.longitude
                            })
                          })
                        })
                      }
                    }}
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    Update My Location
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="restaurant" className="space-y-6">
            <Card className="border-2 border-orange-100 bg-orange-50/50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="bg-orange-100 p-2 rounded-lg">
                    <Package className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 text-lg">{orderData.business_name}</h3>
                    <p className="text-orange-600 text-sm font-medium">Pickup Location</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-lg border border-orange-200">
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-gray-900">Address</p>
                        <p className="text-gray-600 mt-1">
                          {orderData.businesses?.address || 'Address not available'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {orderData.businesses?.phone && (
                    <div className="bg-white p-4 rounded-lg border border-orange-200">
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-orange-600" />
                        <div>
                          <p className="font-medium text-gray-900">Phone</p>
                          <p className="text-gray-600 mt-1">{orderData.businesses.phone}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 p-1 rounded">
                        <AlertTriangle className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-blue-800">Pickup Instructions</p>
                        <p className="text-sm text-blue-700 mt-1">
                          Check in with staff when you arrive. Show them order #{orderData.order_number}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      className="h-12 bg-orange-600 hover:bg-orange-700 text-white font-medium"
                      onClick={() => {
                        if (orderData.businesses?.coordinates) {
                          const coordsMatch = orderData.businesses.coordinates.match(/\(([^,]+),([^)]+)\)/)
                          if (coordsMatch) {
                            const lng = coordsMatch[1]
                            const lat = coordsMatch[2]
                            window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank')
                          }
                        }
                      }}
                    >
                      <Navigation className="mr-2 h-4 w-4" />
                      Navigate
                    </Button>
                    {orderData.businesses?.phone && (
                      <Button
                        variant="outline"
                        className="h-12 border-2 border-orange-200 hover:bg-orange-50"
                        onClick={() => window.open(`tel:${orderData.businesses?.phone}`, '_self')}
                      >
                        <Phone className="mr-2 h-4 w-4 text-orange-600" />
                        Call
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="customer" className="space-y-6">
            <Card className="border-2 border-blue-100 bg-blue-50/50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <Navigation className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 text-lg">{orderData.customer_name}</h3>
                    <p className="text-blue-600 text-sm font-medium">Delivery Destination</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-gray-900">Delivery Address</p>
                        <p className="text-gray-600 mt-1">{orderData.delivery_address}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">Phone Number</p>
                        <p className="text-gray-600 mt-1">{orderData.customer_phone}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-100 p-1 rounded">
                        <AlertTriangle className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-green-800">Delivery Instructions</p>
                        <p className="text-sm text-green-700 mt-1">
                          Call the customer when you arrive. Be polite and professional.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      className="h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium"
                      onClick={() => {
                        const address = encodeURIComponent(orderData.delivery_address)
                        window.open(`https://www.google.com/maps/dir/?api=1&destination=${address}`, '_blank')
                      }}
                    >
                      <Navigation className="mr-1 h-4 w-4" />
                      Navigate
                    </Button>
                    <Button
                      variant="outline"
                      className="h-12 border-2 border-blue-200 hover:bg-blue-50"
                      onClick={() => window.open(`tel:${orderData.customer_phone}`, '_self')}
                    >
                      <Phone className="mr-1 h-4 w-4 text-blue-600" />
                      Call
                    </Button>
                    <Button
                      variant="outline"
                      className="h-12 border-2 border-blue-200 hover:bg-blue-50"
                      onClick={() => window.open(`sms:${orderData.customer_phone}`, '_self')}
                    >
                      <MessageSquare className="mr-1 h-4 w-4 text-blue-600" />
                      Text
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Fixed Action Button */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-white via-white to-white/80 backdrop-blur-sm border-t-2 border-gray-100 shadow-[0_-4px_20px_rgba(0,0,0,0.1)]">
        <div className="max-w-md mx-auto">
          {getNextActionButton()}
        </div>
      </div>

      {/* Bottom padding to prevent content overlap */}
      <div className="h-24"></div>

      {/* Success Message */}
      {successMessage && (
        <div className="fixed top-4 left-4 right-4 z-50">
          <Alert className={`shadow-lg border-2 ${
            successMessage.includes('Congratulations')
              ? 'bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-300 animate-pulse'
              : 'bg-emerald-50 border-emerald-200'
          }`}>
            <CheckCircle className={`h-5 w-5 ${
              successMessage.includes('Congratulations')
                ? 'text-emerald-600 animate-bounce'
                : 'text-emerald-600'
            }`} />
            <AlertDescription className={`font-medium ${
              successMessage.includes('Congratulations')
                ? 'text-emerald-900 text-base'
                : 'text-emerald-800'
            }`}>
              {successMessage}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Pickup Confirmation Dialog */}
      <Dialog open={showPickupConfirmation} onOpenChange={setShowPickupConfirmation}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-emerald-600" />
              <span>Confirm Order Pickup</span>
            </DialogTitle>
            <DialogDescription>
              Please confirm that you have collected the order from <strong>{orderData?.business_name}</strong> and verified all items are included.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-amber-800">Before confirming:</p>
                  <ul className="text-sm text-amber-700 mt-1 space-y-1">
                    <li>• Check all items are present</li>
                    <li>• Verify order number with staff</li>
                    <li>• Ensure food is properly packaged</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowPickupConfirmation(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={confirmPickup}
              disabled={isUpdatingStatus}
              className="flex-1 bg-emerald-600 hover:bg-emerald-700"
            >
              {isUpdatingStatus ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Confirming...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Confirm Pickup
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bottom Spacing for Navigation */}
      <div className="h-20"></div>
    </DriverMobileContainer>
  )
}
