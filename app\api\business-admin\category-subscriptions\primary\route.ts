import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify business admin access
async function verifyBusinessAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user has business admin access
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role, business_id')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (!['business_manager', 'business_staff', 'admin', 'super_admin'].includes(profile.role)) {
      return { authorized: false, error: 'Business admin access required', status: 403 }
    }

    if (!profile.business_id) {
      return { authorized: false, error: 'No business associated with user', status: 403 }
    }

    return { authorized: true, user, profile, businessId: profile.business_id }
  } catch (error) {
    console.error('Error verifying business admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// PUT - Set primary category
export async function PUT(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const businessId = accessCheck.businessId
    const body = await request.json()
    const { category_id } = body

    if (!category_id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // Check if business is subscribed to this category
    const { data: subscription, error: subscriptionError } = await adminClient
      .from('business_categories')
      .select('category_id')
      .eq('business_id', businessId)
      .eq('category_id', category_id)
      .single()

    if (subscriptionError || !subscription) {
      return NextResponse.json(
        { error: 'Not subscribed to this category' },
        { status: 404 }
      )
    }

    // Remove primary status from all categories for this business
    const { error: removeError } = await adminClient
      .from('business_categories')
      .update({ is_primary: false })
      .eq('business_id', businessId)

    if (removeError) {
      console.error('Error removing primary status:', removeError)
      return NextResponse.json(
        { error: 'Failed to update primary category' },
        { status: 500 }
      )
    }

    // Set the new primary category
    const { error: setPrimaryError } = await adminClient
      .from('business_categories')
      .update({ is_primary: true })
      .eq('business_id', businessId)
      .eq('category_id', category_id)

    if (setPrimaryError) {
      console.error('Error setting primary category:', setPrimaryError)
      return NextResponse.json(
        { error: 'Failed to set primary category' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'Primary category updated successfully'
    })
  } catch (error) {
    console.error('Error in PUT /api/business-admin/category-subscriptions/primary:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
