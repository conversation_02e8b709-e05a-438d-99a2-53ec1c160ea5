import type { Metada<PERSON> } from "next"
import Link from "next/link"
import {
  ArrowLeft,
  ChevronDown,
  HelpCircle,
  Mail,
  MapPin,
  Menu,
  Package,
  Phone,
  Settings,
  ShoppingCart,
  User,
} from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export const metadata: Metadata = {
  title: "Customer Profile - Loop Seller Dashboard",
  description: "View and manage customer details on the Loop delivery platform",
}

export default function CustomerProfilePage({ params }: { params: { id: string } }) {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold">
            <Package className="h-6 w-6 text-emerald-500" />
            <span className="text-lg">Loop</span>
          </div>
          <Button variant="outline" size="icon" className="ml-auto h-8 w-8 lg:hidden">
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
          <div className="ml-auto hidden items-center gap-4 lg:flex">
            <Button variant="outline" size="icon" className="rounded-full">
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>
            <Button variant="outline" size="icon" className="rounded-full">
              <HelpCircle className="h-4 w-4" />
              <span className="sr-only">Help</span>
            </Button>
            <Button variant="outline" size="sm" className="rounded-full">
              <img src="/placeholder-user.jpg" alt="Avatar" className="mr-2 h-5 w-5 rounded-full" />
              John Doe
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          <div className="flex items-center gap-4">
            <Link href="/customers">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Customer Profile</h1>
              <p className="text-muted-foreground">View and manage customer details</p>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <Button variant="outline" size="sm" className="h-8">
                <Mail className="mr-2 h-3.5 w-3.5" />
                Send Email
              </Button>
              <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
                <ShoppingCart className="mr-2 h-3.5 w-3.5" />
                View Orders
              </Button>
            </div>
          </div>

          <div className="grid gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col gap-6 md:flex-row md:items-center">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="/placeholder.svg?height=96&width=96" alt="Jackson Davis" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div className="grid gap-1">
                    <h2 className="text-2xl font-bold">Jackson Davis</h2>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                      >
                        Active
                      </Badge>
                      <span className="text-sm text-muted-foreground">Customer since March 15, 2022</span>
                    </div>
                    <div className="flex items-center gap-4 mt-2">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span><EMAIL></span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>+44 20 1234 5678</span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-auto flex flex-col items-end gap-2">
                    <div className="text-sm text-muted-foreground">Customer ID</div>
                    <div className="font-medium">CUST001</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-emerald-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">3 in the last 30 days</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">£345.67</div>
                  <p className="text-xs text-muted-foreground">£89.50 in the last 30 days</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">£28.81</div>
                  <p className="text-xs text-muted-foreground">+£2.50 from average</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Last Order</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">June 12, 2023</div>
                  <p className="text-xs text-muted-foreground">Order #ORD-45678</p>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="orders" className="space-y-4">
              <TabsList>
                <TabsTrigger value="orders">Order History</TabsTrigger>
                <TabsTrigger value="details">Customer Details</TabsTrigger>
                <TabsTrigger value="addresses">Addresses</TabsTrigger>
                <TabsTrigger value="notes">Notes</TabsTrigger>
              </TabsList>

              <TabsContent value="orders" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Orders</CardTitle>
                    <CardDescription>View the customer's order history</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <div className="font-medium">Order #ORD-45678</div>
                          <div className="text-sm text-muted-foreground">June 12, 2023 at 10:30 AM</div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                          >
                            Delivered
                          </Badge>
                          <div className="font-medium">£39.50</div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <div className="font-medium">Order #ORD-45623</div>
                          <div className="text-sm text-muted-foreground">June 5, 2023 at 2:15 PM</div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                          >
                            Delivered
                          </Badge>
                          <div className="font-medium">£27.25</div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <div className="font-medium">Order #ORD-45589</div>
                          <div className="text-sm text-muted-foreground">May 28, 2023 at 11:45 AM</div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                          >
                            Delivered
                          </Badge>
                          <div className="font-medium">£22.75</div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Order #ORD-45542</div>
                          <div className="text-sm text-muted-foreground">May 20, 2023 at 3:30 PM</div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
                          >
                            Delivered
                          </Badge>
                          <div className="font-medium">£35.99</div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full">
                      View All Orders
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                    <CardDescription>Customer's personal details and preferences</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Full Name</h3>
                        <p className="mt-1">Jackson Davis</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Email Address</h3>
                        <p className="mt-1"><EMAIL></p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Phone Number</h3>
                        <p className="mt-1">+44 20 1234 5678</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Date of Birth</h3>
                        <p className="mt-1">April 15, 1985</p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="mb-2 text-sm font-medium">Preferences</h3>
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">Communication Preferences</h4>
                          <div className="mt-1 space-y-1 text-sm">
                            <p>Email: Yes</p>
                            <p>SMS: Yes</p>
                            <p>Marketing: No</p>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">Dietary Preferences</h4>
                          <div className="mt-1 space-y-1 text-sm">
                            <p>Vegetarian: No</p>
                            <p>Allergies: None</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full">
                      <User className="mr-2 h-4 w-4" />
                      Edit Personal Information
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="addresses" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Delivery Addresses</CardTitle>
                    <CardDescription>Addresses used for delivery</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="rounded-md border p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">Home Address</h3>
                            <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800"
                            >
                              Default
                            </Badge>
                          </div>
                          <div className="mt-2 space-y-1 text-sm">
                            <p>123 Main St, Apt 4B</p>
                            <p>London, SW1A 1AA</p>
                            <p>United Kingdom</p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="rounded-md border p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">Work Address</h3>
                          </div>
                          <div className="mt-2 space-y-1 text-sm">
                            <p>456 Business Ave, Floor 12</p>
                            <p>London, EC2A 1NT</p>
                            <p>United Kingdom</p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full">
                      <MapPin className="mr-2 h-4 w-4" />
                      Add New Address
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="notes" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Notes</CardTitle>
                    <CardDescription>Internal notes about this customer</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">Delivery Preference</div>
                          <div className="text-sm text-muted-foreground">Added on June 5, 2023</div>
                        </div>
                        <p className="mt-2 text-sm">
                          Customer prefers deliveries in the evening after 6 PM. Has mentioned that the doorbell doesn't
                          work, so please call when arriving.
                        </p>
                      </div>
                      <div className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">VIP Customer</div>
                          <div className="text-sm text-muted-foreground">Added on April 12, 2023</div>
                        </div>
                        <p className="mt-2 text-sm">
                          Regular customer who orders at least once a week. Consider for loyalty program.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col gap-4">
                    <textarea
                      className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Add a new note about this customer..."
                    ></textarea>
                    <Button className="ml-auto bg-emerald-600 hover:bg-emerald-700">Add Note</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}
