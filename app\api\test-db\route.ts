import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  try {
    console.log("Testing database connection...");
    console.log("Supabase URL:", supabaseUrl);
    console.log("Service key exists:", !!supabaseServiceKey);
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Test database connection
    const { data, error } = await supabase
      .from("users")
      .select("id, email")
      .limit(1);
    
    if (error) {
      console.log("Database query failed:", error);
      return NextResponse.json({ 
        success: false, 
        error: `Database error: ${error.message}` 
      }, { status: 500 });
    }

    console.log("Database query succeeded, found users:", data?.length);
    
    // Test auth admin functions
    try {
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        console.log("Auth admin query failed:", authError);
        return NextResponse.json({ 
          success: true, 
          database: "connected",
          auth: "failed",
          authError: authError.message
        });
      }

      console.log("Auth admin query succeeded, found auth users:", authUsers?.users?.length);
      
      return NextResponse.json({ 
        success: true, 
        database: "connected",
        auth: "connected",
        userCount: data?.length,
        authUserCount: authUsers?.users?.length
      });

    } catch (authException: any) {
      console.log("Auth admin exception:", authException);
      return NextResponse.json({ 
        success: true, 
        database: "connected",
        auth: "exception",
        authError: authException.message
      });
    }

  } catch (error: any) {
    console.error("Connection test error:", error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}
