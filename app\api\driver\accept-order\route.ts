import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { orderId } = await request.json()

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Check if driver is already on a delivery
    const { data: driverStatus } = await supabase
      .from('driver_status')
      .select('is_on_delivery, current_order_id')
      .eq('driver_id', driverProfile.id)
      .single()

    if (driverStatus?.is_on_delivery) {
      return NextResponse.json(
        { error: "Driver is already on a delivery" },
        { status: 409 }
      )
    }

    // Check if order is available for acceptance
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, business_id, business_name, order_number')
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.status !== 'offered') {
      return NextResponse.json(
        { error: `Order cannot be accepted. Current status: ${order.status}` },
        { status: 409 }
      )
    }

    if (order.driver_id && order.driver_id !== driverProfile.id) {
      return NextResponse.json(
        { error: "Order is already assigned to another driver" },
        { status: 409 }
      )
    }

    // Assign order to this driver (offered → assigned)
    const { error: updateOrderError } = await supabase
      .from('orders')
      .update({
        driver_id: driverProfile.id,
        status: 'assigned',
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .eq('status', 'offered') // Only update if still offered (prevents race conditions)

    if (updateOrderError) {
      console.error('Error updating order:', updateOrderError)
      return NextResponse.json(
        { error: "Failed to accept order - it may have been taken by another driver" },
        { status: 409 }
      )
    }

    // Create or update driver business assignment (working relationship)
    const { error: assignmentError } = await supabase
      .from('driver_business_assignments')
      .upsert({
        driver_id: user.auth_id, // Use auth_id, not driver_profile.id
        business_id: order.business_id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'driver_id,business_id'
      })

    if (assignmentError) {
      console.error('Error creating driver business assignment:', assignmentError)
      // Don't fail the request - this is supplementary data
    }

    // Record order acceptance in shift tracking
    const { error: shiftTrackingError } = await supabase
      .rpc('record_shift_order_offer', {
        p_driver_id: driverProfile.id,
        p_order_id: orderId,
        p_action: 'accepted'
      })

    if (shiftTrackingError) {
      console.error('Error recording shift order acceptance:', shiftTrackingError)
      // Don't fail the request if shift tracking fails
    }

    // Update driver status to on delivery
    const { error: driverStatusError } = await supabase
      .from('driver_status')
      .upsert({
        driver_id: driverProfile.id,
        is_online: true,
        is_on_delivery: true,
        current_order_id: orderId,
        last_status_change: new Date().toISOString()
      })

    if (driverStatusError) {
      console.error('Error updating driver status:', driverStatusError)
      // Don't fail the request if driver status update fails
    }

    // Create status history entry
    const { error: historyError } = await supabase
      .from('order_status_history')
      .insert({
        order_id: orderId,
        status: 'assigned',
        notes: 'Driver accepted and was assigned the delivery request',
        driver_id: driverProfile.id,
        created_at: new Date().toISOString()
      })

    if (historyError) {
      console.error('Error creating status history:', historyError)
      // Don't fail the request if history creation fails
    }

    // Get the updated order details
    const { data: updatedOrder, error: updatedOrderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        customer_name,
        customer_phone,
        delivery_address,
        postcode,
        parish,
        delivery_type,
        total,
        status,
        businesses!inner (
          id,
          name,
          address,
          phone,
          latitude,
          coordinates
        )
      `)
      .eq('id', orderId)
      .single()

    if (updatedOrderError) {
      console.error('Error fetching updated order:', updatedOrderError)
    }

    return NextResponse.json({
      success: true,
      message: `Order ${order.order_number} accepted successfully`,
      order: updatedOrder,
      nextAction: "head_to_business",
      nextActionText: `Head to ${order.business_name} to pick up the order`
    })

  } catch (error) {
    console.error('Error in accept order API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
