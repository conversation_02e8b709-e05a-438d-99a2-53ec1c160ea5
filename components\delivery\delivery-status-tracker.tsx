"use client"

import { useState, useEffect } from "react"
import {
  CheckCircle2,
  Clock,
  ChefHat,
  Package,
  Truck,
  Home,
  MapPin
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import dynamic from 'next/dynamic'

// Import the OSM map component with dynamic import to avoid SSR issues with Leaflet
const OSMDeliveryMap = dynamic(
  () => import('@/components/delivery/osm-delivery-map'),
  { ssr: false }
)

export type DeliveryStatus =
  | "order_placed"
  | "preparing"
  | "ready_for_pickup"
  | "out_for_delivery"
  | "delivered"

interface DeliveryStatusTrackerProps {
  status: DeliveryStatus
  estimatedDeliveryTime: string
  restaurantName: string // This can be a restaurant or shop name
  restaurantAddress: string // This can be a restaurant or shop address
  customerAddress: string
  restaurantCoordinates: [number, number] // These can be restaurant or shop coordinates
  customerCoordinates: [number, number]
  className?: string
  isScheduledDelivery?: boolean
  scheduledDeliveryTime?: string
}

export default function DeliveryStatusTracker({
  status,
  estimatedDeliveryTime,
  restaurantName,
  restaurantAddress,
  customerAddress,
  restaurantCoordinates,
  customerCoordinates,
  className = "",
  isScheduledDelivery = false,
  scheduledDeliveryTime,
}: DeliveryStatusTrackerProps) {
  const [progress, setProgress] = useState(0)

  // Map status to progress percentage
  useEffect(() => {
    const statusProgress = {
      order_placed: 0,
      preparing: 25,
      ready_for_pickup: 50,
      out_for_delivery: 75,
      delivered: 100,
    }

    setProgress(statusProgress[status])
  }, [status])

  // Get status step details
  const getStatusSteps = () => {
    const steps = [
      {
        id: "order_placed",
        title: "Order Placed",
        description: "Your order has been received",
        icon: CheckCircle2,
        time: new Date().toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }),
      },
      {
        id: "preparing",
        title: "Preparing",
        description: `Your order is being prepared`,
        icon: ChefHat,
        time: status === "order_placed" ? "Upcoming" : new Date().toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }),
      },
      {
        id: "ready_for_pickup",
        title: "Ready for Pickup",
        description: "Your order is ready for pickup by the driver",
        icon: Package,
        time: status === "order_placed" || status === "preparing" ? "Upcoming" : new Date().toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }),
      },
      {
        id: "out_for_delivery",
        title: "Out for Delivery",
        description: "Your order is on its way",
        icon: Truck,
        time: status === "order_placed" || status === "preparing" || status === "ready_for_pickup" ? "Upcoming" : new Date().toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }),
      },
      {
        id: "delivered",
        title: "Delivered",
        description: "Your order has been delivered",
        icon: Home,
        time: status === "delivered" ? new Date().toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }) : estimatedDeliveryTime,
      },
    ]

    return steps
  }

  const statusSteps = getStatusSteps()

  // Determine if a step is active, completed, or upcoming
  const getStepStatus = (stepId: DeliveryStatus) => {
    const statusOrder = ["order_placed", "preparing", "ready_for_pickup", "out_for_delivery", "delivered"]
    const currentIndex = statusOrder.indexOf(status)
    const stepIndex = statusOrder.indexOf(stepId)

    if (stepIndex < currentIndex) return "completed"
    if (stepIndex === currentIndex) return "active"
    return "upcoming"
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Delivery Status</CardTitle>
        <CardDescription>
          {isScheduledDelivery
            ? `Scheduled delivery for ${scheduledDeliveryTime || estimatedDeliveryTime}`
            : `Estimated delivery by ${estimatedDeliveryTime}`
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress bar */}
        <Progress value={progress} className="h-2" />

        {/* Status steps */}
        <div className="space-y-4">
          {statusSteps.map((step) => {
            const stepStatus = getStepStatus(step.id as DeliveryStatus)
            return (
              <div key={step.id} className="flex items-start gap-3">
                <div className={`rounded-full p-2 ${
                  stepStatus === "completed" ? "bg-emerald-100 text-emerald-600" :
                  stepStatus === "active" ? "bg-blue-100 text-blue-600" :
                  "bg-gray-100 text-gray-400"
                }`}>
                  <step.icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${
                      stepStatus === "upcoming" ? "text-gray-500" : "text-gray-900"
                    }`}>
                      {step.title}
                    </h4>
                    <span className={`text-sm ${
                      stepStatus === "upcoming" ? "text-gray-400" : "text-gray-500"
                    }`}>
                      {step.time}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500">{step.description}</p>
                </div>
              </div>
            )
          })}
        </div>

        {/* Delivery addresses */}
        <div className="mt-6 space-y-3 pt-4 border-t">
          <div className="flex items-start gap-3">
            <div className="rounded-full p-2 bg-orange-100 text-orange-600">
              <MapPin className="h-4 w-4" />
            </div>
            <div>
              <h4 className="font-medium text-sm">Pickup: {restaurantName}</h4>
              <p className="text-xs text-gray-500">{restaurantAddress}</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="rounded-full p-2 bg-emerald-100 text-emerald-600">
              <MapPin className="h-4 w-4" />
            </div>
            <div>
              <h4 className="font-medium text-sm">Delivery Address</h4>
              <p className="text-xs text-gray-500">{customerAddress}</p>
            </div>
          </div>
        </div>

        {/* Map */}
        {status === "out_for_delivery" && (
          <div className="mt-4">
            <OSMDeliveryMap
              restaurantLng={restaurantCoordinates[0]}
              restaurantLat={restaurantCoordinates[1]}
              customerLng={customerCoordinates[0]}
              customerLat={customerCoordinates[1]}
              height="200px"
              showRoute={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
