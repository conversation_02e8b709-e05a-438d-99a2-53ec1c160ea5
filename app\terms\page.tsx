import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms and Conditions | Loop Jersey',
  description: 'Terms and conditions for using the Loop Jersey service.',
};

export default function TermsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Terms and Conditions</h1>
      
      <div className="prose max-w-none">
        <p className="text-lg mb-4">
          Welcome to Loop Jersey. By using our service, you agree to these terms and conditions.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">1. Acceptance of Terms</h2>
        <p>
          By accessing or using Loop Jersey's services, you agree to be bound by these Terms and Conditions.
          If you do not agree to all the terms and conditions, you may not access or use our services.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">2. Service Description</h2>
        <p>
          Loop Jersey provides a platform connecting customers with local restaurants and shops for food and product delivery.
          We do not prepare food or products ourselves but facilitate the ordering and delivery process.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">3. User Accounts</h2>
        <p>
          To use certain features of our service, you may need to create an account. You are responsible for maintaining
          the confidentiality of your account information and for all activities that occur under your account.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">4. Ordering and Delivery</h2>
        <p>
          When you place an order through Loop Jersey, you are making an offer to purchase products from the selected business.
          Delivery times are estimates and may vary based on factors outside our control.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">5. Payments</h2>
        <p>
          All payments are processed securely. By providing payment information, you represent that you are authorized to use
          the payment method. Prices and fees may change at any time.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">6. Cancellations and Refunds</h2>
        <p>
          Cancellation policies vary by business. Refunds may be issued at our discretion for issues with orders or service.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">7. Privacy</h2>
        <p>
          Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">8. Limitation of Liability</h2>
        <p>
          Loop Jersey is not liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use
          of our services or any products ordered through our platform.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">9. Changes to Terms</h2>
        <p>
          We may update these terms from time to time. Continued use of our services after changes constitutes acceptance of the new terms.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">10. Contact Us</h2>
        <p>
          If you have any questions about these Terms and Conditions, please contact us at <a href="mailto:<EMAIL>" className="text-emerald-600 hover:underline"><EMAIL></a>.
        </p>
        
        <p className="mt-8 text-sm text-gray-500">
          Last updated: May 7, 2025
        </p>
      </div>
    </div>
  );
}
