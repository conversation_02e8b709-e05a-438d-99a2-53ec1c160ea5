"use client"

import {
  Arrow<PERSON>pR<PERSON>,
  ArrowDownRight,
  ShoppingBag,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart2,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  TrendingUp,
  Smartphone,
  Globe,
  Phone,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { 
  orderTrendData, 
  orderStatusData, 
  orderSourceData, 
  orderValueTrendData 
} from "../data/mock-data"

export default function OrdersTab() {
  return (
    <>
      {/* Orders Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <ShoppingBag className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                12%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <h3 className="text-2xl font-bold">246</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">+28 from last period</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-green-100 p-2 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                10%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Completed Orders</p>
              <h3 className="text-2xl font-bold">215</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">87.4% completion rate</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <AlertCircle className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                18
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <h3 className="text-2xl font-bold">7.3%</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Current active orders</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-red-100 p-2 rounded-lg">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
                <ArrowDownRight className="mr-1 h-3 w-3" />
                2%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Canceled Orders</p>
              <h3 className="text-2xl font-bold">13</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">5.3% cancellation rate</div>
          </CardContent>
        </Card>
      </div>

      {/* Order Trends & Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Order Volume Trend</CardTitle>
                <CardDescription>Daily order volume for the past 14 days</CardDescription>
              </div>
              <Select defaultValue="orders">
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="orders">All Orders</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="canceled">Canceled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for order trend chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <BarChart2 className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[200px] flex items-end justify-around px-4">
                {orderTrendData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className="relative w-6">
                      <div 
                        className="w-6 bg-emerald-500 rounded-t-sm absolute bottom-0" 
                        style={{ height: `${(item.completed / 16) * 150}px` }}
                      />
                      <div 
                        className="w-6 bg-red-500 rounded-t-sm absolute bottom-0" 
                        style={{ 
                          height: `${(item.canceled / 16) * 150}px`,
                          transform: `translateY(-${(item.completed / 16) * 150}px)` 
                        }}
                      />
                    </div>
                    {index % 2 === 0 && (
                      <div className="text-xs text-gray-400 mt-1">{item.date.split(' ')[1]}</div>
                    )}
                  </div>
                ))}
              </div>
              <div className="absolute top-2 right-2 flex items-center space-x-4 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-emerald-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Completed</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Canceled</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Status</CardTitle>
            <CardDescription>Current order status breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for pie chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <PieChartIcon className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-32 h-32 rounded-full border-8 border-gray-100 relative">
                  {/* Completed segment - 87.4% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 0% 0%, 0% 100%, 100% 100%, 100% 0%)' }}>
                    <div className="absolute inset-0 bg-emerald-500 origin-bottom-right" style={{ transform: 'rotate(314deg)' }}></div>
                  </div>
                  {/* In Progress segment - 7.3% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 100% 0%, 50% 0%)' }}>
                    <div className="absolute inset-0 bg-blue-500"></div>
                  </div>
                  {/* Canceled segment - 5.3% */}
                  <div className="absolute inset-0 overflow-hidden" style={{ clipPath: 'polygon(50% 50%, 50% 0%, 26% 0%)' }}>
                    <div className="absolute inset-0 bg-red-500"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center rounded-full bg-white w-20 h-20 m-auto">
                    <span className="text-lg font-bold">246</span>
                  </div>
                </div>
              </div>
              <div className="absolute bottom-4 left-0 right-0 flex flex-col items-center space-y-2">
                {orderStatusData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between w-full px-6">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: item.color }}></div>
                      <span className="text-sm">{item.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{item.value}</span>
                      <span className="text-xs text-gray-500">{Math.round((item.value / 246) * 100)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Source & Average Order Value */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Order Source</CardTitle>
            <CardDescription>Where your orders are coming from</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="space-y-6 w-full px-6">
                  {orderSourceData.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {index === 0 && <Globe className="h-4 w-4 mr-2 text-emerald-500" />}
                          {index === 1 && <Smartphone className="h-4 w-4 mr-2 text-blue-500" />}
                          {index === 2 && <Phone className="h-4 w-4 mr-2 text-purple-500" />}
                          <span className="text-sm">{item.name}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">{item.value}</span>
                          <span className="text-xs text-gray-500">{Math.round((item.value / 246) * 100)}%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="h-2.5 rounded-full" 
                          style={{ width: `${(item.value / 246) * 100}%`, backgroundColor: item.color }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Average Order Value</CardTitle>
            <CardDescription>Weekly average order value trend</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for line chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[150px]">
                <svg width="100%" height="100%" viewBox="0 0 100 20" preserveAspectRatio="none">
                  <path
                    d="M0,15 L33,12 L66,5 L100,7"
                    fill="none"
                    stroke="#8b5cf6"
                    strokeWidth="1"
                  />
                  {/* Data points */}
                  <circle cx="0" cy="15" r="1.5" fill="#8b5cf6" />
                  <circle cx="33" cy="12" r="1.5" fill="#8b5cf6" />
                  <circle cx="66" cy="5" r="1.5" fill="#8b5cf6" />
                  <circle cx="100" cy="7" r="1.5" fill="#8b5cf6" />
                </svg>
              </div>
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-4 text-xs text-gray-400">
                {orderValueTrendData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <span>£{item.value}</span>
                    <span>{item.date}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-4 space-y-4">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Current Average</p>
                  <p className="text-sm font-medium">£13.93</p>
                </div>
                <Progress value={70} className="h-2" />
              </div>
              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium">Target Average</p>
                  <p className="text-sm font-medium">£20.00</p>
                </div>
                <Progress value={100} className="h-2 bg-gray-200" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Fulfillment Metrics */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Order Fulfillment Metrics</CardTitle>
            <CardDescription>Key metrics for order processing efficiency</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Average Preparation Time</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-blue-700 font-bold text-xl">18<span className="text-sm">min</span></span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">From order to ready</p>
                    <p className="text-xs text-emerald-600 flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 2 min faster than target
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Order Acceptance Time</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-emerald-100 flex items-center justify-center">
                    <span className="text-emerald-700 font-bold text-xl">45<span className="text-sm">sec</span></span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Average response time</p>
                    <p className="text-xs text-emerald-600 flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 15 sec faster than last month
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Order Accuracy</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-amber-100 flex items-center justify-center">
                    <span className="text-amber-700 font-bold text-xl">98<span className="text-sm">%</span></span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Orders without issues</p>
                    <p className="text-xs text-emerald-600 flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" /> 2% from last month
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 border-t pt-6">
              <h4 className="text-sm font-medium mb-4">Order Processing Breakdown</h4>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span>Order Acceptance</span>
                    <span className="font-medium">45 sec</span>
                  </div>
                  <Progress value={5} className="h-1.5" />
                </div>
                <div>
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span>Food Preparation</span>
                    <span className="font-medium">18 min</span>
                  </div>
                  <Progress value={75} className="h-1.5" />
                </div>
                <div>
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span>Waiting for Pickup</span>
                    <span className="font-medium">4 min</span>
                  </div>
                  <Progress value={15} className="h-1.5" />
                </div>
                <div>
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span>Delivery to Customer</span>
                    <span className="font-medium">8 min</span>
                  </div>
                  <Progress value={30} className="h-1.5" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
