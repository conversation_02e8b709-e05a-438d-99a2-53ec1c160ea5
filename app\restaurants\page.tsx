"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function RestaurantsPage() {
  const router = useRouter()

  // Redirect to the search page with restaurant filter
  useEffect(() => {
    router.replace('/search?type=restaurant')
  }, [])

  return (
    <div className="container-fluid py-8">
      <h1 className="text-3xl font-bold mb-6">Redirecting to Search...</h1>
      <p className="text-gray-600">Please wait while we redirect you to the restaurants search page.</p>
    </div>
  )
}
