import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { requireAuth } from "@/lib/auth-utils"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const { user: authUser, profile, error: authError } = await requireAuth(request)

    if (authError || !profile) {
      return NextResponse.json(
        { error: authError || "Authentication required" },
        { status: 401 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: profileError } = await supabase
      .from('driver_profiles')
      .select('*')
      .eq('user_id', profile.id)
      .single()

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        return NextResponse.json(
          { error: "Driver profile not found" },
          { status: 404 }
        )
      }
      console.error('Error fetching driver profile:', profileError)
      return NextResponse.json(
        { error: "Failed to fetch driver profile" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      profile: driverProfile
    })

  } catch (error) {
    console.error('Error in driver profile GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()

    // Require authentication
    const { user: authUser, profile, error: authError } = await requireAuth(request)

    if (authError || !profile) {
      return NextResponse.json(
        { error: authError || "Authentication required" },
        { status: 401 }
      )
    }

    // Update driver profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('driver_profiles')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', profile.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating driver profile:', updateError)
      return NextResponse.json(
        { error: "Failed to update driver profile" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      profile: updatedProfile
    })

  } catch (error) {
    console.error('Error in driver profile PATCH:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
