import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: { orderNumber: string } }
) {
  try {
    const orderNumber = params.orderNumber

    if (!orderNumber) {
      return NextResponse.json(
        { error: "Order number is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Check if order exists and is still available (using order_number instead of id)
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, order_number')
      .eq('order_number', orderNumber)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.status !== 'offered') {
      return NextResponse.json(
        { error: "Order is no longer available to decline" },
        { status: 409 }
      )
    }

    // Log the decline in driver activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverProfile.id,
        activity_type: 'declined_order',
        details: {
          order_id: order.id,
          order_number: order.order_number,
          declined_at: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })

    if (activityError) {
      console.error('Error logging decline activity:', activityError)
      // Don't fail the request, just log the error
    }

    // For now, we'll just log the decline. In a more sophisticated system,
    // you might want to track decline rates, temporarily remove the order
    // from this driver's available list, or implement other logic.

    return NextResponse.json({
      success: true,
      message: "Delivery declined",
      orderId: order.id,
      orderNumber: order.order_number
    })

  } catch (error) {
    console.error('Error in decline delivery API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
