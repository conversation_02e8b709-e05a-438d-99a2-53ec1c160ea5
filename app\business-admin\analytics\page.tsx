"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { format } from "date-fns"
import { useAuth } from "@/context/unified-auth-context"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import {
  Download,
  BarChart3,
  TrendingUp,
  Users,
  ShoppingBag,
  DollarSign,
  Clock,
  Truck,
  Star,
  Calendar,
  Filter,
  HelpCircle
} from "lucide-react"

// Import the date picker component
import { DatePickerWithRange } from "@/components/date-range-picker"

// Import chart components (these will be moved from Dashboard)
import { RevenueChart } from "@/components/charts/revenue-chart"
import { CategoryRevenueChart } from "@/components/charts/category-revenue-chart"
import { OrdersByDayChart } from "@/components/charts/orders-by-day-chart"
import { OrdersByTimeChart } from "@/components/charts/orders-by-time-chart"
import { OverviewChart } from "@/components/charts/overview-chart"
import { TopProductsList } from "@/components/dashboard/top-products-list"
import { SalesMetrics } from "@/components/dashboard/sales-metrics"

export default function BusinessAnalytics() {
  const router = useRouter()
  const { user, userProfile } = useAuth()
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    to: new Date()
  })
  const [timeframe, setTimeframe] = useState("30d")
  const [loading, setLoading] = useState(false)
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Handle export functionality
  const handleExport = (type: string) => {
    // TODO: Implement export functionality
    console.log(`Exporting ${type} data for date range:`, dateRange)
    alert(`Export ${type} functionality will be implemented here`)
  }

  // Get context-sensitive help content based on active tab
  const getHelpContent = () => {
    switch (activeTab) {
      case "overview":
        return {
          title: "Overview Analytics Help",
          description: "Understanding your business performance at a glance",
          sections: [
            {
              title: "Key Metrics Cards",
              content: [
                "• **Total Revenue:** Your total income for the selected period, including percentage change from previous period",
                "• **Total Orders:** Number of completed orders, showing growth trends",
                "• **Average Order Value:** Revenue divided by number of orders - higher values indicate customers are spending more per order",
                "• **Customer Satisfaction:** Average rating from customer reviews and feedback"
              ]
            },
            {
              title: "Revenue Trends Chart",
              content: [
                "• Shows daily revenue patterns over your selected date range",
                "• Look for consistent growth trends or seasonal patterns",
                "• Dips may indicate slower days or external factors affecting sales",
                "• Use this to identify your best performing periods"
              ]
            },
            {
              title: "Order Volume Chart",
              content: [
                "• Displays daily order counts to show demand patterns",
                "• Compare with revenue trends to understand order value changes",
                "• Identify peak days for staffing and inventory planning",
                "• Seasonal trends help with long-term business planning"
              ]
            },
            {
              title: "Category & Time Analysis",
              content: [
                "• **Revenue by Category:** Shows which product types generate most income",
                "• **Peak Hours Analysis:** Identifies busiest times for operational planning",
                "• Use category data to focus marketing and inventory decisions",
                "• Peak hours help optimize staffing and preparation schedules"
              ]
            }
          ],
          tips: [
            "Focus on trends rather than individual day performance",
            "Compare metrics to previous periods to gauge growth",
            "Use date range picker to analyze different time periods",
            "Green indicators show positive changes, red shows areas needing attention"
          ]
        }

      case "revenue":
        return {
          title: "Revenue Analytics Help",
          description: "Deep dive into your financial performance and growth",
          sections: [
            {
              title: "Revenue Analysis Chart",
              content: [
                "• Detailed breakdown of revenue over time with trend analysis",
                "• Shows daily, weekly, or monthly patterns depending on date range",
                "• Identify seasonal trends and growth opportunities",
                "• Compare different periods to measure business growth"
              ]
            },
            {
              title: "Revenue Metrics",
              content: [
                "• **Gross Revenue:** Total income before expenses",
                "• **Net Revenue:** Revenue after platform fees and refunds",
                "• **Average Transaction Value:** Revenue per completed order",
                "• **Revenue per Customer:** Total revenue divided by unique customers"
              ]
            },
            {
              title: "Growth Trends",
              content: [
                "• **Month-over-month:** Short-term growth indicator",
                "• **Year-over-year:** Long-term business health measure",
                "• **Best Performing Day:** Identifies most profitable day of week",
                "• **Peak Revenue Hour:** Optimal time for promotions and staffing"
              ]
            }
          ],
          tips: [
            "Monitor month-over-month growth for business health",
            "Use peak hours data to optimize pricing and promotions",
            "Compare revenue trends with marketing campaigns",
            "Focus on sustainable growth rather than short-term spikes"
          ]
        }

      case "orders":
        return {
          title: "Orders Analytics Help",
          description: "Operational insights and order performance metrics",
          sections: [
            {
              title: "Order Performance Metrics",
              content: [
                "• **Average Prep Time:** Time from order confirmation to ready status",
                "• **Order Accuracy:** Percentage of orders completed without issues",
                "• **Cancellation Rate:** Orders cancelled as percentage of total orders",
                "• Target prep time is typically 20-25 minutes for optimal customer satisfaction"
              ]
            },
            {
              title: "Order Volume Trends",
              content: [
                "• Daily order patterns help predict busy periods",
                "• Seasonal trends assist with inventory and staffing planning",
                "• Compare order volume with revenue to understand order value changes",
                "• Identify growth opportunities and capacity constraints"
              ]
            },
            {
              title: "Operational Insights",
              content: [
                "• Use prep time data to optimize kitchen workflows",
                "• High accuracy rates indicate good quality control",
                "• Monitor cancellation rates to identify potential issues",
                "• Peak order times help with staff scheduling"
              ]
            }
          ],
          tips: [
            "Aim for prep times under 25 minutes during peak hours",
            "Order accuracy above 98% indicates excellent operations",
            "Cancellation rates under 2% are considered good",
            "Use order patterns to optimize inventory ordering"
          ]
        }

      case "products":
        return {
          title: "Products Analytics Help",
          description: "Product performance and category insights",
          sections: [
            {
              title: "Top Performing Products",
              content: [
                "• Lists best-selling items by revenue and quantity",
                "• Shows which products drive the most profit",
                "• Identifies customer favorites for promotion opportunities",
                "• Helps optimize menu placement and pricing"
              ]
            },
            {
              title: "Product Performance Analysis",
              content: [
                "• **Revenue by Category:** Shows which product types perform best",
                "• **Sales Velocity:** How quickly products sell",
                "• **Profit Margins:** Revenue minus cost of goods sold",
                "• **Seasonal Performance:** Products that perform better at certain times"
              ]
            },
            {
              title: "Menu Optimization",
              content: [
                "• Use top performers to guide menu design and placement",
                "• Consider promoting high-margin items",
                "• Remove or modify poor-performing products",
                "• Analyze category performance for inventory planning"
              ]
            }
          ],
          tips: [
            "Focus marketing on top-performing products",
            "Consider seasonal menu adjustments based on trends",
            "Use category data for inventory optimization",
            "Promote high-margin items to increase profitability"
          ]
        }

      case "customers":
        return {
          title: "Customer Analytics Help",
          description: "Understanding your customer base and behavior",
          sections: [
            {
              title: "Customer Metrics",
              content: [
                "• **Total Customers:** Unique customers who have placed orders",
                "• **Returning Customers:** Percentage who have ordered more than once",
                "• **Customer Lifetime Value:** Average total revenue per customer",
                "• **New Customer Acquisition:** Rate of gaining new customers"
              ]
            },
            {
              title: "Customer Behavior",
              content: [
                "• **Order Frequency:** How often customers return",
                "• **Average Spend:** Typical order value per customer",
                "• **Preferred Categories:** Most popular product types",
                "• **Peak Ordering Times:** When customers are most active"
              ]
            },
            {
              title: "Retention Insights",
              content: [
                "• High returning customer percentage indicates good satisfaction",
                "• Customer lifetime value shows long-term business health",
                "• Use behavior data to personalize marketing efforts",
                "• Identify opportunities to increase order frequency"
              ]
            }
          ],
          tips: [
            "Aim for 70%+ returning customer rate",
            "Focus on increasing customer lifetime value",
            "Use peak times for targeted promotions",
            "Reward loyal customers to improve retention"
          ]
        }

      case "delivery":
        return {
          title: "Delivery Analytics Help",
          description: "Delivery performance and customer satisfaction metrics",
          sections: [
            {
              title: "Delivery Performance Metrics",
              content: [
                "• **Average Delivery Time:** Total time from order to delivery",
                "• **On-Time Delivery:** Percentage delivered within promised time",
                "• **Delivery Rating:** Customer satisfaction with delivery service",
                "• Target delivery time is typically 30-45 minutes"
              ]
            },
            {
              title: "Delivery Efficiency",
              content: [
                "• **Route Optimization:** Efficiency of delivery routes",
                "• **Driver Performance:** Individual driver metrics",
                "• **Delivery Zones:** Performance by geographic area",
                "• **Peak Delivery Times:** Busiest periods for delivery demand"
              ]
            },
            {
              title: "Customer Satisfaction",
              content: [
                "• Delivery ratings directly impact customer retention",
                "• On-time delivery builds customer trust and loyalty",
                "• Fast delivery times can justify premium pricing",
                "• Monitor delivery issues to improve service quality"
              ]
            }
          ],
          tips: [
            "Maintain 90%+ on-time delivery rate",
            "Aim for delivery ratings above 4.5 stars",
            "Optimize routes during peak hours",
            "Communicate delays proactively to customers"
          ]
        }

      default:
        return {
          title: "Analytics Help",
          description: "Understanding your business analytics",
          sections: [],
          tips: []
        }
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Business Analytics</h1>
          <p className="text-gray-600">Deep dive into your business performance and insights</p>
        </div>

        <div className="flex items-center gap-3">
          {/* Date Range Picker */}
          <DatePickerWithRange
            date={dateRange}
            onDateChange={(range) => setDateRange(range)}
            className="w-auto"
          />

          {/* Export Button */}
          <Button variant="outline" onClick={() => handleExport('analytics')}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>

          {/* Help Button */}
          <Button
            variant="outline"
            onClick={() => setIsHelpDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <HelpCircle className="h-4 w-4" />
            Help
          </Button>
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="delivery">Delivery</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(12450.50)}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+20.1%</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+12.4%</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(25.75)}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+2.3%</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+0.2</span> from last period
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Charts */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
                <CardDescription>
                  Revenue performance over {format(dateRange.from, "MMM d")} - {format(dateRange.to, "MMM d, yyyy")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OverviewChart isLoading={loading} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Order Volume</CardTitle>
                <CardDescription>
                  Daily order patterns and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrdersByDayChart isLoading={loading} />
              </CardContent>
            </Card>
          </div>

          {/* Secondary Charts */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Category</CardTitle>
                <CardDescription>
                  Breakdown of revenue by product categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CategoryRevenueChart isLoading={loading} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Peak Hours Analysis</CardTitle>
                <CardDescription>
                  Order patterns throughout the day
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrdersByTimeChart isLoading={loading} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Revenue Tab */}
        <TabsContent value="revenue" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analysis</CardTitle>
                <CardDescription>
                  Detailed revenue breakdown and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RevenueChart isLoading={loading} />
              </CardContent>
            </Card>

            <div className="grid gap-6 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <SalesMetrics isLoading={loading} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Growth Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm">Month-over-month growth</span>
                      <span className="font-medium text-green-600">+15.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Year-over-year growth</span>
                      <span className="font-medium text-green-600">+45.8%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Best performing day</span>
                      <span className="font-medium">Saturday</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Peak revenue hour</span>
                      <span className="font-medium">7:00 PM - 8:00 PM</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders" className="space-y-6">
          <div className="grid gap-6">
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Prep Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24 min</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">-2 min</span> vs target
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Order Accuracy</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">98.7%</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+0.3%</span> from last period
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Cancellation Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1.2%</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-red-600">+0.1%</span> from last period
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Order Volume Trends</CardTitle>
                <CardDescription>
                  Daily order patterns and seasonal trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrdersByDayChart isLoading={loading} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Products</CardTitle>
                <CardDescription>
                  Best sellers by revenue and quantity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TopProductsList isLoading={loading} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Performance</CardTitle>
                <CardDescription>
                  Category breakdown and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CategoryRevenueChart isLoading={loading} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+156</span> new this month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Returning Customers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">76%</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+3%</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Customer Lifetime Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(127.50)}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+£12.30</span> from last period
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Customer Insights</CardTitle>
              <CardDescription>
                Understanding your customer base
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="mx-auto h-16 w-16 text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">
                  Customer analytics will be displayed here
                </p>
                <p className="text-xs text-muted-foreground">
                  Detailed customer behavior and segmentation analysis
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Delivery Tab */}
        <TabsContent value="delivery" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Avg Delivery Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">28 min</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">-3 min</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94.2%</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+1.2%</span> from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Delivery Rating</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.7</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+0.1</span> from last period
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Delivery Performance</CardTitle>
              <CardDescription>
                Delivery efficiency and customer satisfaction
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Truck className="mx-auto h-16 w-16 text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">
                  Delivery analytics will be displayed here
                </p>
                <p className="text-xs text-muted-foreground">
                  Route optimization, delivery zones, and performance metrics
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Context-Sensitive Help Dialog */}
      <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-600" />
              {getHelpContent().title}
            </DialogTitle>
            <DialogDescription>
              {getHelpContent().description}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 max-h-[60vh] overflow-y-auto">
            {getHelpContent().sections.map((section, index) => (
              <div key={index}>
                <h4 className="font-medium text-sm mb-3 text-blue-900">{section.title}</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  {section.content.map((item, itemIndex) => (
                    <div key={itemIndex} className="leading-relaxed">
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {/* Best Practices / Tips */}
            {getHelpContent().tips.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-sm mb-2 text-blue-900">Best Practices</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  {getHelpContent().tips.map((tip, index) => (
                    <li key={index}>• {tip}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* General Analytics Tips */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2 text-green-900">General Analytics Tips</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Use the date range picker to compare different time periods</li>
                <li>• Export data regularly for deeper analysis and record keeping</li>
                <li>• Focus on trends over time rather than single-day performance</li>
                <li>• Green percentages indicate positive changes, red indicates areas for improvement</li>
                <li>• Switch between tabs to get a complete view of your business performance</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsHelpDialogOpen(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
