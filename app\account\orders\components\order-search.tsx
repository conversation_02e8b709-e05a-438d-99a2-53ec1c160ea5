'use client'

import { Input } from '@/components/ui/input'
import { Search, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useRef, useEffect } from 'react'

interface OrderSearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export function OrderSearch({ value, onChange, placeholder = "Search orders..." }: OrderSearchProps) {
  const inputRef = useRef<HTMLInputElement>(null)

  // Add keyboard shortcut (Ctrl/Cmd + K) to focus search
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        inputRef.current?.focus()
      }

      // ESC to clear search
      if (event.key === 'Escape' && value) {
        onChange('')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [value, onChange])

  return (
    <div className="relative">
      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
      <Input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-12 pr-12 h-12 text-base rounded-xl border-gray-200 bg-white shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
      />
      {value && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onChange('')}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 rounded-full"
          title="Clear search (ESC)"
        >
          <X className="h-4 w-4" />
        </Button>
      )}

      {/* Keyboard shortcut hint */}
      {!value && (
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-gray-400 pointer-events-none">
          ⌘K
        </div>
      )}
    </div>
  )
}
