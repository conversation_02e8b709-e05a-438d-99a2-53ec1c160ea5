"use client";

import React, { useState } from 'react';
import { useLocation } from '@/context/location-context';
import { useAuth } from '@/context/unified-auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon, MapPin, AlertTriangle } from 'lucide-react';

export default function LocationTestPage() {
  const {
    postcode,
    setPostcode,
    coordinates,
    geocodePostcode,
    isLoading,
    error,
    userId
  } = useLocation();

  const { user, isLoading: authLoading } = useAuth();
  const [testPostcode, setTestPostcode] = useState('');
  const [testResult, setTestResult] = useState<any>(null);

  // Function to validate Jersey postcode format
  const isValidJerseyPostcode = (postcode: string): boolean => {
    if (!postcode || typeof postcode !== 'string') return false;

    // Jersey postcodes follow the format JE1 1AA, JE2 3BT, etc.
    // JE followed by a digit (1-4), then a space, then another digit and two letters
    const jerseyPostcodeRegex = /^JE[1-4]\s\d[A-Z]{2}$/i;

    // Also accept format without space (JE11AA) and standardize it
    const noSpaceRegex = /^JE[1-4]\d[A-Z]{2}$/i;

    return jerseyPostcodeRegex.test(postcode.trim()) || noSpaceRegex.test(postcode.trim());
  };

  // Format a Jersey postcode to ensure it has a space
  const formatJerseyPostcode = (postcode: string): string => {
    const trimmed = postcode.trim().toUpperCase();

    // If it already has a space, return as is
    if (trimmed.includes(' ')) return trimmed;

    // Otherwise, insert a space after the 3rd character (e.g., JE11AA -> JE1 1AA)
    if (trimmed.length >= 5) {
      return `${trimmed.substring(0, 3)} ${trimmed.substring(3)}`;
    }

    return trimmed;
  };

  const handleTest = async () => {
    if (!testPostcode.trim()) return;

    try {
      setTestResult(null);

      // Validate the postcode format first
      const trimmedPostcode = testPostcode.trim();
      if (!isValidJerseyPostcode(trimmedPostcode)) {
        setTestResult({
          success: false,
          error: `"${trimmedPostcode}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc.`,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Format the postcode properly
      const formattedPostcode = formatJerseyPostcode(trimmedPostcode);

      // Process the postcode
      const coords = await geocodePostcode(formattedPostcode);
      setTestResult({
        success: true,
        postcode: formattedPostcode,
        coordinates: coords,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error testing postcode:', error);
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Location Context Test Page</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Current Location State</CardTitle>
            <CardDescription>Information from the location context</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-1">Authentication Status</h3>
              {authLoading ? (
                <p>Loading authentication status...</p>
              ) : (
                <p className={user ? "text-green-600 font-medium" : "text-amber-600 font-medium"}>
                  {user ? `Logged in as ${user.email}` : "Not logged in"}
                </p>
              )}
              {userId && (
                <p className="text-xs text-gray-500 mt-1">User ID: {userId}</p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">Current Postcode</h3>
              <p className={postcode ? "font-medium" : "text-gray-500 italic"}>
                {postcode || "No postcode set"}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">Current Coordinates</h3>
              {coordinates ? (
                <div>
                  <p className="font-medium">
                    [{coordinates[0].toFixed(6)}, {coordinates[1].toFixed(6)}]
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Longitude: {coordinates[0].toFixed(6)}, Latitude: {coordinates[1].toFixed(6)}
                  </p>
                </div>
              ) : (
                <p className="text-gray-500 italic">No coordinates set</p>
              )}
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Postcode Processing</CardTitle>
            <CardDescription>Enter a postcode to test the geocoding functionality</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <div className="relative flex-grow">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Enter a postcode (e.g., JE2 3NG)"
                  className="pl-10"
                  value={testPostcode}
                  onChange={(e) => setTestPostcode(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleTest();
                    }
                  }}
                />
              </div>
              <Button
                onClick={handleTest}
                disabled={isLoading || !testPostcode.trim()}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isLoading ? "Processing..." : "Test"}
              </Button>
            </div>

            {testResult && (
              <div className="mt-4">
                <h3 className="text-sm font-medium mb-2">Test Result</h3>
                {testResult.success ? (
                  <div className="bg-emerald-50 border border-emerald-200 rounded-md p-3">
                    <p className="text-emerald-800 font-medium">Successfully processed postcode</p>
                    <p className="text-sm mt-1">Postcode: <span className="font-medium">{testResult.postcode}</span></p>
                    {testResult.coordinates && (
                      <p className="text-sm mt-1">
                        Coordinates: <span className="font-medium">
                          [{testResult.coordinates[0].toFixed(6)}, {testResult.coordinates[1].toFixed(6)}]
                        </span>
                      </p>
                    )}
                    <p className="text-xs text-gray-500 mt-2">{testResult.timestamp}</p>
                  </div>
                ) : (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-red-800 font-medium">Error processing postcode</p>
                    <p className="text-sm mt-1">{testResult.error}</p>
                    <p className="text-xs text-gray-500 mt-2">{testResult.timestamp}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="bg-gray-50 text-xs text-gray-500 rounded-b-lg">
            <div className="flex items-start space-x-2">
              <InfoIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <p>
                This test will process the postcode using the location context. If you're logged in, it will first check your saved addresses for a matching postcode. If not found or you're not logged in, it will use the geocoding service. All coordinates are validated to be within Jersey bounds.
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-8">
        <h2 className="text-lg font-medium text-blue-800 mb-2">Test Instructions</h2>
        <ul className="list-disc list-inside space-y-2 text-blue-700">
          <li>Try entering a valid Jersey postcode (e.g., JE2 3NG)</li>
          <li>Try entering an invalid or non-Jersey postcode to see the fallback behavior</li>
          <li>If logged in, try entering a postcode that matches one of your saved addresses</li>
          <li>Check that coordinates are always within Jersey bounds</li>
        </ul>
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-8">
        <h2 className="text-lg font-medium text-amber-800 mb-2">Valid Jersey Postcode Format</h2>
        <p className="text-amber-700 mb-2">
          Jersey postcodes must follow this format:
        </p>
        <ul className="list-disc list-inside space-y-1 text-amber-700">
          <li><strong>JE1 1AA</strong>, <strong>JE2 3BT</strong>, etc.</li>
          <li>Start with <strong>JE</strong> followed by a digit (1-4)</li>
          <li>Then a space followed by another digit</li>
          <li>End with two uppercase letters</li>
        </ul>
        <p className="text-amber-700 mt-2">
          Examples: <code className="bg-amber-100 px-1 rounded">JE1 1AA</code>, <code className="bg-amber-100 px-1 rounded">JE2 3NG</code>, <code className="bg-amber-100 px-1 rounded">JE3 1BT</code>, <code className="bg-amber-100 px-1 rounded">JE4 9WA</code>
        </p>
        <p className="text-xs text-amber-600 mt-2">
          Note: The system will also accept postcodes without spaces (e.g., JE11AA) and will format them correctly.
        </p>
      </div>
    </div>
  );
}
