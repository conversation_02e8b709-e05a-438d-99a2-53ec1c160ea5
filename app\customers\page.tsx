import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import {
  ChevronDown,
  Download,
  Filter,
  HelpCircle,
  LineChart,
  Mail,
  Menu,
  Package,
  Plus,
  Search,
  Settings,
  ShoppingCart,
  SlidersHorizontal,
  Users,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CustomerList } from "@/components/customer-list"
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export const metadata: Metadata = {
  title: "Customers - Loop Seller Dashboard",
  description: "Manage your customers on the Loop delivery platform",
}

export default function CustomersPage() {
  return (
    <TooltipProvider>
      <div className="flex min-h-screen flex-col">
        <div className="border-b">
          <div className="flex h-16 items-center px-4 md:px-6">
            <div className="flex items-center gap-2 font-semibold">
              <Package className="h-6 w-6 text-emerald-500" />
              <span className="text-lg">Loop</span>
            </div>
            <Button variant="outline" size="icon" className="ml-auto h-8 w-8 lg:hidden">
              <Menu className="h-4 w-4" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
            <div className="ml-auto hidden items-center gap-4 lg:flex">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="Search..." className="w-64 rounded-lg bg-background pl-8" />
              </div>
              <Button variant="outline" size="icon" className="rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Button variant="outline" size="icon" className="rounded-full">
                <HelpCircle className="h-4 w-4" />
                <span className="sr-only">Help</span>
              </Button>
              <Button variant="outline" size="sm" className="rounded-full">
                <img src="/placeholder-user.jpg" alt="Avatar" className="mr-2 h-5 w-5 rounded-full" />
                John Doe
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      <div className="grid flex-1 lg:grid-cols-[240px_1fr]">
        <div className="hidden border-r bg-muted/40 lg:block">
          <div className="flex h-full max-h-screen flex-col gap-2">
            <div className="flex-1 overflow-auto py-2">
              <nav className="grid items-start px-4 text-sm font-medium">
                <Link
                  href="/"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <LineChart className="h-4 w-4" />
                  Dashboard
                </Link>
                <Link
                  href="/orders"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <ShoppingCart className="h-4 w-4" />
                  Orders
                </Link>
                <Link
                  href="/products"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <Package className="h-4 w-4" />
                  Products
                </Link>
                <Link
                  href="/customers"
                  className="flex items-center gap-3 rounded-lg bg-emerald-50 px-3 py-2 text-emerald-900 transition-all hover:text-emerald-900"
                >
                  <Users className="h-4 w-4" />
                  Customers
                </Link>
              </nav>
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Customers</h1>
                <p className="text-muted-foreground">Manage your customer relationships and data</p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="h-8">
                  <Download className="mr-2 h-3.5 w-3.5" />
                  Export
                </Button>
                <Button variant="outline" size="sm" className="h-8">
                  <Mail className="mr-2 h-3.5 w-3.5" />
                  Email All
                </Button>
                <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
                  <Plus className="mr-2 h-3.5 w-3.5" />
                  Add Customer
                </Button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                  <Users className="h-4 w-4 text-emerald-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2,834</div>
                  <p className="text-xs text-muted-foreground">+156 new this month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center">
                    <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-[200px] text-xs">Customers who have placed an order in the last 30 days</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,429</div>
                  <p className="text-xs text-muted-foreground">50.4% of total customers</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">£32.75</div>
                  <p className="text-xs text-muted-foreground">+£2.50 from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">76%</div>
                  <p className="text-xs text-muted-foreground">+2% from last month</p>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="all" className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All Customers</TabsTrigger>
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="inactive">Inactive</TabsTrigger>
                  <TabsTrigger value="new">New</TabsTrigger>
                </TabsList>
                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8">
                        <Filter className="mr-2 h-3.5 w-3.5" />
                        Filter
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[200px]">
                      <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuCheckboxItem checked>Active Customers</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>Inactive Customers</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>New Customers</DropdownMenuCheckboxItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel>Order Count</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuCheckboxItem checked>1+ Orders</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>5+ Orders</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>10+ Orders</DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8">
                        <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                        Sort
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[180px]">
                      <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Name (A-Z)</DropdownMenuItem>
                      <DropdownMenuItem>Name (Z-A)</DropdownMenuItem>
                      <DropdownMenuItem>Date Added (Newest)</DropdownMenuItem>
                      <DropdownMenuItem>Date Added (Oldest)</DropdownMenuItem>
                      <DropdownMenuItem>Total Spent (High to Low)</DropdownMenuItem>
                      <DropdownMenuItem>Total Spent (Low to High)</DropdownMenuItem>
                      <DropdownMenuItem>Order Count (High to Low)</DropdownMenuItem>
                      <DropdownMenuItem>Order Count (Low to High)</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search customers..."
                      className="w-64 rounded-lg bg-background pl-8 h-8"
                    />
                  </div>
                </div>
              </div>

              <TabsContent value="all" className="space-y-4">
                <Card>
                  <CardHeader className="p-4">
                    <div className="flex items-center justify-between">
                      <CardTitle>Customer Directory</CardTitle>
                      <CardDescription>Showing 1-10 of 2,834 customers</CardDescription>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0 overflow-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                            <TableHead className="w-[40px]">
                              <Checkbox />
                            </TableHead>
                            <TableHead className="min-w-[50px]">
                              <div className="flex items-center gap-1">
                                ID
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Unique identifier for the customer</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Name
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Customer's full name</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[180px]">
                              <div className="flex items-center gap-1">
                                Email
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Customer's email address</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[120px]">
                              <div className="flex items-center gap-1">
                                Phone
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Customer's phone number</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Status
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Current status of the customer</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Orders
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Total number of orders placed</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Total Spent
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Total amount spent by the customer</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Last Order
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Date of the customer's most recent order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Joined
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Date when the customer first registered</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <CustomerList />
                        </TableBody>
                      </Table>
                  </CardContent>
                  <CardFooter className="flex items-center justify-between p-4">
                    <div className="text-xs text-muted-foreground">Showing 1-10 of 2,834 customers</div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" disabled>
                        Previous
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        1
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        2
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        3
                      </Button>
                      <Button variant="outline" size="sm">
                        Next
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="active" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Active Customers</CardTitle>
                    <CardDescription>Customers who have placed an order in the last 30 days</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only active customers
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="inactive" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Inactive Customers</CardTitle>
                    <CardDescription>Customers who haven't placed an order in the last 30 days</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only inactive customers
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="new" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>New Customers</CardTitle>
                    <CardDescription>Customers who registered in the last 7 days</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only new customers
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </div>
    </TooltipProvider>
  )
}
