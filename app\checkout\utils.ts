// Utility functions for the checkout page

// Format business name from slug
export const formatBusinessName = (name: string): string => {
  if (!name) return '';

  // If the name doesn't contain hyphens, it's likely already formatted
  if (!name.includes('-')) {
    return name;
  }

  // Special handling for known business slugs
  if (name === 'jersey-wings') {
    return 'Jersey Wings';
  } else if (name === 'jersey-grill') {
    return 'Jersey Grill';
  } else if (name === 'st-brelade-bistro') {
    return 'St Brelade Bistro';
  }

  // For "Business jersey Wings" format, fix it to "Jersey Wings"
  if (name.toLowerCase().startsWith('business jersey')) {
    return name.replace(/^business jersey/i, 'Jersey');
  }

  // Split by hyphens, capitalize each word, and join with spaces
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Define interface for checkout form state
export interface CheckoutFormState {
  paymentMethod: string;
  address: string;
  parish: string;
  customerCoords: [number, number] | null;
  firstName: string;
  lastName: string;
  phone: string;
  instructions: string;
  postcode: string;
  deliveryType: string;
  scheduledDeliveryTime: string | null;
}

// Helper function to load form state from sessionStorage
export const loadFormState = (): CheckoutFormState | null => {
  if (typeof window === 'undefined') return null;
  try {
    const savedState = sessionStorage.getItem('loopJerseyCheckoutFormState');
    if (savedState) {
      return JSON.parse(savedState);
    }
  } catch (error) {
    console.error('Error loading checkout form state:', error);
  }
  return null;
};

// Validate phone number
export const validatePhoneNumber = (phone: string): boolean => {
  // Basic validation for Jersey phone numbers
  const phoneRegex = /^(07\d{9}|01534\d{6})$/;
  return phoneRegex.test(phone.replace(/\s+/g, ''));
};

// Save form state to sessionStorage
export const saveFormState = (formState: CheckoutFormState): void => {
  if (typeof window === 'undefined') return;
  try {
    sessionStorage.setItem('loopJerseyCheckoutFormState', JSON.stringify(formState));
    console.log('Checkout form state saved to sessionStorage');
  } catch (error) {
    console.error('Error saving checkout form state:', error);
  }
};
