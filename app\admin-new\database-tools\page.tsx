"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Database } from "lucide-react"

export default function DatabaseToolsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isCalculatingRatings, setIsCalculatingRatings] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const updateRatingColumn = async () => {
    try {
      setIsLoading(true)
      setResult(null)

      // Add a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      try {
        const response = await fetch('/api/admin/update-rating-column', {
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if the request completes

        const data = await response.json();

        if (!response.ok) {
          console.error('Error updating rating column:', data);
          throw new Error(data.message || 'Failed to update rating column');
        }

        setResult({
          success: true,
          message: data.message || 'Successfully updated rating column in businesses table'
        });
      } catch (fetchError) {
        if (fetchError.name === 'AbortError') {
          throw new Error('Request timed out. The server took too long to respond.');
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('Error in updateRatingColumn:', error);
      setResult({
        success: false,
        message: error instanceof Error
          ? error.message
          : 'Unknown error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  }

  const calculateAverageRatings = async () => {
    try {
      setIsCalculatingRatings(true)
      setResult(null)

      // Add a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      try {
        const response = await fetch('/api/admin/calculate-average-ratings', {
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if the request completes

        const data = await response.json();

        if (!response.ok) {
          console.error('Error calculating average ratings:', data);
          throw new Error(data.message || 'Failed to calculate average ratings');
        }

        setResult({
          success: true,
          message: data.message || 'Successfully calculated average ratings for businesses'
        });
      } catch (fetchError) {
        if (fetchError.name === 'AbortError') {
          throw new Error('Request timed out. The server took too long to respond.');
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('Error in calculateAverageRatings:', error);
      setResult({
        success: false,
        message: error instanceof Error
          ? error.message
          : 'Unknown error occurred'
      });
    } finally {
      setIsCalculatingRatings(false);
    }
  }

  return (
    <>
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background px-4 sm:px-6">
        <div className="flex flex-1 items-center gap-4">
          <h1 className="text-xl font-semibold">Database Tools</h1>
        </div>
      </header>

      <div className="p-4 sm:p-6 lg:p-8">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-emerald-500" />
                Update Business Ratings Column
              </CardTitle>
              <CardDescription>
                Ensure the rating column in the businesses table is properly set up
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This will ensure the existing rating column in the businesses table is properly
                configured with the correct data type and default values (0.0 to 5.0).
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={updateRatingColumn}
                disabled={isLoading || isCalculatingRatings}
                className="w-full"
              >
                {isLoading ? 'Updating Column...' : 'Update Rating Column'}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-emerald-500" />
                Calculate Average Ratings
              </CardTitle>
              <CardDescription>
                Calculate average ratings from reviews for all businesses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This will calculate the average rating for each business based on their reviews
                and set up triggers to automatically update ratings when reviews change.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={calculateAverageRatings}
                disabled={isLoading || isCalculatingRatings}
                className="w-full"
              >
                {isCalculatingRatings ? 'Calculating Ratings...' : 'Calculate Average Ratings'}
              </Button>
            </CardFooter>
          </Card>
        </div>

        {result && (
          <Alert
            className={`mt-6 ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
          >
            {result.success ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertTitle>{result.success ? 'Success' : 'Error'}</AlertTitle>
            <AlertDescription>{result.message}</AlertDescription>
          </Alert>
        )}
      </div>
    </>
  )
}
