'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = React.useState(false)

  // Only render the theme provider after the component has mounted to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // If not mounted yet, just render children without theme context
  if (!mounted) {
    return <>{children}</>
  }

  return (
    <NextThemesProvider {...props}>{children}</NextThemesProvider>
  )
}
