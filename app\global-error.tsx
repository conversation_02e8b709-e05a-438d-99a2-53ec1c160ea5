'use client'

import { useEffect } from 'react'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error)
  }, [error])

  const isChunkError = error.message && (
    error.message.includes('ChunkLoadError') || 
    error.message.includes('Loading chunk') || 
    error.message.includes('Failed to fetch dynamically imported module')
  )

  const handleReset = () => {
    // Clear cache and reload if it's a chunk error
    if (isChunkError) {
      // Clear browser cache for this site
      if ('caches' in window) {
        caches.keys().then((names) => {
          names.forEach((name) => {
            caches.delete(name)
          })
        })
      }
      
      // Hard reload the page
      window.location.reload()
    } else {
      // For other errors, just use the provided reset function
      reset()
    }
  }

  return (
    <html lang="en" className={inter.className}>
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen px-4 py-16 text-center bg-gray-50">
          <div className="max-w-md mx-auto">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              {isChunkError ? 'Application Update Available' : 'Something went wrong!'}
            </h2>
            <p className="text-gray-600 mb-6">
              {isChunkError 
                ? 'It looks like the application has been updated. Please reload to get the latest version.'
                : 'An unexpected error occurred. Please try again or contact support if the problem persists.'}
            </p>
            <button 
              onClick={handleReset}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md font-medium transition-colors"
            >
              {isChunkError ? 'Reload Application' : 'Try again'}
            </button>
          </div>
        </div>
      </body>
    </html>
  )
}
