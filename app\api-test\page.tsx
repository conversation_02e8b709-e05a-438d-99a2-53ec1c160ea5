'use client'

import { useState, useEffect } from 'react'

export default function ApiTestPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      
      try {
        console.log('Fetching data from direct-businesses API...')
        const response = await fetch('/api/direct-businesses')
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `API returned ${response.status}`)
        }
        
        const apiData = await response.json()
        console.log('API data:', apiData)
        setData(apiData)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">API Test Page</h1>
      
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}
      
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border p-4 rounded">
            <h2 className="text-xl font-semibold mb-2">Business Types ({data.businessTypes?.length || 0})</h2>
            {data.businessTypes && data.businessTypes.length > 0 ? (
              <ul className="list-disc pl-5">
                {data.businessTypes.map((type: any) => (
                  <li key={type.id}>
                    {type.name} (ID: {type.id}, Slug: {type.slug})
                  </li>
                ))}
              </ul>
            ) : (
              <p>No business types found</p>
            )}
          </div>
          
          <div className="border p-4 rounded">
            <h2 className="text-xl font-semibold mb-2">Restaurants ({data.restaurants?.length || 0})</h2>
            {data.restaurants && data.restaurants.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {data.restaurants.map((business: any) => (
                  <div key={business.id} className="border p-2 rounded">
                    <h3 className="font-bold">{business.name}</h3>
                    <p className="text-sm text-gray-600">{business.location}</p>
                    <p className="text-sm">Rating: {business.rating || 'N/A'}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p>No restaurants found</p>
            )}
          </div>
        </div>
      )}
      
      {data && data.environment && (
        <div className="mt-4 border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Environment Info</h2>
          <p>NEXT_PUBLIC_SUPABASE_URL: {data.environment.supabaseUrl}</p>
          <p>SUPABASE_SERVICE_ROLE_KEY: {data.environment.supabaseServiceKey}</p>
          <p>NODE_ENV: {data.environment.nodeEnv}</p>
        </div>
      )}
    </div>
  )
}
