"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from "recharts"
import {
  AlertCircle,
  ArrowLeft,
  Clock,
  Download,
  Refresh<PERSON>c<PERSON>,
  Filter
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"

interface TriggerLog {
  id: string
  trigger_name: string
  table_name: string
  operation: string
  execution_time_ms: number
  affected_rows: number
  order_id: string | null
  business_count: number | null
  created_at: string
}

interface TriggerSummary {
  count: number
  avg_time: number
  max_time: number
  min_time: number
}

export default function TriggerPerformancePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [metrics, setMetrics] = useState<TriggerLog[]>([])
  const [summary, setSummary] = useState<Record<string, TriggerSummary>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState("7")
  const [activeTab, setActiveTab] = useState("summary")

  useEffect(() => {
    fetchPerformanceMetrics()
  }, [timeRange])

  const fetchPerformanceMetrics = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/monitoring/trigger-performance?days=${timeRange}`)
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          router.push("/login?redirectTo=/admin/monitoring/trigger-performance")
          return
        }
        
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch performance metrics")
      }
      
      const data = await response.json()
      setMetrics(data.metrics)
      setSummary(data.summary)
    } catch (err: any) {
      console.error("Error fetching performance metrics:", err)
      setError(err.message || "Failed to load performance metrics")
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to load performance metrics"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = () => {
    fetchPerformanceMetrics()
  }

  const handleExport = () => {
    // Create CSV content
    const headers = "Trigger Name,Table Name,Operation,Execution Time (ms),Affected Rows,Order ID,Business Count,Created At\n"
    const rows = metrics.map(log => 
      `"${log.trigger_name}","${log.table_name}","${log.operation}",${log.execution_time_ms},${log.affected_rows},"${log.order_id || ''}",${log.business_count || 0},"${log.created_at}"`
    ).join("\n")
    
    const csvContent = `data:text/csv;charset=utf-8,${headers}${rows}`
    const encodedUri = encodeURI(csvContent)
    
    // Create download link and trigger download
    const link = document.createElement("a")
    link.setAttribute("href", encodedUri)
    link.setAttribute("download", `trigger-performance-${new Date().toISOString().split('T')[0]}.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Prepare chart data
  const summaryChartData = Object.entries(summary).map(([name, stats]) => ({
    name: name,
    avgTime: parseFloat(stats.avg_time.toFixed(2)),
    maxTime: stats.max_time,
    count: stats.count
  }))

  // Prepare time-based chart data
  const timeChartData = metrics.slice().sort((a, b) => 
    new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  ).map(log => ({
    time: new Date(log.created_at).toLocaleTimeString(),
    name: log.trigger_name,
    executionTime: log.execution_time_ms,
    businessCount: log.business_count || 0
  }))

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <RefreshCcw className="h-12 w-12 animate-spin text-emerald-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading performance metrics...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>There was a problem loading the performance metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center text-red-600">
            <AlertCircle className="mr-2 h-5 w-5" />
            <p>{error}</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => router.push("/admin")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" className="mr-4" onClick={() => router.push("/admin")}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Trigger Performance Monitoring</h1>
            <p className="text-muted-foreground">Monitor database trigger performance for multi-business orders</p>
          </div>
        </div>
        <div className="flex items-center gap-2 mt-4 md:mt-0">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Last 24 hours</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCcw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="details">Detailed Logs</TabsTrigger>
          <TabsTrigger value="charts">Charts</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Triggers</CardTitle>
                <Filter className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{Object.keys(summary).length}</div>
                <p className="text-xs text-muted-foreground">Unique triggers monitored</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(summary).reduce((total, stats) => total + stats.count, 0)}
                </div>
                <p className="text-xs text-muted-foreground">Trigger executions logged</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Execution Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(Object.values(summary).reduce((total, stats) => total + stats.avg_time * stats.count, 0) / 
                    Object.values(summary).reduce((total, stats) => total + stats.count, 0)).toFixed(2)} ms
                </div>
                <p className="text-xs text-muted-foreground">Average across all triggers</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Max Execution Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.max(...Object.values(summary).map(stats => stats.max_time)).toFixed(2)} ms
                </div>
                <p className="text-xs text-muted-foreground">Slowest trigger execution</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Trigger Performance Summary</CardTitle>
              <CardDescription>Performance metrics for each trigger</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Trigger Name</TableHead>
                    <TableHead className="text-right">Count</TableHead>
                    <TableHead className="text-right">Avg Time (ms)</TableHead>
                    <TableHead className="text-right">Max Time (ms)</TableHead>
                    <TableHead className="text-right">Min Time (ms)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(summary).map(([name, stats]) => (
                    <TableRow key={name}>
                      <TableCell className="font-medium">{name}</TableCell>
                      <TableCell className="text-right">{stats.count}</TableCell>
                      <TableCell className="text-right">{stats.avg_time.toFixed(2)}</TableCell>
                      <TableCell className="text-right">{stats.max_time.toFixed(2)}</TableCell>
                      <TableCell className="text-right">{stats.min_time.toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Trigger Logs</CardTitle>
              <CardDescription>Individual trigger execution logs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Trigger Name</TableHead>
                      <TableHead>Table</TableHead>
                      <TableHead>Operation</TableHead>
                      <TableHead className="text-right">Time (ms)</TableHead>
                      <TableHead className="text-right">Businesses</TableHead>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Timestamp</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">{log.trigger_name}</TableCell>
                        <TableCell>{log.table_name}</TableCell>
                        <TableCell>{log.operation}</TableCell>
                        <TableCell className="text-right">
                          <span className={log.execution_time_ms > 100 ? "text-amber-600" : 
                                          log.execution_time_ms > 500 ? "text-red-600" : ""}>
                            {log.execution_time_ms.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">{log.business_count || "-"}</TableCell>
                        <TableCell>{log.order_id ? log.order_id.substring(0, 8) + "..." : "-"}</TableCell>
                        <TableCell>{new Date(log.created_at).toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="charts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Average Execution Time by Trigger</CardTitle>
              <CardDescription>Comparison of average execution times</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={summaryChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="avgTime" name="Avg Time (ms)" fill="#10b981" />
                    <Bar dataKey="maxTime" name="Max Time (ms)" fill="#f59e0b" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Execution Time vs Business Count</CardTitle>
              <CardDescription>Correlation between execution time and number of businesses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="executionTime" 
                      name="Execution Time (ms)" 
                      stroke="#10b981" 
                      activeDot={{ r: 8 }} 
                    />
                    <Line 
                      yAxisId="right"
                      type="monotone" 
                      dataKey="businessCount" 
                      name="Business Count" 
                      stroke="#f59e0b" 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
