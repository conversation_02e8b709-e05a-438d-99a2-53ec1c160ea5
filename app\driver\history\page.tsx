"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Search,
  Filter,
  ChevronDown,
  CheckCircle,
  XCircle,
  Clock,
  MapPin,
  Calendar,
  ArrowRight,
  Download,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// Mock data for delivery history
const deliveries = [
  {
    id: "JE-5280",
    restaurant: {
      name: "Jersey Grill",
      address: "15 King Street, St Helier",
    },
    customer: {
      name: "<PERSON>",
      address: "42 Roseville Street, St Helier",
    },
    status: "delivered",
    date: "Today",
    time: "14:32",
    earnings: 6.5,
    distance: "2.8 km",
    duration: "22 min",
  },
  {
    id: "JE-5275",
    restaurant: {
      name: "Jersey Seafood",
      address: "8 Pier Road, St Helier",
    },
    customer: {
      name: "Michael Brown",
      address: "15 Beachfront, St Helier",
    },
    status: "delivered",
    date: "Today",
    time: "12:15",
    earnings: 7.25,
    distance: "3.2 km",
    duration: "25 min",
  },
  {
    id: "JE-5268",
    restaurant: {
      name: "Jersey Pizza",
      address: "23 Bath Street, St Helier",
    },
    customer: {
      name: "Emma Wilson",
      address: "7 Victoria Street, St Helier",
    },
    status: "delivered",
    date: "Yesterday",
    time: "19:45",
    earnings: 5.75,
    distance: "1.9 km",
    duration: "18 min",
  },
  {
    id: "JE-5262",
    restaurant: {
      name: "Jersey Curry House",
      address: "10 Colomberie, St Helier",
    },
    customer: {
      name: "James Taylor",
      address: "28 Havre des Pas, St Helier",
    },
    status: "cancelled",
    date: "Yesterday",
    time: "18:20",
    earnings: 0,
    distance: "0 km",
    duration: "0 min",
    cancellationReason: "Restaurant was closed",
  },
  {
    id: "JE-5255",
    restaurant: {
      name: "Jersey Sushi",
      address: "5 Halkett Place, St Helier",
    },
    customer: {
      name: "Olivia Davis",
      address: "12 Gorey Village, Grouville",
    },
    status: "delivered",
    date: "2 days ago",
    time: "13:10",
    earnings: 8.5,
    distance: "5.6 km",
    duration: "32 min",
  },
]

// Status badge component
function DeliveryStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "delivered":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle className="mr-1 h-3 w-3" />
          Delivered
        </Badge>
      )
    case "cancelled":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <XCircle className="mr-1 h-3 w-3" />
          Cancelled
        </Badge>
      )
    default:
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
          <Clock className="mr-1 h-3 w-3" />
          Unknown
        </Badge>
      )
  }
}

export default function DeliveryHistoryPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [dateFilter, setDateFilter] = useState("all_time")

  // Filter deliveries based on active tab, search query, and date filter
  const filteredDeliveries = deliveries.filter((delivery) => {
    // Filter by tab
    if (activeTab !== "all" && delivery.status !== activeTab) {
      return false
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        delivery.id.toLowerCase().includes(query) ||
        delivery.restaurant.name.toLowerCase().includes(query) ||
        delivery.customer.name.toLowerCase().includes(query) ||
        delivery.customer.address.toLowerCase().includes(query)
      )
    }

    // Filter by date
    if (dateFilter === "today" && delivery.date !== "Today") {
      return false
    }
    if (dateFilter === "yesterday" && delivery.date !== "Yesterday") {
      return false
    }
    if (
      dateFilter === "this_week" &&
      delivery.date !== "Today" &&
      delivery.date !== "Yesterday" &&
      !delivery.date.includes("days ago")
    ) {
      return false
    }

    return true
  })

  // Calculate earnings
  const totalEarnings = filteredDeliveries.reduce((total, delivery) => total + delivery.earnings, 0)
  const totalDeliveries = filteredDeliveries.filter((d) => d.status === "delivered").length

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Delivery History</h1>
          <p className="text-gray-500">View and manage your past deliveries</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" className="flex items-center">
            <Download className="mr-2 h-4 w-4" />
            Export History
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <CheckCircle className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Deliveries</p>
              <h3 className="text-2xl font-bold">{totalDeliveries}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">From {filteredDeliveries.length} orders</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <MapPin className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Distance</p>
              <h3 className="text-2xl font-bold">13.5 km</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Across all filtered deliveries</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <h3 className="text-2xl font-bold">£{totalEarnings.toFixed(2)}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">For the selected period</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search orders, restaurants, addresses..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-3">
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_time">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="this_week">This Week</SelectItem>
              <SelectItem value="this_month">This Month</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="flex items-center">
            <Filter size={16} className="mr-2" />
            Filters
            <ChevronDown size={16} className="ml-2" />
          </Button>
        </div>
      </div>

      {/* Delivery Tabs */}
      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="all">All Deliveries</TabsTrigger>
          <TabsTrigger value="delivered">Delivered</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredDeliveries.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No deliveries found</p>
            </div>
          ) : (
            filteredDeliveries.map((delivery) => <DeliveryCard key={delivery.id} delivery={delivery} />)
          )}
        </TabsContent>

        <TabsContent value="delivered" className="space-y-4">
          {filteredDeliveries.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No delivered orders</p>
            </div>
          ) : (
            filteredDeliveries.map((delivery) => <DeliveryCard key={delivery.id} delivery={delivery} />)
          )}
        </TabsContent>

        <TabsContent value="cancelled" className="space-y-4">
          {filteredDeliveries.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No cancelled orders</p>
            </div>
          ) : (
            filteredDeliveries.map((delivery) => <DeliveryCard key={delivery.id} delivery={delivery} />)
          )}
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious href="#" />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#" isActive>
              1
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">2</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">3</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
          <PaginationItem>
            <PaginationNext href="#" />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )
}

// Delivery Card Component
function DeliveryCard({ delivery }: { delivery: any }) {
  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <div className="flex items-center mb-2 md:mb-0">
              <div className="font-medium mr-3">Order #{delivery.id}</div>
              <DeliveryStatusBadge status={delivery.status} />
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              {delivery.date} at {delivery.time}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Restaurant Info */}
            <div>
              <p className="text-xs text-gray-500 mb-1">Pickup</p>
              <p className="font-medium">{delivery.restaurant.name}</p>
              <div className="flex items-start mt-1">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                <p className="text-sm text-gray-600">{delivery.restaurant.address}</p>
              </div>
            </div>

            {/* Customer Info */}
            <div>
              <p className="text-xs text-gray-500 mb-1">Dropoff</p>
              <p className="font-medium">{delivery.customer.name}</p>
              <div className="flex items-start mt-1">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                <p className="text-sm text-gray-600">{delivery.customer.address}</p>
              </div>
            </div>

            {/* Delivery Details */}
            <div className="flex flex-col items-end justify-between">
              <div className="text-right">
                <p className="text-sm text-gray-500">Earnings</p>
                <p className="text-xl font-bold">£{delivery.earnings.toFixed(2)}</p>
                <div className="flex items-center justify-end mt-1 text-sm text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {delivery.duration}
                  <span className="mx-1">•</span>
                  <MapPin className="h-3 w-3 mr-1" />
                  {delivery.distance}
                </div>
              </div>

              <div className="mt-2">
                <Link href={`/driver/history/${delivery.id}`}>
                  <Button variant="outline" size="sm" className="flex items-center">
                    View Details
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {delivery.status === "cancelled" && delivery.cancellationReason && (
            <div className="mt-4 p-3 bg-red-50 rounded-md">
              <p className="text-sm font-medium text-red-800">Cancellation Reason:</p>
              <p className="text-sm text-red-700">{delivery.cancellationReason}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
