'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Heart, Star, Clock, Truck } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import FallbackImage from '@/components/fallback-image'


interface SquareBusinessCardProps {
  id: string
  name: string
  image: string
  businessType: string
  rating?: number
  deliveryTime: string
  deliveryTimeRange?: string
  deliveryFee: string
  distance?: string
  sponsored?: boolean
  offers?: {
    text: string
    color: string
  }[]
  className?: string
  location?: string
  deliveryRadius?: string
  preparationTime?: string
  deliveryAvailable?: boolean
}

export default function SquareBusinessCard({
  id,
  name,
  image,
  businessType,
  rating,
  deliveryTime,
  deliveryTimeRange,
  deliveryFee,
  distance,
  sponsored = false,
  offers = [],
  className,
  location,
  deliveryRadius,
  preparationTime,
  deliveryAvailable = true
}: SquareBusinessCardProps) {
  const [isFavorite, setIsFavorite] = useState(false)

  // Format preparation time
  const displayPrepTime = preparationTime?.includes('mins')
    ? preparationTime.replace('mins', 'min')
    : (preparationTime?.includes('min') ? preparationTime : `${preparationTime || '15'} min`)

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsFavorite(!isFavorite)
  }

  return (
    <Link
      href={`/business/${id}`}
      className={cn("block", className)}
    >
      <div className="relative rounded-lg overflow-hidden bg-white shadow-md hover:shadow-lg border border-gray-100 transition-all duration-200">
        <div className="flex flex-col md:flex-row">
          {/* Image Container - Larger square image */}
          <div className="relative w-full md:w-2/5 aspect-square">
            <FallbackImage
              src={image}
              alt={name}
              fallbackSrc="/placeholder.svg"
              className="w-full h-full object-cover"
            />

            {/* Favorite Button */}
            <button
              onClick={toggleFavorite}
              className="absolute top-2 right-2 z-10 w-6 h-6 md:w-5 md:h-5 flex items-center justify-center bg-white rounded-full shadow-md"
            >
              <Heart
                className={cn(
                  "h-4 w-4 md:h-3 md:w-3 transition-colors",
                  isFavorite ? "fill-red-500 text-red-500" : "text-gray-400"
                )}
              />
            </button>

            {/* Offers */}
            {offers.length > 0 && (
              <div className="absolute left-2 top-2 flex flex-col gap-1">
                {offers.map((offer, index) => (
                  <div
                    key={index}
                    className={`text-white text-xs font-bold px-2 py-0.5 rounded-md shadow-sm ${offer.color}`}
                    style={{
                      backdropFilter: 'blur(4px)',
                      WebkitBackdropFilter: 'blur(4px)'
                    }}
                  >
                    {offer.text}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Content - Redesigned for Deliveroo-style layout */}
          <div className="p-3 md:p-2 md:w-3/5 md:flex md:flex-col md:justify-between">
            {/* Business Name in Bold */}
            <h3 className="font-bold text-gray-900 text-sm md:text-base tracking-tight">{name}</h3>

            {/* Sponsored Tag */}
            {sponsored && (
              <div className="text-xs font-medium text-emerald-600 bg-emerald-50 inline-block px-1.5 py-0.5 rounded mt-1">Sponsored</div>
            )}

            {/* Metadata with Icons - Clean Format */}
            <div className="mt-2 space-y-1.5">
              {/* Rating with Star Icon */}
              {rating && (
                <div className="flex items-center text-xs">
                  <span className="mr-1 text-yellow-500">⭐</span>
                  <span className="text-emerald-600 font-semibold">{rating}</span>
                </div>
              )}

              {/* Parish Location with Map Icon */}
              {location && (
                <div className="flex items-center text-xs text-gray-700">
                  <span className="mr-1 text-gray-500">🗺️</span>
                  <span>{location}</span>
                </div>
              )}

              {/* Pickup-Only Badge - Shown if delivery is not available */}
              {!deliveryAvailable && (
                <div className="flex items-center text-xs">
                  <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200 text-xs">
                    Pickup Only
                  </Badge>
                </div>
              )}

              {/* Delivery and Pickup Options */}
              <div className="mt-2 grid grid-cols-2 gap-2 border-t pt-2">
                {/* Pickup Option - Always shown */}
                <div className="flex flex-col">
                  <div className="text-xs font-medium text-gray-700 mb-1 flex items-center">
                    <Clock className="h-3 w-3 mr-1 text-gray-500" />
                    Pickup
                  </div>
                  <div className="flex items-center text-xs">
                    <span>{displayPrepTime}</span>
                  </div>
                </div>

                {/* Delivery Option - Only shown if available */}
                {deliveryAvailable && (
                  <div className="flex flex-col">
                    <div className="text-xs font-medium text-gray-700 mb-1 flex items-center">
                      <Truck className="h-3 w-3 mr-1 text-gray-500" />
                      Delivery
                    </div>
                    <div className="flex items-center text-xs">
                      <span>{deliveryFee}</span>
                    </div>
                    <div className="flex items-center text-xs mt-1">
                      <span>{deliveryTimeRange || 'Calculating...'}</span>
                      <span className="text-xs text-gray-500 ml-1">(Total time)</span>
                    </div>
                  </div>
                )}

                {/* Empty space if delivery is not available */}
                {!deliveryAvailable && (
                  <div className="flex items-center justify-center">
                    {/* Intentionally empty */}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
