import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// POST endpoint to validate a cart
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: { user } } = await supabaseClient.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    
    // Get delivery address for validation if provided
    const deliveryAddress = body.deliveryAddress;
    
    // Validate the cart
    const { data, error } = await supabase.rpc(
      'validate_cart',
      {
        p_user_id: user.id
      }
    );
    
    if (error) {
      console.error('Error validating cart:', error);
      return NextResponse.json(
        { error: 'Failed to validate cart', details: error.message },
        { status: 500 }
      );
    }
    
    // If delivery address is provided, validate it against business delivery radius
    if (deliveryAddress && deliveryAddress.coordinates) {
      // Get all businesses in the cart
      const businessIds = [];
      
      // Get the cart to extract business IDs
      const { data: cartData } = await supabase.rpc(
        'get_user_cart_enhanced',
        { p_user_id: user.id }
      );
      
      if (cartData && cartData.cart && cartData.cart.items) {
        // Extract unique business IDs
        const items = cartData.cart.items;
        for (const item of items) {
          if (!businessIds.includes(item.businessId)) {
            businessIds.push(item.businessId);
          }
        }
        
        // Check delivery radius for each business
        const deliveryValidation = await Promise.all(
          businessIds.map(async (businessId) => {
            const { data: business } = await supabase
              .from('businesses')
              .select('name, delivery_radius, coordinates')
              .eq('slug', businessId)
              .single();
            
            if (!business) {
              return {
                businessId,
                valid: false,
                message: 'Business not found'
              };
            }
            
            // Calculate distance between business and delivery address
            // This is a simplified calculation - in a real app, you'd use a proper distance calculation
            const businessCoords = business.coordinates;
            const deliveryCoords = deliveryAddress.coordinates;
            
            // Simple Euclidean distance calculation (not accurate for real-world use)
            const distance = Math.sqrt(
              Math.pow(businessCoords.x - deliveryCoords.x, 2) +
              Math.pow(businessCoords.y - deliveryCoords.y, 2)
            );
            
            return {
              businessId,
              businessName: business.name,
              valid: distance <= business.delivery_radius,
              distance: distance,
              deliveryRadius: business.delivery_radius,
              message: distance <= business.delivery_radius
                ? 'Delivery address is within range'
                : 'Delivery address is outside delivery range'
            };
          })
        );
        
        // Add delivery validation to the response
        return NextResponse.json({
          ...data,
          deliveryValidation,
          deliveryValid: deliveryValidation.every(v => v.valid)
        });
      }
    }
    
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
