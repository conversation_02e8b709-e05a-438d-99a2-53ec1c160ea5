"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Bell, User, Shield, Smartphone, AlertCircle } from "lucide-react"
import { DriverInterfacePreference } from "@/components/driver-interface-preference"

export default function DriverSettingsPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [driverStatus, setDriverStatus] = useState<{
    isDriver: boolean
    isVerified: boolean
    isActive: boolean
  } | null>(null)

  useEffect(() => {
    const checkDriverStatus = async () => {
      if (!user) {
        router.push('/login?redirectTo=/driver/settings')
        return
      }

      try {
        const response = await fetch(`/api/driver/verify?userId=${user.id}`)
        const status = await response.json()
        
        setDriverStatus(status)
        
        if (!status.isDriver) {
          router.push('/partners/riders/apply')
          return
        }
      } catch (error) {
        console.error('Error checking driver status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkDriverStatus()
  }, [user, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (!driverStatus?.isDriver) {
    return null // Will redirect
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Driver Settings</h1>
        <p className="text-gray-500">Manage your driver account preferences and settings</p>
      </div>

      {/* Verification Status */}
      {!driverStatus?.isVerified && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Account Pending Verification</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Your driver application is being reviewed. Some settings may be limited until verification is complete.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Interface Preference */}
        <DriverInterfacePreference />

        {/* Account Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Account Settings</span>
            </CardTitle>
            <CardDescription>
              Manage your driver profile and account preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button variant="outline" className="w-full justify-start">
              <User className="h-4 w-4 mr-2" />
              Edit Profile Information
            </Button>
            
            <Button variant="outline" className="w-full justify-start">
              <Shield className="h-4 w-4 mr-2" />
              Vehicle & Documents
            </Button>
            
            <Separator />
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-gray-500">Receive updates via email</p>
              </div>
              <Switch id="email-notifications" />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="sms-notifications">SMS Notifications</Label>
                <p className="text-sm text-gray-500">Receive urgent updates via SMS</p>
              </div>
              <Switch id="sms-notifications" />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Notification Preferences</span>
            </CardTitle>
            <CardDescription>
              Control how and when you receive notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="new-orders">New Order Alerts</Label>
                <p className="text-sm text-gray-500">Get notified of new delivery requests</p>
              </div>
              <Switch id="new-orders" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="order-updates">Order Updates</Label>
                <p className="text-sm text-gray-500">Status changes and customer messages</p>
              </div>
              <Switch id="order-updates" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="earnings-updates">Earnings Updates</Label>
                <p className="text-sm text-gray-500">Payment confirmations and summaries</p>
              </div>
              <Switch id="earnings-updates" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="promotional">Promotional Notifications</Label>
                <p className="text-sm text-gray-500">Special offers and announcements</p>
              </div>
              <Switch id="promotional" />
            </div>
          </CardContent>
        </Card>

        {/* Mobile App Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Smartphone className="h-5 w-5" />
              <span>Mobile App</span>
            </CardTitle>
            <CardDescription>
              Mobile app installation and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Install Mobile App</h4>
              <p className="text-sm text-blue-700 mb-3">
                Get the best driver experience with our mobile app. Perfect for on-the-go delivery management.
              </p>
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <a href="/driver-mobile/dashboard" target="_blank">
                  <Smartphone className="h-4 w-4 mr-2" />
                  Open Mobile Interface
                </a>
              </Button>
            </div>
            
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Mobile Features:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Real-time order notifications</li>
                <li>GPS navigation integration</li>
                <li>Touch-optimized interface</li>
                <li>Offline mode support</li>
                <li>Quick order acceptance</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
