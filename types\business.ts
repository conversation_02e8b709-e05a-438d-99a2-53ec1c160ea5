// Common types for businesses (restaurants, shops, etc.)

// Opening hours interface
export interface OpeningHours {
  monday: { open: string; close: string };
  tuesday: { open: string; close: string };
  wednesday: { open: string; close: string };
  thursday: { open: string; close: string };
  friday: { open: string; close: string };
  saturday: { open: string; close: string };
  sunday: { open: string; close: string };
}

// Base business type
export interface Business {
  id: number; // Changed from string to number to match database schema
  slug: string; // Added slug for routing
  name: string;
  coverImage: string;
  rating: number;
  reviewCount: number;
  deliveryTime: string | number; // Changed to allow both string and number formats
  delivery_time_minutes?: number; // Total delivery time from database
  preparationTimeMinutes: number;
  deliveryFee: number;
  minimum_order_amount?: number; // Minimum order amount for delivery
  opening_hours?: OpeningHours; // Opening hours for each day of the week
  hygiene_rating?: number; // Food hygiene rating (0-5)
  last_inspection_date?: string; // Date of last food hygiene inspection
  allergen_info?: string | string[]; // Allergen information or list of allergens present in the food
  isNew?: boolean;
  offer?: string | null;
  address?: string; // Full address from database
  location: string; // General location (e.g., "St Helier")
  coordinates?: [number, number]; // [longitude, latitude] coordinates
  delivery_radius?: number; // Delivery radius in kilometers
  phone: string;
  email?: string;
  description: string;
  // Temporary closure status
  is_temporarily_closed?: boolean; // Whether the business is temporarily closed to new orders
  closure_message?: string; // Message displayed to customers when business is closed
  // Custom categories for search functionality
  customCategories?: string[]; // Business-specific custom categories for enhanced search
}

// Restaurant-specific types
export interface MenuItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
  isPopular?: boolean;
  customizations?: MenuItemCustomization[];
  attributes?: {
    dietary?: string[];
    allergen?: string[];
    spice_level?: string[];
    prep_time_minutes?: string[];
    popularity?: string[];
    [key: string]: string[] | undefined;
  };
}

export interface MenuItemCustomization {
  id: string;
  name: string;
  required: boolean;
  isMultiple: boolean;
  options: MenuItemOption[];
}

export interface MenuItemOption {
  id: string;
  name: string;
  price: number;
}

export interface MenuCategory {
  id: string;
  name: string;
  items: MenuItem[];
}

export interface Restaurant extends Business {
  cuisines: string[];
  menuCategories: MenuCategory[];
  // Additional fields for business cards
  image?: string; // Logo or banner image for business cards
  deliveryTimeRange?: string; // Formatted time range (e.g., "25-35 min")
  preparationTime?: string; // Formatted preparation time (e.g., "15 min")
  deliveryFeeFormatted?: string; // Formatted delivery fee (e.g., "£2.50" or "Free delivery")
  businessType?: string; // Type of business (e.g., "restaurant", "cafe")
  deliveryAvailable?: boolean; // Whether delivery is available
  // Delivery fee calculation fields
  delivery_fee_model?: string; // Delivery fee model (fixed, distance, mixed)
  delivery_fee_per_km?: number; // Delivery fee per kilometer for distance-based models
}

// Shop-specific types
export interface ProductVariant {
  name: string;
  priceAdjustment: number;
  isDefault?: boolean;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
  isPopular?: boolean;
  inStock?: boolean;
  variants?: ProductVariant[];
  attributes?: {
    dietary?: string[];
    allergen?: string[];
    spice_level?: string[];
    prep_time_minutes?: string[];
    popularity?: string[];
    [key: string]: string[] | undefined;
  };
}

export interface ProductCategory {
  id: string;
  name: string;
  products: Product[];
}

export interface Shop extends Business {
  storeTypes: string[];
  productCategories: ProductCategory[];
}

// Cafe-specific types
export interface Cafe extends Business {
  cuisineTypes: string[];
  menuCategories: MenuCategory[];
}

// Pharmacy-specific types
export interface PharmacyProduct extends Product {
  requiresPrescription?: boolean;
}

export interface PharmacyCategory {
  id: string;
  name: string;
  products: PharmacyProduct[];
}

export interface Pharmacy extends Business {
  pharmacyTypes: string[];
  pharmacyCategories: PharmacyCategory[];
}
