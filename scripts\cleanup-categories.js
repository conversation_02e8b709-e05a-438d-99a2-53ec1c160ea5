// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function cleanupCategories() {
  try {
    console.log('=== CLEANING UP DUPLICATE AND SIMILAR CATEGORIES ===\n');

    // Define categories to remove (keeping the better/newer ones)
    const categoriesToRemove = [
      // Restaurant duplicates - keep the newer/better named ones
      211, // Appetizers (keep Starters & Appetizers)
      44,  // Drinks (keep restaurant-beverages)
      47,  // British (keep British Cuisine)
      15,  // Burgers (keep Burgers & Sandwiches)
      22,  // Desserts (keep desserts-new)
      48,  // French (keep French Cuisine)
      46,  // Grill (keep Grilled & BBQ)
      20,  // Seafood (keep seafood-new)
      43,  // Sides (keep Sides & Extras)
      223, // Soups (keep Soups & Salads)
      212, // Starters (keep Starters & Appetizers)
      318, // Pasta (keep Pizza & Pasta)

      // Shop duplicates
      326, // Clothing (keep Clothing & Accessories)
      325, // Electronics (keep Electronics & Tech)
      200, // Home Goods (keep Home & Garden)

      // Pharmacy duplicates
      329, // Personal Care (keep personal-care-new in Shop)
      202, // Prescription (keep Prescription Medications)

      // Cafe duplicates
      205, // Breakfast (keep Breakfast Items)
      206, // Lunch (keep Light Lunch)
      24,  // Pastries (keep Pastries & Baked Goods)

      // Errand duplicates
      197, // Delivery (keep specific delivery types)
      196, // Shopping (keep specific shopping types)
      198, // Tasks (keep specific task types)

      // Unknown business type categories (these should be reassigned or removed)
      34,  // Alcoholic Beverages
      28,  // Bakery
      41,  // Boneless Wings
      42,  // Classic Wings
      23,  // Coffee
      45,  // Combos
      27,  // Dairy & Eggs
      36,  // First Aid
      18,  // Italian
      33,  // Juices
      35,  // Medications
      19,  // Mexican
      29,  // Meat & Seafood
      40,  // Oral Care
      30,  // Pantry Staples
      25,  // Sandwiches
      38,  // Skincare
      31,  // Soft Drinks
      17,  // Sushi
      21,  // Vegetarian
      37,  // Vitamins & Supplements
      32,  // Water
      39,  // Hair Care
      26,  // Fresh Produce

      // Cuisine categories that are too specific or duplicated
      354, // American
      355, // BBQ
      356, // Chicken
      353, // Brazilian
      350, // Caribbean
      339, // Dumplings
      348, // Falafel
      341, // Fish and Chips
      342, // Fried Chicken
      343, // Greek
      347, // Kebab
      335, // Korean
      351, // Latin American
      346, // Lebanese
      345, // Middle Eastern
      340, // Noodles
      352, // Peruvian
      338, // Pho
      337, // Ramen
      359, // Salads
      349, // Shawarma
      357, // Steakhouse
      358, // Tapas
      334, // Thai
      344, // Turkish
      336, // Vietnamese
    ];

    console.log(`📋 Planning to remove ${categoriesToRemove.length} categories\n`);

    // First, check if any of these categories are being used by businesses
    console.log('🔍 Checking for business category subscriptions...');
    const { data: businessCategories, error: bcError } = await supabase
      .from('business_categories')
      .select('category_id, business_id')
      .in('category_id', categoriesToRemove);

    if (bcError) {
      console.error('Error checking business categories:', bcError);
      return;
    }

    if (businessCategories && businessCategories.length > 0) {
      console.log(`⚠️  Found ${businessCategories.length} business category subscriptions that need to be handled:`);
      
      // Group by category
      const subscriptionsByCategory = {};
      businessCategories.forEach(bc => {
        if (!subscriptionsByCategory[bc.category_id]) {
          subscriptionsByCategory[bc.category_id] = [];
        }
        subscriptionsByCategory[bc.category_id].push(bc.business_id);
      });

      Object.keys(subscriptionsByCategory).forEach(categoryId => {
        const businessIds = subscriptionsByCategory[categoryId];
        console.log(`   Category ${categoryId}: ${businessIds.length} businesses subscribed`);
      });

      console.log('\n🔄 Removing business category subscriptions first...');
      const { error: deleteError } = await supabase
        .from('business_categories')
        .delete()
        .in('category_id', categoriesToRemove);

      if (deleteError) {
        console.error('Error removing business category subscriptions:', deleteError);
        return;
      }

      console.log('✅ Business category subscriptions removed');
    } else {
      console.log('✅ No business category subscriptions found for categories to be removed');
    }

    // Now remove the categories in batches
    console.log('\n🗑️  Removing categories...');
    
    const batchSize = 20;
    for (let i = 0; i < categoriesToRemove.length; i += batchSize) {
      const batch = categoriesToRemove.slice(i, i + batchSize);
      
      const { error: deleteError } = await supabase
        .from('categories')
        .delete()
        .in('id', batch);

      if (deleteError) {
        console.error(`Error removing category batch ${i / batchSize + 1}:`, deleteError);
        continue;
      }

      console.log(`✅ Removed batch ${i / batchSize + 1} (${batch.length} categories)`);
    }

    // Get final count
    const { data: remainingCategories, error: countError } = await supabase
      .from('categories')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('Error counting remaining categories:', countError);
    } else {
      console.log(`\n📊 Categories remaining: ${remainingCategories.length}`);
      console.log(`📊 Categories removed: ${165 - remainingCategories.length}`);
    }

    console.log('\n✨ Category cleanup completed!');

  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

cleanupCategories()
  .then(() => {
    console.log('\n🎉 Cleanup process finished!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  });
