import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    console.log("Simple business registration API called");

    // Parse the request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (parseError: any) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: `Invalid request format: ${parseError.message}` },
        { status: 400 }
      );
    }

    const {
      email,
      name,
      phone,
      businessName,
      businessDescription,
      businessAddress,
      businessPostcode,
      businessPhone,
      businessTypeId,
      deliveryFee,
      minimumOrderAmount,
      selectedCategories
    } = requestBody;

    // Validate required fields
    if (!email || !name || !businessName) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Create a Supabase client
    console.log("Creating Supabase client");
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase URL or service role key");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
    
    console.log("Supabase client created successfully");

    // 1. Create or get user
    let userId;
    
    // Check if user exists
    const { data: existingUser, error: getUserError } = await supabase
      .from("users")
      .select("id")
      .eq("email", email)
      .maybeSingle();
      
    if (!getUserError && existingUser) {
      console.log("User already exists:", existingUser.id);
      userId = existingUser.id;
    } else {
      // Create new user with a placeholder password_hash
      console.log("Creating new user with placeholder password_hash");
      
      const { data: userData, error: userError } = await supabase
        .from("users")
        .insert({
          name,
          email,
          phone: phone || '',
          role: "customer", // Default to customer role
          password_hash: "auth_managed", // Add placeholder for NOT NULL constraint
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
        
      if (userError) {
        console.error("User creation error:", userError);
        return NextResponse.json(
          { error: `Failed to create user: ${userError.message}` },
          { status: 500 }
        );
      }
      
      userId = userData[0].id;
      console.log("Created user with ID:", userId);
    }

    // 2. Create business registration
    console.log("Creating business registration");
    
    const { data: registrationData, error: registrationError } = await supabase
      .from('business_registrations')
      .insert({
        user_id: userId,
        business_type_id: businessTypeId || 1, // Default to 1 if not provided
        name: businessName,
        description: businessDescription || '',
        address: businessAddress || '',
        postcode: businessPostcode || '',
        phone: businessPhone || '',
        delivery_fee: deliveryFee || 0,
        minimum_order_amount: minimumOrderAmount || 0,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();
      
    if (registrationError) {
      console.error("Business registration error:", registrationError);
      return NextResponse.json(
        { error: `Failed to create business registration: ${registrationError.message}` },
        { status: 500 }
      );
    }
    
    const registrationId = registrationData[0].id;
    console.log("Created business registration with ID:", registrationId);

    // Return success response
    const responseData = {
      success: true,
      registrationId,
      message: "Business registration submitted successfully and is pending approval"
    };
    
    console.log("Business registration successful, returning:", responseData);
    
    return NextResponse.json(responseData);
  } catch (error: any) {
    console.error("Unexpected error in business registration:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
