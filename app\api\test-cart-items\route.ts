import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get recent cart items with delivery attributes
    const { data: cartItems, error } = await supabase
      .from('cart_items')
      .select(`
        id,
        name,
        quantity,
        price,
        weight_class_kg,
        thermal_requirement,
        size_category,
        business_id,
        cart_id,
        created_at
      `)
      .order('created_at', { ascending: false })
      .limit(20)

    if (error) {
      console.error('Error fetching cart items:', error)
      return NextResponse.json(
        { error: "Failed to fetch cart items" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      cartItems: cartItems || [],
      count: cartItems?.length || 0
    })

  } catch (error) {
    console.error('Error in test cart items API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
