'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { StandardizedAddress } from '@/lib/address-standardizer';
import { StandardizedAddressInput } from '@/components/delivery/standardized-address-input';

// Sample addresses for testing
const sampleAddresses = [
  {
    name: 'Complete Address',
    address: '123 Main St, St Helier, JE2 4UE',
  },
  {
    name: 'Missing Postcode',
    address: '45 Royal Square, St. Helier',
  },
  {
    name: 'Complex Address',
    address: 'Flat 2, 15 Beresford Street, St Helier, JE2 4WN',
  },
  {
    name: 'French Street Name',
    address: 'The Old Forge, La Rue de la Ville au Bas, Trinity, JE3 5HH',
  },
  {
    name: 'Abbreviated Parish',
    address: '78 La Route de St Aubin, St. Brelade, JE3 8BS',
  },
];

export default function AddressTestPage() {
  const [result, setResult] = useState<StandardizedAddress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('manual');

  const handleAddressChange = (standardizedAddress: StandardizedAddress) => {
    setResult(standardizedAddress);
    setError(null);
  };

  const handleValidationError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Address Standardization Test</h1>
      <p className="mb-6 text-gray-600">
        This page tests the PostGIS address_standardizer extension integration with our address handling system.
      </p>

      <Tabs defaultValue="manual" value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="manual">Manual Input</TabsTrigger>
          <TabsTrigger value="samples">Sample Addresses</TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Input Address</CardTitle>
                <CardDescription>
                  Enter an address to standardize. You can provide a full address or individual components.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <StandardizedAddressInput
                  onAddressChange={handleAddressChange}
                  onValidationError={handleValidationError}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Standardization Result</CardTitle>
                <CardDescription>
                  The standardized address components will appear here.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                  </div>
                )}

                {result && (
                  <div className="space-y-4">
                    <div className={`px-4 py-3 rounded mb-4 ${
                      result.is_valid
                        ? "bg-green-50 border border-green-200 text-green-700"
                        : "bg-yellow-50 border border-yellow-200 text-yellow-700"
                    }`}>
                      {result.is_valid
                        ? 'Address standardized successfully'
                        : 'Address standardized with warnings: ' + result.validation_message}
                    </div>

                    <div className="space-y-2">
                      <h3 className="font-semibold">Standardized Components</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium">Address Line 1:</div>
                        <div className="text-sm">{result.address_line1 || 'N/A'}</div>

                        <div className="text-sm font-medium">Address Line 2:</div>
                        <div className="text-sm">{result.address_line2 || 'N/A'}</div>

                        <div className="text-sm font-medium">Parish:</div>
                        <div className="text-sm">{result.parish || 'N/A'}</div>

                        <div className="text-sm font-medium">Postcode:</div>
                        <div className="text-sm">{result.postcode || 'N/A'}</div>
                      </div>
                    </div>

                    {result.coordinates && (
                      <>
                        <Separator />
                        <div className="space-y-2">
                          <h3 className="font-semibold">Geocoding Result</h3>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="text-sm font-medium">Latitude:</div>
                            <div className="text-sm">{result.latitude}</div>

                            <div className="text-sm font-medium">Longitude:</div>
                            <div className="text-sm">{result.longitude}</div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )}

                {!error && !result && (
                  <div className="text-center py-8 text-gray-500">
                    Enter an address and click "Standardize Address" to see the result.
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="samples" className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Sample Addresses</CardTitle>
              <CardDescription>
                Try standardizing these sample Jersey addresses to see how the system works.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sampleAddresses.map((sample, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader className="p-4 pb-2">
                      <CardTitle className="text-base">{sample.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <p className="text-sm text-gray-600 mb-3">{sample.address}</p>
                      <StandardizedAddressInput
                        initialAddress={sample.address}
                        onAddressChange={handleAddressChange}
                        onValidationError={handleValidationError}
                      />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Standardization Result</CardTitle>
              <CardDescription>
                The standardized address components will appear here.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                  {error}
                </div>
              )}

              {result && (
                <div className="space-y-4">
                  <div className={`px-4 py-3 rounded mb-4 ${
                    result.is_valid
                      ? "bg-green-50 border border-green-200 text-green-700"
                      : "bg-yellow-50 border border-yellow-200 text-yellow-700"
                  }`}>
                    {result.is_valid
                      ? 'Address standardized successfully'
                      : 'Address standardized with warnings: ' + result.validation_message}
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold">Standardized Components</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium">Address Line 1:</div>
                      <div className="text-sm">{result.address_line1 || 'N/A'}</div>

                      <div className="text-sm font-medium">Address Line 2:</div>
                      <div className="text-sm">{result.address_line2 || 'N/A'}</div>

                      <div className="text-sm font-medium">Parish:</div>
                      <div className="text-sm">{result.parish || 'N/A'}</div>

                      <div className="text-sm font-medium">Postcode:</div>
                      <div className="text-sm">{result.postcode || 'N/A'}</div>
                    </div>
                  </div>

                  {result.coordinates && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <h3 className="font-semibold">Geocoding Result</h3>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium">Latitude:</div>
                          <div className="text-sm">{result.latitude}</div>

                          <div className="text-sm font-medium">Longitude:</div>
                          <div className="text-sm">{result.longitude}</div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}

              {!error && !result && (
                <div className="text-center py-8 text-gray-500">
                  Select a sample address and click "Standardize Address" to see the result.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
