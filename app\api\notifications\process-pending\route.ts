import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// POST - Manually trigger processing of pending notifications
export async function POST(request: NextRequest) {
  try {
    // Verify user access (admin only)
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Check if user has admin permissions (you may want to add this check)
    // For now, any authenticated user can trigger this for testing

    console.log('Manually triggering pending notifications processing...')

    // Call the Supabase Edge Function
    const { data, error } = await supabase.functions.invoke('process-pending-notifications', {
      body: {}
    })

    if (error) {
      console.error('Error calling process-pending-notifications function:', error)
      return NextResponse.json(
        { error: 'Failed to process pending notifications', details: error },
        { status: 500 }
      )
    }

    console.log('Pending notifications processing result:', data)

    return NextResponse.json({
      message: 'Pending notifications processing triggered successfully',
      result: data
    })

  } catch (error: any) {
    console.error('Error triggering pending notifications processing:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get status of pending notifications
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Get count of pending notifications
    const { count: pendingCount, error: countError } = await supabase
      .from('order_status_history')
      .select('*', { count: 'exact', head: true })
      .eq('notification_sent', false)

    if (countError) {
      console.error('Error counting pending notifications:', countError)
      return NextResponse.json(
        { error: 'Failed to count pending notifications' },
        { status: 500 }
      )
    }

    // Get recent status history with notification details
    const { data: recentStatusHistory, error: statusError } = await supabase
      .from('order_status_history')
      .select(`
        id, order_id, status, created_at,
        notification_sent, notification_sent_at,
        customer_notified, business_notified, driver_notified,
        orders!inner(order_number, user_id, business_name)
      `)
      .order('created_at', { ascending: false })
      .limit(10)

    if (statusError) {
      console.error('Error fetching recent status history:', statusError)
    }

    // Get recent notification logs
    const { data: recentLogs, error: logsError } = await supabase
      .from('notification_log')
      .select('id, user_id, title, type, status, sent_at, error_message, order_id')
      .order('created_at', { ascending: false })
      .limit(10)

    if (logsError) {
      console.error('Error fetching recent notification logs:', logsError)
    }

    // Get notification statistics
    const { data: notificationStats, error: statsError } = await supabase
      .from('notification_log')
      .select('status')

    let stats = { sent: 0, failed: 0, total: 0 }
    if (!statsError && notificationStats) {
      stats = notificationStats.reduce((acc, log) => {
        acc.total++
        if (log.status === 'sent') acc.sent++
        if (log.status === 'failed') acc.failed++
        return acc
      }, { sent: 0, failed: 0, total: 0 })
    }

    return NextResponse.json({
      pendingNotifications: pendingCount || 0,
      recentStatusHistory: recentStatusHistory || [],
      recentLogs: recentLogs || [],
      notificationStats: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    console.error('Error getting notification status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
