"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Lock, Mail, LogIn, UserPlus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { signInWithEmailPassword } from "@/services/auth-service-direct"

export default function CheckoutLoginBlock() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleLogin = async () => {
    setError(null)
    setIsLoading(true)

    try {
      // Use direct API authentication service
      const { data, error } = await signInWithEmailPassword(email, password)

      if (error) {
        setError(error.message)
        setIsLoading(false)
        return
      }

      if (!data?.session) {
        setError("Authentication succeeded but no session was created. Please try again.")
        setIsLoading(false)
        return
      }

      // Store the token in both localStorage and cookie to ensure it's available everywhere
      if (data?.session?.access_token) {
        // Import and use the token storage utility
        const { storeAuthToken } = await import('@/utils/auth-token');
        storeAuthToken(data.session.access_token);

        // Set a flag to indicate active session
        localStorage.setItem('loop_jersey_session_active', 'true');

        // Store user data for immediate access
        if (data.session.user) {
          localStorage.setItem('loop_jersey_user', JSON.stringify(data.session.user));
        }
      }

      // Force a full page reload to ensure the auth state is properly updated
      window.location.href = window.location.pathname
    } catch (err) {
      console.error("Checkout login error:", err)
      setError("An unexpected error occurred. Please try again.")
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
      <h2 className="text-xl font-semibold mb-4 text-gray-800">Login Required</h2>
      <p className="text-gray-600 mb-6">Please login or create an account to complete your order</p>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              className="pl-10"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
        </div>
        <div className="grid gap-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password">Password</Label>
            <Link href="/reset-password" className="text-sm font-medium text-emerald-600 hover:text-emerald-700">
              Forgot password?
            </Link>
          </div>
          <div className="relative">
            <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              className="pl-10 pr-10"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>
        <Button
          onClick={handleLogin}
          className="w-full bg-emerald-600 hover:bg-emerald-700"
          disabled={isLoading}
        >
          <LogIn className="mr-2 h-4 w-4" />
          {isLoading ? "Signing in..." : "Sign In"}
        </Button>
      </div>

      <div className="mt-6 pt-6 border-t border-gray-200">
        <p className="text-sm text-gray-600 mb-4">Don&apos;t have an account yet?</p>
        <Link href={`/register?redirectTo=${encodeURIComponent('/checkout')}`}>
          <Button variant="outline" className="w-full">
            <UserPlus className="mr-2 h-4 w-4" />
            Create an Account
          </Button>
        </Link>
      </div>
    </div>
  )
}
