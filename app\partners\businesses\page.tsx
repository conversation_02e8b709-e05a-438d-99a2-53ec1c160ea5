import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function BusinessPartnersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Partner with Loop Jersey</h1>

        <Tabs defaultValue="restaurants">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="restaurants">Restaurants</TabsTrigger>
            <TabsTrigger value="shops">Shops</TabsTrigger>
          </TabsList>

          <TabsContent value="restaurants">
            <div className="grid gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Expand Your Reach</CardTitle>
                  <CardDescription>Connect with more customers across Jersey</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Partner with Loop Jersey to reach more customers and grow your restaurant business. Our platform connects you with hungry customers looking for quality food delivery options.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Seamless Integration</CardTitle>
                  <CardDescription>Easy-to-use tablet and software solutions</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>We provide all the tools you need to manage orders efficiently. Our tablet and software solutions integrate seamlessly with your existing systems, making it easy to manage delivery orders alongside your regular operations.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Join Our Network</CardTitle>
                  <CardDescription>Get started today</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Joining Loop Jersey is simple. Fill out the form below, and our team will contact you to discuss the next steps.</p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700">Register Your Restaurant</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="shops">
            <div className="grid gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Deliver More Than Food</CardTitle>
                  <CardDescription>Expand your retail business with delivery</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Jersey Eats isn't just for restaurants. We help shops and retail businesses deliver products directly to customers' doors, creating new revenue streams and expanding your customer base.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Flexible Solutions</CardTitle>
                  <CardDescription>Tailored to your retail business needs</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Whether you're a grocery store, pharmacy, or specialty shop, we have delivery solutions designed for your specific needs. Our platform is flexible and can be customized to accommodate various product types and delivery requirements.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Join Our Network</CardTitle>
                  <CardDescription>Get started today</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Ready to offer delivery to your customers? Fill out the form below, and our team will contact you to discuss the next steps.</p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700">Register Your Shop</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
