"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { AlertCircle, ArrowRight, Home } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { OrderDebug } from "@/components/debug/order-debug"

export default function OrderFailedPage() {
  const [orderId, setOrderId] = useState<string | null>(null)
  const [orderIds, setOrderIds] = useState<string[] | null>(null)
  const [isMultiBusiness, setIsMultiBusiness] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [isDevelopment, setIsDevelopment] = useState(false)

  useEffect(() => {
    // Check if we're in development mode
    setIsDevelopment(process.env.NODE_ENV === 'development')

    // Only run client-side code in the browser
    if (typeof window !== 'undefined') {
      // Get order ID, order IDs, and error from URL query parameters
      const urlParams = new URLSearchParams(window.location.search)
      const id = urlParams.get('orderId')
      const ids = urlParams.get('orderIds')
      const isMulti = urlParams.get('isMultiBusiness') === 'true'
      const error = urlParams.get('error')

      console.log('URL parameters:', {
        orderId: id,
        orderIds: ids,
        isMultiBusiness: isMulti,
        error
      })

    // Set order ID or order IDs based on what's available
    if (ids) {
      try {
        const parsedIds = JSON.parse(ids)
        if (Array.isArray(parsedIds) && parsedIds.length > 0) {
          setOrderIds(parsedIds)
          setIsMultiBusiness(true)
          console.log(`Found ${parsedIds.length} order IDs in URL:`, parsedIds)
        }
      } catch (parseError) {
        console.error('Error parsing order IDs:', parseError)
      }
    } else if (id) {
      setOrderId(id)
      console.log(`Found order ID in URL: ${id}`)
    }

    // Set multi-business flag if specified
    if (isMulti) {
      setIsMultiBusiness(true)
      console.log('Order is marked as multi-business')
    }

    // Set error message if available
    if (error) {
      setErrorMessage(decodeURIComponent(error))
    }

    // Do NOT clear the cart data when an order fails
    // This allows the user to try again with the same cart items

    // Only clear the temporary order details from session storage
    try {
      // Don't remove order details in development mode so we can debug
      if (typeof window !== 'undefined') {
        if (process.env.NODE_ENV !== 'development') {
          sessionStorage.removeItem('orderDetails')
          console.log('Order details cleared, but cart data preserved for retry')
        } else {
          console.log('Development mode: Order details preserved for debugging')
        }
      }
    } catch (error) {
      console.error('Error handling order details:', error)
    }
    }
  }, [])

  return (
    <div className="container max-w-5xl py-8 mx-auto text-center">
      <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center mb-5 mx-auto">
        <AlertCircle className="h-10 w-10 text-red-600" />
      </div>
      <h1 className="text-3xl font-bold text-gray-800">Order Processing Failed</h1>
      <p className="text-gray-600 mt-3 max-w-lg mx-auto">
        {isMultiBusiness && orderIds && orderIds.length > 0 ? (
          <>We were unable to process your multi-business order with {orderIds.length} businesses.</>
        ) : orderId ? (
          <>We were unable to process your order <span className="font-medium">{orderId}</span>.</>
        ) : (
          <>We were unable to process your order. Please try again or contact customer support.</>
        )}
      </p>

      <Alert variant="destructive" className="mt-6 max-w-lg mx-auto">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          There was a problem with your order. Your payment has not been processed, and no order has been created.
          {errorMessage && isDevelopment && (
            <div className="mt-2 text-sm font-mono bg-red-50 p-2 rounded text-left">
              Error: {errorMessage}
            </div>
          )}
        </AlertDescription>
      </Alert>

      {isDevelopment && (
        <div className="mt-6 max-w-lg mx-auto bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-2">Developer Information</h3>
          <p className="text-sm text-gray-600 mb-4">
            This information is only visible in development mode and can help diagnose order creation issues.
          </p>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Error Message:</h4>
              <div className="bg-white p-3 rounded border border-gray-300 text-sm font-mono overflow-auto max-h-32">
                {errorMessage || "No specific error message provided"}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-700 mb-1">Order Details:</h4>
              <p className="text-xs text-gray-500 mb-2">
                These are the order details that were being sent to the API when the error occurred.
              </p>
              <button
                className="text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-1 px-2 rounded border border-blue-200 transition-colors"
                onClick={() => {
                  try {
                    const orderDetails = sessionStorage.getItem('orderDetails');
                    if (orderDetails) {
                      const parsedDetails = JSON.parse(orderDetails);
                      console.log('Order details from session storage:', parsedDetails);

                      // Log specific parts of the order details for easier debugging
                      if (parsedDetails.businesses) {
                        console.log('Businesses in order:', parsedDetails.businesses);

                        // Check if businesses is an array or object
                        if (Array.isArray(parsedDetails.businesses)) {
                          console.log('Businesses is an array with', parsedDetails.businesses.length, 'items');
                          parsedDetails.businesses.forEach((business, index) => {
                            console.log(`Business ${index + 1}:`, {
                              id: business.business_id,
                              name: business.business_name,
                              type: business.business_type,
                              items: business.items?.length || 0
                            });
                          });
                        } else {
                          console.log('Businesses is an object with', Object.keys(parsedDetails.businesses).length, 'keys');
                          Object.entries(parsedDetails.businesses).forEach(([id, items]) => {
                            console.log(`Business ${id}:`, {
                              items: Array.isArray(items) ? items.length : 'not an array'
                            });
                          });
                        }
                      }

                      if (parsedDetails.items) {
                        console.log('Items in order:', parsedDetails.items.length);
                      }
                    } else {
                      console.log('No order details found in session storage');
                    }
                  } catch (e) {
                    console.error('Error logging order details:', e);
                  }
                }}
              >
                Log Order Details to Console
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 flex flex-wrap justify-center gap-4">
        <Link href="/">
          <Button variant="outline" className="border-gray-300">
            <Home className="mr-2 h-4 w-4 text-gray-600" />
            Return Home
          </Button>
        </Link>
        <Link href="/checkout">
          <Button className="bg-blue-600 hover:bg-blue-700">
            Try Again
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>

      {/* Debug information - only shown in development mode */}
      {isDevelopment && (
        <OrderDebug
          error={errorMessage || undefined}
          orderId={orderId || undefined}
          orderIds={orderIds || undefined}
          isMultiBusiness={isMultiBusiness}
        />
      )}
    </div>
  )
}
