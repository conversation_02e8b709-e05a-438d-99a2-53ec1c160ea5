import { NextRequest, NextResponse } from 'next/server'
import { adminClient } from '@/lib/supabase-admin'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Create a test notification
    const { data: notification, error } = await adminClient
      .from('admin_notifications')
      .insert({
        admin_user_id: userId,
        type: 'system',
        title: 'Test Notification',
        message: 'This is a test notification to verify the notification system is working correctly.',
        action_url: '/admin/settings',
        priority: 'medium'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating test notification:', error)
      return NextResponse.json({ error: 'Failed to create test notification' }, { status: 500 })
    }

    return NextResponse.json({ notification })
  } catch (error) {
    console.error('Error in test notification endpoint:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
