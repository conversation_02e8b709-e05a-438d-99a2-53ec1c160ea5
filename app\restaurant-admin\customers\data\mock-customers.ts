import type { Customer } from "@/types/customer"

export const customers: Customer[] = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+44 7911 123456",
    address: "15 Beachfront, St Helier, JE2 3NG",
    avatar: "/thoughtful-brunette.png",
    customerSince: "June 2023",
    totalOrders: 12,
    totalSpent: 245.80,
    lastOrder: "2 days ago",
    status: "loyal",
    favoriteItems: ["Jersey Crab Cakes", "Grilled Sea Bass"],
    notes: "Prefers extra sauce with crab cakes",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+44 7911 234567",
    address: "42 Roseville Street, St Helier, JE2 4PL",
    avatar: "/thoughtful-spectacled-man.png",
    customerSince: "July 2023",
    totalOrders: 10,
    totalSpent: 198.50,
    lastOrder: "5 days ago",
    status: "loyal",
    favoriteItems: ["Jersey Beef Burger", "Apple Crumble"],
    notes: "",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+44 7911 345678",
    address: "8 La Route de St Aubin, St Helier, JE2 3SF",
    avatar: "/sunlit-blonde.png",
    customerSince: "August 2023",
    totalOrders: 8,
    totalSpent: 176.40,
    lastOrder: "1 week ago",
    status: "regular",
    favoriteItems: ["Seared Jersey Scallops", "Jersey Lobster"],
    notes: "Allergic to nuts",
  },
  {
    id: 4,
    name: "James Taylor",
    email: "<EMAIL>",
    phone: "+44 7911 456789",
    address: "23 Colomberie, St Helier, JE2 4QA",
    avatar: "/thoughtful-bearded-man.png",
    customerSince: "September 2023",
    totalOrders: 7,
    totalSpent: 142.90,
    lastOrder: "3 days ago",
    status: "regular",
    favoriteItems: ["Jersey Cream Brûlée", "Classic Prawn Cocktail"],
    notes: "",
  },
  {
    id: 5,
    name: "Olivia Davis",
    email: "<EMAIL>",
    phone: "+44 7911 567890",
    address: "5 Le Mont Felard, St Lawrence, JE3 1JA",
    avatar: "/fiery-portrait.png",
    customerSince: "October 2023",
    totalOrders: 6,
    totalSpent: 128.70,
    lastOrder: "2 weeks ago",
    status: "regular",
    favoriteItems: ["Jersey Beef Burger", "French Onion Soup"],
    notes: "Prefers medium-rare burgers",
  },
  {
    id: 6,
    name: "Daniel Smith",
    email: "<EMAIL>",
    phone: "+44 7911 678901",
    address: "12 Gorey Village, Grouville, JE3 9EP",
    avatar: "/thoughtful-portrait.png",
    customerSince: "November 2023",
    totalOrders: 4,
    totalSpent: 89.60,
    lastOrder: "3 weeks ago",
    status: "occasional",
    favoriteItems: ["Grilled Sea Bass", "Apple Crumble"],
    notes: "",
  },
  {
    id: 7,
    name: "Sophia Martinez",
    email: "<EMAIL>",
    phone: "+44 7911 789012",
    address: "27 Havre des Pas, St Helier, JE2 4UL",
    avatar: "/placeholder.svg",
    customerSince: "December 2023",
    totalOrders: 3,
    totalSpent: 67.85,
    lastOrder: "1 month ago",
    status: "occasional",
    favoriteItems: ["Seafood Linguine", "Chocolate Fondant"],
    notes: "Prefers gluten-free options when available",
  },
  {
    id: 8,
    name: "William Johnson",
    email: "<EMAIL>",
    phone: "+44 7911 890123",
    address: "9 La Grande Route de St Jean, St John, JE3 4FL",
    avatar: "/placeholder.svg",
    customerSince: "January 2024",
    totalOrders: 2,
    totalSpent: 45.90,
    lastOrder: "6 weeks ago",
    status: "at-risk",
    favoriteItems: ["Fish & Chips", "Sticky Toffee Pudding"],
    notes: "",
  },
  {
    id: 9,
    name: "Charlotte Thompson",
    email: "<EMAIL>",
    phone: "+44 7911 901234",
    address: "14 La Rue de la Ville au Neveu, St Ouen, JE3 2HB",
    avatar: "/placeholder.svg",
    customerSince: "February 2024",
    totalOrders: 1,
    totalSpent: 22.95,
    lastOrder: "2 months ago",
    status: "at-risk",
    favoriteItems: ["Caesar Salad"],
    notes: "Vegetarian",
  },
  {
    id: 10,
    name: "Thomas Wilson",
    email: "<EMAIL>",
    phone: "+44 7911 012345",
    address: "31 La Rue des Pres Trading Estate, St Saviour, JE2 7QN",
    avatar: "/placeholder.svg",
    customerSince: "March 2024",
    totalOrders: 1,
    totalSpent: 18.90,
    lastOrder: "2 months ago",
    status: "at-risk",
    favoriteItems: ["Margherita Pizza"],
    notes: "",
  },
]

export function getCustomerById(id: number): Customer | undefined {
  return customers.find(customer => customer.id === id)
}
