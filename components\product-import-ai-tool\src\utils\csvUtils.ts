
import { ProductType } from '@/context/ProductUploadContext';

/**
 * Parse CSV string into a 2D array
 */
export const parseCSV = (csvContent: string): string[][] => {
  // Split content by lines
  const lines = csvContent
    .split('\n')
    .filter(line => line.trim().length > 0);
  
  if (lines.length === 0) {
    return [];
  }
  
  // Parse each line into an array of values
  return lines.map(line => {
    // Handle quoted values (that might contain commas)
    const regex = /(".*?"|[^",\s]+)(?=\s*,|\s*$)/g;
    const matches = line.match(regex) || [];
    
    // Remove quotes from values and trim whitespace
    return matches.map(value => 
      value.startsWith('"') && value.endsWith('"') 
        ? value.slice(1, -1).trim() 
        : value.trim()
    );
  });
};

/**
 * Extract headers from parsed CSV data
 */
export const extractHeaders = (csvData: string[][]): string[] => {
  if (csvData.length === 0) return [];
  return csvData[0];
};

/**
 * Transform CSV data to product objects based on column mapping
 */
export const transformToProducts = (
  csvData: string[][],
  headers: string[],
  columnMapping: {
    [key: string]: {
      sourceColumn: string;
      useAI: boolean;
    };
  }
): ProductType[] => {
  // Skip header row
  const dataRows = csvData.slice(1);
  
  return dataRows.map((row, index) => {
    const product: ProductType = {
      id: `product-${index}`,
      name: '',
      description: '',
      categories: [],
      attributes: {},
      price: '',
      images: [],
    };
    
    // Map fields based on column mapping
    Object.entries(columnMapping).forEach(([field, config]) => {
      if (config.sourceColumn && config.sourceColumn !== 'none') {
        const columnIndex = headers.indexOf(config.sourceColumn);
        if (columnIndex !== -1) {
          const value = row[columnIndex] || '';
          
          switch (field) {
            case 'productId':
              product.productId = value;
              break;
            case 'name':
              product.name = value;
              break;
            case 'description':
              product.description = value;
              break;
            case 'price':
              product.price = value;
              break;
            case 'categories':
              if (value) {
                product.categories = value.split(',').map(c => c.trim());
              }
              break;
            case 'attributes':
              if (value) {
                const attributes: Record<string, string> = {};
                value.split(',').forEach(pair => {
                  const [key, val] = pair.split(':').map(s => s.trim());
                  if (key && val) {
                    attributes[key] = val;
                  }
                });
                product.attributes = attributes;
              }
              break;
            case 'images':
              if (value && value.trim() !== '') {
                product.images = value.split(',').map(img => img.trim());
              }
              break;
          }
        }
      }
    });
    
    // If no name was found, use a fallback
    if (!product.name) {
      product.name = `Product ${index + 1}`;
    }
    
    return product;
  });
};

/**
 * Validate CSV content
 */
export const validateCSV = (csvData: string[][]): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check if CSV has data
  if (csvData.length === 0) {
    return {
      valid: false,
      errors: ['The CSV file appears to be empty.']
    };
  }
  
  // Check if CSV has headers
  if (csvData.length === 1) {
    errors.push('The CSV file only contains headers but no data.');
  }
  
  // Check for inconsistent row lengths
  const headerCount = csvData[0].length;
  const inconsistentRows = csvData.slice(1).filter(row => row.length !== headerCount);
  
  if (inconsistentRows.length > 0) {
    errors.push(`Found ${inconsistentRows.length} rows with inconsistent number of columns.`);
  }
  
  // Check for product name column
  const hasNameColumn = csvData[0].some(header => 
    header.toLowerCase().includes('name') || 
    header.toLowerCase().includes('product') || 
    header.toLowerCase().includes('title')
  );
  
  if (!hasNameColumn) {
    errors.push('Could not identify a product name column. Please include a column with "name", "product", or "title" in the header.');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};
