"use client"

import React, { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { format, addDays } from "date-fns"

interface BasicTimeSelectorProps {
  selectedTime: Date | null
  onSelectTime: (date: Date) => void
  className?: string
}

export function BasicTimeSelector({
  selectedTime,
  onSelectTime,
  className = ""
}: BasicTimeSelectorProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedHour, setSelectedHour] = useState<number>(12)
  const [selectedMinute, setSelectedMinute] = useState<number>(0)

  // Generate available dates (today + next 6 days)
  const availableDates = Array.from({ length: 7 }, (_, i) => addDays(new Date(), i))

  // Generate hours (9 AM to 10 PM)
  const hours = Array.from({ length: 14 }, (_, i) => i + 9)

  // Generate minutes (0 and 30)
  const minutes = [0, 30]

  // Sync with external selected time if provided - only run once on mount or when selectedTime changes significantly
  useEffect(() => {
    if (!selectedTime) return;

    // Skip if we already have a selected date and time that's close to the external time
    const currentDate = new Date(selectedDate);
    currentDate.setHours(selectedHour, selectedMinute, 0, 0);

    // If the current selection is within 1 minute of the external time, don't update
    if (Math.abs(currentDate.getTime() - selectedTime.getTime()) < 60000) {
      return;
    }

    // Find the closest date
    const closestDate = availableDates.reduce((prev, curr) => {
      return Math.abs(curr.getTime() - selectedTime.getTime()) <
             Math.abs(prev.getTime() - selectedTime.getTime()) ? curr : prev;
    });

    // Update the state in a single batch to prevent multiple re-renders
    setSelectedDate(closestDate);
    setSelectedHour(selectedTime.getHours());
    setSelectedMinute(selectedTime.getMinutes() >= 30 ? 30 : 0);
  }, [selectedTime ? selectedTime.toISOString() : null]);

  // When user manually selects a date, hour, or minute, notify parent component
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    updateParent(date, selectedHour, selectedMinute);
  };

  const handleHourSelect = (hour: number) => {
    setSelectedHour(hour);
    updateParent(selectedDate, hour, selectedMinute);
  };

  const handleMinuteSelect = (minute: number) => {
    setSelectedMinute(minute);
    updateParent(selectedDate, selectedHour, minute);
  };

  // Helper function to update parent with new date
  const updateParent = (date: Date, hour: number, minute: number) => {
    const newDate = new Date(date);
    newDate.setHours(hour, minute, 0, 0);
    onSelectTime(newDate);
  };

  return (
    <div className={className}>
      <div className="mb-4">
        <Label className="mb-2 block">Select Date</Label>
        <div className="grid grid-cols-4 gap-2">
          {availableDates.map((date, index) => {
            const isSelected = selectedDate &&
              date.getDate() === selectedDate.getDate() &&
              date.getMonth() === selectedDate.getMonth() &&
              date.getFullYear() === selectedDate.getFullYear()

            return (
              <Button
                key={index}
                type="button"
                variant={isSelected ? "default" : "outline"}
                className={`flex flex-col items-center justify-center h-20 p-1 rounded-md ${isSelected ? 'bg-emerald-600 text-white' : 'bg-white'}`}
                onClick={() => handleDateSelect(date)}
              >
                <span className="text-xs">{format(date, "EEE")}</span>
                <span className="text-xl font-semibold">{format(date, "d")}</span>
                <span className="text-xs">{format(date, "MMM")}</span>
              </Button>
            )
          })}
        </div>
      </div>

      <div className="mb-4">
        <Label className="mb-2 block">Select Hour</Label>
        <div className="grid grid-cols-7 gap-2">
          {hours.map((hour) => (
            <Button
              key={hour}
              type="button"
              variant={selectedHour === hour ? "default" : "outline"}
              className={`${selectedHour === hour ? 'bg-emerald-600 text-white' : 'bg-white'}`}
              onClick={() => handleHourSelect(hour)}
            >
              {hour > 12 ? `${hour - 12} PM` : hour === 12 ? '12 PM' : `${hour} AM`}
            </Button>
          ))}
        </div>
      </div>

      <div className="mb-4">
        <Label className="mb-2 block">Select Minute</Label>
        <div className="grid grid-cols-2 gap-2">
          {minutes.map((minute) => (
            <Button
              key={minute}
              type="button"
              variant={selectedMinute === minute ? "default" : "outline"}
              className={`${selectedMinute === minute ? 'bg-emerald-600 text-white' : 'bg-white'}`}
              onClick={() => handleMinuteSelect(minute)}
            >
              {minute === 0 ? '00' : minute}
            </Button>
          ))}
        </div>
      </div>

      {selectedDate && selectedHour !== undefined && selectedMinute !== undefined && (
        <div className="p-4 bg-emerald-50 rounded-md border border-emerald-200">
          <div className="text-emerald-700 font-medium">
            Scheduled for {format(selectedDate, "EEEE, MMMM d")} at{' '}
            {selectedHour > 12 ? selectedHour - 12 : selectedHour}:{selectedMinute === 0 ? '00' : selectedMinute}{' '}
            {selectedHour >= 12 ? 'PM' : 'AM'}
          </div>
        </div>
      )}
    </div>
  )
}
