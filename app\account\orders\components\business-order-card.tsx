'use client'

import { useState } from 'react'
import { BusinessOrder, OrderSession } from '@/types/customer-orders'
import { useReorder } from '@/hooks/use-reorder'
import { printBusinessReceipt } from '@/utils/receipt-generator'
import { OrderTrackingModal } from './order-tracking-modal'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { highlightSearchTerm } from '@/utils/search-highlight'
import {
  Store,
  Clock,
  Eye,
  RotateCcw,
  Receipt,
  Truck,
  MapPin
} from 'lucide-react'

interface BusinessOrderCardProps {
  order: BusinessOrder
  session: OrderSession
  isLast?: boolean
  searchQuery?: string
}

export function BusinessOrderCard({ order, session, isLast, searchQuery = '' }: BusinessOrderCardProps) {
  const { reorderBusiness, isReordering } = useReorder()
  const [showTracking, setShowTracking] = useState(false)
  // Format status for display
  const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': 'Pending',
      'confirmed': 'Confirmed',
      'preparing': 'Preparing',
      'ready': 'Ready for Pickup',
      'out_for_delivery': 'On the way',
      'delivered': 'Delivered',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    }
    return statusMap[status] || status
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'out_for_delivery':
      case 'ready':
        return 'bg-blue-100 text-blue-800'
      case 'preparing':
      case 'confirmed':
        return 'bg-yellow-100 text-yellow-800'
      case 'pending':
        return 'bg-orange-100 text-orange-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get delivery method display
  const getDeliveryMethodDisplay = (method: string) => {
    switch (method?.toLowerCase()) {
      case 'delivery':
        return { icon: <Truck className="h-3 w-3" />, text: 'Delivery', color: 'bg-blue-100 text-blue-800' }
      case 'pickup':
        return { icon: <MapPin className="h-3 w-3" />, text: 'Pickup', color: 'bg-purple-100 text-purple-800' }
      default:
        return { icon: <MapPin className="h-3 w-3" />, text: method || 'Unknown', color: 'bg-gray-100 text-gray-800' }
    }
  }

  // Format delivery time
  const formatDeliveryTime = (minutes: number | null, deliveryType: string) => {
    if (!minutes) return deliveryType === 'asap' ? 'ASAP' : 'Scheduled'

    if (deliveryType === 'scheduled') {
      return 'Scheduled'
    }

    return `${minutes} minutes`
  }

  const deliveryMethod = getDeliveryMethodDisplay(order.delivery_method)
  const formattedStatus = formatStatus(order.status)
  const deliveryTime = formatDeliveryTime(order.estimated_delivery_time, order.delivery_type)

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
      {/* Business Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Store className="h-4 w-4 text-gray-600" />
          <div>
            <div className="font-medium text-gray-900 text-sm">
              {searchQuery ? highlightSearchTerm(order.business_name, searchQuery) : order.business_name}
            </div>
            <div className="text-xs text-gray-500">
              #{searchQuery ? highlightSearchTerm(order.order_number, searchQuery) : order.order_number}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge
            variant="outline"
            className={`text-xs px-2 py-0.5 ${getStatusColor(order.status)}`}
          >
            {formattedStatus}
          </Badge>
          <div className="text-sm font-medium text-gray-900">
            £{order.total.toFixed(2)}
          </div>
        </div>
      </div>

      {/* Order Items */}
      {order.cart_items && order.cart_items.length > 0 && (
        <div className="mb-3">
          <div className="text-xs font-medium text-gray-700 mb-2">Items Ordered:</div>
          <div className="space-y-1">
            {order.cart_items.map((item, index) => (
              <div key={index} className="flex justify-between text-xs text-gray-600">
                <span>
                  {item.quantity}x {searchQuery ? highlightSearchTerm(item.name, searchQuery) : item.name}
                </span>
                <span>£{(item.price * item.quantity).toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Order Details - Simplified */}
      <div className="text-xs text-gray-600 space-y-1">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>£{order.subtotal.toFixed(2)}</span>
        </div>
        {order.delivery_fee > 0 && (
          <div className="flex justify-between">
            <span>Delivery:</span>
            <span>£{order.delivery_fee.toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Service:</span>
          <span>£{order.service_fee.toFixed(2)}</span>
        </div>

        {/* Delivery Time */}
        {order.estimated_delivery_time && (
          <div className="flex items-center gap-1 pt-1">
            <Clock className="h-3 w-3" />
            <span>{deliveryMethod.text}: {deliveryTime}</span>
          </div>
        )}
      </div>

      {/* Action Buttons - Simplified */}
      <div className="flex gap-2 mt-3 pt-2 border-t border-gray-200">
        <Button
          variant="outline"
          size="sm"
          onClick={() => reorderBusiness(order.order_id, order.business_name)}
          disabled={isReordering}
          className="text-xs flex-1"
        >
          <RotateCcw className={`h-3 w-3 mr-1 ${isReordering ? 'animate-spin' : ''}`} />
          {isReordering ? 'Adding...' : 'Reorder'}
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => printBusinessReceipt(order, session)}
          className="text-xs"
        >
          <Receipt className="h-3 w-3 mr-1" />
          Receipt
        </Button>

        {(order.status === 'confirmed' || order.status === 'preparing' || order.status === 'out_for_delivery' || order.status === 'ready') && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTracking(true)}
            className="text-xs"
          >
            <Truck className="h-3 w-3 mr-1" />
            Track
          </Button>
        )}
      </div>

      {/* Order Tracking Modal */}
      <OrderTrackingModal
        order={order}
        isOpen={showTracking}
        onClose={() => setShowTracking(false)}
      />
    </div>
  )
}
