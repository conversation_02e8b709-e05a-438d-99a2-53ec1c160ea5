"use client";

import React from "react";
import { User, MapPin, Clock, CreditCard, CheckCircle } from "lucide-react";
import { useCheckout } from "../checkout-context";

export const CheckoutSteps: React.FC = () => {
  const { currentStep, stepsCompleted, goToStep } = useCheckout();

  const steps = [
    {
      id: 1,
      name: "Customer Info",
      shortName: "Info",
      icon: User,
      completed: stepsCompleted.customerInfo
    },
    {
      id: 2,
      name: "Delivery Address",
      shortName: "Address",
      icon: MapPin,
      completed: stepsCompleted.deliveryAddress
    },
    {
      id: 3,
      name: "Delivery Time",
      shortName: "Time",
      icon: Clock,
      completed: stepsCompleted.deliveryTime
    },
    {
      id: 4,
      name: "Payment",
      shortName: "Payment",
      icon: CreditCard,
      completed: stepsCompleted.paymentMethod
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      {/* Desktop Layout */}
      <div className="hidden md:flex justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div
              className={`flex items-center ${
                currentStep === step.id ? 'text-emerald-600 font-medium' :
                step.completed ? 'text-gray-600' : 'text-gray-400'
              } cursor-pointer`}
              onClick={() => {
                // Only allow navigation to completed steps or the current step
                if (step.completed || step.id <= currentStep) {
                  goToStep(step.id);
                }
              }}
            >
              <div className={`
                flex items-center justify-center h-8 w-8 rounded-full mr-2
                ${currentStep === step.id ? 'bg-emerald-100 text-emerald-600' :
                  step.completed ? 'bg-gray-100 text-emerald-600' : 'bg-gray-100 text-gray-400'}
              `}>
                {step.completed ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <step.icon className="h-5 w-5" />
                )}
              </div>
              <span>{step.name}</span>
            </div>

            {/* Connector line between steps */}
            {index < steps.length - 1 && (
              <div className="h-px w-12 bg-gray-200 self-center mx-2"></div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <div className="flex justify-between items-center">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div
                className={`flex flex-col items-center ${
                  currentStep === step.id ? 'text-emerald-600 font-medium' :
                  step.completed ? 'text-gray-600' : 'text-gray-400'
                } cursor-pointer`}
                onClick={() => {
                  // Only allow navigation to completed steps or the current step
                  if (step.completed || step.id <= currentStep) {
                    goToStep(step.id);
                  }
                }}
              >
                <div className={`
                  flex items-center justify-center h-10 w-10 rounded-full mb-1
                  ${currentStep === step.id ? 'bg-emerald-100 text-emerald-600 border-2 border-emerald-600' :
                    step.completed ? 'bg-emerald-100 text-emerald-600' : 'bg-gray-100 text-gray-400'}
                `}>
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <span className="text-xs text-center font-medium">{step.shortName}</span>
              </div>

              {/* Connector line between steps on mobile */}
              {index < steps.length - 1 && (
                <div className="flex-1 h-px bg-gray-200 mx-2 mt-[-12px]"></div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};
