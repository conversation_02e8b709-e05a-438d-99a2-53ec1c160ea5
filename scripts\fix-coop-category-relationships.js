/**
 * <PERSON><PERSON>t to fix Jersey Co-op category parent-child relationships
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixCoopCategoryRelationships() {
  try {
    console.log('🔧 Fixing Jersey Co-op category relationships...\n');

    // Find Jersey Co-op
    const { data: coop, error: coopError } = await supabase
      .from('businesses')
      .select('id, name, slug')
      .eq('slug', 'jersey-coop')
      .single();

    if (coopError || !coop) {
      console.error('❌ Error finding Jersey Co-op:', coopError);
      return;
    }

    // Get all categories
    const { data: categories, error: categoriesError } = await supabase
      .from('business_custom_categories')
      .select('id, name, level, parent_category')
      .eq('business_id', coop.id)
      .order('level', { ascending: true });

    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
      return;
    }

    const level0Categories = categories.filter(cat => cat.level === 0);
    const level1Categories = categories.filter(cat => cat.level === 1);

    console.log(`📂 Found ${level0Categories.length} Level 0 categories and ${level1Categories.length} Level 1 categories\n`);

    // Define the relationships based on logical grouping
    const relationships = {
      'Fresh Produce': ['Fruits', 'Vegetables', 'Herbs & Spices'],
      'Dairy & Eggs': ['Milk & Cream', 'Cheese', 'Yogurt & Desserts', 'Eggs'],
      'Meat & Seafood': ['Fresh Meat', 'Poultry', 'Fresh Fish'],
      'Bakery': ['Fresh Bread', 'Pastries & Cakes'],
      'Frozen Foods': ['Frozen Vegetables', 'Frozen Meals', 'Ice Cream'],
      'Pantry Essentials': ['Canned Goods', 'Pasta & Rice', 'Sauces & Condiments', 'Breakfast Cereals'],
      'Beverages': ['Soft Drinks', 'Juices', 'Tea & Coffee', 'Water'],
      'Health & Beauty': ['Personal Care', 'Vitamins & Supplements']
    };

    console.log('🔄 Updating category relationships...\n');

    for (const [parentName, childNames] of Object.entries(relationships)) {
      const parentCategory = level0Categories.find(cat => cat.name === parentName);
      
      if (!parentCategory) {
        console.log(`⚠️  Parent category '${parentName}' not found`);
        continue;
      }

      console.log(`📁 ${parentName} (ID: ${parentCategory.id}):`);

      for (const childName of childNames) {
        const childCategory = level1Categories.find(cat => cat.name === childName);
        
        if (!childCategory) {
          console.log(`   ⚠️  Child category '${childName}' not found`);
          continue;
        }

        // Update the parent_category relationship
        const { error: updateError } = await supabase
          .from('business_custom_categories')
          .update({ parent_category: parentCategory.id })
          .eq('id', childCategory.id);

        if (updateError) {
          console.log(`   ❌ Error updating '${childName}':`, updateError);
        } else {
          console.log(`   ✅ ${childName} -> ${parentName}`);
        }
      }
      console.log('');
    }

    // Verify the relationships
    console.log('🔍 Verifying updated relationships...\n');
    
    const { data: updatedCategories, error: verifyError } = await supabase
      .from('business_custom_categories')
      .select('id, name, level, parent_category')
      .eq('business_id', coop.id)
      .order('level', { ascending: true })
      .order('display_order', { ascending: true });

    if (verifyError) {
      console.error('❌ Error verifying categories:', verifyError);
      return;
    }

    const updatedLevel0 = updatedCategories.filter(cat => cat.level === 0);
    const updatedLevel1 = updatedCategories.filter(cat => cat.level === 1);

    updatedLevel0.forEach(parent => {
      const children = updatedLevel1.filter(child => child.parent_category === parent.id);
      console.log(`📁 ${parent.name} (${children.length} subcategories):`);
      children.forEach(child => {
        console.log(`   - ${child.name}`);
      });
      console.log('');
    });

    console.log('🎉 Category relationships fixed successfully!');
    console.log(`\n🌐 Test the aisle layout at:`);
    console.log(`   Main page: http://localhost:3000/business/jersey-coop/categories`);
    console.log(`   Example category: http://localhost:3000/business/jersey-coop/categories/fresh-produce`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixCoopCategoryRelationships();
