-- Update Jersey Co-op Fresh Produce Images
-- This script adds high-quality product images to Fresh Fruits and Fresh Vegetables

DO $$
DECLARE
    jersey_coop_business_id INTEGER;
BEGIN
    -- Get Jersey Co-op business ID
    SELECT id INTO jersey_coop_business_id FROM businesses WHERE name = 'Jersey Co-op';
    
    IF jersey_coop_business_id IS NULL THEN
        RAISE EXCEPTION 'Jersey Co-op business not found';
    END IF;

    -- Update Fresh Fruits images
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'royal-gala-apples';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'bananas';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1498557850523-fd3d118b962e?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'strawberries';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1544511916-0148ccdeb877?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'blueberries';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1547514701-42782101795e?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'oranges';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1587486913049-53fc88980cfc?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'lemons';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'avocados';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1515348922-d8de1b8b5b3e?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'red-grapes';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'pineapple';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'mango';

    -- Update Fresh Vegetables images
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'carrots';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'potatoes';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'onions';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'tomatoes';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'broccoli';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1568584711271-946d4d46b7d5?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'cauliflower';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'bell-peppers';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1604977042946-1eecc30f269e?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'cucumber';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'courgettes';
    
    UPDATE products SET image_url = 'https://images.unsplash.com/photo-1629276301820-0f3eedc29fd0?w=800&h=600&fit=crop&crop=center', updated_at = NOW()
    WHERE business_id = jersey_coop_business_id AND slug = 'sweet-potatoes';

    RAISE NOTICE 'Successfully updated images for Jersey Co-op Fresh Produce items';
END $$;
