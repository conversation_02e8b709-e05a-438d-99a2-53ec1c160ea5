"use client"

import { useState, use<PERSON>ffect, use<PERSON>emo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Database,
  RefreshCw,
  AlertTriangle,
  Search,
  SortAsc,
  SortDesc,
  Filter,
  X
} from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"

// Types for business statistics
interface BusinessStats {
  business_name: string
  business_type: string
  approval_status: string
  product_count: number
  allocated_categories_count: number
  used_categories_count: number
  null_counts: {
    businesses: number
    products: number
    users: {
      customer: number
      business_staff: number
      business_manager: number
      admin: number
      super_admin: number
    }
    orders: number
  }
  row_counts: {
    businesses: number
    products: number
    users: {
      customer: number
      business_staff: number
      business_manager: number
      admin: number
      super_admin: number
    }
    orders: number
  }
}



// Sort direction type
type SortDirection = "asc" | "desc";

// Sort field type
type SortField = "business_name" | "business_type" | "approval_status" | "product_count" | "allocated_categories_count" | "used_categories_count" | "null_counts.businesses" | "null_counts.products" | "null_counts.orders";

export default function DataAuditPage() {
  const [businessStats, setBusinessStats] = useState<BusinessStats[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  // Sorting state
  const [sortField, setSortField] = useState<SortField>("business_name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")

  // Filtering state
  const [searchTerm, setSearchTerm] = useState("")
  const [businessTypeFilter, setBusinessTypeFilter] = useState<string>("all")
  const [approvalStatusFilter, setApprovalStatusFilter] = useState<string>("all")

  // Fetch data from API
  const fetchData = async () => {
    // Only show loading state on initial load, not during refreshes
    if (!lastUpdated) {
      setIsLoading(true)
    }
    setError(null)

    try {
      // Fetch business statistics
      const businessResponse = await fetch("/api/admin/data-audit/business-stats");

      // Handle business statistics response
      if (!businessResponse.ok) {
        console.error(`Business statistics error: ${businessResponse.status} ${businessResponse.statusText}`);
        const errorText = await businessResponse.text().catch(() => "Could not read error response");
        console.error("Business statistics error details:", errorText);
        throw new Error(`Failed to fetch business statistics: ${businessResponse.statusText}`);
      }
      const businessData = await businessResponse.json();

      // Ensure we have an array, even if empty
      if (Array.isArray(businessData)) {
        setBusinessStats(businessData);
      } else {
        console.error("Business data is not an array:", businessData);
        setBusinessStats([]);
      }

      setLastUpdated(new Date())
    } catch (err) {
      console.error("Error fetching data audit information:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Initial data fetch
  useEffect(() => {
    fetchData()
  }, [])

  // Get unique business types for filter dropdown
  const businessTypes = useMemo(() => {
    const types = new Set<string>()
    businessStats.forEach(business => {
      if (business.business_type) {
        types.add(business.business_type)
      }
    })
    return Array.from(types).sort()
  }, [businessStats])

  // Get unique approval statuses for filter dropdown
  const approvalStatuses = useMemo(() => {
    const statuses = new Set<string>()
    businessStats.forEach(business => {
      if (business.approval_status) {
        statuses.add(business.approval_status)
      }
    })
    return Array.from(statuses).sort()
  }, [businessStats])

  // Apply sorting and filtering
  const filteredAndSortedBusinessStats = useMemo(() => {
    // First, filter the data
    let filtered = businessStats.filter(business => {
      // Apply search term filter
      const matchesSearch = searchTerm === "" ||
        business.business_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.business_type.toLowerCase().includes(searchTerm.toLowerCase())

      // Apply business type filter
      const matchesType = businessTypeFilter === "all" ||
        business.business_type === businessTypeFilter

      // Apply approval status filter
      const matchesStatus = approvalStatusFilter === "all" ||
        business.approval_status === approvalStatusFilter

      return matchesSearch && matchesType && matchesStatus
    })

    // Then, sort the filtered data
    return filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      // Handle nested properties like null_counts.businesses
      if (sortField.includes('.')) {
        const [parent, child] = sortField.split('.')
        aValue = a[parent][child]
        bValue = b[parent][child]
      } else {
        aValue = a[sortField]
        bValue = b[sortField]
      }

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      // Handle number comparison
      return sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue
    })
  }, [businessStats, searchTerm, businessTypeFilter, approvalStatusFilter, sortField, sortDirection])

  // Toggle sort direction or change sort field
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field and default to ascending
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("")
    setBusinessTypeFilter("all")
    setApprovalStatusFilter("all")
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Data Audit</h1>
          <p className="text-gray-500">
            Comprehensive database statistics and data quality metrics
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button
            onClick={fetchData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            Refresh Data
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-4 md:items-center">
        {/* Search input */}
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="text"
            placeholder="Search businesses..."
            className="pl-9 pr-4"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
              onClick={() => setSearchTerm("")}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Business Type filter */}
        <div className="w-full md:w-48">
          <Select
            value={businessTypeFilter}
            onValueChange={setBusinessTypeFilter}
          >
            <SelectTrigger>
              <SelectValue placeholder="Business Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {businessTypes.map(type => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Approval Status filter */}
        <div className="w-full md:w-48">
          <Select
            value={approvalStatusFilter}
            onValueChange={setApprovalStatusFilter}
          >
            <SelectTrigger>
              <SelectValue placeholder="Approval Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {approvalStatuses.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Clear filters button */}
        <Button
          variant="outline"
          size="sm"
          onClick={clearFilters}
          disabled={searchTerm === "" && businessTypeFilter === "all" && approvalStatusFilter === "all"}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          Clear Filters
        </Button>
      </div>

      {/* Results count and last updated */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          {!isLoading && (
            <p className="text-sm text-gray-500">
              Found {filteredAndSortedBusinessStats.length} {filteredAndSortedBusinessStats.length === 1 ? 'business' : 'businesses'}
              {(searchTerm || businessTypeFilter !== "all" || approvalStatusFilter !== "all") &&
                ` (filtered from ${businessStats.length})`
              }
            </p>
          )}
        </div>
        {lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleString()}
          </p>
        )}
      </div>

      {/* Business Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Business Data Audit
          </CardTitle>
          <CardDescription>
            Data quality metrics for each business in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto" style={{ maxHeight: '70vh' }}>
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10">
                <TableRow>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("business_name")}>
                    <div className="flex items-center space-x-1">
                      <span>Business Name</span>
                      {sortField === "business_name" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("business_type")}>
                    <div className="flex items-center space-x-1">
                      <span>Business Type</span>
                      {sortField === "business_type" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("approval_status")}>
                    <div className="flex items-center space-x-1">
                      <span>Approval Status</span>
                      {sortField === "approval_status" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("product_count")}>
                    <div className="flex items-center space-x-1">
                      <span>Products</span>
                      {sortField === "product_count" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("allocated_categories_count")}>
                    <div className="flex items-center space-x-1">
                      <span>Allocated Categories</span>
                      {sortField === "allocated_categories_count" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("used_categories_count")}>
                    <div className="flex items-center space-x-1">
                      <span>Used Categories</span>
                      {sortField === "used_categories_count" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("null_counts.businesses")}>
                    <div className="flex items-center space-x-1">
                      <span>Null Cells (Businesses)</span>
                      {sortField === "null_counts.businesses" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("null_counts.products")}>
                    <div className="flex items-center space-x-1">
                      <span>Null Cells (Products)</span>
                      {sortField === "null_counts.products" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer" onClick={() => handleSort("null_counts.orders")}>
                    <div className="flex items-center space-x-1">
                      <span>Null Cells (Orders)</span>
                      {sortField === "null_counts.orders" && (
                        sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-2"></div>
                        <span className="text-gray-500">Loading business statistics...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredAndSortedBusinessStats.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <span className="text-gray-500">
                        {businessStats.length === 0
                          ? "No business data available"
                          : "No businesses match the current filters"}
                      </span>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAndSortedBusinessStats.map((business, index) => (
                    <TableRow key={index} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{business.business_name}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-gray-100">
                          {business.business_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={business.approval_status === "Approved" ? "success" : "secondary"}
                          className={business.approval_status === "Approved" ? "bg-green-100 text-green-800" : ""}
                        >
                          {business.approval_status}
                        </Badge>
                      </TableCell>
                      <TableCell>{business.product_count}</TableCell>
                      <TableCell>{business.allocated_categories_count}</TableCell>
                      <TableCell>{business.used_categories_count}</TableCell>
                      <TableCell>
                        {business.null_counts.businesses} / {business.row_counts.businesses}
                      </TableCell>
                      <TableCell>
                        {business.null_counts.products} / {business.row_counts.products}
                      </TableCell>
                      <TableCell>
                        {business.null_counts.orders} / {business.row_counts.orders}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>


    </div>
  )
}
