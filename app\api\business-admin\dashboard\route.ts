import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { verifyBusinessAdminAccess } from "@/lib/simple-auth"

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');

    console.log("Dashboard API called with businessId:", businessId);

    // Verify business admin access using simple auth
    const authResult = await verifyBusinessAdminAccess(request);

    if (!authResult.authorized) {
      return NextResponse.json(
        { error: authResult.error || "Authentication failed" },
        { status: authResult.status || 401 }
      );
    }

    const { user, profile: userProfile } = authResult;
    console.log("User has appropriate permissions:", userProfile?.role)

    // For admin users, check if a specific business ID was requested
    const isAdmin = userProfile?.role === 'admin';
    const isSuperAdmin = userProfile?.role === 'super_admin';

    if (isAdmin || isSuperAdmin) {
      // If a specific business ID was requested, fetch that business data
      if (businessId) {
        console.log(`Admin user requesting dashboard for business ID: ${businessId}`);

        // Check if the business exists
        const { data: business, error: businessError } = await adminClient
          .from("businesses")
          .select(`
            id,
            name,
            description,
            address,
            postcode,
            phone,
            delivery_radius,
            minimum_order_amount,
            delivery_fee,
            opening_hours,
            logo_url,
            business_type_id,
            business_types(name)
          `)
          .eq("id", businessId)
          .single();

        if (businessError) {
          console.error("Error fetching business data:", businessError);
          return NextResponse.json(
            { error: "Business data not found" },
            { status: 404 }
          );
        }

        // Get order stats for this business
        const { data: orders, error: ordersError } = await adminClient
          .from("orders")
          .select("id, status, total_amount, created_at")
          .eq("business_id", business.id)
          .order("created_at", { ascending: false });

        if (ordersError) {
          console.error("Error fetching orders:", ordersError);
          return NextResponse.json(
            { error: "Failed to fetch orders" },
            { status: 500 }
          );
        }

        // Calculate order stats
        const totalOrders = orders ? orders.length : 0;
        const pendingOrders = orders ? orders.filter(o => o.status === 'pending' || o.status === 'preparing').length : 0;
        const completedOrders = orders ? orders.filter(o => o.status === 'delivered').length : 0;
        const totalRevenue = orders ? orders.reduce((sum, order) => sum + (order.total_amount || 0), 0) : 0;

        // Get recent orders (last 5)
        const recentOrders = orders ? orders.slice(0, 5) : [];

        // Return the stats and business data
        return NextResponse.json({
          totalOrders,
          pendingOrders,
          completedOrders,
          totalRevenue,
          recentOrders,
          business: {
            id: business.id,
            name: business.name,
            description: business.description,
            address: business.address,
            postcode: business.postcode,
            phone: business.phone,
            delivery_radius: business.delivery_radius,
            minimum_order_amount: business.minimum_order_amount,
            delivery_fee: business.delivery_fee,
            opening_hours: business.opening_hours,
            logo_url: business.logo_url,
            business_type_id: business.business_type_id,
            business_type: business.business_types?.name || "Business"
          }
        });
      }

      // If no specific business ID was requested, return default data
      console.log("Admin user detected, returning default data (no business ID provided)");
      return NextResponse.json({
        totalOrders: 0,
        pendingOrders: 0,
        completedOrders: 0,
        totalRevenue: 0,
        recentOrders: []
      });
    }

    // Get the business managed by this user
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerError) {
      console.error("Error fetching business manager data:", managerError)
      return NextResponse.json(
        { error: "Business manager data not found" },
        { status: 404 }
      )
    }

    if (!managerData || !managerData.business_id) {
      console.error("No business ID found for this user")
      return NextResponse.json(
        { error: "No business found for this user" },
        { status: 404 }
      )
    }

    // We've already checked that managerData and business_id exist

    // Check if the business exists
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select(`
        id,
        name,
        description,
        address,
        postcode,
        phone,
        delivery_radius,
        minimum_order_amount,
        delivery_fee,
        opening_hours,
        logo_url,
        business_type_id,
        business_types(name)
      `)
      .eq("id", managerData.business_id)
      .single()

    if (businessError) {
      console.error("Error fetching business data:", businessError)
      return NextResponse.json(
        { error: "Business data not found" },
        { status: 404 }
      )
    }

    // Get order stats for this business
    const { data: orders, error: ordersError } = await adminClient
      .from("orders")
      .select("id, status, total_amount, created_at")
      .eq("business_id", business.id)
      .order("created_at", { ascending: false })

    if (ordersError) {
      console.error("Error fetching orders:", ordersError)
      return NextResponse.json(
        { error: "Failed to fetch orders" },
        { status: 500 }
      )
    }

    // Calculate order stats
    const totalOrders = orders ? orders.length : 0
    const pendingOrders = orders ? orders.filter(o => o.status === 'pending' || o.status === 'preparing').length : 0
    const completedOrders = orders ? orders.filter(o => o.status === 'delivered').length : 0
    const totalRevenue = orders ? orders.reduce((sum, order) => sum + (order.total_amount || 0), 0) : 0

    // Get recent orders (last 5)
    const recentOrders = orders ? orders.slice(0, 5) : []

    // Return the stats and business data
    return NextResponse.json({
      totalOrders,
      pendingOrders,
      completedOrders,
      totalRevenue,
      recentOrders,
      business: {
        id: business.id,
        name: business.name,
        description: business.description,
        address: business.address,
        postcode: business.postcode,
        phone: business.phone,
        delivery_radius: business.delivery_radius,
        minimum_order_amount: business.minimum_order_amount,
        delivery_fee: business.delivery_fee,
        opening_hours: business.opening_hours,
        logo_url: business.logo_url,
        business_type_id: business.business_type_id,
        business_type: business.business_types?.name || "Business"
      }
    })
  } catch (error: any) {
    console.error("Error in business admin dashboard stats:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
