import { NextRequest, NextResponse } from 'next/server'

// GET - Simple status check for notifications (no auth required for testing)
export async function GET(request: NextRequest) {
  try {
    // For testing purposes, return a simple status
    return NextResponse.json({
      hasActiveSubscription: false,
      message: 'Notification service is available'
    })
  } catch (error: any) {
    console.error('Error checking notification status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
