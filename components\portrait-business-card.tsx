'use client'

import { useState } from 'react'
import { Heart, Star, Clock, Truck, MapPin } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import FallbackImage from '@/components/fallback-image'

interface PortraitBusinessCardProps {
  id: string
  name: string
  image: string
  businessType: string
  rating?: number
  deliveryTime: string
  deliveryTimeRange?: string
  deliveryFee: string
  distance?: string
  sponsored?: boolean
  offers?: {
    text: string
    color: string
  }[]
  className?: string
  location?: string
  deliveryRadius?: string
  preparationTime?: string
  deliveryAvailable?: boolean
}

export default function PortraitBusinessCard(props: PortraitBusinessCardProps) {
  const {
    id,
    name,
    image,
    businessType,
    rating,
    deliveryTime,
    deliveryTimeRange,
    deliveryFee,
    sponsored = false,
    offers = [],
    className,
    location,
    preparationTime,
    deliveryAvailable = true
  } = props;

  const [isFavorite, setIsFavorite] = useState(false);

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  // Format preparation time to match the example (e.g., "25 min")
  const formattedPrepTime = preparationTime?.includes('mins')
    ? preparationTime.replace('mins', 'min')
    : (preparationTime?.includes('min') ? preparationTime : `${preparationTime || '15'} min`);

  // Use the passed delivery time range prop
  const formattedTimeRange = deliveryTimeRange || 'Calculating...';

  // Use the generic business page URL instead of type-specific routes
  const url = `/business/${id}`;

  return (
    <div className={cn("block h-full", className)}>
      <a href={url} className="block h-full">
        <div className="relative rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md border border-gray-100 transition-all duration-300 h-full flex flex-col group hover:border-emerald-200">
          {/* Image Container */}
          <div className="relative w-full">
            <div className="aspect-square w-full bg-white relative">
              <FallbackImage
                src={image}
                alt={name}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain p-2"
              />
            </div>

            {/* Favorite Button */}
            <button
              onClick={toggleFavorite}
              className="absolute top-2 right-2 z-10 w-7 h-7 flex items-center justify-center bg-white rounded-full shadow-md"
            >
              <Heart
                className={cn(
                  "h-4 w-4 transition-colors",
                  isFavorite ? "fill-red-500 text-red-500" : "text-gray-400"
                )}
              />
            </button>

            {/* Sponsored Tag */}
            {sponsored && (
              <div className="absolute bottom-2 right-2 z-10 text-xs font-medium text-emerald-600 bg-white bg-opacity-90 px-2 py-0.5 rounded-md shadow-sm">
                Sponsored
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-3 flex-grow flex flex-col relative z-10 bg-white">
            {/* Business Name */}
            <h3 className="font-bold text-gray-900 text-sm md:text-base tracking-tight whitespace-nowrap overflow-hidden text-ellipsis">
              {name}
            </h3>

            {/* Rating and Location */}
            <div className="flex items-center justify-between mb-3 mt-2">
              {rating !== undefined && (
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                  <span className="text-xs font-semibold text-gray-700">{rating}</span>
                </div>
              )}

              {location && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-rose-400 mr-1" />
                  <span className="text-xs text-gray-600">{location}</span>
                </div>
              )}
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200"></div>

            {/* Delivery and Pickup */}
            <div className="pt-3">
              <div className="space-y-2">
                {/* Pickup */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1 text-emerald-600" />
                    <span className="text-xs font-medium text-gray-700">Pickup</span>
                  </div>
                  <span className="text-xs font-medium text-emerald-600">
                    {formattedPrepTime}
                  </span>
                </div>

                {/* Delivery section - Always maintain consistent height */}
                <div className="min-h-[40px]">
                  {/* Delivery */}
                  {deliveryAvailable && (
                    <>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <Truck className="h-4 w-4 mr-1 text-emerald-600" />
                          <span className="text-xs font-medium text-gray-700">Delivery</span>
                        </div>
                        <span className="text-xs font-medium text-emerald-600">
                          {deliveryFee}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Estimate</span>
                        <span className="text-xs font-medium text-emerald-600">
                          {formattedTimeRange}
                        </span>
                      </div>
                    </>
                  )}

                  {/* Pickup Only Badge */}
                  {!deliveryAvailable && (
                    <div className="flex justify-center mt-1">
                      <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200 text-xs">
                        Pickup Only
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div>
  );
}
