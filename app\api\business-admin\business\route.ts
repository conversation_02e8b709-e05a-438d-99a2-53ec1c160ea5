import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// GET - Fetch business data
export async function GET(request: NextRequest) {
  try {
    // Get business ID from session/auth
    // For now, we'll use a placeholder - this should be replaced with proper auth
    const businessId = 1 // TODO: Get from authenticated user session

    const { data: business, error } = await adminClient
      .from('businesses')
      .select('id, name, slug, page_layout, description, logo_url, banner_url, address, location, delivery_radius, preparation_time_minutes, rating, review_count, is_featured, created_at, updated_at')
      .eq('id', businessId)
      .single()

    if (error) {
      console.error('Error fetching business:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business data' },
        { status: 500 }
      )
    }

    if (!business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      business
    })

  } catch (error) {
    console.error('Error in business fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
