"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, ResponsiveContainer, <PERSON>ltip, XAxis, YA<PERSON>s } from "recharts"

const data = [
  {
    name: "6am",
    orders: 4,
  },
  {
    name: "8am",
    orders: 8,
  },
  {
    name: "10am",
    orders: 15,
  },
  {
    name: "12pm",
    orders: 22,
  },
  {
    name: "2pm",
    orders: 16,
  },
  {
    name: "4pm",
    orders: 12,
  },
  {
    name: "6pm",
    orders: 24,
  },
  {
    name: "8pm",
    orders: 30,
  },
  {
    name: "10pm",
    orders: 18,
  },
]

export function OrdersChart() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Bar dataKey="orders" fill="#10b981" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}
