'use client';

import { useEffect } from 'react';

/**
 * GlobalErrorHandler component
 * 
 * This component sets up global error handlers to catch unhandled promise rejections
 * and other errors that might not be caught by React error boundaries.
 */
export default function GlobalErrorHandler() {
  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // Check if the rejection is an Event object (common source of the issue)
      if (event.reason && typeof event.reason === 'object' && event.reason.constructor.name === 'Event') {
        console.error('Unhandled promise rejection is an Event object:', {
          type: event.reason.type,
          target: event.reason.target,
          currentTarget: event.reason.currentTarget,
          timeStamp: event.reason.timeStamp
        });
      }
      
      // Prevent the default browser behavior (showing error in console)
      event.preventDefault();
      
      // Log additional context if available
      if (event.promise) {
        console.error('Promise that was rejected:', event.promise);
      }
    };

    // Handle general errors
    const handleError = (event: ErrorEvent) => {
      console.error('Global error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    };

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    // Cleanup function
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);

  // This component doesn't render anything
  return null;
}
