import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      featureName,
      featureCategory,
      description,
      customerName,
      customerEmail,
      notes
    } = body

    if (!featureName || !description || !customerName || !customerEmail) {
      return NextResponse.json(
        { error: 'Feature name, description, customer name, and email are required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get user session if available
    const { data: { session } } = await supabase.auth.getSession()

    // Check if user has already voted for this feature
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('user_votes')
      .select('id')
      .eq('user_email', customerEmail)
      .eq('request_type', 'feature')
      .eq('request_name', featureName)
      .single()

    if (voteCheckError && voteCheckError.code !== 'PGRST116') {
      console.error('Error checking existing vote:', voteCheckError)
      return NextResponse.json(
        { error: 'Failed to check existing votes' },
        { status: 500 }
      )
    }

    if (existingVote) {
      return NextResponse.json(
        { error: 'You have already voted for this feature' },
        { status: 400 }
      )
    }

    // Check if this feature already exists
    const { data: existingFeature, error: featureError } = await supabase
      .from('feature_requests')
      .select('id, vote_count')
      .eq('feature_name', featureName)
      .eq('status', 'pending')
      .single()

    if (featureError && featureError.code !== 'PGRST116') {
      console.error('Error checking existing feature:', featureError)
      return NextResponse.json(
        { error: 'Failed to check existing features' },
        { status: 500 }
      )
    }

    if (existingFeature) {
      // Feature exists, increment vote count
      const { data: updatedRequest, error: updateError } = await supabase
        .from('feature_requests')
        .update({
          vote_count: existingFeature.vote_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingFeature.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating feature request:', updateError)
        return NextResponse.json(
          { error: 'Failed to update feature request' },
          { status: 500 }
        )
      }

      // Record this user's vote
      await supabase
        .from('user_votes')
        .insert({
          user_id: session?.user?.id || null,
          user_email: customerEmail,
          request_type: 'feature',
          request_name: featureName
        })

      return NextResponse.json({
        message: 'Vote added successfully',
        request: updatedRequest
      })
    }

    // Create new feature request
    const { data: newRequest, error: insertError } = await supabase
      .from('feature_requests')
      .insert({
        feature_name: featureName,
        feature_category: featureCategory,
        description: description,
        customer_name: customerName,
        customer_email: customerEmail,
        user_id: session?.user?.id || null,
        notes: notes || null,
        vote_count: 1,
        status: 'pending'
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating feature request:', insertError)
      return NextResponse.json(
        { error: 'Failed to create feature request' },
        { status: 500 }
      )
    }

    // Record this user's vote for the new request
    await supabase
      .from('user_votes')
      .insert({
        user_id: session?.user?.id || null,
        user_email: customerEmail,
        request_type: 'feature',
        request_name: featureName
      })

    // Create admin notification
    try {
      await supabase
        .from('admin_notifications')
        .insert({
          type: 'feature_request',
          title: 'New Feature Request',
          message: `${customerName} requested: ${featureName}`,
          data: {
            feature_name: featureName,
            customer_name: customerName,
            customer_email: customerEmail,
            request_id: newRequest.id
          },
          created_at: new Date().toISOString()
        })
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      message: 'Feature request created successfully',
      request: newRequest
    })

  } catch (error) {
    console.error('Error in feature requests API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'vote_count'
    const order = searchParams.get('order') || 'desc'

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get feature requests sorted by vote count
    const { data: requests, error, count } = await supabase
      .from('feature_requests')
      .select('*', { count: 'exact' })
      .eq('status', 'pending')
      .order(sortBy, { ascending: order === 'asc' })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching feature requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch feature requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      requests: requests || [],
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in feature requests GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
