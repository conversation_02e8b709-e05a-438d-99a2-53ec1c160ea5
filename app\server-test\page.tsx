import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export default async function ServerTestPage() {
  // Fetch business types
  const { data: businessTypes, error: typesError } = await adminClient
    .from('business_types')
    .select('id, name, slug')
    .order('name')
  
  if (typesError) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Server-Side Supabase Test</h1>
        <p className="text-red-500">Error fetching business types: {typesError.message}</p>
      </div>
    )
  }
  
  // Get restaurant type ID
  const restaurantTypeId = businessTypes.find(t => t.slug === 'restaurant')?.id
  
  if (!restaurantTypeId) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Server-Side Supabase Test</h1>
        <p className="text-red-500">Restaurant type not found</p>
        <div className="mt-4">
          <h2 className="text-xl font-semibold mb-2">Business Types</h2>
          <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(businessTypes, null, 2)}
          </pre>
        </div>
      </div>
    )
  }
  
  // Fetch restaurants
  const { data: restaurants, error: restaurantsError } = await adminClient
    .from('businesses')
    .select('*')
    .eq('business_type_id', restaurantTypeId)
    .limit(10)
  
  if (restaurantsError) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Server-Side Supabase Test</h1>
        <p className="text-red-500">Error fetching restaurants: {restaurantsError.message}</p>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Server-Side Supabase Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Business Types ({businessTypes.length})</h2>
          <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(businessTypes, null, 2)}
          </pre>
        </div>
        
        <div className="border p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Restaurants ({restaurants?.length || 0})</h2>
          {restaurants && restaurants.length > 0 ? (
            <div className="grid grid-cols-1 gap-2">
              {restaurants.map(business => (
                <div key={business.id} className="border p-2 rounded">
                  <h3 className="font-bold">{business.name}</h3>
                  <p className="text-sm text-gray-600">{business.location}</p>
                  <p className="text-sm">Rating: {business.rating || 'N/A'}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>No restaurants found</p>
          )}
        </div>
      </div>
      
      <div className="mt-4">
        <h2 className="text-xl font-semibold mb-2">Environment Info</h2>
        <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set'}</p>
        <p>SUPABASE_SERVICE_ROLE_KEY: {process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set'}</p>
        <p>NODE_ENV: {process.env.NODE_ENV}</p>
      </div>
    </div>
  )
}
