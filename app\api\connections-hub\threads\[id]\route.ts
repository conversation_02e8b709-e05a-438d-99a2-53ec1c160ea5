import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get all messages in a specific thread
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { id: threadId } = await params
    const { searchParams } = new URL(request.url)

    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const include_deleted = searchParams.get('include_deleted') === 'true'

    // Build query
    let query = supabase
      .from('communications')
      .select(`
        id,
        sender_id,
        recipient_id,
        connection_id,
        order_id,
        business_id,
        channel_type,
        message_type,
        subject,
        content,
        thread_id,
        parent_message_id,
        is_read,
        is_urgent,
        is_automated,
        priority,
        audio_data,
        audio_file_name,
        audio_file_size,
        audio_duration,
        created_at,
        read_at,
        deleted_at
      `)
      .eq('thread_id', threadId)
      .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
      .order('created_at', { ascending: true })

    // Filter out deleted messages unless specifically requested
    if (!include_deleted) {
      query = query.is('deleted_at', null)
    }

    const { data: messages, error } = await query.range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching thread messages:', error)
      return NextResponse.json(
        { error: 'Failed to fetch thread messages' },
        { status: 500 }
      )
    }

    if (!messages || messages.length === 0) {
      return NextResponse.json(
        { error: 'Thread not found or you do not have access to it' },
        { status: 404 }
      )
    }

    // Get thread metadata from first message
    const firstMessage = messages[0]
    const threadMetadata = {
      thread_id: threadId,
      channel_type: firstMessage.channel_type,
      order_id: firstMessage.order_id,
      business_id: firstMessage.business_id,
      connection_id: firstMessage.connection_id,
      participants: [
        ...new Set(messages.map(m => m.sender_id).concat(messages.map(m => m.recipient_id)))
      ],
      created_at: firstMessage.created_at,
      last_message_at: messages[messages.length - 1]?.created_at
    }

    // Count unread messages for current user
    const unreadCount = messages.filter(m =>
      m.recipient_id === user.id && !m.is_read
    ).length

    return NextResponse.json({
      thread: threadMetadata,
      messages: messages || [],
      unread_count: unreadCount,
      total_messages: messages.length
    })

  } catch (error: any) {
    console.error('Error in thread GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT - Update thread (mark all messages as read)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { id: threadId } = await params
    const body = await request.json()

    if (body.action === 'mark_all_read') {
      // Mark all messages in thread as read for current user
      const { error } = await supabase
        .from('communications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('thread_id', threadId)
        .eq('recipient_id', user.id)
        .eq('is_read', false)

      if (error) {
        console.error('Error marking thread as read:', error)
        return NextResponse.json(
          { error: 'Failed to mark thread as read' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: 'Thread marked as read successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Supported actions: mark_all_read' },
      { status: 400 }
    )

  } catch (error: any) {
    console.error('Error in thread PUT:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
