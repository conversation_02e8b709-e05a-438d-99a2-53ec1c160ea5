"use client";

import React from "react";
import { CreditCard, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SimpleRadioGroup, SimpleRadioGroupItem } from "@/components/ui/simple-radio-group";
import { useCheckout } from "../checkout-context";

export const PaymentMethod: React.FC = () => {
  const {
    paymentMethod,
    setPaymentMethod,
    stepsCompleted,
    goToPrevStep,
    handleSubmit,
    isSubmitting
  } = useCheckout();

  return (
    <>
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-emerald-100 p-2 rounded-full mr-3">
            <CreditCard className="h-5 w-5 text-emerald-600" />
          </div>
          <h3 className="font-medium text-lg text-gray-800">Payment Method</h3>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-5 shadow-sm">
          <SimpleRadioGroup
            value={paymentMethod}
            onValueChange={setPaymentMethod}
            className="space-y-3"
          >
            <div className="flex items-center space-x-2 p-3 bg-white rounded-md border border-gray-200 hover:border-emerald-200 transition-colors">
              <SimpleRadioGroupItem value="card" id="card" className="text-emerald-600" />
              <Label htmlFor="card" className="cursor-pointer flex-1">
                <div className="font-medium">Credit/Debit Card</div>
                <div className="text-sm text-gray-500">Pay securely with your card</div>
              </Label>
              <div className="flex space-x-1">
                <div className="w-8 h-5 bg-blue-600 rounded"></div>
                <div className="w-8 h-5 bg-red-500 rounded"></div>
                <div className="w-8 h-5 bg-yellow-400 rounded"></div>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-3 bg-white rounded-md border border-gray-200 hover:border-emerald-200 transition-colors">
              <SimpleRadioGroupItem value="cash" id="cash" className="text-emerald-600" />
              <Label htmlFor="cash" className="cursor-pointer flex-1">
                <div className="font-medium">Cash on Delivery</div>
                <div className="text-sm text-gray-500">Pay with cash when your order arrives</div>
              </Label>
            </div>
          </SimpleRadioGroup>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100 mb-6">
        <h4 className="font-medium text-yellow-800 mb-2 flex items-center">
          <AlertTriangle className="h-4 w-4 mr-2" />
          Important Information
        </h4>
        <p className="text-sm text-yellow-700">
          By placing your order, you agree to our Terms of Service and Privacy Policy.
          Your order will be processed immediately and cannot be canceled once confirmed.
        </p>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={goToPrevStep}
        >
          Back
        </Button>
        <Button
          type="submit"
          onClick={(e) => {
            console.log('🔘 PAYMENT METHOD: Place Order button clicked!');
            console.log('🔘 PAYMENT METHOD: handleSubmit function:', typeof handleSubmit);
            handleSubmit(e);
          }}
          className="bg-emerald-600 hover:bg-emerald-700"
          disabled={!stepsCompleted.paymentMethod || isSubmitting}
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
              Processing...
            </div>
          ) : (
            "Place Order"
          )}
        </Button>
      </div>
    </>
  );
};
