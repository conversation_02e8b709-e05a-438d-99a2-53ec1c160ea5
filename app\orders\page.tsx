import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import {
  ChevronDown,
  Clock,
  Download,
  Filter,
  HelpCircle,
  LineChart,
  Menu,
  Package,
  Search,
  Settings,
  ShoppingCart,
  SlidersHorizontal,
  Truck,
  Users,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/date-range-picker"
import { OrderList } from "@/components/order-list"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export const metadata: Metadata = {
  title: "Orders - Loop Seller Dashboard",
  description: "Manage your orders on the Loop delivery platform",
}

export default function OrdersPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold">
            <Package className="h-6 w-6 text-emerald-500" />
            <span className="text-lg">Loop</span>
          </div>
          <Button variant="outline" size="icon" className="ml-auto h-8 w-8 lg:hidden">
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
          <div className="ml-auto hidden items-center gap-4 lg:flex">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search..." className="w-64 rounded-lg bg-background pl-8" />
            </div>
            <Button variant="outline" size="icon" className="rounded-full">
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>
            <Button variant="outline" size="icon" className="rounded-full">
              <HelpCircle className="h-4 w-4" />
              <span className="sr-only">Help</span>
            </Button>
            <Button variant="outline" size="sm" className="rounded-full">
              <img src="/placeholder-user.jpg" alt="Avatar" className="mr-2 h-5 w-5 rounded-full" />
              John Doe
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="grid flex-1 lg:grid-cols-[240px_1fr]">
        <div className="hidden border-r bg-muted/40 lg:block">
          <div className="flex h-full max-h-screen flex-col gap-2">
            <div className="flex-1 overflow-auto py-2">
              <nav className="grid items-start px-4 text-sm font-medium">
                <Link
                  href="/"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <LineChart className="h-4 w-4" />
                  Dashboard
                </Link>
                <Link
                  href="/orders"
                  className="flex items-center gap-3 rounded-lg bg-emerald-50 px-3 py-2 text-emerald-900 transition-all hover:text-emerald-900"
                >
                  <ShoppingCart className="h-4 w-4" />
                  Orders
                </Link>
                <Link
                  href="/products"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <Package className="h-4 w-4" />
                  Products
                </Link>
                <Link
                  href="#"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-emerald-900"
                >
                  <Users className="h-4 w-4" />
                  Customers
                </Link>
              </nav>
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Orders</h1>
                <p className="text-muted-foreground">Manage and track your customer orders</p>
              </div>
              <div className="flex items-center gap-2">
                <DatePickerWithRange />
                <Button variant="outline" size="sm" className="h-8">
                  <Download className="mr-2 h-3.5 w-3.5" />
                  Export
                </Button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-emerald-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,248</div>
                  <p className="text-xs text-muted-foreground">+156 from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
                  <Clock className="h-4 w-4 text-yellow-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">42</div>
                  <p className="text-xs text-muted-foreground">Awaiting processing</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Out for Delivery</CardTitle>
                  <Truck className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">18</div>
                  <p className="text-xs text-muted-foreground">Currently in transit</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Delivery Time</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-emerald-500"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24 min</div>
                  <p className="text-xs text-muted-foreground">-2 min from last week</p>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="all" className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="all">All Orders</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="processing">Processing</TabsTrigger>
                  <TabsTrigger value="completed">Completed</TabsTrigger>
                  <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
                </TabsList>
                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8">
                        <Filter className="mr-2 h-3.5 w-3.5" />
                        Filter
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[200px]">
                      <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuCheckboxItem checked>Today</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem checked>This Week</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>This Month</DropdownMenuCheckboxItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel>Payment Status</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuCheckboxItem checked>Paid</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>Unpaid</DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem>Refunded</DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8">
                        <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                        Sort
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[180px]">
                      <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Order ID (Asc)</DropdownMenuItem>
                      <DropdownMenuItem>Order ID (Desc)</DropdownMenuItem>
                      <DropdownMenuItem>Date (Newest First)</DropdownMenuItem>
                      <DropdownMenuItem>Date (Oldest First)</DropdownMenuItem>
                      <DropdownMenuItem>Customer Name (A-Z)</DropdownMenuItem>
                      <DropdownMenuItem>Customer Name (Z-A)</DropdownMenuItem>
                      <DropdownMenuItem>Total (High to Low)</DropdownMenuItem>
                      <DropdownMenuItem>Total (Low to High)</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search orders..."
                      className="w-64 rounded-lg bg-background pl-8 h-8"
                    />
                  </div>
                </div>
              </div>

              <TabsContent value="all" className="space-y-4">
                <Card>
                  <CardHeader className="p-4">
                    <div className="flex items-center justify-between">
                      <CardTitle>Order History</CardTitle>
                      <CardDescription>Showing orders for the selected date range</CardDescription>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0 overflow-auto">
                    <TooltipProvider>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[40px]">
                              <Checkbox />
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Order ID
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Unique identifier for the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Customer
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Customer who placed the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Date
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Date and time when the order was placed</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Status
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Current status of the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Payment
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Payment status of the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Items
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Number of items in the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[100px]">
                              <div className="flex items-center gap-1">
                                Total
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Total amount of the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Delivery Time
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Estimated or actual delivery time</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="min-w-[150px]">
                              <div className="flex items-center gap-1">
                                Address
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    <p className="w-[200px] text-xs">Delivery address for the order</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <OrderList />
                        </TableBody>
                      </Table>
                    </TooltipProvider>
                  </CardContent>
                  <CardFooter className="flex items-center justify-between p-4">
                    <div className="text-xs text-muted-foreground">Showing 1-10 of 1,248 orders</div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" disabled>
                        Previous
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        1
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        2
                      </Button>
                      <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                        3
                      </Button>
                      <Button variant="outline" size="sm">
                        Next
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="pending" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Pending Orders</CardTitle>
                    <CardDescription>Orders that are awaiting processing</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only pending orders
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="processing" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Processing Orders</CardTitle>
                    <CardDescription>Orders that are currently being processed</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only processing orders
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="completed" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Completed Orders</CardTitle>
                    <CardDescription>Orders that have been delivered successfully</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only completed orders
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="cancelled" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Cancelled Orders</CardTitle>
                    <CardDescription>Orders that have been cancelled</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      This tab would display only cancelled orders
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </div>
  )
}
