import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    console.log("Business registration API called");

    // Parse the request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (parseError: any) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: `Invalid request format: ${parseError.message}` },
        { status: 400 }
      );
    }

    const {
      email,
      name,
      password, // Get the password for auth registration
      businessName,
      businessDescription = "",
      businessAddress = "",
      businessPostcode = "",
      businessParish = "",
      businessPhone = "",
      deliveryAvailable = true,
      businessTypeId = 1,
      deliveryFee,
      minimumOrderAmount,
      categories = [] // Get the selected categories
    } = requestBody;

    // Validate minimal required fields (password is now optional)
    if (!email || !name || !businessName) {
      return NextResponse.json(
        { error: "Missing required fields (email, name, businessName)" },
        { status: 400 }
      )
    }

    console.log("Business registration request:", {
      email,
      name,
      businessName,
      hasPassword: !!password,
      businessTypeId,
      categoriesCount: categories?.length || 0
    });

    // Create a Supabase client with admin privileges
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase URL or service role key");
      console.error("URL exists:", !!supabaseUrl);
      console.error("Service key exists:", !!supabaseServiceKey);
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    console.log("Supabase URL:", supabaseUrl);
    console.log("Service key length:", supabaseServiceKey?.length);

    // Create client with explicit headers to bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          'X-Client-Info': 'business-registration-api',
        },
      },
    });

    console.log("Supabase admin client created");

    // Start a transaction by using a single batch operation
    try {
      // Step 1: Check if user exists or create a new user
      let userId;
      let authUserCreated = false;

      // First, check if the user exists in the auth system
      let authUser = null;
      try {
        const { data, error } = await supabase.auth.admin.getUserByEmail(email);
        if (error) {
          console.error("Error checking for existing auth user:", error);
        } else {
          authUser = data;
        }
      } catch (err) {
        console.error("Exception checking for existing auth user:", err);
        // Continue anyway, as we'll try to create the user
      }

      // Check if user exists in the public users table
      const { data: existingUser, error: getUserError } = await supabase
        .from("users")
        .select("id, role, auth_id")
        .eq("email", email)
        .maybeSingle();

      if (getUserError) {
        console.error("Error checking for existing user:", getUserError);
        return NextResponse.json(
          { error: `Error checking user: ${getUserError.message}` },
          { status: 500 }
        );
      }

      if (existingUser) {
        console.log("User already exists in public users table:", existingUser.id);
        userId = existingUser.id;

        // Check if auth user already exists and is linked
        if (authUser && existingUser.auth_id) {
          console.log("Auth user already exists and is linked");
          authUserCreated = true;
        }

        // If auth user exists but public user doesn't have auth_id, link them
        if (authUser && !existingUser.auth_id) {
          console.log("Linking existing public user to existing auth user");
          const { error: linkError } = await supabase
            .from("users")
            .update({
              auth_id: authUser.user.id,
              updated_at: new Date().toISOString()
            })
            .eq("id", userId);

          if (linkError) {
            console.error("Error linking existing users:", linkError);
          } else {
            console.log("Successfully linked existing users");
            authUserCreated = true;
          }
        } else if (authUser) {
          // Auth user already exists and is linked
          authUserCreated = true;
        }

        // Update user role to business_manager if they're currently a customer
        if (existingUser.role === 'customer') {
          const { error: updateRoleError } = await supabase
            .from("users")
            .update({
              role: "business_manager",
              updated_at: new Date().toISOString()
            })
            .eq("id", userId);

          if (updateRoleError) {
            console.error("Error updating user role:", updateRoleError);
            // Continue anyway, this is not critical
          } else {
            console.log("Updated user role to business_manager");
          }
        }

        // If user exists in public users table but not in auth, create auth user (only if password provided)
        if (!authUser && password) {
          console.log("User exists in public users table but not in auth, creating auth user");

          // Create user in auth system
          try {
            console.log("Attempting to create auth user for existing public user:", { email, name });

            // Try admin.createUser first, fallback to signUp if it fails
            let newAuthUser, createAuthError;

            try {
              const adminResult = await supabase.auth.admin.createUser({
                email,
                password,
                email_confirm: true, // Auto-confirm email
                user_metadata: {
                  name,
                  role: "business_manager"
                }
              });
              newAuthUser = adminResult.data;
              createAuthError = adminResult.error;
              console.log("Admin createUser result:", { success: !createAuthError, error: createAuthError?.message });
            } catch (adminError) {
              console.log("Admin createUser failed, trying signUp:", adminError.message);

              // Fallback to regular signUp
              const signUpResult = await supabase.auth.signUp({
                email,
                password,
                options: {
                  data: {
                    name,
                    role: "business_manager"
                  }
                }
              });
              newAuthUser = signUpResult.data;
              createAuthError = signUpResult.error;
              console.log("SignUp fallback result:", { success: !createAuthError, error: createAuthError?.message });
            }
            console.log("Auth user creation result for existing user:", { data: newAuthUser, error: createAuthError });

            if (createAuthError) {
              console.error("Error creating auth user:", createAuthError);
              console.log("Continuing with business registration without auth user...");
              // Continue without auth user - business registration can still proceed
            } else {
              const authUserId = newAuthUser?.user?.id;
              console.log("Created auth user for existing public user:", authUserId);

              // Update the public user record with the auth_id
              const { error: updateAuthIdError } = await supabase
                .from("users")
                .update({
                  auth_id: authUserId,
                  updated_at: new Date().toISOString()
                })
                .eq("id", userId);

              if (updateAuthIdError) {
                console.error("Error updating user with auth_id:", updateAuthIdError);
                console.log("Continuing without linking auth user...");
              } else {
                console.log("Updated public user with auth_id:", authUserId);
                authUserCreated = true;
              }
            }
          } catch (err) {
            console.error("Exception creating auth user:", err);
            console.log("Continuing with business registration without auth user...");
            // Continue without auth user - business registration can still proceed
          }
        }
      } else if (authUser) {
        // Auth user exists but public user doesn't, create public user
        console.log("Auth user exists but public user doesn't, creating public user");

        const { data: newUser, error: createUserError } = await supabase
          .from("users")
          .insert({
            auth_id: authUser.user.id, // Link to existing auth user
            email,
            name,
            role: "business_manager",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        if (createUserError) {
          console.error("Error creating public user for existing auth user:", createUserError);
          return NextResponse.json(
            { error: `Failed to create public user: ${createUserError.message}` },
            { status: 500 }
          );
        }

        userId = newUser[0].id;
        console.log("Created public user for existing auth user:", userId);
        authUserCreated = true;
      } else {
        // User doesn't exist in either system, create in both

        // 1. Create user in auth system first (only if password provided)
        console.log("Creating new user - password provided:", !!password);
        let authUserId = null;

        if (password) {
          try {
            console.log("Attempting to create auth user with data:", { email, name });

          // Try admin.createUser first, fallback to signUp if it fails
          let newAuthUser, createAuthError;

          try {
            const adminResult = await supabase.auth.admin.createUser({
              email,
              password,
              user_metadata: {
                name,
                role: "business_manager"
              }
            });
            newAuthUser = adminResult.data;
            createAuthError = adminResult.error;
            console.log("Admin createUser result:", { success: !createAuthError, error: createAuthError?.message });
          } catch (adminError) {
            console.log("Admin createUser failed, trying signUp:", adminError.message);

            // Fallback to regular signUp
            const signUpResult = await supabase.auth.signUp({
              email,
              password,
              options: {
                data: {
                  name,
                  role: "business_manager"
                }
              }
            });
            newAuthUser = signUpResult.data;
            createAuthError = signUpResult.error;
            console.log("SignUp fallback result:", { success: !createAuthError, error: createAuthError?.message });
          }
          console.log("Auth user creation result:", { data: newAuthUser, error: createAuthError });

          if (createAuthError) {
            console.error("Error creating auth user:", createAuthError);
            console.error("Auth error details:", createAuthError.message);
            console.log("Continuing with business registration without auth user...");
            // Continue without auth user - business registration can still proceed
            authUserId = null;
          } else {
            authUserId = newAuthUser?.user?.id;
            console.log("Successfully created auth user:", authUserId);
            console.log("Auth user email:", newAuthUser?.user?.email);
            authUserCreated = true;
          }
          } catch (err) {
            console.error("Exception creating auth user:", err);
            console.log("Continuing with business registration without auth user...");
            // Continue without auth user - business registration can still proceed
            authUserId = null;
          }
        } else {
          console.log("No password provided, skipping auth user creation");
        }

        // 2. Create user in public users table with auth_id
        const { data: newUser, error: createUserError } = await supabase
          .from("users")
          .insert({
            auth_id: authUserId, // Link to auth user (may be null if auth creation failed)
            email,
            name,
            role: "business_manager", // Set role as business_manager directly
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        if (createUserError) {
          console.error("Error creating user in public table:", createUserError);
          return NextResponse.json(
            { error: `Failed to create user in public table: ${createUserError.message}` },
            { status: 500 }
          );
        }

        userId = newUser[0].id;
        console.log("Created new user in public table with ID:", userId, "linked to auth ID:", authUserId);
      }

      // Step 2: Create a business record with is_approved set to null (pending)
      // Generate a unique slug from the business name
      const generateUniqueSlug = async (baseName: string): Promise<string> => {
        const baseSlug = baseName
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

        let slug = baseSlug;
        let counter = 1;

        while (true) {
          // Check if this slug already exists
          const { data: existingBusiness } = await supabase
            .from("businesses")
            .select("id")
            .eq("slug", slug)
            .single();

          if (!existingBusiness) {
            // Slug is unique, we can use it
            return slug;
          }

          // Slug exists, try with a number suffix
          slug = `${baseSlug}-${counter}`;
          counter++;
        }
      };

      const slug = await generateUniqueSlug(businessName);

      console.log("Creating business with slug:", slug);

      const { data: business, error: businessError } = await supabase
        .from("businesses")
        .insert({
          name: businessName,
          slug: slug,
          description: businessDescription,
          address: businessAddress,
          postcode: businessPostcode,
          phone: businessPhone,
          business_type_id: businessTypeId,
          delivery_fee: deliveryFee || 2.50,
          minimum_order_amount: minimumOrderAmount || 15.00,
          delivery_available: deliveryAvailable,
          is_approved: null, // Pending approval
          location: businessParish || "Jersey", // Use parish as location, fallback to Jersey
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      if (businessError) {
        console.error("Error creating business:", businessError);
        console.error("Business data that failed:", {
          name: businessName,
          slug: slug,
          description: businessDescription,
          address: businessAddress,
          postcode: businessPostcode,
          phone: businessPhone,
          business_type_id: businessTypeId,
          location: businessParish || "Jersey"
        });
        return NextResponse.json(
          { error: `Failed to create business: ${businessError.message}` },
          { status: 500 }
        );
      }

      const businessId = business[0].id;
      console.log("Created business with ID:", businessId);

      // Step 3: Create a business_managers record to link the user to the business
      const { error: managerError } = await supabase
        .from("business_managers")
        .insert({
          user_id: userId,
          business_id: businessId,
          is_primary: true,
          created_at: new Date().toISOString()
        });

      if (managerError) {
        console.error("Error creating business manager link:", managerError);
        // This is not critical, so we'll continue even if it fails
        // But we should log it for debugging
      } else {
        console.log("Created business manager link for user", userId, "and business", businessId);
      }

      // Step 4: Assign categories to the business
      try {
        // Check if user selected any categories
        if (categories && categories.length > 0) {
          console.log("User selected categories:", categories);

          // Create category records for each selected category
          const categoryRecords = categories.map((categoryId: number, index: number) => ({
            business_id: businessId,
            category_id: categoryId,
            is_primary: index === 0, // First one is primary
            created_at: new Date().toISOString()
          }));

          const { error: categoriesError } = await supabase
            .from("business_categories")
            .insert(categoryRecords);

          if (categoriesError) {
            console.error("Error assigning user-selected categories:", categoriesError);
            // This is not critical, so we'll continue even if it fails
          } else {
            console.log("Assigned user-selected categories to business", businessId);
          }
        } else {
          // No categories selected, assign a default category based on business type
          console.log("No categories selected, assigning default category");

          // Map business types to default categories
          const businessTypeToCategory: Record<number, number> = {
            1: 46,  // Restaurant -> Grill
            2: 199, // Shop -> Groceries
            3: 202, // Pharmacy -> Prescription
            4: 204, // Cafe -> Coffee & Drinks
            38: 196 // Errand -> Shopping
          };

          const defaultCategoryId = businessTypeToCategory[businessTypeId] || 46;

          // Create a business_categories record
          const { error: categoryError } = await supabase
            .from("business_categories")
            .insert({
              business_id: businessId,
              category_id: defaultCategoryId,
              is_primary: true,
              created_at: new Date().toISOString()
            });

          if (categoryError) {
            console.error("Error assigning default category:", categoryError);
            // This is not critical, so we'll continue even if it fails
          } else {
            console.log("Assigned default category", defaultCategoryId, "to business", businessId);
          }
        }
      } catch (categoryErr) {
        console.error("Error in category assignment:", categoryErr);
        // Continue anyway as this is not critical
      }

      // Return success with the business ID and additional info
      return NextResponse.json({
        success: true,
        registrationId: businessId,
        message: "Business registration submitted successfully",
        userEmail: email,
        businessName: businessName,
        authUserCreated: authUserCreated,
        note: !authUserCreated ? "Auth user creation failed, but business registration completed. You may need to create an account separately to sign in." : "Please check your email and click the confirmation link to verify your account before signing in."
      });

    } catch (dbError: any) {
      console.error("Database operation error:", dbError);
      return NextResponse.json(
        { error: `Database error: ${dbError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Unexpected error in business registration:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
