import Image from "next/image"
import { Check, Edit, <PERSON><PERSON><PERSON><PERSON><PERSON>, X } from "lucide-react"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { TableCell, TableRow } from "@/components/ui/table"

const products = [
  {
    id: "PROD001",
    business_id: 1,
    business_name: "Tech Galaxy",
    category_id: 1,
    category_name: "Electronics",
    name: "Premium Headphones",
    description: "High-quality wireless headphones with noise cancellation",
    price: 89.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: true,
    is_popular: true,
    unit: "piece",
    quantity: 142,
    created_at: "2023-04-15T10:30:00",
    updated_at: "2023-05-20T14:45:00",
    slug: "premium-headphones",
  },
  {
    id: "PROD002",
    business_id: 1,
    business_name: "Tech Galaxy",
    category_id: 1,
    category_name: "Electronics",
    name: "Wireless Charger",
    description: "Fast wireless charging pad compatible with all devices",
    price: 29.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: true,
    unit: "piece",
    quantity: 98,
    created_at: "2023-04-18T09:15:00",
    updated_at: "2023-05-22T11:30:00",
    slug: "wireless-charger",
  },
  {
    id: "PROD003",
    business_id: 2,
    business_name: "Gadget Hub",
    category_id: 1,
    category_name: "Electronics",
    name: "Smart Watch",
    description: "Feature-rich smartwatch with health monitoring",
    price: 199.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: true,
    is_popular: false,
    unit: "piece",
    quantity: 76,
    created_at: "2023-04-20T14:20:00",
    updated_at: "2023-05-25T16:10:00",
    slug: "smart-watch",
  },
  {
    id: "PROD004",
    business_id: 2,
    business_name: "Gadget Hub",
    category_id: 1,
    category_name: "Electronics",
    name: "Bluetooth Speaker",
    description: "Portable speaker with excellent sound quality",
    price: 49.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: false,
    unit: "piece",
    quantity: 64,
    created_at: "2023-04-22T11:45:00",
    updated_at: "2023-05-26T09:20:00",
    slug: "bluetooth-speaker",
  },
  {
    id: "PROD005",
    business_id: 3,
    business_name: "Digital World",
    category_id: 1,
    category_name: "Electronics",
    name: "Power Bank",
    description: "High-capacity portable charger for all devices",
    price: 29.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: false,
    unit: "piece",
    quantity: 52,
    created_at: "2023-04-25T16:30:00",
    updated_at: "2023-05-28T10:15:00",
    slug: "power-bank",
  },
  {
    id: "PROD006",
    business_id: 3,
    business_name: "Digital World",
    category_id: 1,
    category_name: "Electronics",
    name: "Fitness Tracker",
    description: "Lightweight fitness band with activity tracking",
    price: 59.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: false,
    unit: "piece",
    quantity: 8,
    created_at: "2023-04-28T13:10:00",
    updated_at: "2023-05-30T15:45:00",
    slug: "fitness-tracker",
  },
  {
    id: "PROD007",
    business_id: 4,
    business_name: "Smart Store",
    category_id: 1,
    category_name: "Electronics",
    name: "Wireless Earbuds",
    description: "True wireless earbuds with noise isolation",
    price: 79.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: true,
    is_popular: true,
    unit: "piece",
    quantity: 120,
    created_at: "2023-05-02T10:20:00",
    updated_at: "2023-06-01T12:30:00",
    slug: "wireless-earbuds",
  },
  {
    id: "PROD008",
    business_id: 4,
    business_name: "Smart Store",
    category_id: 1,
    category_name: "Electronics",
    name: "Smart Home Hub",
    description: "Central control for all your smart home devices",
    price: 129.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: false,
    is_featured: false,
    is_popular: false,
    unit: "piece",
    quantity: 0,
    created_at: "2023-05-05T09:45:00",
    updated_at: "2023-06-03T14:20:00",
    slug: "smart-home-hub",
  },
  {
    id: "PROD009",
    business_id: 5,
    business_name: "Future Tech",
    category_id: 2,
    category_name: "Accessories",
    name: "Laptop Sleeve",
    description: "Protective sleeve for laptops up to 15 inches",
    price: 19.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: false,
    unit: "piece",
    quantity: 85,
    created_at: "2023-05-08T15:30:00",
    updated_at: "2023-06-05T11:10:00",
    slug: "laptop-sleeve",
  },
  {
    id: "PROD010",
    business_id: 5,
    business_name: "Future Tech",
    category_id: 2,
    category_name: "Accessories",
    name: "USB-C Cable Pack",
    description: "Set of 3 durable USB-C cables of different lengths",
    price: 12.99,
    image_url: "/placeholder.svg?height=80&width=80",
    is_available: true,
    is_featured: false,
    is_popular: false,
    unit: "pack",
    quantity: 200,
    created_at: "2023-05-10T12:15:00",
    updated_at: "2023-06-08T10:30:00",
    slug: "usb-c-cable-pack",
  },
]

export function ProductList() {
  return (
    <>
      {products.map((product) => (
        <TableRow key={product.id}>
          <TableCell>
            <Checkbox />
          </TableCell>
          <TableCell>
            <Image
              src={product.image_url || "/placeholder.svg"}
              alt={product.name}
              width={40}
              height={40}
              className="rounded-md object-cover"
            />
          </TableCell>
          <TableCell className="font-medium">{product.id}</TableCell>
          <TableCell className="font-medium">
            <div className="font-medium">{product.name}</div>
            <div className="text-xs text-muted-foreground truncate max-w-[200px]">{product.description}</div>
          </TableCell>
          <TableCell>{product.business_name}</TableCell>
          <TableCell>{product.category_name}</TableCell>
          <TableCell>£{product.price.toFixed(2)}</TableCell>
          <TableCell>{product.unit}</TableCell>
          <TableCell>
            {product.quantity === 0 ? (
              <span className="text-red-500">Out of stock</span>
            ) : product.quantity < 10 ? (
              <span className="text-yellow-500">{product.quantity} left</span>
            ) : (
              product.quantity
            )}
          </TableCell>
          <TableCell>
            {product.is_available ? (
              <Badge
                variant="outline"
                className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
              >
                <Check className="mr-1 h-3 w-3" /> Available
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800">
                <X className="mr-1 h-3 w-3" /> Unavailable
              </Badge>
            )}
          </TableCell>
          <TableCell>
            {product.is_featured ? (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800">
                Featured
              </Badge>
            ) : (
              <span className="text-muted-foreground text-sm">-</span>
            )}
          </TableCell>
          <TableCell>
            {product.is_popular ? (
              <Badge
                variant="outline"
                className="bg-purple-50 text-purple-700 hover:bg-purple-100 hover:text-purple-800"
              >
                Popular
              </Badge>
            ) : (
              <span className="text-muted-foreground text-sm">-</span>
            )}
          </TableCell>
          <TableCell className="text-sm text-muted-foreground">
            {new Date(product.created_at).toLocaleDateString()}
          </TableCell>
          <TableCell className="text-sm text-muted-foreground">
            {new Date(product.updated_at).toLocaleDateString()}
          </TableCell>
          <TableCell className="text-sm text-muted-foreground">{product.slug}</TableCell>
          <TableCell className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Product
                </DropdownMenuItem>
                <DropdownMenuItem>View Details</DropdownMenuItem>
                <DropdownMenuItem>Duplicate</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Archive Product</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">Delete Product</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      ))}
    </>
  )
}
