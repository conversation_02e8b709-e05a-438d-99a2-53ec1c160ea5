import Link from "next/link"
import { <PERSON>, <PERSON>, Bike, DollarSign } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import FallbackImage from "@/components/fallback-image"
import type { Shop } from "@/types/shop"

interface ShopCardProps {
  shop: Shop
}

export default function ShopCard({ shop }: ShopCardProps) {
  return (
    <Link href={`/shops/${shop.id}`}>
      <Card className="overflow-hidden h-full transition-all hover:shadow-lg">
        <div className="relative h-48 bg-white">
          <FallbackImage
            src={shop.logo_url || shop.image || shop.coverImage}
            alt={`${shop.name} logo`}
            fallbackSrc="/placeholder.svg"
            className="w-full h-full object-contain p-4"
          />
          {shop.isNew && <Badge className="absolute top-2 left-2 bg-emerald-600">New</Badge>}
          {shop.offer && <Badge className="absolute top-2 right-2 bg-orange-500">{shop.offer}</Badge>}
        </div>
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-bold text-lg">{shop.name}</h3>
            <div className="flex items-center bg-green-50 px-2 py-1 rounded text-sm">
              <Star className="h-3 w-3 text-yellow-500 mr-1" />
              <span>{shop.rating}</span>
            </div>
          </div>

          <p className="text-gray-500 text-sm mb-3">{shop.storeTypes.join(", ")}</p>

          {/* Delivery Information Card */}
          <div className="mt-4 bg-gray-50 p-3 rounded-md">
            {/* Card Header */}
            <div className="flex items-center mb-2">
              <div className="bg-emerald-50 p-1.5 rounded-full mr-2">
                <Bike className="h-4 w-4 text-emerald-600" />
              </div>
              <span className="font-medium text-gray-700">Delivery Information</span>
            </div>

            {/* Delivery Details Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
              {/* Delivery Time */}
              <div className="flex items-center">
                <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                </div>
                <span className="font-medium">{shop.deliveryTime} min</span>
              </div>

              {/* Delivery Fee */}
              <div className="flex items-center">
                <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                  <DollarSign className="h-4 w-4 text-gray-600" />
                </div>
                <div className={`text-sm font-medium ${shop.deliveryFee === 0 ? 'text-emerald-600' : 'text-gray-700'}`}>
                  {shop.deliveryFee === 0 ? (
                    "Free Delivery"
                  ) : (
                    `£${shop.deliveryFee.toFixed(2)} delivery`
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
