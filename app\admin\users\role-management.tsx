"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, AlertTriangle, CheckCircle, Crown, Users, Store, User } from 'lucide-react'

interface User {
  id: number
  email: string
  name: string
  role: string
  created_at: string
  emergency_access?: boolean
  environment_override?: boolean
}

interface RoleManagementProps {
  users: User[]
  onRoleUpdate: (userId: number, newRole: string) => Promise<void>
}

const ROLES = [
  { 
    value: 'customer', 
    label: 'Customer', 
    icon: User, 
    color: 'bg-gray-100 text-gray-800',
    description: 'Standard platform user - can place orders and manage their profile'
  },
  { 
    value: 'business_staff', 
    label: 'Business Staff', 
    icon: Store, 
    color: 'bg-blue-100 text-blue-800',
    description: 'Business employee - can manage orders and basic business operations'
  },
  { 
    value: 'business_manager', 
    label: 'Business Manager', 
    icon: Store, 
    color: 'bg-purple-100 text-purple-800',
    description: 'Business owner/manager - full business management access'
  },
  { 
    value: 'admin', 
    label: 'Platform Admin', 
    icon: Shield, 
    color: 'bg-emerald-100 text-emerald-800',
    description: 'Platform administrator - can manage users, businesses, and platform operations'
  },
  { 
    value: 'super_admin', 
    label: 'Super Admin', 
    icon: Crown, 
    color: 'bg-red-100 text-red-800',
    description: 'System owner - full system access including platform settings'
  }
]

export default function RoleManagement({ users, onRoleUpdate }: RoleManagementProps) {
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [newRole, setNewRole] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  const getRoleInfo = (role: string) => {
    return ROLES.find(r => r.value === role) || ROLES[0]
  }

  const handleRoleChange = async () => {
    if (!selectedUser || !newRole) return

    setIsUpdating(true)
    try {
      await onRoleUpdate(selectedUser.id, newRole)
      setShowConfirmDialog(false)
      setSelectedUser(null)
      setNewRole('')
    } catch (error) {
      console.error('Failed to update role:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const openRoleDialog = (user: User) => {
    setSelectedUser(user)
    setNewRole(user.role)
    setShowConfirmDialog(true)
  }

  const isRoleChangeSignificant = (currentRole: string, newRole: string) => {
    const roleHierarchy = ['customer', 'business_staff', 'business_manager', 'admin', 'super_admin']
    const currentIndex = roleHierarchy.indexOf(currentRole)
    const newIndex = roleHierarchy.indexOf(newRole)
    
    // Significant if promoting to admin/super_admin or demoting from admin/super_admin
    return (currentIndex < 3 && newIndex >= 3) || (currentIndex >= 3 && newIndex < 3)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Users className="w-5 h-5" />
          User Role Management
        </h3>
        <Badge variant="outline" className="text-xs">
          {users.length} users
        </Badge>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Role changes take effect immediately. Admin and Super Admin roles grant significant platform access.
          Environment-configured admins have additional fallback access.
        </AlertDescription>
      </Alert>

      <div className="grid gap-4">
        {users.map((user) => {
          const roleInfo = getRoleInfo(user.role)
          const RoleIcon = roleInfo.icon

          return (
            <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <RoleIcon className="w-4 h-4 text-gray-500" />
                  <div>
                    <div className="font-medium">{user.name}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </div>
                
                {user.emergency_access && (
                  <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                    Emergency Access
                  </Badge>
                )}
                
                {user.environment_override && (
                  <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                    Env Override
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Badge className={`${roleInfo.color} border-0`}>
                  {roleInfo.label}
                </Badge>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openRoleDialog(user)}
                  className="text-xs"
                >
                  Change Role
                </Button>
              </div>
            </div>
          )
        })}
      </div>

      {/* Role Change Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Update the role for {selectedUser?.name} ({selectedUser?.email})
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select New Role</label>
              <Select value={newRole} onValueChange={setNewRole}>
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {ROLES.map((role) => {
                    const RoleIcon = role.icon
                    return (
                      <SelectItem key={role.value} value={role.value}>
                        <div className="flex items-center gap-2">
                          <RoleIcon className="w-4 h-4" />
                          <span>{role.label}</span>
                        </div>
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>

            {newRole && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm font-medium">{getRoleInfo(newRole).label}</div>
                <div className="text-xs text-gray-600 mt-1">
                  {getRoleInfo(newRole).description}
                </div>
              </div>
            )}

            {selectedUser && newRole && isRoleChangeSignificant(selectedUser.role, newRole) && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  This is a significant role change that will {
                    ['admin', 'super_admin'].includes(newRole) ? 'grant' : 'remove'
                  } administrative privileges. Please confirm this action.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRoleChange}
              disabled={isUpdating || !newRole || newRole === selectedUser?.role}
            >
              {isUpdating ? 'Updating...' : 'Update Role'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
