"use client"

import { useState, useEffect } from "react"
import { Calendar } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { SimpleSelect, SimpleSelectItem } from "./simple-select"
import { format, addDays, setHours, setMinutes, isBefore, getDay } from "date-fns"

interface TimeSlotSelectorProps {
  selectedTime: Date | null;
  onSelectTime: (date: Date) => void;
  earliestAvailableTime: Date;
  latestAvailableTime: Date;
  className?: string;
}

// Map day of week number to day name
const dayOfWeekMap = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday'
];

export function TimeSlotSelector({
  selectedTime: externalSelectedTime,
  onSelectTime,
  earliestAvailableTime,
  latestAvailableTime,
  className = ""
}: TimeSlotSelectorProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>("")
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [availableTimes, setAvailableTimes] = useState<string[]>([])

  // Generate available dates (today + next 6 days)
  useEffect(() => {
    const dates = []
    const now = new Date()

    // Add today
    dates.push(now)

    // Add next 6 days
    for (let i = 1; i <= 6; i++) {
      dates.push(addDays(now, i))
    }

    setAvailableDates(dates)
    setSelectedDate(now)
  }, [])

  // Generate available time slots based on selected date
  useEffect(() => {
    if (!selectedDate) return;

    console.log("Generating time slots for date:", selectedDate);

    const times: string[] = [];
    const now = new Date();
    const isToday = selectedDate.getDate() === now.getDate() &&
                    selectedDate.getMonth() === now.getMonth() &&
                    selectedDate.getFullYear() === now.getFullYear();

    // Get earliest available time for today
    let minHour = 9; // Default to 9 AM
    let minMinute = 0;

    if (isToday && earliestAvailableTime) {
      minHour = earliestAvailableTime.getHours();
      minMinute = earliestAvailableTime.getMinutes();
      console.log("Using earliest available time:", minHour, minMinute);
    }

    // Get latest available time (default to 10 PM)
    let maxHour = 22;
    let maxMinute = 0;

    if (latestAvailableTime) {
      maxHour = latestAvailableTime.getHours();
      maxMinute = latestAvailableTime.getMinutes();
      console.log("Using latest available time:", maxHour, maxMinute);
    }

    // Create time slots in 30-minute intervals
    for (let hour = minHour; hour <= maxHour; hour++) {
      for (let minute of [0, 30]) {
        // Skip times before the start time if it's today
        if (isToday && hour === minHour && minute < minMinute) {
          continue;
        }

        // Skip times after the end time
        if (hour === maxHour && minute > maxMinute) {
          continue;
        }

        // Format the time as HH:MM
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        times.push(timeString);
      }
    }

    console.log("Generated time slots:", times);

    // Always update the available times
    setAvailableTimes(times);

    // Select the first available time slot by default if needed
    if (times.length > 0 && (!selectedTime || !times.includes(selectedTime))) {
      console.log("Setting default selected time:", times[0]);
      setSelectedTime(times[0]);
    }
  }, [selectedDate, earliestAvailableTime, latestAvailableTime]);

  // When date or time changes, notify parent component - with debounce to prevent infinite loops
  useEffect(() => {
    // Skip initial render
    if (!selectedDate || !selectedTime) return;

    // Use a timeout to debounce frequent updates
    const timeoutId = setTimeout(() => {
      const [hours, minutes] = selectedTime.split(":").map(Number)
      const deliveryDate = new Date(selectedDate)
      deliveryDate.setHours(hours, minutes, 0, 0)

      // Only update if the value has actually changed
      if (!externalSelectedTime ||
          externalSelectedTime.getTime() !== deliveryDate.getTime()) {
        onSelectTime(deliveryDate)
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [selectedDate, selectedTime])

  // Sync with external selected time if provided - only run once when component mounts or when external time changes
  useEffect(() => {
    // Skip if no external time is provided or dates aren't loaded yet
    if (!externalSelectedTime || availableDates.length === 0) return;

    // Skip if we already have a selected date and time that matches the external time
    if (selectedDate && selectedTime) {
      const [hours, minutes] = selectedTime.split(":").map(Number);
      const currentSelectedDateTime = new Date(selectedDate);
      currentSelectedDateTime.setHours(hours, minutes, 0, 0);

      // If the current selection matches the external time (within 1 minute), don't update
      if (Math.abs(currentSelectedDateTime.getTime() - externalSelectedTime.getTime()) < 60000) {
        return;
      }
    }

    // Find the closest date to the external selected time
    const closestDate = availableDates.reduce((prev, curr) => {
      return Math.abs(curr.getTime() - externalSelectedTime.getTime()) <
             Math.abs(prev.getTime() - externalSelectedTime.getTime()) ? curr : prev;
    });

    // Only update if the date is different
    if (!selectedDate ||
        selectedDate.getDate() !== closestDate.getDate() ||
        selectedDate.getMonth() !== closestDate.getMonth() ||
        selectedDate.getFullYear() !== closestDate.getFullYear()) {
      setSelectedDate(closestDate);
    }

    // Set the selected time if it's available
    const timeString = format(externalSelectedTime, "HH:mm");
    if (availableTimes.includes(timeString) && timeString !== selectedTime) {
      setSelectedTime(timeString);
    }
  }, [externalSelectedTime]);

  return (
    <div className={`${className}`}>
      <div className="mb-4">
        <Label className="mb-2 block">Select Date</Label>
        <div className="grid grid-cols-4 gap-2">
          {availableDates.map((date, index) => {
            const isSelected = selectedDate &&
              date.getDate() === selectedDate.getDate() &&
              date.getMonth() === selectedDate.getMonth() &&
              date.getFullYear() === selectedDate.getFullYear();

            return (
              <Button
                key={index}
                type="button"
                variant={isSelected ? "default" : "outline"}
                className={`flex flex-col items-center justify-center h-20 p-1 rounded-md ${isSelected ? 'bg-emerald-600 text-white' : 'bg-white'}`}
                onClick={() => setSelectedDate(date)}
              >
                <span className="text-xs">{format(date, "EEE")}</span>
                <span className="text-xl font-semibold">{format(date, "d")}</span>
                <span className="text-xs">{format(date, "MMM")}</span>
              </Button>
            );
          })}
        </div>
      </div>

      <div className="mb-4">
        <Label htmlFor="time-select" className="mb-2 block">Select Time</Label>
        <SimpleSelect
          id="time-select"
          value={selectedTime}
          onValueChange={setSelectedTime}
          placeholder="Select a time slot"
          className="w-full"
        >
          {availableTimes.map((time) => (
            <SimpleSelectItem key={time} value={time}>
              {time}
            </SimpleSelectItem>
          ))}
        </SimpleSelect>
      </div>

      {selectedDate && selectedTime && (
        <div className="p-4 bg-emerald-50 rounded-md border border-emerald-200">
          <div className="flex items-center text-emerald-700">
            <Calendar className="h-5 w-5 mr-2" />
            <span className="font-medium">
              Scheduled for {format(selectedDate, "EEEE, MMMM d")} at {selectedTime}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
