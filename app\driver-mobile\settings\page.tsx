"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useTheme } from "next-themes"
import { Bell, Fingerprint, Globe, HelpCircle, LogOut, Moon, Shield, User, Wifi, MapPin, Package, DollarSign, Star, Monitor } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileNavigation } from "@/components/driver-mobile-navigation"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import { biometricAuthService } from "@/services/biometric-auth-service"

export default function SettingsPage() {
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [biometricSupported, setBiometricSupported] = useState(false)
  const [biometricEnabled, setBiometricEnabled] = useState(false)

  // Handle theme toggle
  const toggleDarkMode = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  // Mount effect to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const checkBiometricSupport = async () => {
      try {
        const supported = await biometricAuthService.isSupported()
        setBiometricSupported(supported)

        if (supported) {
          setBiometricEnabled(biometricAuthService.hasCredentials())
        }
      } catch (err) {
        console.error("Error checking biometric support:", err)
      }
    }

    checkBiometricSupport()
  }, [])

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      <div className="p-4 space-y-6">
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">Account</h2>
          <div className="bg-white rounded-lg shadow">
            <Link href="/driver-mobile/settings/profile" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <User className="h-5 w-5 text-gray-500 mr-3" />
                <span>Profile Information</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
            <Separator />
            <Link href="/driver-mobile/settings/notifications" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-gray-500 mr-3" />
                <span>Notification Preferences</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
            {biometricSupported && (
              <>
                <Separator />
                <Link href="/driver-mobile/settings/biometric" className="flex items-center justify-between p-4">
                  <div className="flex items-center">
                    <Fingerprint className="h-5 w-5 text-gray-500 mr-3" />
                    <span>Biometric Authentication</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-xs mr-2 rounded-full px-2 py-1 bg-gray-100 text-gray-800">
                      {biometricEnabled ? "Enabled" : "Disabled"}
                    </span>
                    <div className="text-gray-400">→</div>
                  </div>
                </Link>
              </>
            )}
          </div>
        </div>

        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">App Settings</h2>
          <div className="bg-white rounded-lg shadow">
            {/* Desktop Dashboard Link */}
            <Link href="/driver" target="_blank" rel="noopener noreferrer" className="flex items-center justify-between p-4 border-b border-blue-100 bg-blue-50">
              <div className="flex items-center">
                <Monitor className="h-5 w-5 text-blue-600 mr-3" />
                <span className="text-blue-700 font-medium">Desktop Dashboard</span>
              </div>
              <div className="text-blue-400">↗</div>
            </Link>
            <Separator />
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Moon className="h-5 w-5 text-gray-500 mr-3" />
                <span>Dark Mode</span>
              </div>
              {mounted && (
                <Switch
                  checked={theme === "dark"}
                  onCheckedChange={toggleDarkMode}
                />
              )}
            </div>
            <Separator />
            <Link href="/driver-mobile/settings/offline" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Wifi className="h-5 w-5 text-gray-500 mr-3" />
                <span>Offline Mode</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
            <Separator />
            <Link href="/driver-mobile/settings/interface" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Monitor className="h-5 w-5 text-gray-500 mr-3" />
                <span>Interface Preference</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
            <Separator />
            <Link href="/driver-mobile/settings/language" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Globe className="h-5 w-5 text-gray-500 mr-3" />
                <span>Language</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs mr-2">English</span>
                <div className="text-gray-400">→</div>
              </div>
            </Link>
            <Separator />
            <Link href="/driver-mobile/settings/location" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-gray-500 mr-3" />
                <span>Location Settings</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
          </div>
        </div>

        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">Support</h2>
          <div className="bg-white rounded-lg shadow">
            <Link href="/driver-mobile/help" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <HelpCircle className="h-5 w-5 text-gray-500 mr-3" />
                <span>Help Center</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
            <Separator />
            <Link href="/driver-mobile/privacy" className="flex items-center justify-between p-4">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-gray-500 mr-3" />
                <span>Privacy & Security</span>
              </div>
              <div className="text-gray-400">→</div>
            </Link>
          </div>
        </div>

          <Button
            variant="destructive"
            className="w-full flex items-center justify-center gap-2"
            onClick={() => router.push("/driver-mobile/login")}
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>

          {/* Bottom Spacing for Navigation */}
          <div className="h-20"></div>
        </div>

        <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
