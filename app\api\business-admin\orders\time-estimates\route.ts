import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Simple GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: "Time estimates API is working",
    timestamp: new Date().toISOString(),
    environment: {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
    }
  })
}

export async function PATCH(request: NextRequest) {
  try {
    // Create a Supabase admin client to have proper permissions
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Parse the request body
    let body
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError)
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      )
    }

    const { orderId, readyTime, preparationTime, sendNotification = true } = body

    console.log('Time estimates API called with:', {
      orderId,
      readyTime,
      preparationTime,
      sendNotification,
      bodyKeys: Object.keys(body),
      method: request.method,
      url: request.url
    })



    // Validate input
    if (!orderId) {
      return NextResponse.json({ error: "Order ID is required" }, { status: 400 })
    }

    if (!readyTime && preparationTime === undefined) {
      return NextResponse.json({ error: "Either ready time or preparation time must be provided" }, { status: 400 })
    }

    // Validate ready time format if provided
    if (readyTime) {
      const readyDateTime = new Date(readyTime)
      if (isNaN(readyDateTime.getTime())) {
        return NextResponse.json({ error: "Invalid ready time format" }, { status: 400 })
      }

      // Check if ready time is in the past
      const now = new Date()
      if (readyDateTime < now) {
        return NextResponse.json({ error: "Ready time cannot be in the past" }, { status: 400 })
      }

      // Check if ready time is too far in the future (more than 48 hours)
      const maxTime = new Date(now.getTime() + 48 * 60 * 60 * 1000)
      if (readyDateTime > maxTime) {
        return NextResponse.json({ error: "Ready time cannot be more than 48 hours in the future" }, { status: 400 })
      }
    }

    // Get the current order to calculate times
    const { data: currentOrder, error: fetchError } = await supabase
      .from("orders")
      .select("created_at, preparation_time, ready_time, business_id, delivery_type")
      .eq("id", orderId)
      .single()

    if (fetchError) {
      console.error('Error fetching order:', fetchError)
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }



    // Prepare update data
    let updateData: any = {}
    let calculatedPrepTime = preparationTime

    if (readyTime) {
      // If ready time is provided, calculate preparation time
      const orderCreatedAt = new Date(currentOrder.created_at)
      const readyDateTime = new Date(readyTime)

      // Calculate preparation time in minutes
      const timeDiffMs = readyDateTime.getTime() - orderCreatedAt.getTime()
      calculatedPrepTime = Math.max(1, Math.round(timeDiffMs / (1000 * 60)))



      updateData = {
        ready_time: readyTime,
        preparation_time: calculatedPrepTime
      }
    } else {
      // If preparation time is provided, calculate ready time
      const orderCreatedAt = new Date(currentOrder.created_at)
      const readyDateTime = new Date(orderCreatedAt.getTime() + preparationTime * 60000)



      updateData = {
        preparation_time: preparationTime,
        ready_time: readyDateTime.toISOString()
      }
      calculatedPrepTime = preparationTime
    }



    // Update the order
    const { data: order, error } = await supabase
      .from("orders")
      .update(updateData)
      .eq("id", orderId)
      .select("*")
      .single()

    if (error) {
      console.error("Error updating order preparation time:", error)
      return NextResponse.json(
        { error: "Failed to update order preparation time", details: error.message },
        { status: 500 }
      )
    }



    // If notification is requested, add a record to the notifications table
    if (sendNotification && order) {
      try {
        // Determine notification message based on delivery type
        let notificationMessage = ""

        if (order.delivery_type === "pickup") {
          notificationMessage = `Your order is being prepared and will be ready for pickup in ${calculatedPrepTime} minutes.`
        } else {
          notificationMessage = `Your order is being prepared and will be ready for our driver in ${calculatedPrepTime} minutes.`
        }

        // Insert notification
        const { error: notificationError } = await supabase
          .from("order_notifications")
          .insert({
            order_id: orderId,
            message: notificationMessage,
            type: "time_update",
            status: "pending",
            created_at: new Date().toISOString()
          })

        if (notificationError) {
          console.error("Error creating notification:", notificationError)
          // Don't fail the whole operation if just the notification fails
        }
      } catch (notificationError) {
        console.error("Error in notification creation:", notificationError)
        // Don't fail the whole operation if just the notification fails
      }
    }

    return NextResponse.json({
      success: true,
      message: "Order preparation time updated successfully",
      order
    })

  } catch (error: any) {
    console.error("Error in PATCH /api/business-admin/orders/time-estimates:", error)
    console.error("Error stack:", error?.stack)

    // Return a more detailed error response
    return NextResponse.json(
      {
        error: "An unexpected error occurred",
        details: error?.message || "Unknown error",
        type: error?.constructor?.name || "UnknownError",
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
