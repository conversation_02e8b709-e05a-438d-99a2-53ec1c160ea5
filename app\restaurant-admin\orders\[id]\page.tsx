"use client"

import { useState } from "react"
import React from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Clock,
  MapPin,
  Phone,
  Mail,
  CreditCard,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Truck,
  MessageSquare,
  Printer,
  MoreHorizontal,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"

// Mock order data
const orderData = {
  id: "JE-5289",
  customer: {
    name: "<PERSON>",
    address: "15 Beachfront, St Helier, JE2 3NG",
    phone: "+44 7911 123456",
    email: "<EMAIL>",
    avatar: "/thoughtful-brunette.png",
  },
  items: [
    { name: "Jersey Crab Cakes", quantity: 2, price: 9.95, options: [] },
    { name: "Grilled Sea Bass", quantity: 1, price: 22.95, options: ["French Fries", "Lemon Butter"] },
  ],
  subtotal: 42.85,
  deliveryFee: 2.5,
  serviceFee: 0.5,
  total: 45.85,
  status: "new",
  time: "Today at 14:32",
  paymentMethod: "Card",
  notes: "Please include extra sauce",
  orderHistory: [
    { status: "Order Placed", time: "Today at 14:32", description: "Customer placed order" },
    { status: "Payment Confirmed", time: "Today at 14:32", description: "Payment of £45.85 confirmed" },
  ],
}

// Status badge component
function OrderStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "new":
      return (
        <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
          <Clock className="mr-1 h-3 w-3" />
          New Order
        </Badge>
      )
    case "preparing":
      return (
        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
          <Clock className="mr-1 h-3 w-3" />
          Preparing
        </Badge>
      )
    case "out_for_delivery":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Truck className="mr-1 h-3 w-3" />
          Out for Delivery
        </Badge>
      )
    case "delivered":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle2 className="mr-1 h-3 w-3" />
          Delivered
        </Badge>
      )
    case "cancelled":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <XCircle className="mr-1 h-3 w-3" />
          Cancelled
        </Badge>
      )
    default:
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
          <AlertCircle className="mr-1 h-3 w-3" />
          Unknown
        </Badge>
      )
  }
}

export default function OrderDetailsPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)
  const [orderStatus, setOrderStatus] = useState(orderData.status)

  const handleStatusChange = (newStatus: string) => {
    setOrderStatus(newStatus)
    // In a real app, you would update the order status in the database here
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Link href="/restaurant-admin/orders" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Order #{unwrappedParams.id}</h1>
            <div className="flex items-center mt-1">
              <OrderStatusBadge status={orderStatus} />
              <span className="ml-3 text-sm text-gray-500">{orderData.time}</span>
            </div>
          </div>
        </div>

        <div className="mt-4 md:mt-0 flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Printer className="mr-2 h-4 w-4" />
                Print Receipt
              </DropdownMenuItem>
              <DropdownMenuItem>
                <MessageSquare className="mr-2 h-4 w-4" />
                Contact Customer
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <XCircle className="mr-2 h-4 w-4" />
                Cancel Order
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {orderStatus === "new" && (
            <>
              <Button variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                Reject Order
              </Button>
              <Button className="bg-emerald-600 hover:bg-emerald-700">Accept Order</Button>
            </>
          )}

          {orderStatus === "preparing" && (
            <Button className="bg-blue-600 hover:bg-blue-700">Mark Ready for Delivery</Button>
          )}

          {orderStatus === "out_for_delivery" && (
            <Button className="bg-green-600 hover:bg-green-700">Mark as Delivered</Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {orderData.items.map((item, index) => (
                  <div key={index} className="flex justify-between pb-4 border-b last:border-0 last:pb-0">
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium">{item.quantity}×</span>
                        <span className="ml-2">{item.name}</span>
                      </div>
                      {item.options.length > 0 && (
                        <div className="mt-1 text-sm text-gray-500">{item.options.join(", ")}</div>
                      )}
                    </div>
                    <div className="font-medium">£{(item.price * item.quantity).toFixed(2)}</div>
                  </div>
                ))}
              </div>

              {orderData.notes && (
                <div className="mt-6 p-3 bg-gray-50 rounded-md">
                  <p className="text-sm font-medium">Customer Notes:</p>
                  <p className="text-sm text-gray-600 mt-1">{orderData.notes}</p>
                </div>
              )}

              <div className="mt-6 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>£{orderData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Fee</span>
                  <span>£{orderData.deliveryFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Service Fee</span>
                  <span>£{orderData.serviceFee.toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>£{orderData.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Order Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="text-sm">
                  Current Status: <OrderStatusBadge status={orderStatus} />
                </div>
                <Select value={orderStatus} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Update Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New Order</SelectItem>
                    <SelectItem value="preparing">Preparing</SelectItem>
                    <SelectItem value="out_for_delivery">Out for Delivery</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                {orderData.orderHistory.map((event, index) => (
                  <div key={index} className="flex">
                    <div className="mr-4 relative">
                      <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                        <CheckCircle2 className="h-5 w-5 text-emerald-600" />
                      </div>
                      {index < orderData.orderHistory.length - 1 && (
                        <div className="absolute top-10 bottom-0 left-1/2 w-0.5 -ml-px bg-gray-200"></div>
                      )}
                    </div>
                    <div className="pb-4">
                      <p className="font-medium">{event.status}</p>
                      <p className="text-sm text-gray-500">{event.time}</p>
                      <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                    </div>
                  </div>
                ))}

                {/* Add Note Form */}
                <div className="mt-6">
                  <p className="text-sm font-medium mb-2">Add Internal Note</p>
                  <Textarea placeholder="Add a note about this order..." className="mb-2" />
                  <Button size="sm">Add Note</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customer Details */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start mb-4">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={orderData.customer.avatar || "/placeholder.svg"} alt={orderData.customer.name} />
                  <AvatarFallback>{orderData.customer.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{orderData.customer.name}</p>
                  <p className="text-sm text-gray-500">Customer since June 2023</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Delivery Address</p>
                    <p className="text-sm text-gray-600">{orderData.customer.address}</p>
                  </div>
                </div>

                <div className="flex">
                  <Phone className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Phone Number</p>
                    <p className="text-sm text-gray-600">{orderData.customer.phone}</p>
                  </div>
                </div>

                <div className="flex">
                  <Mail className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-gray-600">{orderData.customer.email}</p>
                  </div>
                </div>

                <div className="flex">
                  <CreditCard className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Payment Method</p>
                    <p className="text-sm text-gray-600">{orderData.paymentMethod}</p>
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" className="w-full">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Customer
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Order ID</span>
                  <span className="font-medium">{orderData.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Date & Time</span>
                  <span className="font-medium">{orderData.time}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Items</span>
                  <span className="font-medium">{orderData.items.reduce((acc, item) => acc + item.quantity, 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Payment Status</span>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Paid
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total Amount</span>
                  <span className="font-bold">£{orderData.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
