import { EnhancedOrdersTable, Order } from '@/components/orders/enhanced-orders-table'
import { Pagination } from '@/components/orders/pagination'

interface OrdersTabContentProps {
  orders: Order[]
  totalPages: number
  currentPage: number
  isLoading: boolean
  onViewOrder: (order: Order) => void
  onUpdateStatus: (order: Order, newStatus: string) => void
  onPrintOrder: (order: Order) => void
  onRefreshOrders: () => void
  onPageChange: (page: number) => void
}

export function OrdersTabContent({
  orders,
  totalPages,
  currentPage,
  isLoading,
  onViewOrder,
  onUpdateStatus,
  onPrintOrder,
  onRefreshOrders,
  onPageChange
}: OrdersTabContentProps) {
  return (
    <div className="space-y-4">
      <EnhancedOrdersTable
        orders={orders}
        onViewOrder={onViewOrder}
        onUpdateStatus={onUpdateStatus}
        onPrintOrder={onPrintOrder}
        onRefreshOrders={onRefreshOrders}
        isLoading={isLoading}
      />

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
        />
      )}
    </div>
  )
}
