'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Filter, X, Loader2, Clock } from 'lucide-react'
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
  Sheet<PERSON><PERSON>,
  She<PERSON><PERSON>ooter
} from '@/components/ui/sheet'
import FilterSidebar from './filter-sidebar'
import { useAttributeFilters, FilterOption } from '@/hooks/use-attribute-filters'
import { parseAttributeFilterId } from '@/services/attribute-service'

type FilterCompactProps = {
  businessType: string
  activeFilters: string[]
  onFilterChange: (filterId: string) => void
  onClearFilters: () => void
  priceRange: [number, number]
  onPriceRangeChange: (range: [number, number]) => void
  maxDeliveryTime: number
  onMaxDeliveryTimeChange: (time: number) => void
  maxPrepTime: number
  onMaxPrepTimeChange: (time: number) => void
  onTypeChange?: (type: string) => void
}

export default function FilterCompact({
  businessType,
  activeFilters,
  onFilterChange,
  onClearFilters,
  priceRange,
  onPriceRangeChange,
  maxDeliveryTime,
  onMaxDeliveryTimeChange,
  maxPrepTime,
  onMaxPrepTimeChange,
  onTypeChange
}: FilterCompactProps) {
  // Get common quick filters
  const commonQuickFilters = getCommonQuickFilters()

  // Get dynamic attribute filters based on business type
  const {
    quickFilters: attributeQuickFilters,
    loading: attributesLoading
  } = useAttributeFilters(businessType)

  // Combine common quick filters with attribute quick filters
  const quickFilters = [...commonQuickFilters, ...attributeQuickFilters]

  return (
    <div className="w-full">
      {/* Quick Filter Pills */}
      <div className="flex items-center gap-2 overflow-x-auto pb-2 scrollbar-hide">
        {/* Filter Button */}
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 bg-white border-gray-200 hover:bg-gray-50 transition-all whitespace-nowrap"
            >
              <Filter className="h-4 w-4" />
              Filters
              {activeFilters.length > 0 && (
                <Badge className="ml-1 bg-emerald-600 hover:bg-emerald-700 text-white text-xs">
                  {activeFilters.length}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[85vh] rounded-t-xl">
            <SheetHeader className="mb-4">
              <SheetTitle>Filters</SheetTitle>
            </SheetHeader>
            <div className="overflow-y-auto max-h-[calc(85vh-10rem)]">
              <FilterSidebar
                businessType={businessType}
                activeFilters={activeFilters}
                onFilterChange={onFilterChange}
                onClearFilters={onClearFilters}
                priceRange={priceRange}
                onPriceRangeChange={onPriceRangeChange}
                maxDeliveryTime={maxDeliveryTime}
                onMaxDeliveryTimeChange={onMaxDeliveryTimeChange}
                maxPrepTime={maxPrepTime}
                onMaxPrepTimeChange={onMaxPrepTimeChange}
                onTypeChange={onTypeChange}
              />
            </div>
            <SheetFooter className="mt-4">
              <SheetClose asChild>
                <Button className="w-full bg-emerald-600 hover:bg-emerald-700 text-white">
                  Apply Filters
                </Button>
              </SheetClose>
            </SheetFooter>
          </SheetContent>
        </Sheet>

        {/* Quick Filters */}
        {attributesLoading ? (
          <Button
            variant="outline"
            size="sm"
            className="bg-white border-gray-200 hover:bg-gray-50 transition-all whitespace-nowrap"
            disabled
          >
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            Loading...
          </Button>
        ) : (
          quickFilters.map((filter) => (
            <Button
              key={filter.id}
              variant={activeFilters.includes(filter.id) ? "default" : "outline"}
              size="sm"
              onClick={() => onFilterChange(filter.id)}
              className={`transition-all whitespace-nowrap ${
                activeFilters.includes(filter.id)
                  ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                  : "bg-white border-gray-200 hover:bg-gray-50"
              }`}
            >
              {filter.label}
            </Button>
          ))
        )}

        {/* Time Filter - Shows either Delivery Time or Prep Time based on mode */}
        <TimeFilterButton
          maxTime={maxDeliveryTime}
          onMaxTimeChange={onMaxDeliveryTimeChange}
        />
      </div>

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {activeFilters.map((filterId) => (
            <Badge
              key={filterId}
              variant="outline"
              className="bg-emerald-50 text-emerald-700 border-emerald-200 flex items-center gap-1"
            >
              {getFilterLabel(filterId, businessType)}
              <button
                onClick={() => onFilterChange(filterId)}
                className="ml-1 text-emerald-700 hover:text-emerald-900"
              >
                <X size={14} />
              </button>
            </Badge>
          ))}
          {activeFilters.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-gray-500 hover:text-gray-700 h-6 px-2"
            >
              Clear all
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

// Helper function to get common quick filters
function getCommonQuickFilters(): FilterOption[] {
  return [
    { id: 'free_delivery', label: 'Free Delivery', type: 'offer', value: 'free_delivery' },
    { id: 'deals', label: 'Deals', type: 'offer', value: 'deals' },
    { id: 'rating_4.5', label: 'Top Rated', type: 'rating', value: '4.5+' }
  ]
}

// Helper function to get filter label
function getFilterLabel(filterId: string, businessType: string): string {
  // First check if it's an attribute filter
  const attributeFilter = parseAttributeFilterId(filterId);
  if (attributeFilter) {
    return attributeFilter.value;
  }

  // Otherwise, check common filters
  const filterMap: Record<string, string> = {
    'free_delivery': 'Free Delivery',
    'deals': 'Deals',
    'discounts': 'Discounts',
    'rating_4.5': '4.5+',
    'rating_4': '4.0+',
    'rating_3.5': '3.5+'
  }

  return filterMap[filterId] || filterId;
}

// Time Filter Button component for max delivery time
function TimeFilterButton({
  maxTime,
  onMaxTimeChange
}: {
  maxTime: number,
  onMaxTimeChange: (time: number) => void
}) {
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState(maxTime.toString())

  // Update the input value when maxTime changes
  useEffect(() => {
    setInputValue(maxTime.toString())
  }, [maxTime])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  // Handle input blur - apply the filter and exit edit mode
  const handleInputBlur = () => {
    const value = parseInt(inputValue)
    if (!isNaN(value) && value > 0) {
      onMaxTimeChange(value)
    } else {
      // Reset to current maxTime if invalid
      setInputValue(maxTime.toString())
    }
    setIsEditing(false)
  }

  // Handle key press - apply on Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur()
    } else if (e.key === 'Escape') {
      setInputValue(maxTime.toString())
      setIsEditing(false)
    }
  }

  if (isEditing) {
    return (
      <div className="inline-flex items-center bg-white border border-gray-300 rounded-md px-3 h-9">
        <input
          type="number"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          min="1"
          max="60"
          className="w-12 h-7 px-0 text-sm text-center focus:outline-none"
          autoFocus
        />
        <span className="ml-1 text-xs text-gray-600">mins</span>
      </div>
    )
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setIsEditing(true)}
      className="bg-white border-gray-200 hover:bg-gray-50 transition-all whitespace-nowrap"
    >
      <Clock className="h-3 w-3 mr-1" />
      Max. Time: {maxTime}
    </Button>
  )
}
