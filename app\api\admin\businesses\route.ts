import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { verifyAdminAccess } from "@/lib/simple-auth"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting admin businesses API request")

    // Verify admin access using simple auth
    const authResult = await verifyAdminAccess(request);

    if (!authResult.authorized) {
      return NextResponse.json(
        { error: authResult.error || "Authentication failed" },
        { status: authResult.status || 401 }
      );
    }

    console.log("Admin access verified")

    // Fetch all businesses
    const { data: businesses, error, status } = await adminClient
      .from("businesses")
      .select('id, slug, name, address, postcode');

    if (error) {
      console.error("Error fetching businesses:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      businesses,
      error: null,
      status
    })
  } catch (error: any) {
    console.error("Unexpected error in businesses API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
