// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function finalCategoryCleanup() {
  try {
    console.log('=== FINAL CATEGORY CLEANUP ===\n');

    // Categories to remove based on user request
    const categoriesToRemove = [
      { name: 'Prescription', id: 202 },
      { name: 'Prescription Medications', id: 274 },
      { name: 'Airport Runs', id: 302 }
    ];

    console.log('🗑️  Categories to remove:');
    categoriesToRemove.forEach(cat => {
      console.log(`   ❌ ${cat.name} (ID: ${cat.id})`);
    });

    // First, remove any business category subscriptions for these categories
    console.log('\n🔄 Removing business category subscriptions...');
    
    const categoryIds = categoriesToRemove.map(cat => cat.id);
    
    const { data: existingSubscriptions, error: checkError } = await supabase
      .from('business_categories')
      .select('business_id, category_id')
      .in('category_id', categoryIds);

    if (checkError) {
      console.error('Error checking existing subscriptions:', checkError);
      return;
    }

    if (existingSubscriptions && existingSubscriptions.length > 0) {
      console.log(`Found ${existingSubscriptions.length} subscriptions to remove:`);
      existingSubscriptions.forEach(sub => {
        const catName = categoriesToRemove.find(c => c.id === sub.category_id)?.name;
        console.log(`   • Business ${sub.business_id} -> ${catName}`);
      });

      const { error: deleteSubError } = await supabase
        .from('business_categories')
        .delete()
        .in('category_id', categoryIds);

      if (deleteSubError) {
        console.error('Error removing subscriptions:', deleteSubError);
        return;
      }
      console.log('✅ Business subscriptions removed');
    } else {
      console.log('✅ No existing subscriptions found');
    }

    // Remove the categories
    console.log('\n🗑️  Removing categories...');
    
    const { error: deleteCatError } = await supabase
      .from('categories')
      .delete()
      .in('id', categoryIds);

    if (deleteCatError) {
      console.error('Error removing categories:', deleteCatError);
      return;
    }

    console.log('✅ Categories removed successfully');

    // Update British Cuisine icon to pie
    console.log('\n🥧 Updating British Cuisine icon to pie...');
    
    const { error: updateIconError } = await supabase
      .from('categories')
      .update({ 
        icon: 'pie-chart', // Using pie-chart as closest to pie icon
        updated_at: new Date().toISOString()
      })
      .eq('name', 'British Cuisine');

    if (updateIconError) {
      console.error('Error updating British Cuisine icon:', updateIconError);
    } else {
      console.log('✅ British Cuisine icon updated to pie');
    }

    // Show final category count
    const { data: finalCategories, error: countError } = await supabase
      .from('categories')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('Error counting final categories:', countError);
    } else {
      console.log(`\n📊 Final category count: ${finalCategories.length}`);
    }

    // Show remaining categories by type
    console.log('\n📋 REMAINING CATEGORIES BY TYPE');
    console.log('='.repeat(50));

    const { data: remainingCategories, error: remainingError } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        icon,
        business_type_id,
        business_types!business_type_id (
          name
        )
      `)
      .order('business_type_id')
      .order('name');

    if (remainingError) {
      console.error('Error fetching remaining categories:', remainingError);
    } else {
      const remainingByType = {};
      remainingCategories.forEach(cat => {
        const typeName = cat.business_types?.name || 'Unknown';
        if (!remainingByType[typeName]) {
          remainingByType[typeName] = [];
        }
        remainingByType[typeName].push(cat);
      });

      Object.keys(remainingByType).forEach(typeName => {
        console.log(`\n🏢 ${typeName} (${remainingByType[typeName].length} categories):`);
        remainingByType[typeName].forEach(cat => {
          const icon = cat.icon ? `${cat.icon}` : 'no-icon';
          console.log(`   ✅ ${cat.name} (${cat.slug}) - Icon: ${icon} - ID: ${cat.id}`);
        });
      });
    }

    // Check if any businesses need new category subscriptions after removing prescription categories
    console.log('\n🔍 Checking if pharmacy businesses need alternative categories...');
    
    const { data: pharmacyBusinesses, error: pharmacyError } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        business_type_id,
        business_types!business_type_id (
          name
        )
      `)
      .eq('is_approved', true);

    if (pharmacyError) {
      console.error('Error fetching pharmacy businesses:', pharmacyError);
    } else {
      const pharmacies = pharmacyBusinesses.filter(b => b.business_types?.name === 'Pharmacy');
      
      if (pharmacies.length > 0) {
        console.log(`Found ${pharmacies.length} pharmacy businesses:`);
        
        for (const pharmacy of pharmacies) {
          // Check current subscriptions
          const { data: currentSubs, error: subError } = await supabase
            .from('business_categories')
            .select(`
              category_id,
              categories!category_id (
                name
              )
            `)
            .eq('business_id', pharmacy.id);

          if (subError) {
            console.error(`Error checking subscriptions for ${pharmacy.name}:`, subError);
            continue;
          }

          console.log(`\n   📍 ${pharmacy.name}:`);
          if (currentSubs && currentSubs.length > 0) {
            currentSubs.forEach(sub => {
              console.log(`      • ${sub.categories?.name}`);
            });
          } else {
            console.log(`      ⚠️  No category subscriptions found - may need manual assignment`);
          }
        }
      }
    }

  } catch (error) {
    console.error('Error in final cleanup:', error);
  }
}

finalCategoryCleanup()
  .then(() => {
    console.log('\n✨ Final category cleanup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Final cleanup failed:', error);
    process.exit(1);
  });
