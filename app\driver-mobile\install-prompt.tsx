"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function InstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)

  useEffect(() => {
    // Check if the app is already installed
    const isAppInstalled = window.matchMedia("(display-mode: standalone)").matches

    if (isAppInstalled) {
      return
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Stash the event so it can be triggered later
      setDeferredPrompt(e)
      // Show the install prompt
      setShowPrompt(true)
    }

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
    }
  }, [])

  const handleInstall = () => {
    if (!deferredPrompt) return

    // Show the install prompt
    deferredPrompt.prompt()

    // Wait for the user to respond to the prompt
    deferredPrompt.userChoice.then((choiceResult: { outcome: string }) => {
      if (choiceResult.outcome === "accepted") {
        console.log("User accepted the install prompt")
      } else {
        console.log("User dismissed the install prompt")
      }
      // Clear the saved prompt since it can't be used again
      setDeferredPrompt(null)
      setShowPrompt(false)
    })
  }

  const dismissPrompt = () => {
    setShowPrompt(false)
    // Save to localStorage to prevent showing again for some time
    localStorage.setItem("installPromptDismissed", Date.now().toString())
  }

  if (!showPrompt) return null

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 px-4 pb-4">
      <Card className="border-2 border-emerald-100 shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-bold">Install Jersey Eats Driver</h3>
            <Button variant="ghost" size="icon" onClick={dismissPrompt} className="h-6 w-6 -mr-1 -mt-1">
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Install this app on your device for the best experience with offline support and quick access.
          </p>
          <div className="flex space-x-2">
            <Button variant="outline" className="flex-1 text-sm" onClick={dismissPrompt}>
              Not Now
            </Button>
            <Button className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-sm" onClick={handleInstall}>
              Install
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
