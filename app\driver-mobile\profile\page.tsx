"use client"

import { useState, useEffect } from 'react'
import { User, Star, Package, Clock, MapPin, DollarSign, Settings, LogOut, Monitor } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DriverDashboardHeader } from '@/components/driver-dashboard-header'
import { DriverMobileNavigation } from '@/components/driver-mobile-navigation'
import { DriverMobileContainer } from '@/components/driver-mobile-container'
import Link from 'next/link'

interface DriverProfileData {
  driver: {
    id: string
    authId: string
    name: string
    email: string
    phone: string
    isVerified: boolean
    isActive: boolean
    vehicleType: string
    totalDeliveries: number
    averageRating: number
    memberSince: string
    averageDeliveriesPerDay: number
  }
  stats: {
    todayDeliveries: number
    totalDeliveries: number
    averageRating: number
  }
  earnings: {
    today: number
    thisWeek: number
    currency: string
  }
}

export default function DriverMobileProfilePage() {
  const [profileData, setProfileData] = useState<DriverProfileData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const fetchProfileData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/driver/dashboard')

      if (!response.ok) {
        throw new Error(`Failed to fetch profile data: ${response.statusText}`)
      }

      const data = await response.json()
      setProfileData(data)
    } catch (err) {
      console.error('Error fetching profile data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load profile data')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchProfileData()
  }, [])

  if (isLoading) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
          </div>
        </div>
      </DriverMobileContainer>
    )
  }

  if (error) {
    return (
      <DriverMobileContainer>
        <DriverDashboardHeader />
        <div className="p-4">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6 text-center">
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={fetchProfileData} variant="outline" className="border-red-200 text-red-700 hover:bg-red-100">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </DriverMobileContainer>
    )
  }

  if (!profileData) {
    return null
  }

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      <div className="p-4 space-y-4">
          {/* Driver Info Card */}
          <Card className="bg-white border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center">
                  <User className="h-8 w-8 text-emerald-600" />
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-gray-900">{profileData.driver.name}</h2>
                  <p className="text-sm text-gray-600">Driver ID: {profileData.driver.authId?.slice(-6) || profileData.driver.id.slice(-6)}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge
                      variant="outline"
                      className={profileData.driver.isVerified ? "bg-green-50 text-green-700 border-green-200" : "bg-yellow-50 text-yellow-700 border-yellow-200"}
                    >
                      {profileData.driver.isVerified ? "Verified" : "Pending"}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={profileData.driver.isActive ? "bg-blue-50 text-blue-700 border-blue-200" : "bg-gray-50 text-gray-700 border-gray-200"}
                    >
                      {profileData.driver.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Vehicle Type:</span>
                  <span className="font-medium text-gray-900">{profileData.driver.vehicleType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Member Since:</span>
                  <span className="font-medium text-gray-900">
                    {new Date(profileData.driver.memberSince).toLocaleDateString('en-GB')}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Performance */}
          <Card className="bg-gradient-to-r from-emerald-50 to-blue-50 border-emerald-200">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Today's Performance</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-700">
                    {formatCurrency(profileData.earnings.today)}
                  </div>
                  <div className="text-xs text-emerald-600 font-medium">Earnings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-700">{profileData.stats.todayDeliveries}</div>
                  <div className="text-xs text-blue-600 font-medium">Deliveries</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Stats */}
          <Card className="bg-white border-gray-200">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Performance</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold text-gray-900">{profileData.driver.totalDeliveries}</div>
                  <div className="text-xs text-gray-600">Total Deliveries</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-gray-900">
                    {profileData.driver.averageRating ? profileData.driver.averageRating.toFixed(1) : 'N/A'}
                  </div>
                  <div className="text-xs text-gray-600">Rating</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-gray-900">{profileData.driver.averageDeliveriesPerDay}</div>
                  <div className="text-xs text-gray-600">Avg/Day</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Actions */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900">Account</h3>

            <Button
              variant="outline"
              className="w-full justify-start h-12 border-gray-200 hover:bg-gray-50"
              asChild
            >
              <Link href="/driver-mobile/settings">
                <Settings className="h-5 w-5 mr-3 text-gray-600" />
                <span>Settings</span>
              </Link>
            </Button>

            {/* Desktop Dashboard Link */}
            <Button
              variant="outline"
              className="w-full justify-start h-12 border-blue-200 text-blue-700 hover:bg-blue-50"
              asChild
            >
              <Link href="/driver" target="_blank" rel="noopener noreferrer">
                <Monitor className="h-5 w-5 mr-3" />
                <span>Desktop Dashboard</span>
              </Link>
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start h-12 border-red-200 text-red-700 hover:bg-red-50"
              disabled
            >
              <LogOut className="h-5 w-5 mr-3" />
              <span>Sign Out (Coming Soon)</span>
            </Button>
          </div>

          {/* Bottom Spacing for Navigation */}
          <div className="h-20"></div>
        </div>

        <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
