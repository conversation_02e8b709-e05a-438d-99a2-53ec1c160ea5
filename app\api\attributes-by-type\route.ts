import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create Supabase client with service role key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Simple in-memory cache for API responses
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const businessType = searchParams.get('businessType') || 'all';

    // Check cache first
    const cacheKey = `attributes-${businessType}`;
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log(`Returning cached attributes for business type: ${businessType}`);
      return NextResponse.json(cached.data);
    }

    console.log(`Fetching attributes for business type: ${businessType}`);

    let allAttributes: any[] = [];

    if (businessType === 'all') {
      // Use parallel queries for better performance
      const [businessResult, productResult] = await Promise.all([
        supabase.from('business_attributes').select('attribute_type, attribute_value'),
        supabase.from('product_attributes').select('attribute_type, attribute_value')
      ]);

      if (businessResult.error) {
        console.error('Error fetching business attributes:', businessResult.error);
      }
      if (productResult.error) {
        console.error('Error fetching product attributes:', productResult.error);
      }

      allAttributes = [
        ...(businessResult.data || []),
        ...(productResult.data || [])
      ];
    } else {
      // Use optimized approach for specific business types
      allAttributes = await getAttributesByBusinessType(supabase, businessType);
    }

    // Group by attribute_type and deduplicate values
    const attributeMap = new Map<string, Set<string>>();

    allAttributes.forEach(attr => {
      if (!attributeMap.has(attr.attribute_type)) {
        attributeMap.set(attr.attribute_type, new Set());
      }
      attributeMap.get(attr.attribute_type)!.add(attr.attribute_value);
    });

    // Convert back to the expected format
    const deduplicatedAttributes = Array.from(attributeMap.entries()).map(([type, values]) => ({
      attribute_type: type,
      values: Array.from(values).sort()
    }));

    console.log(`Found ${allAttributes.length} total attributes`);
    console.log(`After deduplication: ${deduplicatedAttributes.length} unique attribute types`);

    const responseData = {
      attributes: deduplicatedAttributes,
      businessType,
      // Keep original format for backward compatibility
      businessAttributes: allAttributes.filter(attr => attr.source !== 'product'),
      productAttributes: allAttributes.filter(attr => attr.source === 'product')
    };

    // Cache the response
    cache.set(cacheKey, { data: responseData, timestamp: Date.now() });

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error in attributes-by-type API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch business attributes' },
      { status: 500 }
    );
  }
}

// Optimized function to get attributes by business type
async function getAttributesByBusinessType(supabase: any, businessType: string): Promise<any[]> {
  try {
    // Get business type ID first
    const { data: businessTypeData, error: businessTypeError } = await supabase
      .from('business_types')
      .select('id')
      .eq('slug', businessType)
      .single();

    if (businessTypeError || !businessTypeData) {
      console.log(`Business type ${businessType} not found`);
      return [];
    }

    // Get business IDs for this type
    const { data: businesses, error: businessesError } = await supabase
      .from('businesses')
      .select('id')
      .eq('business_type_id', businessTypeData.id);

    if (businessesError || !businesses || businesses.length === 0) {
      console.log(`No businesses found for type ${businessType}`);
      return [];
    }

    const businessIds = businesses.map(b => b.id);

    // Use parallel queries to get both business and product attributes
    const [businessAttrResult, productAttrResult] = await Promise.all([
      // Business attributes for these businesses
      supabase
        .from('business_attributes')
        .select('attribute_type, attribute_value')
        .in('business_id', businessIds),

      // Product attributes for products from these businesses
      supabase
        .from('product_attributes')
        .select('attribute_type, attribute_value')
        .in('product_id',
          supabase
            .from('products')
            .select('id')
            .in('business_id', businessIds)
        )
    ]);

    if (businessAttrResult.error) {
      console.error('Error fetching business attributes:', businessAttrResult.error);
    }
    if (productAttrResult.error) {
      console.error('Error fetching product attributes:', productAttrResult.error);
    }

    return [
      ...(businessAttrResult.data || []),
      ...(productAttrResult.data || [])
    ];
  } catch (error) {
    console.error('Error in getAttributesByBusinessType:', error);
    return [];
  }
}
