import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to get the current user from request
async function getCurrentUser(request: NextRequest) {
  let user = null;
  
  // First try to get the user from the Authorization header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    
    // Create a Supabase client with the token
    const supabaseClientWithToken = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );
    
    // Get the user from the token
    const { data: userData, error: userError } = await supabaseClientWithToken.auth.getUser();
    
    if (!userError && userData?.user) {
      user = userData.user;
    }
  }
  
  // If we couldn't get the user from the Authorization header, try cookies
  if (!user) {
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: userData } = await supabaseClient.auth.getUser();
    user = userData?.user;
  }
  
  return user;
}

// Helper function to get the user's cart ID
async function getUserCartId(userId: string) {
  // Get the user's ID from the users table using auth_id
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('auth_id', userId)
    .single();

  if (userError) {
    console.error('Error fetching user ID:', userError);
    return { error: `Failed to fetch user ID: ${userError.message}` };
  }

  if (!userData) {
    console.error('User not found in database');
    return { error: 'User not found in database' };
  }

  // Get the user's cart
  const { data: userCart, error: cartError } = await supabase
    .from('user_carts')
    .select('id')
    .eq('user_id', userData.id)
    .single();

  if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
    console.error('Error fetching user cart:', cartError);
    return { error: `Failed to fetch user cart: ${cartError.message}` };
  }

  // If no cart exists, return error
  if (!userCart) {
    return { error: 'No cart found for user' };
  }

  return { cartId: userCart.id };
}

// DELETE endpoint to remove an item from the cart
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const itemId = params.id;

    // Get the current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the user's cart ID
    const cartResult = await getUserCartId(user.id);
    if ('error' in cartResult) {
      return NextResponse.json(
        { error: cartResult.error },
        { status: 404 }
      );
    }

    // Delete the item from the cart
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', itemId)
      .eq('cart_id', cartResult.cartId);

    if (error) {
      console.error('Error deleting cart item:', error);
      return NextResponse.json(
        { error: 'Failed to delete cart item', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Item removed from cart'
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// PUT endpoint to update an item's quantity
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const itemId = params.id;

    // Get the current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the user's cart ID
    const cartResult = await getUserCartId(user.id);
    if ('error' in cartResult) {
      return NextResponse.json(
        { error: cartResult.error },
        { status: 404 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const quantity = body.quantity;

    if (typeof quantity !== 'number' || quantity < 1) {
      return NextResponse.json(
        { error: 'Invalid quantity. Must be a positive number.' },
        { status: 400 }
      );
    }

    // Update the item quantity
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity, updated_at: new Date().toISOString() })
      .eq('id', itemId)
      .eq('cart_id', cartResult.cartId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating cart item:', error);
      return NextResponse.json(
        { error: 'Failed to update cart item', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Item quantity updated',
      item: {
        id: data.id,
        productId: data.product_id,
        quantity: data.quantity,
        price: data.price
      }
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
