"use client"

import { Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function SearchForm() {
  return (
    <form className="flex flex-col sm:flex-row gap-4" onSubmit={(e) => e.preventDefault()}>
      <div className="relative flex-grow">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <Input placeholder="Enter your postcode" className="pl-10 h-12 bg-white text-black w-full" />
      </div>
      <Button 
        type="submit" 
        className="bg-emerald-600 hover:bg-emerald-700 h-12"
        onClick={() => alert('Searching for nearby restaurants and shops...')}
      >
        Find Nearby
      </Button>
    </form>
  )
}
