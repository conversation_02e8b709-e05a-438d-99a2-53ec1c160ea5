'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

export default function SupabaseExample() {
  const [restaurants, setRestaurants] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [envStatus, setEnvStatus] = useState<{url: boolean, key: boolean}>({ url: false, key: false });

  // Initialize Supabase client with client-side environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

  // Check if environment variables are set
  useEffect(() => {
    setEnvStatus({
      url: !!supabaseUrl,
      key: !!supabaseAnonKey
    });
  }, [supabaseUrl, supabase<PERSON>nonKey]);

  // Only create the client if we have the required values
  const supabase = supabaseUrl && supabaseAnonKey ? createClient(supabaseUrl, supabaseAnonKey) : null;

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Check if Supabase client is initialized
        if (!supabase) {
          throw new Error('Supabase client not initialized. Missing environment variables.');
        }

        // Try to fetch users first
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*');

        if (userError) {
          console.warn('Error fetching users:', userError);
        }

        // Then try to fetch restaurants
        const { data: restaurantData, error: restaurantError } = await supabase
          .from('restaurants')
          .select('*');

        if (restaurantError) {
          throw restaurantError;
        }

        // Set the restaurants data
        setRestaurants(restaurantData || []);

        // If we have user data, add it to the restaurants array for display
        if (userData && userData.length > 0) {
          console.log('Found user data:', userData.length, 'users');
          setRestaurants(prev => [
            ...prev,
            {
              id: 'user-data',
              name: 'User Data Found',
              description: `Found ${userData.length} users in the database`,
              location: 'auth.users'
            }
          ]);
        }
      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError(error.message || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [supabase]);

  // Show environment status regardless of loading state
  if (!envStatus.url || !envStatus.key) {
    return (
      <div className="p-4">
        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 mb-4">
          <h2 className="text-xl font-bold mb-2">Missing Supabase Configuration</h2>
          <p className="mb-2">The Supabase client could not be initialized because environment variables are missing:</p>
          <ul className="list-disc ml-5">
            <li>NEXT_PUBLIC_SUPABASE_URL: {envStatus.url ? '✅ Set' : '❌ Missing'}</li>
            <li>NEXT_PUBLIC_SUPABASE_ANON_KEY: {envStatus.key ? '✅ Set' : '❌ Missing'}</li>
          </ul>
          <p className="mt-4">Please add these environment variables to your project:</p>
          <ol className="list-decimal ml-5 mt-2">
            <li>Create a <code className="bg-yellow-100 px-1 rounded">.env.local</code> file in your project root</li>
            <li>Add the Supabase URL and anon key to the file</li>
            <li>Restart your development server</li>
            <li>For deployment, add these as environment variables in your hosting platform</li>
          </ol>
        </div>
        <p>
          <a href="/supabase-setup" className="text-blue-500 hover:underline">View the Supabase setup guide</a>
        </p>
      </div>
    );
  }

  if (loading) {
    return <div className="p-4">Loading restaurants...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700 mb-4">
          <h2 className="font-bold mb-2">Error connecting to Supabase</h2>
          <p>{error}</p>
        </div>
        <p>
          <a href="/supabase-debug" className="text-blue-500 hover:underline">Debug Supabase connection</a>
        </p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Restaurants from Supabase</h2>

      {restaurants.length === 0 ? (
        <p>No restaurants found. Please add some to your Supabase database.</p>
      ) : (
        <ul className="space-y-2">
          {restaurants.map((restaurant) => (
            <li key={restaurant.id} className="border p-3 rounded">
              <h3 className="font-semibold">{restaurant.name}</h3>
              <p className="text-sm text-gray-600">{restaurant.description}</p>
              <div className="text-sm mt-1">
                <span className="bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full text-xs">
                  {restaurant.location}
                </span>
              </div>
            </li>
          ))}
        </ul>
      )}

      <div className="mt-6 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Database Connection Info</h3>
        <p className="text-sm">
          Connected to Supabase project: <code className="bg-gray-200 px-1 py-0.5 rounded">{supabaseUrl}</code>
        </p>
        <p className="text-sm mt-2">
          User account found: <code className="bg-gray-200 px-1 py-0.5 rounded">{restaurants.some(r => r.id) ? 'Yes' : 'No'}</code>
        </p>
      </div>
    </div>
  );
}
