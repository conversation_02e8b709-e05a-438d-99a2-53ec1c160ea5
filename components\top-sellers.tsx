import { Users } from "lucide-react"

interface TopSellersProps {
  metric: "revenue" | "orders"
}

export function TopSellers({ metric }: TopSellersProps) {
  const revenueData = [
    { name: "Tech Galaxy", value: "£8,450", count: "245 orders" },
    { name: "Gadget Hub", value: "£6,320", count: "186 orders" },
    { name: "Digital World", value: "£5,780", count: "164 orders" },
    { name: "Smart Store", value: "£4,920", count: "142 orders" },
    { name: "Future Tech", value: "£3,650", count: "98 orders" },
  ]

  const ordersData = [
    { name: "Tech Galaxy", value: "245", count: "£8,450 revenue" },
    { name: "Gadget Hub", value: "186", count: "£6,320 revenue" },
    { name: "Digital World", value: "164", count: "£5,780 revenue" },
    { name: "Smart Store", value: "142", count: "£4,920 revenue" },
    { name: "Future Tech", value: "98", count: "£3,650 revenue" },
  ]

  const data = metric === "revenue" ? revenueData : ordersData

  return (
    <div className="space-y-8">
      {data.map((item, index) => (
        <div key={index} className="flex items-center">
          <div className="flex h-9 w-9 items-center justify-center rounded-full bg-emerald-50">
            <Users className="h-5 w-5 text-emerald-500" />
          </div>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{item.name}</p>
            <p className="text-sm text-muted-foreground">{item.count}</p>
          </div>
          <div className="ml-auto font-medium">{item.value}</div>
        </div>
      ))}
    </div>
  )
}
