"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, Clock, AlertTriangle, Eye } from "lucide-react"
import { useAuth } from "@/context/unified-auth-context"
import { supabase } from "@/lib/supabase"
import Link from "next/link"

export default function BusinessApprovalsPage() {
  const router = useRouter()
  const { user, userProfile } = useAuth()
  const [pendingBusinesses, setPendingBusinesses] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  useEffect(() => {
    // Check if user is admin
    if (user && userProfile && !userProfile.role?.includes('admin')) {
      router.push('/')
      return
    }

    const fetchPendingBusinesses = async () => {
      try {
        // Fetch pending business registrations
        const { data: registrations, error: registrationsError } = await supabase
          .from('business_registrations')
          .select('*, users(name, email)')
          .eq('status', 'pending')
          .order('created_at', { ascending: false })

        if (registrationsError) {
          throw registrationsError
        }

        // Fetch businesses that are not approved
        const { data: businesses, error: businessesError } = await supabase
          .from('businesses')
          .select('*, business_types(name), business_managers(user_id, users(name, email))')
          .eq('is_approved', false)
          .order('created_at', { ascending: false })

        if (businessesError) {
          throw businessesError
        }

        // Combine both sets of data
        const allPending = [
          ...(registrations || []).map((reg: any) => ({
            ...reg,
            source: 'registration',
            userName: reg.users?.name,
            userEmail: reg.users?.email,
            businessTypeName: null // We'll need to fetch this separately
          })),
          ...(businesses || []).map((bus: any) => ({
            ...bus,
            source: 'business',
            userName: bus.business_managers?.[0]?.users?.name,
            userEmail: bus.business_managers?.[0]?.users?.email,
            businessTypeName: bus.business_types?.name
          }))
        ]

        // Fetch business types for registrations
        if (registrations && registrations.length > 0) {
          const businessTypeIds = [...new Set(registrations.map((reg: any) => reg.business_type_id))]

          const { data: businessTypes, error: typesError } = await supabase
            .from('business_types')
            .select('id, name')
            .in('id', businessTypeIds)

          if (!typesError && businessTypes) {
            // Create a map of business type id to name
            const typeMap = businessTypes.reduce((map: any, type: any) => {
              map[type.id] = type.name
              return map
            }, {})

            // Update the business type names in the pending businesses
            allPending.forEach((item: any) => {
              if (item.source === 'registration') {
                item.businessTypeName = typeMap[item.business_type_id] || 'Unknown'
              }
            })
          }
        }

        setPendingBusinesses(allPending)
      } catch (err: any) {
        console.error('Error fetching pending businesses:', err)
        setError(err.message || 'Failed to load pending businesses')
      } finally {
        setIsLoading(false)
      }
    }

    if (user && userProfile?.role?.includes('admin')) {
      fetchPendingBusinesses()
    }
  }, [user, userProfile, router])

  const handleApprove = async (item: any) => {
    try {
      if (item.source === 'registration') {
        // Create a business from the registration
        const slug = item.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')

        // Insert the business record
        const { data: businessRecord, error: businessError } = await supabase
          .from('businesses')
          .insert({
            name: item.name,
            slug: slug,
            description: item.description || '',
            address: item.address,
            location: item.postcode,
            phone: item.phone,
            business_type_id: item.business_type_id,
            delivery_fee: item.delivery_fee,
            minimum_order_amount: item.minimum_order_amount,
            is_approved: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()

        if (businessError) {
          throw businessError
        }

        if (businessRecord && businessRecord.length > 0) {
          const businessId = businessRecord[0].id

          // Create a business manager record
          const { error: managerError } = await supabase
            .from('business_managers')
            .insert({
              user_id: item.user_id,
              business_id: businessId,
              is_primary: true,
              is_approved: true
            })

          if (managerError) {
            throw managerError
          }

          // Update the registration status
          const { error: updateError } = await supabase
            .from('business_registrations')
            .update({ status: 'approved', updated_at: new Date().toISOString() })
            .eq('id', item.id)

          if (updateError) {
            throw updateError
          }

          // Generate coordinates for the business
          if (businessRecord && businessRecord.length > 0 && businessRecord[0].address) {
            try {
              const { geocodeAndUpdateBusinessCoordinates } = await import('@/lib/address-utils')
              const { success, coordinates } = await geocodeAndUpdateBusinessCoordinates(
                businessRecord[0].id.toString(),
                businessRecord[0].address,
                businessRecord[0].postcode || item.postcode
              )

              if (success) {
                console.log("Business coordinates updated successfully:", coordinates)
              } else {
                console.warn("Failed to update business coordinates, but business was approved")
              }
            } catch (geocodeError) {
              console.error("Error geocoding business address:", geocodeError)
              // Continue anyway, as the business was approved
            }
          }

          // Copy categories if any
          const { data: categories, error: categoriesError } = await supabase
            .from('registration_categories')
            .select('*')
            .eq('registration_id', item.id)

          if (!categoriesError && categories && categories.length > 0) {
            const categoryInserts = categories.map((cat: any) => ({
              business_id: businessId,
              category_id: cat.category_id,
              is_primary: cat.is_primary
            }))

            await supabase
              .from('business_categories')
              .insert(categoryInserts)
          }
        }
      } else if (item.source === 'business') {
        // Update the business approval status
        const { error: businessError } = await supabase
          .from('businesses')
          .update({ is_approved: true, updated_at: new Date().toISOString() })
          .eq('id', item.id)

        if (businessError) {
          throw businessError
        }

        // Update the business manager approval status
        const { error: managerError } = await supabase
          .from('business_managers')
          .update({ is_approved: true })
          .eq('business_id', item.id)

        if (managerError) {
          throw managerError
        }
      }

      // Refresh the list
      setPendingBusinesses(current => current.filter((bus: any) =>
        bus.source !== item.source || bus.id !== item.id
      ))

      setSuccessMessage(`${item.name} has been approved successfully`)

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null)
      }, 3000)
    } catch (err: any) {
      console.error('Error approving business:', err)
      setError(err.message || 'Failed to approve business')

      // Clear error after 3 seconds
      setTimeout(() => {
        setError(null)
      }, 3000)
    }
  }

  const handleReject = async (item: any) => {
    try {
      if (item.source === 'registration') {
        // Update the registration status
        const { error: updateError } = await supabase
          .from('business_registrations')
          .update({ status: 'rejected', updated_at: new Date().toISOString() })
          .eq('id', item.id)

        if (updateError) {
          throw updateError
        }
      } else if (item.source === 'business') {
        // Delete the business
        const { error: businessError } = await supabase
          .from('businesses')
          .delete()
          .eq('id', item.id)

        if (businessError) {
          throw businessError
        }
      }

      // Refresh the list
      setPendingBusinesses(current => current.filter((bus: any) =>
        bus.source !== item.source || bus.id !== item.id
      ))

      setSuccessMessage(`${item.name} has been rejected`)

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null)
      }, 3000)
    } catch (err: any) {
      console.error('Error rejecting business:', err)
      setError(err.message || 'Failed to reject business')

      // Clear error after 3 seconds
      setTimeout(() => {
        setError(null)
      }, 3000)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-emerald-600"></div>
          <p className="mt-6 text-lg text-gray-600">Loading pending business approvals...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container-fluid py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Approvals</h1>
          <p className="text-gray-600 mt-2">Review and approve business registrations</p>
        </div>
        <Link href="/admin/dashboard">
          <Button variant="outline">Back to Dashboard</Button>
        </Link>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
        </Alert>
      )}

      {pendingBusinesses.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Clock className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-xl font-medium text-gray-700">No pending approvals</p>
            <p className="text-gray-500 mt-2">All business registrations have been processed</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pendingBusinesses.map((item: any) => (
            <Card key={`${item.source}-${item.id}`} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-xl">{item.name}</CardTitle>
                  <Badge variant={item.source === 'registration' ? 'outline' : 'secondary'} className="ml-2">
                    {item.source === 'registration' ? 'New Registration' : 'Pending Business'}
                  </Badge>
                </div>
                <CardDescription>{item.businessTypeName || 'Business'}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Submitted By</p>
                    <p>{item.userName || 'Unknown'}</p>
                    <p className="text-sm text-gray-500">{item.userEmail || 'No email'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Address</p>
                    <p>{item.address || item.location || 'No address'}</p>
                    <p>{item.postcode || ''}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Contact</p>
                    <p>{item.phone || 'No phone'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Submitted On</p>
                    <p>{new Date(item.created_at).toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 border-red-200 hover:bg-red-50"
                  onClick={() => handleReject(item)}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
                <Button
                  size="sm"
                  className="bg-emerald-600 hover:bg-emerald-700"
                  onClick={() => handleApprove(item)}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Approve
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
