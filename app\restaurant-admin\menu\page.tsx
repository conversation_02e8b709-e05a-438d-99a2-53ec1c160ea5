"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import {
  Search,
  Plus,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  Star,
  DollarSign,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// Sample menu categories for the admin interface
const initialMenuCategories = [
  {
    id: "starters",
    name: "Starters",
    items: [
      {
        id: "garlic-bread",
        name: "Garlic Bread",
        description: "Freshly baked bread with garlic butter",
        price: 4.95,
        isPopular: true,
        image: "/placeholder.svg"
      },
      {
        id: "soup",
        name: "Soup of the Day",
        description: "Chef's special soup served with crusty bread",
        price: 5.95,
        isPopular: false,
        image: "/placeholder.svg"
      }
    ]
  },
  {
    id: "mains",
    name: "Main Courses",
    items: [
      {
        id: "steak",
        name: "Sirloin Steak",
        description: "8oz sirloin steak with chips and salad",
        price: 18.95,
        isPopular: true,
        image: "/placeholder.svg"
      },
      {
        id: "pasta",
        name: "Pasta Carbonara",
        description: "Classic pasta dish with cream sauce and pancetta",
        price: 12.95,
        isPopular: false,
        image: "/placeholder.svg"
      }
    ]
  }
]

export default function MenuManagementPage() {
  const [menuCategories, setMenuCategories] = useState(initialMenuCategories)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<{ categoryId: string; itemId: string } | null>(null)
  const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false)
  const [currentItem, setCurrentItem] = useState<any>(null)
  const [currentCategoryId, setCurrentCategoryId] = useState<string | null>(null)

  // Filter menu items based on search query and active tab
  const filteredCategories = menuCategories
    .map((category) => {
      const filteredItems = category.items.filter((item) => {
        // Filter by search query
        if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase())) {
          return false
        }

        // Filter by tab
        if (activeTab === "popular" && !item.isPopular) {
          return false
        }

        return true
      })

      if (filteredItems.length === 0) {
        return null
      }

      return {
        ...category,
        items: filteredItems,
      }
    })
    .filter(Boolean) as typeof menuCategories

  const handleDeleteItem = () => {
    if (!itemToDelete) return

    const { categoryId, itemId } = itemToDelete

    setMenuCategories((prevCategories) =>
      prevCategories.map((category) => {
        if (category.id === categoryId) {
          return {
            ...category,
            items: category.items.filter((item) => item.id !== itemId),
          }
        }
        return category
      }),
    )

    setIsDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  const handleAddEditItem = (formData: any) => {
    // This would normally save to a database
    console.log("Form data:", formData)
    setIsAddEditDialogOpen(false)
  }

  const openAddDialog = (categoryId: string) => {
    setCurrentItem(null)
    setCurrentCategoryId(categoryId)
    setIsAddEditDialogOpen(true)
  }

  const openEditDialog = (categoryId: string, item: any) => {
    setCurrentItem(item)
    setCurrentCategoryId(categoryId)
    setIsAddEditDialogOpen(true)
  }

  const confirmDelete = (categoryId: string, itemId: string) => {
    setItemToDelete({ categoryId, itemId })
    setIsDeleteDialogOpen(true)
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Menu Management</h1>
          <p className="text-gray-500">Manage your restaurant menu items and categories</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Link href="/restaurant-admin/menu/categories">
            <Button variant="outline">Manage Categories</Button>
          </Link>
          <Button className="bg-emerald-600 hover:bg-emerald-700">
            <Plus className="mr-2 h-4 w-4" />
            Add New Item
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search menu items..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-3">
          <Select defaultValue="name">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="price-asc">Price: Low to High</SelectItem>
              <SelectItem value="price-desc">Price: High to Low</SelectItem>
              <SelectItem value="popularity">Popularity</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="flex items-center">
            <Filter size={16} className="mr-2" />
            Filters
            <ChevronDown size={16} className="ml-2" />
          </Button>
        </div>
      </div>

      {/* Menu Tabs */}
      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Items</TabsTrigger>
          <TabsTrigger value="popular">Popular Items</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {filteredCategories.length === 0 ? (
            <Alert>
              <AlertDescription>No menu items found. Try adjusting your search.</AlertDescription>
            </Alert>
          ) : (
            filteredCategories.map((category) => (
              <div key={category.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">{category.name}</h2>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-emerald-600"
                    onClick={() => openAddDialog(category.id)}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add to {category.name}
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.items.map((item) => (
                    <MenuItemCard
                      key={item.id}
                      item={item}
                      onEdit={() => openEditDialog(category.id, item)}
                      onDelete={() => confirmDelete(category.id, item.id)}
                    />
                  ))}
                </div>
              </div>
            ))
          )}
        </TabsContent>

        <TabsContent value="popular" className="space-y-6">
          {filteredCategories.length === 0 ? (
            <Alert>
              <AlertDescription>No popular items found. Try adjusting your search.</AlertDescription>
            </Alert>
          ) : (
            filteredCategories.map((category) => (
              <div key={category.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">{category.name}</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.items.map((item) => (
                    <MenuItemCard
                      key={item.id}
                      item={item}
                      onEdit={() => openEditDialog(category.id, item)}
                      onDelete={() => confirmDelete(category.id, item.id)}
                    />
                  ))}
                </div>
              </div>
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this menu item? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteItem}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Item Dialog */}
      <Dialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{currentItem ? "Edit Menu Item" : "Add New Menu Item"}</DialogTitle>
            <DialogDescription>
              {currentItem
                ? "Update the details of this menu item"
                : "Fill in the details to add a new menu item to your restaurant"}
            </DialogDescription>
          </DialogHeader>
          <MenuItemForm
            item={currentItem}
            categoryId={currentCategoryId}
            onSubmit={handleAddEditItem}
            onCancel={() => setIsAddEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Menu Item Card Component
function MenuItemCard({ item, onEdit, onDelete }: { item: any; onEdit: () => void; onDelete: () => void }) {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="relative h-40 bg-gray-100">
          {item.image ? (
            <img src={item.image || "/placeholder.svg"} alt={item.name} className="w-full h-full object-cover" />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <span className="text-gray-400">No Image</span>
            </div>
          )}
          {item.isPopular && (
            <Badge className="absolute top-2 right-2 bg-orange-500">
              <Star className="mr-1 h-3 w-3" />
              Popular
            </Badge>
          )}
        </div>

        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-semibold">{item.name}</h3>
            <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
              <DollarSign className="mr-1 h-3 w-3" />
              {item.price.toFixed(2)}
            </Badge>
          </div>

          <p className="text-sm text-gray-500 line-clamp-2 mb-4">{item.description || "No description available"}</p>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Switch id={`available-${item.id}`} defaultChecked />
              <Label htmlFor={`available-${item.id}`} className="ml-2 text-sm">
                Available
              </Label>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <ArrowUpDown className="mr-2 h-4 w-4" />
                  Move
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600" onClick={onDelete}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Menu Item Form Component
function MenuItemForm({
  item,
  categoryId,
  onSubmit,
  onCancel,
}: {
  item: any
  categoryId: string | null
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    name: item?.name || "",
    description: item?.description || "",
    price: item?.price || 0,
    isPopular: item?.isPopular || false,
    image: item?.image || "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({
      ...formData,
      id: item?.id || `item-${Date.now()}`,
      categoryId,
    })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="name" className="text-right">
            Name
          </Label>
          <Input id="name" name="name" value={formData.name} onChange={handleChange} className="col-span-3" required />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="description" className="text-right">
            Description
          </Label>
          <Input
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="col-span-3"
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="price" className="text-right">
            Price (£)
          </Label>
          <Input
            id="price"
            name="price"
            type="number"
            step="0.01"
            value={formData.price}
            onChange={handleChange}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="image" className="text-right">
            Image URL
          </Label>
          <Input id="image" name="image" value={formData.image} onChange={handleChange} className="col-span-3" />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Popular Item</Label>
          <div className="col-span-3 flex items-center">
            <Switch
              id="isPopular"
              checked={formData.isPopular}
              onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, isPopular: checked }))}
            />
            <Label htmlFor="isPopular" className="ml-2">
              Mark as popular
            </Label>
          </div>
        </div>
      </div>
      <div className="flex justify-end space-x-2 mt-4">
        <Button variant="outline" type="button" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" className="bg-emerald-600 hover:bg-emerald-700">
          {item ? "Update Item" : "Add Item"}
        </Button>
      </div>
    </form>
  )
}
