import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get business ID from query parameters
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('business_id')
    
    if (!businessId) {
      return NextResponse.json(
        { error: "Business ID is required" },
        { status: 400 }
      )
    }

    // Verify user has access to this business
    const { data: businessAccess, error: accessError } = await supabase
      .from('business_managers')
      .select('id')
      .eq('user_id', user.id)
      .eq('business_id', businessId)
      .single()

    if (accessError || !businessAccess) {
      return NextResponse.json(
        { error: "Access denied to this business" },
        { status: 403 }
      )
    }

    // Get search parameters
    const isActive = searchParams.get('active') !== 'false' // default to true
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get approved drivers for this business
    let driversQuery = supabase
      .from('driver_business_assignments')
      .select(`
        id,
        driver_id,
        is_active,
        created_at,
        updated_at,
        driver_profiles!inner (
          id,
          user_id,
          vehicle_type,
          vehicle_make,
          vehicle_model,
          vehicle_year,
          vehicle_color,
          cargo_capacity_category,
          max_weight_kg,
          has_thermal_bag,
          has_cooler_bag,
          has_large_bag,
          delivery_types,
          store_types,
          equipment_notes,
          is_verified,
          is_active,
          average_rating,
          total_deliveries,
          created_at as driver_joined_date,
          users!driver_profiles_user_id_fkey (
            id,
            name,
            first_name,
            last_name,
            email,
            phone
          )
        ),
        driver_business_approvals!inner (
          status,
          application_date,
          decision_date,
          decision_by,
          notes
        )
      `)
      .eq('business_id', businessId)
      .eq('driver_business_approvals.status', 'approved')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Filter by active status if specified
    if (isActive) {
      driversQuery = driversQuery.eq('is_active', true)
      driversQuery = driversQuery.eq('driver_profiles.is_active', true)
    }

    const { data: drivers, error: driversError } = await driversQuery

    if (driversError) {
      console.error('Error fetching approved drivers:', driversError)
      return NextResponse.json(
        { error: "Failed to fetch approved drivers" },
        { status: 500 }
      )
    }

    // Get driver performance data (recent ratings, delivery stats)
    const driverIds = drivers?.map(d => d.driver_id) || []
    
    // Get recent ratings for these drivers
    const { data: recentRatings } = await supabase
      .from('driver_ratings')
      .select('driver_id, rating, comment, created_at')
      .in('driver_id', driverIds)
      .order('created_at', { ascending: false })
      .limit(100) // Get recent ratings

    // Get recent earnings for these drivers
    const { data: recentEarnings } = await supabase
      .from('driver_earnings')
      .select('driver_id, total_amount, created_at')
      .in('driver_id', driverIds)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
      .order('created_at', { ascending: false })

    // Create maps for quick lookup
    const ratingsMap = new Map()
    recentRatings?.forEach(rating => {
      if (!ratingsMap.has(rating.driver_id)) {
        ratingsMap.set(rating.driver_id, [])
      }
      ratingsMap.get(rating.driver_id).push(rating)
    })

    const earningsMap = new Map()
    recentEarnings?.forEach(earning => {
      if (!earningsMap.has(earning.driver_id)) {
        earningsMap.set(earning.driver_id, [])
      }
      earningsMap.get(earning.driver_id).push(earning)
    })

    // Transform drivers for response
    const transformedDrivers = drivers?.map(assignment => {
      const driver = assignment.driver_profiles
      const approval = assignment.driver_business_approvals
      const driverRatings = ratingsMap.get(assignment.driver_id) || []
      const driverEarnings = earningsMap.get(assignment.driver_id) || []

      return {
        assignment_id: assignment.id,
        driver_id: assignment.driver_id,
        is_active: assignment.is_active,
        approved_date: assignment.created_at,
        last_updated: assignment.updated_at,
        driver: {
          id: driver.id,
          user: {
            id: driver.users.id,
            name: driver.users.name,
            first_name: driver.users.first_name,
            last_name: driver.users.last_name,
            email: driver.users.email,
            phone: driver.users.phone
          },
          vehicle: {
            type: driver.vehicle_type,
            make: driver.vehicle_make,
            model: driver.vehicle_model,
            year: driver.vehicle_year,
            color: driver.vehicle_color,
            capacity_category: driver.cargo_capacity_category,
            max_weight_kg: driver.max_weight_kg
          },
          equipment: {
            has_thermal_bag: driver.has_thermal_bag,
            has_cooler_bag: driver.has_cooler_bag,
            has_large_bag: driver.has_large_bag,
            notes: driver.equipment_notes
          },
          capabilities: {
            delivery_types: driver.delivery_types,
            store_types: driver.store_types
          },
          performance: {
            average_rating: driver.average_rating,
            total_deliveries: driver.total_deliveries,
            is_verified: driver.is_verified,
            is_active: driver.is_active,
            recent_ratings_count: driverRatings.length,
            monthly_earnings: driverEarnings.reduce((sum, e) => sum + parseFloat(e.total_amount), 0)
          },
          joined_date: driver.driver_joined_date
        },
        approval: {
          application_date: approval.application_date,
          decision_date: approval.decision_date,
          decision_by: approval.decision_by,
          notes: approval.notes
        }
      }
    })

    // Get summary statistics
    const { data: allAssignments } = await supabase
      .from('driver_business_assignments')
      .select('is_active, driver_profiles!inner(is_active)')
      .eq('business_id', businessId)

    const summary = {
      total_approved: allAssignments?.length || 0,
      active_drivers: allAssignments?.filter(a => a.is_active && a.driver_profiles.is_active).length || 0,
      inactive_drivers: allAssignments?.filter(a => !a.is_active || !a.driver_profiles.is_active).length || 0
    }

    return NextResponse.json({
      success: true,
      drivers: transformedDrivers,
      summary,
      pagination: {
        limit,
        offset,
        total: transformedDrivers?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in business approved drivers GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Deactivate/reactivate driver for business
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { assignment_id, is_active, reason } = body

    if (!assignment_id || typeof is_active !== 'boolean') {
      return NextResponse.json(
        { error: "Assignment ID and is_active status are required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    const userEmail = '<EMAIL>'

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get assignment and verify access
    const { data: assignment, error: assignmentError } = await supabase
      .from('driver_business_assignments')
      .select('id, driver_id, business_id')
      .eq('id', assignment_id)
      .single()

    if (assignmentError || !assignment) {
      return NextResponse.json(
        { error: "Assignment not found" },
        { status: 404 }
      )
    }

    // Verify user has access to this business
    const { data: businessAccess, error: accessError } = await supabase
      .from('business_managers')
      .select('id')
      .eq('user_id', user.id)
      .eq('business_id', assignment.business_id)
      .single()

    if (accessError || !businessAccess) {
      return NextResponse.json(
        { error: "Access denied to this business" },
        { status: 403 }
      )
    }

    // Update assignment status
    const { data: updatedAssignment, error: updateError } = await supabase
      .from('driver_business_assignments')
      .update({
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', assignment_id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating assignment:', updateError)
      return NextResponse.json(
        { error: "Failed to update driver status" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Driver ${is_active ? 'activated' : 'deactivated'} successfully`,
      assignment: updatedAssignment
    })

  } catch (error) {
    console.error('Error in business approved drivers PATCH:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
