import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET() {
  try {
    console.log('SQL Query API called');
    
    // Query to get all tables in the public schema
    const { data: tablesData, error: tablesError } = await adminClient.rpc(
      'execute_sql',
      {
        sql_query: `
          SELECT table_name
          FROM information_schema.tables
          WHERE table_schema = 'public'
          ORDER BY table_name;
        `
      }
    );
    
    // If the RPC function doesn't exist, try a direct query
    let directQueryResult = null;
    let directQueryError = null;
    
    if (tablesError) {
      try {
        const { data, error } = await adminClient.from('pg_tables').select('*').eq('schemaname', 'public');
        directQueryResult = data;
        directQueryError = error;
      } catch (e) {
        directQueryError = e;
      }
    }
    
    // Try to query the business_types table directly
    const { data: businessTypesData, error: businessTypesError } = await adminClient
      .from('business_types')
      .select('*')
      .limit(5);
    
    return NextResponse.json({
      rpc_query: {
        data: tablesData,
        error: tablesError ? tablesError.message : null
      },
      direct_query: {
        data: directQueryResult,
        error: directQueryError ? directQueryError.message : null
      },
      business_types: {
        data: businessTypesData,
        error: businessTypesError ? businessTypesError.message : null
      }
    });
  } catch (error) {
    console.error('Error in sql-query API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
