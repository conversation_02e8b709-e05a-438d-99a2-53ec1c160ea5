"use client"

import dynamic from 'next/dynamic'

// Import the OSM delivery map component with dynamic import to avoid SSR issues with Leaflet
const OSMDeliveryMapSimple = dynamic(
  () => import('./osm-delivery-map-simple'),
  { ssr: false }
)

interface DeliveryMapProps {
  restaurantLng: number // This can be a restaurant or shop longitude
  restaurantLat: number // This can be a restaurant or shop latitude
  customerLng: number
  customerLat: number
  height?: string
  className?: string
  interactive?: boolean
  showRoute?: boolean
}

// This is a wrapper component that dynamically imports the OSM map component
// to avoid SSR issues with Leaflet
function DeliveryMap(props: DeliveryMapProps) {
  return <OSMDeliveryMapSimple {...props} />
}

export default DeliveryMap;
