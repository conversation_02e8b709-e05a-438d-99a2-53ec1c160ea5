'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>er, Clock, MapPin, Phone, User } from 'lucide-react'

// Helper function to format variant and customization details
const formatItemDetails = (item: OrderItem): string[] => {
  const details: string[] = [];

  // Add variant information (check both possible field names)
  const variantName = item.variant_name || item.variantName;
  if (variantName) {
    details.push(`Size: ${variantName}`);
  }

  // Add customization information
  if (item.customizations && Array.isArray(item.customizations)) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  return details;
};

interface OrderItem {
  id: string | number
  name?: string // cart_items.name column
  product_name?: string // fallback for compatibility
  quantity: number
  unit_price?: number
  price?: number // cart_items.price column
  total_price?: number
  variant_name?: string // Now properly populated from product_variants table
  variantName?: string // Alternative field name for compatibility
  customizations?: Array<{
    groupName: string
    options: Array<{
      name: string
      price: number
    }>
  }> | string | object // Support both new structured format and legacy formats
  notes?: string
}

interface Order {
  id: string | number
  order_number?: string
  order_id?: string
  created_at: string
  status: string
  delivery_type: string
  delivery_timing?: string
  scheduled_delivery_time?: string | null
  preparation_time?: number | null
  ready_time?: string | null
  customer_name?: string
  customer_phone?: string
  delivery_address?: string
  delivery_instructions?: string
  parish?: string
  total?: number | string
  subtotal?: number | string
  delivery_fee?: number | string
  service_fee?: number | string
  priority_level?: number
}

interface PrintableOrderSlipProps {
  order: Order
  orderItems: OrderItem[]
  businessName?: string
}

export function PrintableOrderSlip({ order, orderItems, businessName = "Your Business" }: PrintableOrderSlipProps) {

  // Format currency helper
  const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined || amount === '') return "£0.00"
    try {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
      if (isNaN(numAmount)) return "£0.00"
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP',
        minimumFractionDigits: 2
      }).format(numAmount)
    } catch (err) {
      return "£0.00"
    }
  }

  // Format time helper - displays time in Jersey timezone (BST/GMT)
  const formatTime = (dateString: string) => {
    if (!dateString) return ""
    try {
      const utcDate = new Date(dateString)
      return new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZone: 'Europe/London'
      }).format(utcDate)
    } catch (err) {
      return ""
    }
  }

  // Calculate ready time
  const getReadyTime = () => {
    if (order.ready_time) {
      return formatTime(order.ready_time)
    } else if (order.preparation_time) {
      const orderTime = new Date(order.created_at)
      const readyTime = new Date(orderTime.getTime() + order.preparation_time * 60000)
      return formatTime(readyTime.toISOString())
    }
    return 'N/A'
  }

  // Get priority indicator
  const getPriorityText = (priority: number | undefined) => {
    if (!priority) return ''
    switch(priority) {
      case 1: return '🔴 URGENT'
      case 2: return '🟠 HIGH'
      case 3: return '🔵 NORMAL'
      case 4: return '🟢 LOW'
      default: return ''
    }
  }

  // Print function
  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="max-w-md mx-auto bg-white">
      {/* Print Button - Hidden when printing */}
      <div className="mb-4 print:hidden">
        <Button onClick={handlePrint} className="w-full">
          <Printer className="mr-2 h-4 w-4" />
          Print Order Slip
        </Button>
      </div>

      {/* Printable Order Slip */}
      <div className="p-4 border border-gray-300 print:border-none print:p-0">
        {/* Header */}
        <div className="text-center border-b-2 border-dashed border-gray-400 pb-3 mb-3">
          <h1 className="text-lg font-bold">{businessName}</h1>
          <h2 className="text-sm font-medium">ORDER SLIP</h2>
        </div>

        {/* Order Info */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-lg font-bold">#{order.order_number || order.order_id || order.id}</span>
            {order.priority_level && order.priority_level <= 2 && (
              <span className="text-sm font-bold">{getPriorityText(order.priority_level)}</span>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <strong>Ordered:</strong> {formatTime(order.created_at)}
            </div>
            <div>
              <strong>Ready by:</strong> <span className="font-bold text-blue-600">{getReadyTime()}</span>
            </div>
          </div>

          {/* Delivery Type */}
          <div className="mt-2 p-2 bg-gray-100 rounded">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {order.delivery_type === 'pickup' ? (
                  <Clock className="mr-1 h-4 w-4" />
                ) : (
                  <MapPin className="mr-1 h-4 w-4" />
                )}
                <span className="font-medium">
                  {order.delivery_type === 'pickup' ? 'PICKUP' : 'DELIVERY'}
                </span>
              </div>
              <span className="text-sm">
                {order.delivery_timing === 'scheduled' && order.scheduled_delivery_time
                  ? `Scheduled: ${formatTime(order.scheduled_delivery_time)}`
                  : 'ASAP'
                }
              </span>
            </div>
          </div>
        </div>

        {/* Customer Info (Minimal) */}
        <div className="mb-4 border-b border-dashed border-gray-300 pb-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <User className="mr-1 h-3 w-3" />
              <span>{order.customer_name || 'Guest'}</span>
            </div>
            {order.customer_phone && (
              <div className="flex items-center">
                <Phone className="mr-1 h-3 w-3" />
                <span>{order.customer_phone}</span>
              </div>
            )}
          </div>

          {order.delivery_type === 'delivery' && order.delivery_address && (
            <div className="mt-1 text-xs text-gray-600">
              📍 {order.parish || 'Unknown Parish'}
            </div>
          )}

          {order.delivery_instructions && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
              <strong>Instructions:</strong> {order.delivery_instructions}
            </div>
          )}
        </div>

        {/* Order Items - Main Focus */}
        <div className="mb-4">
          <h3 className="font-bold text-sm mb-2 border-b border-gray-300 pb-1">ITEMS TO PREPARE</h3>

          {orderItems && orderItems.length > 0 ? (
            <div className="space-y-3">
              {orderItems.map((item, index) => (
                <div key={item.id || index} className="border-b border-dotted border-gray-300 pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        <span className="inline-block w-6 h-6 bg-gray-200 text-center text-xs font-bold rounded mr-2">
                          {item.quantity}
                        </span>
                        {item.name || item.product_name || 'Unknown Product'}
                      </div>

                      {/* Customizations and Variants */}
                      {(() => {
                        const details = formatItemDetails(item);
                        return details.length > 0 && (
                          <div className="ml-8 mt-1 text-xs text-gray-600">
                            <strong>Details:</strong> {details.join(", ")}
                          </div>
                        );
                      })()}

                      {/* Special Notes */}
                      {item.notes && (
                        <div className="ml-8 mt-1 p-1 bg-yellow-100 border border-yellow-300 rounded text-xs">
                          <strong>⚠️ Note:</strong> {item.notes}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-sm text-gray-500 italic">No items found</div>
          )}
        </div>

        {/* Order Total (Simple) */}
        <div className="border-t-2 border-dashed border-gray-400 pt-2">
          <div className="flex justify-between items-center">
            <span className="font-bold">TOTAL:</span>
            <span className="font-bold text-lg">{formatCurrency(order.total || 0)}</span>
          </div>
          {order.delivery_type === 'delivery' && order.delivery_fee && (
            <div className="text-xs text-gray-500 text-right">
              (Includes £{formatCurrency(order.delivery_fee)} delivery)
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-4 text-center text-xs text-gray-500 border-t border-dashed border-gray-300 pt-2">
          <div>Status: <strong>{order.status.toUpperCase()}</strong></div>
          <div className="mt-1">Printed: {new Date().toLocaleString('en-GB')}</div>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .printable-order-slip, .printable-order-slip * {
            visibility: visible;
          }
          .printable-order-slip {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          @page {
            size: A5;
            margin: 0.5in;
          }
        }
      `}</style>
    </div>
  )
}
