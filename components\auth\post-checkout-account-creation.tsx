"use client"

import { useState } from "react"
import { Eye, EyeOff, Lock, Mail, User, UserPlus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { signUp } from "@/services/auth-service-direct"

export default function PostCheckoutAccountCreation({ customerName, customerPhone, orderId }: { 
  customerName?: string;
  customerPhone?: string;
  orderId?: string;
}) {
  const [name, setName] = useState(customerName || "")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState(customerPhone || "")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)
    setIsLoading(true)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match")
      setIsLoading(false)
      return
    }

    try {
      const { error, data } = await signUp(email, password, {
        name,
        phone,
        order_id: orderId
      })

      if (error) {
        // Handle specific Supabase auth errors with user-friendly messages
        if (error.message.includes("email already registered")) {
          setError("This email is already registered. Please use a different email or try logging in.")
        } else if (error.message.includes("password")) {
          setError("Password error: " + error.message)
        } else {
          setError(error.message)
        }
        setIsLoading(false)
        return
      }

      // Show success message
      setSuccess("Account created successfully! You can now log in to track your orders and save your delivery addresses for future purchases.")

      // Clear form fields to prevent resubmission
      setName("")
      setEmail("")
      setPhone("")
      setPassword("")
      setConfirmPassword("")
    } catch (err) {
      console.error("Registration error:", err)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Account Created!</CardTitle>
          <CardDescription>Your account has been successfully created.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="bg-emerald-50 text-emerald-800 border-emerald-200">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={() => window.location.href = "/login"} 
            className="w-full bg-emerald-600 hover:bg-emerald-700"
          >
            Log In Now
          </Button>
        </CardFooter>
      </Card>
    )
  }

  if (!showForm) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Save Your Information</CardTitle>
          <CardDescription>Create an account to track orders and save delivery addresses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-100 mb-4">
            <p className="text-sm text-blue-700">
              Creating an account makes future orders faster and easier. You'll be able to:
            </p>
            <ul className="list-disc list-inside mt-2 text-sm text-blue-700">
              <li>Track your order status</li>
              <li>Save multiple delivery addresses</li>
              <li>Reorder your favorite items</li>
              <li>Get exclusive offers and discounts</li>
            </ul>
          </div>
          <Button 
            onClick={() => setShowForm(true)} 
            className="w-full bg-emerald-600 hover:bg-emerald-700"
          >
            <UserPlus className="mr-2 h-4 w-4" />
            Create an Account
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Your Account</CardTitle>
        <CardDescription>Fill in your details to create an account</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleRegister}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="name"
                  placeholder="John Doe"
                  type="text"
                  className="pl-10"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                placeholder="07797123456"
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              <p className="text-xs text-gray-500">Password must be at least 8 characters long</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  className="pl-10"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
            </div>
            <Button type="submit" className="w-full bg-emerald-600 hover:bg-emerald-700" disabled={isLoading}>
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Account...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Account
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline" 
          className="w-full" 
          onClick={() => setShowForm(false)}
        >
          Maybe Later
        </Button>
      </CardFooter>
    </Card>
  )
}
