import { NextRequest, NextResponse } from "next/server";
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";

// API key from environment variables
const API_KEY = process.env.GOOGLE_AI_API_KEY || "";

// Initialize the Google Generative AI client
let genAI: GoogleGenerativeAI | null = null;
try {
  genAI = new GoogleGenerativeAI(API_KEY);
} catch (error) {
  console.error("Error initializing Google Generative AI client:", error);
}

export async function POST(request: NextRequest) {
  try {
    // Get the raw request text to handle any parsing errors
    const requestText = await request.text();
    let prompt;

    try {
      const body = JSON.parse(requestText);
      prompt = body.prompt;
    } catch (parseError) {
      return NextResponse.json(
        { error: "Invalid JSON in request body", details: parseError.message },
        { status: 400 }
      );
    }

    // Validate the request
    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Validate API key
    if (!API_KEY) {
      return NextResponse.json(
        { error: "API key is not configured" },
        { status: 500 }
      );
    }

    console.log(`Sending prompt to Gemini API: ${prompt.substring(0, 100)}...`);

    try {
      // Check if genAI was initialized successfully
      if (!genAI) {
        return NextResponse.json(
          { error: "Google Generative AI client not initialized. Check API key and package installation." },
          { status: 500 }
        );
      }

      // Get the Gemini model
      const model = genAI.getGenerativeModel({ model: "gemini-1.0-pro" });

      // Configure the generation
      const generationConfig = {
        temperature: 0.2,
        maxOutputTokens: 2048,
        topK: 40,
        topP: 0.95,
      };

      // Configure safety settings
      const safetySettings = [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
      ];

      // Generate content
      console.log("Generating content with Gemini API...");
      const result = await model.generateContent({
        contents: [{ role: "user", parts: [{ text: prompt }] }],
        generationConfig,
        safetySettings,
      });

      // Get the response
      const response = result.response;
      const text = response.text();

      // Log the response for debugging
      console.log("Gemini API response:", text.substring(0, 200) + "...");

      // Return the response
      return NextResponse.json({
        text,
        model: "gemini-1.0-pro",
        rawResponse: JSON.stringify(response)
      });
    } catch (error: any) {
      console.error("Error calling Gemini API:", error);

      // Check for specific error types
      let errorMessage = error.message || "Error calling Gemini API";
      let errorDetails = {};

      // Handle specific error cases
      if (error.response) {
        errorDetails = {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        };
      }

      // Return detailed error information
      return NextResponse.json(
        {
          error: errorMessage,
          details: errorDetails,
          prompt: prompt.substring(0, 100) + "..."
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Unexpected error: " + (error.message || "Unknown error") },
      { status: 500 }
    );
  }
}
