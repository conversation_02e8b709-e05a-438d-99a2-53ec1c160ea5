import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Types for our multi-business cart system
interface CartItem {
  id: string;
  businessId: number;
  name: string;
  price: number;
  quantity: number;
  variantId?: string;
  imageUrl?: string;
  cartItemId?: string;
}

// Helper function to get current user
async function getCurrentUser(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const supabaseClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        {
          auth: { autoRefreshToken: false, persistSession: false },
          global: { headers: { Authorization: `Bearer ${token}` } }
        }
      );

      const { data: { user } } = await supabaseClient.auth.getUser();
      return user;
    }
  } catch (error) {
    console.error('Error getting user:', error);
  }
  return null;
}

// Get or create business-specific cart
async function getOrCreateBusinessCart(isUserCart: boolean, cartIdentifier: string, businessId: number, sessionId: string): Promise<string> {
  try {
    // First, try to find existing cart for this business using is_anonymous flag (exclude archived carts)
    let query = supabase
      .from('user_carts')
      .select('id')
      .eq('business_id', businessId)
      .eq('is_archived', false); // Exclude archived carts

    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: existingCart } = await query.maybeSingle();

    if (existingCart) {
      return existingCart.id;
    }

    // Get business delivery availability to determine appropriate delivery method
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('delivery_available')
      .eq('id', businessId)
      .single();

    if (businessError) {
      console.error(`Error fetching business ${businessId} delivery availability:`, businessError);
    }

    // Determine appropriate delivery method based on business availability
    const deliveryMethod = (business?.delivery_available === false) ? 'pickup' : 'delivery';
    const deliveryFee = (deliveryMethod === 'pickup') ? 0 : 0; // Will be calculated later if delivery

    console.log(`📦 Creating new cart for business ${businessId} with delivery_method: ${deliveryMethod} (delivery_available: ${business?.delivery_available})`);

    // Create new cart for this business
    const cartData: any = {
      business_id: businessId,
      delivery_method: deliveryMethod, // Set based on business delivery availability
      delivery_fee: deliveryFee,
      is_anonymous: !isUserCart, // Set is_anonymous flag based on user type
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    if (isUserCart) {
      cartData.user_id = cartIdentifier;
      cartData.session_id = sessionId; // Always store session_id for order grouping
    } else {
      cartData.session_id = sessionId;
      cartData.user_id = null;
    }

    const { data: newCart, error: createError } = await supabase
      .from('user_carts')
      .insert(cartData)
      .select('id')
      .single();

    if (createError) throw createError;

    console.log(`✅ Created new cart for business ${businessId}: ${newCart.id} with delivery_method: ${deliveryMethod}`);
    return newCart.id;

  } catch (error: any) {
    console.error('Error getting/creating business cart:', error);
    throw error;
  }
}

// Main POST handler for cart actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, sessionId } = body;

    // Determine cart identifier
    const isUserCart = !!userId;
    const cartIdentifier = userId || sessionId;

    if (!cartIdentifier) {
      return NextResponse.json(
        { error: 'User ID or session ID required' },
        { status: 400 }
      );
    }

    // Always require session_id for order grouping
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required for order grouping' },
        { status: 400 }
      );
    }

    console.log(`🛒 Cart action: ${action} for ${isUserCart ? 'user' : 'session'} ${cartIdentifier} (session: ${sessionId})`);

    switch (action) {
      case 'get':
        return await handleGetCart(isUserCart, cartIdentifier);
      case 'add':
        return await handleAddItem(body, isUserCart, cartIdentifier, sessionId);
      case 'remove':
        return await handleRemoveItem(body, isUserCart, cartIdentifier);
      case 'updateQuantity':
        return await handleUpdateQuantity(body, isUserCart, cartIdentifier);
      case 'clear':
        return await handleClearCart(isUserCart, cartIdentifier);
      case 'updateDeliveryMethod':
        return await handleUpdateDeliveryMethod(body, isUserCart, cartIdentifier, sessionId);
      case 'updateDeliveryFee':
        return await handleUpdateDeliveryFee(body, isUserCart, cartIdentifier, sessionId);
      case 'getCartIds':
        return await handleGetCartIds(isUserCart, cartIdentifier);
      default:
        return NextResponse.json(
          { error: `Invalid action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Cart API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

// Get cart data
async function handleGetCart(isUserCart: boolean, cartIdentifier: string): Promise<NextResponse> {
  try {
    // Get all carts using is_anonymous flag to determine which identifier to use (exclude archived carts)
    let query = supabase
      .from('user_carts')
      .select('id, business_id, delivery_method, delivery_fee, is_anonymous, user_id, session_id')
      .eq('is_archived', false) // Exclude archived carts
      .order('updated_at', { ascending: false })
      .limit(50); // Limit to prevent excessive queries

    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: userCarts, error: cartError } = await query;

    if (cartError) throw cartError;

    if (!userCarts || userCarts.length === 0) {
      return NextResponse.json({
        cart: [],
        businessNames: {},
        deliveryMethods: {},
        deliveryFees: {},
        preparationTimes: {}
      });
    }

    // Get all cart items
    const cartIds = userCarts.map(cart => cart.id);
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('*')
      .in('cart_id', cartIds);

    if (itemsError) throw itemsError;

    // Get customizations for all cart items
    const cartItemIds = (cartItems || []).map(item => item.id);
    let customizations: any[] = [];

    if (cartItemIds.length > 0) {
      const { data: customizationData, error: customizationError } = await supabase
        .from('cart_item_customizations')
        .select('*')
        .in('cart_item_id', cartItemIds);

      if (customizationError) {
        console.error('Error fetching customizations:', customizationError);
      } else {
        customizations = customizationData || [];
      }
    }

    // Filter out carts that have no items
    const cartsWithItems = userCarts.filter(cart =>
      cartItems?.some(item => item.cart_id === cart.id)
    );

    // Get unique variant IDs to fetch variant names
    const variantIds = [...new Set(cartItems?.filter(item => item.variant_id).map(item => item.variant_id) || [])];
    const variantNames: Record<number, string> = {};

    // Fetch variant names if we have variant IDs
    if (variantIds.length > 0) {
      const { data: variants } = await supabase
        .from('product_variants')
        .select('id, name')
        .in('id', variantIds);

      if (variants) {
        variants.forEach(variant => {
          variantNames[variant.id] = variant.name;
        });
      }
    }

    // Format cart items with customizations
    const formattedItems = (cartItems || []).map(item => {
      // Get customizations for this cart item
      const itemCustomizations = customizations.filter(c => c.cart_item_id === item.id);

      // Group customizations by group name
      const groupedCustomizations: Record<string, any[]> = {};
      itemCustomizations.forEach(customization => {
        if (!groupedCustomizations[customization.customization_group_name]) {
          groupedCustomizations[customization.customization_group_name] = [];
        }
        groupedCustomizations[customization.customization_group_name].push({
          id: customization.id,
          name: customization.customization_option_name,
          price: customization.price
        });
      });

      // Convert to the expected format
      const formattedCustomizations = Object.entries(groupedCustomizations).map(([groupName, options]) => ({
        groupId: 0, // We don't store group ID in cart_item_customizations
        groupName,
        options
      }));

      return {
        id: item.product_id.toString(),
        businessId: item.business_id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        variantId: item.variant_id,
        variantName: item.variant_id ? variantNames[item.variant_id] || null : null,
        customizations: formattedCustomizations.length > 0 ? formattedCustomizations : undefined,
        imageUrl: item.image_url,
        cartItemId: item.id,
        // Include delivery attributes
        weight_class_kg: item.weight_class_kg,
        thermal_requirement: item.thermal_requirement,
        size_category: item.size_category
      };
    });

    // Get business names (only for carts that have items)
    const businessIds = [...new Set(cartsWithItems.map(cart => cart.business_id))];
    const businessNames: Record<string, string> = {};

    for (const businessId of businessIds) {
      const { data: business } = await supabase
        .from('businesses')
        .select('name')
        .eq('id', businessId)
        .single();

      businessNames[businessId] = business?.name || `Business ${businessId}`;
    }

    // Extract delivery methods and fees (only for carts that have items)
    const deliveryMethods: Record<string, 'delivery' | 'pickup'> = {};
    const deliveryFees: Record<string, number> = {};

    cartsWithItems.forEach(cart => {
      if (cart.delivery_method) {
        deliveryMethods[cart.business_id] = cart.delivery_method;
      }
      if (cart.delivery_fee !== null) {
        deliveryFees[cart.business_id] = cart.delivery_fee;
      }
    });

    return NextResponse.json({
      cart: formattedItems,
      businessNames,
      deliveryMethods,
      deliveryFees,
      preparationTimes: {}
    });

  } catch (error: any) {
    console.error('Error getting cart:', error);
    throw error;
  }
}

// Add item to cart
async function handleAddItem(body: any, isUserCart: boolean, cartIdentifier: string, sessionId: string): Promise<NextResponse> {
  const { item } = body;

  // Validate item
  if (!item?.id || !item?.businessId || !item?.name || typeof item?.price !== 'number' || typeof item?.quantity !== 'number') {
    return NextResponse.json(
      { error: 'Invalid item data' },
      { status: 400 }
    );
  }

  const businessId = parseInt(item.businessId.toString(), 10);
  if (isNaN(businessId)) {
    return NextResponse.json(
      { error: 'Invalid business ID' },
      { status: 400 }
    );
  }

  try {
    // Get or create business-specific cart
    const cartId = await getOrCreateBusinessCart(isUserCart, cartIdentifier, businessId, sessionId);

    // Check if item already exists (considering product, variant, AND customizations)
    let query = supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId)
      .eq('product_id', item.id)
      .eq('business_id', businessId);

    if (item.variantId) {
      query = query.eq('variant_id', item.variantId);
    } else {
      query = query.is('variant_id', null);
    }

    const { data: existingItems } = await query;

    // For items with customizations, we need to check if the exact same customizations exist
    let existingItem = null;

    if (existingItems && existingItems.length > 0) {
      if (!item.customizations || item.customizations.length === 0) {
        // If new item has no customizations, find existing item with no customizations
        for (const existing of existingItems) {
          const { data: existingCustomizations } = await supabase
            .from('cart_item_customizations')
            .select('id')
            .eq('cart_item_id', existing.id)
            .limit(1);

          if (!existingCustomizations || existingCustomizations.length === 0) {
            existingItem = existing;
            break;
          }
        }
      } else {
        // If new item has customizations, find existing item with identical customizations
        for (const existing of existingItems) {
          const { data: existingCustomizations } = await supabase
            .from('cart_item_customizations')
            .select('customization_group_id, customization_option_id')
            .eq('cart_item_id', existing.id)
            .order('customization_group_id, customization_option_id');

          // Create a comparable signature for existing customizations
          const existingSignature = (existingCustomizations || [])
            .map(c => `${c.customization_group_id}-${c.customization_option_id}`)
            .sort()
            .join('|');

          // Create a comparable signature for new customizations
          const newCustomizationOptions = item.customizations.flatMap(group =>
            group.options.map(option => `${group.groupId}-${option.id}`)
          ).sort();
          const newSignature = newCustomizationOptions.join('|');

          if (existingSignature === newSignature) {
            existingItem = existing;
            break;
          }
        }
      }
    }

    if (existingItem) {
      // Update quantity for existing item with identical customizations
      console.log(`📦 Updating quantity for existing cart item ${existingItem.id}: ${existingItem.quantity} + ${item.quantity}`);
      await supabase
        .from('cart_items')
        .update({
          quantity: existingItem.quantity + item.quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingItem.id);
    } else {
      // Fetch delivery attributes from products table
      console.log(`📦 Fetching delivery attributes for product ${item.id}...`);
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('weight_class_kg, thermal_requirement, size_category')
        .eq('id', item.id)
        .single();

      if (productError) {
        console.warn(`⚠️ Could not fetch delivery attributes for product ${item.id}:`, productError);
      }

      // Insert new item with delivery attributes
      console.log(`📦 Inserting new cart item for cart ${cartId}:`, {
        product_id: item.id,
        business_id: businessId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        weight_class_kg: productData?.weight_class_kg ?? null,
        thermal_requirement: productData?.thermal_requirement ?? null,
        size_category: productData?.size_category ?? null
      });

      const { data: insertedItem, error: insertError } = await supabase
        .from('cart_items')
        .insert({
          cart_id: cartId,
          product_id: item.id,
          variant_id: item.variantId || null,
          business_id: businessId,
          business_type: item.businessType,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          image_url: item.imageUrl,
          weight_class_kg: productData?.weight_class_kg ?? null,
          thermal_requirement: productData?.thermal_requirement ?? null,
          size_category: productData?.size_category ?? null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('*');

      if (insertError) {
        console.error('❌ Error inserting cart item:', insertError);
        throw insertError;
      }

      console.log('✅ Cart item inserted successfully:', insertedItem);

      // Handle customizations if they exist
      if (item.customizations && item.customizations.length > 0 && insertedItem && insertedItem[0]) {
        const cartItemId = insertedItem[0].id;
        console.log(`🎨 Adding ${item.customizations.length} customizations for cart item ${cartItemId}`);

        for (const customization of item.customizations) {
          for (const option of customization.options) {
            const { error: customizationError } = await supabase
              .from('cart_item_customizations')
              .insert({
                cart_item_id: cartItemId,
                customization_group_id: customization.groupId,
                customization_group_name: customization.groupName,
                customization_option_id: option.id,
                customization_option_name: option.name,
                price: option.price || 0
              });

            if (customizationError) {
              console.error('❌ Error inserting customization:', customizationError);
              // Don't throw error for customizations - continue with cart item creation
            } else {
              console.log(`✅ Added customization: ${customization.groupName} - ${option.name} (+£${(option.price || 0).toFixed(2)})`);
            }
          }
        }
      }
    }

    // Update cart timestamp
    await supabase
      .from('user_carts')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', cartId);

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error adding item:', error);
    throw error;
  }
}

// Remove item from cart
async function handleRemoveItem(body: any, isUserCart: boolean, cartIdentifier: string): Promise<NextResponse> {
  const { itemId, variantId } = body;

  try {
    // Find all carts for this user/session using is_anonymous flag (exclude archived carts)
    let query = supabase.from('user_carts').select('id, business_id').eq('is_archived', false);
    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: carts } = await query;
    if (!carts?.length) return NextResponse.json({ success: true });

    const cartIds = carts.map(cart => cart.id);

    // Find and delete the cart item, and get the business_id of the deleted item
    // Note: Customizations will be automatically deleted due to foreign key constraints
    let deleteQuery = supabase
      .from('cart_items')
      .delete()
      .in('cart_id', cartIds)
      .eq('product_id', itemId)
      .select('business_id, cart_id, id');

    if (variantId) {
      deleteQuery = deleteQuery.eq('variant_id', variantId);
    } else {
      deleteQuery = deleteQuery.is('variant_id', null);
    }

    const { data: deletedItems } = await deleteQuery;

    // Log customization cleanup (they should be auto-deleted by foreign key constraints)
    if (deletedItems && deletedItems.length > 0) {
      console.log(`🗑️ Deleted ${deletedItems.length} cart items, customizations auto-deleted by FK constraints`);
    }

    // Check if any business carts are now empty and should be removed
    if (deletedItems && deletedItems.length > 0) {
      for (const deletedItem of deletedItems) {
        const { business_id, cart_id } = deletedItem;

        // Check if there are any remaining items for this business cart
        const { data: remainingItems, error: checkError } = await supabase
          .from('cart_items')
          .select('id')
          .eq('cart_id', cart_id)
          .limit(1);

        if (checkError) {
          console.error('Error checking remaining items:', checkError);
          continue;
        }

        // If no remaining items, remove the business cart
        if (!remainingItems || remainingItems.length === 0) {
          console.log(`🗑️ Removing empty business cart for business ${business_id}, cart ${cart_id}`);

          const { error: deleteCartError } = await supabase
            .from('user_carts')
            .delete()
            .eq('id', cart_id);

          if (deleteCartError) {
            console.error('Error removing empty business cart:', deleteCartError);
          } else {
            console.log(`✅ Successfully removed empty business cart for business ${business_id}`);
          }
        }
      }
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error removing item:', error);
    throw error;
  }
}

// Update item quantity
async function handleUpdateQuantity(body: any, isUserCart: boolean, cartIdentifier: string): Promise<NextResponse> {
  const { itemId, variantId, quantity } = body;

  if (typeof quantity !== 'number' || quantity < 0) {
    return NextResponse.json(
      { error: 'Invalid quantity' },
      { status: 400 }
    );
  }

  try {
    // Find all carts for this user/session using is_anonymous flag (exclude archived carts)
    let query = supabase.from('user_carts').select('id').eq('is_archived', false);
    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: carts } = await query;
    if (!carts?.length) return NextResponse.json({ success: true });

    const cartIds = carts.map(cart => cart.id);

    if (quantity === 0) {
      // Remove item if quantity is 0
      let deleteQuery = supabase
        .from('cart_items')
        .delete()
        .in('cart_id', cartIds)
        .eq('product_id', itemId)
        .select('business_id, cart_id');

      if (variantId) {
        deleteQuery = deleteQuery.eq('variant_id', variantId);
      } else {
        deleteQuery = deleteQuery.is('variant_id', null);
      }

      const { data: deletedItems } = await deleteQuery;

      // Check if any business carts are now empty and should be removed
      if (deletedItems && deletedItems.length > 0) {
        for (const deletedItem of deletedItems) {
          const { business_id, cart_id } = deletedItem;

          // Check if there are any remaining items for this business cart
          const { data: remainingItems, error: checkError } = await supabase
            .from('cart_items')
            .select('id')
            .eq('cart_id', cart_id)
            .limit(1);

          if (checkError) {
            console.error('Error checking remaining items:', checkError);
            continue;
          }

          // If no remaining items, remove the business cart
          if (!remainingItems || remainingItems.length === 0) {
            console.log(`🗑️ Removing empty business cart for business ${business_id}, cart ${cart_id} (quantity set to 0)`);

            const { error: deleteCartError } = await supabase
              .from('user_carts')
              .delete()
              .eq('id', cart_id);

            if (deleteCartError) {
              console.error('Error removing empty business cart:', deleteCartError);
            } else {
              console.log(`✅ Successfully removed empty business cart for business ${business_id}`);
            }
          }
        }
      }
    } else {
      // Update quantity
      let updateQuery = supabase
        .from('cart_items')
        .update({
          quantity,
          updated_at: new Date().toISOString()
        })
        .in('cart_id', cartIds)
        .eq('product_id', itemId);

      if (variantId) {
        updateQuery = updateQuery.eq('variant_id', variantId);
      } else {
        updateQuery = updateQuery.is('variant_id', null);
      }

      await updateQuery;
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error updating quantity:', error);
    throw error;
  }
}

// Clear cart
async function handleClearCart(isUserCart: boolean, cartIdentifier: string): Promise<NextResponse> {
  try {
    // Find all carts for this user/session using is_anonymous flag (exclude archived carts)
    let query = supabase.from('user_carts').select('id').eq('is_archived', false);
    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: carts } = await query;
    if (!carts?.length) return NextResponse.json({ success: true });

    const cartIds = carts.map(cart => cart.id);

    // CRITICAL FIX: Preserve cart_items that are linked to orders
    // Only delete cart_items that are NOT referenced by any orders
    console.log(`🗑️ Clearing cart for ${cartIds.length} carts...`);

    // Check which cart_ids are referenced by orders
    const { data: ordersWithCarts, error: ordersError } = await supabase
      .from('orders')
      .select('cart_id')
      .in('cart_id', cartIds)
      .not('cart_id', 'is', null);

    if (ordersError) {
      console.error('Error checking orders for cart references:', ordersError);
      // Continue with deletion if we can't check orders
    }

    const cartIdsWithOrders = new Set(ordersWithCarts?.map(order => order.cart_id) || []);
    const cartIdsToDeleteItems = cartIds.filter(cartId => !cartIdsWithOrders.has(cartId));

    console.log(`🗑️ Cart IDs with orders (preserving items): ${cartIdsWithOrders.size}`);
    console.log(`🗑️ Cart IDs without orders (deleting items): ${cartIdsToDeleteItems.length}`);

    // Only delete cart_items for carts that are NOT linked to orders
    if (cartIdsToDeleteItems.length > 0) {
      const { error: deleteItemsError } = await supabase
        .from('cart_items')
        .delete()
        .in('cart_id', cartIdsToDeleteItems);

      if (deleteItemsError) {
        console.error('Error deleting cart items:', deleteItemsError);
      } else {
        console.log(`✅ Deleted cart items for ${cartIdsToDeleteItems.length} carts without orders`);
      }
    }

    // For carts with orders, we still clear the user_carts table to remove them from the user's interface
    // but preserve the cart_items for business fulfillment
    if (cartIdsWithOrders.size > 0) {
      console.log(`🔄 Preserving cart items for ${cartIdsWithOrders.size} carts linked to orders`);
    }

    // For carts linked to orders, we cannot delete the user_carts record due to foreign key constraints
    // Instead, we'll only delete user_carts that are NOT linked to orders
    if (cartIdsToDeleteItems.length > 0) {
      const { error: deleteCartsError } = await supabase
        .from('user_carts')
        .delete()
        .in('id', cartIdsToDeleteItems);

      if (deleteCartsError) {
        console.error('Error deleting user carts without orders:', deleteCartsError);
      } else {
        console.log(`✅ Deleted ${cartIdsToDeleteItems.length} user carts without orders`);
      }
    }

    // For carts with orders, we preserve both the user_carts and cart_items records
    // This ensures businesses can access the order data for fulfillment
    if (cartIdsWithOrders.size > 0) {
      console.log(`🔒 Preserved ${cartIdsWithOrders.size} user_carts records linked to orders (cannot delete due to foreign key constraints)`);
    }

    // CRITICAL FIX: After clearing, we need to ensure the user gets fresh cart IDs for their next session
    // Mark the preserved carts as "archived" so they won't be reused for new shopping sessions
    if (cartIdsWithOrders.size > 0) {
      const { error: archiveError } = await supabase
        .from('user_carts')
        .update({
          is_archived: true,
          updated_at: new Date().toISOString()
        })
        .in('id', Array.from(cartIdsWithOrders));

      if (archiveError) {
        console.error('Error archiving carts with orders:', archiveError);
        // Continue anyway - this is not critical for the clearing operation
      } else {
        console.log(`📦 Archived ${cartIdsWithOrders.size} carts linked to orders to prevent reuse`);
      }
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error clearing cart:', error);
    throw error;
  }
}

// Get cart IDs for all businesses with items
async function handleGetCartIds(isUserCart: boolean, cartIdentifier: string): Promise<NextResponse> {
  try {
    // Get all carts using is_anonymous flag to determine which identifier to use (exclude archived carts)
    let query = supabase
      .from('user_carts')
      .select('id, business_id')
      .eq('is_archived', false) // Exclude archived carts
      .order('updated_at', { ascending: false });

    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: userCarts, error: cartError } = await query;

    if (cartError) throw cartError;

    if (!userCarts || userCarts.length === 0) {
      return NextResponse.json({
        cartIds: {}
      });
    }

    // Get all cart items to filter out empty carts
    const cartIds = userCarts.map(cart => cart.id);
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('cart_id')
      .in('cart_id', cartIds);

    if (itemsError) throw itemsError;

    // Filter out carts that have no items and create business_id -> cart_id mapping
    const cartIdsWithItems = new Set(cartItems?.map(item => item.cart_id) || []);
    const businessCartIds: Record<string, string> = {};

    userCarts.forEach(cart => {
      if (cartIdsWithItems.has(cart.id)) {
        businessCartIds[cart.business_id.toString()] = cart.id;
      }
    });

    console.log(`🆔 Retrieved cart IDs for ${Object.keys(businessCartIds).length} businesses:`, businessCartIds);

    return NextResponse.json({
      cartIds: businessCartIds
    });

  } catch (error: any) {
    console.error('Error getting cart IDs:', error);
    throw error;
  }
}

// Update delivery method
async function handleUpdateDeliveryMethod(body: any, isUserCart: boolean, cartIdentifier: string, sessionId: string): Promise<NextResponse> {
  const { businessId, deliveryMethod, deliveryFee, userLocation } = body;

  if (!businessId || !deliveryMethod || !['delivery', 'pickup'].includes(deliveryMethod)) {
    return NextResponse.json(
      { error: 'Invalid delivery method data' },
      { status: 400 }
    );
  }

  try {
    // Find the specific business cart using is_anonymous flag (exclude archived carts)
    let query = supabase
      .from('user_carts')
      .select('id')
      .eq('business_id', businessId)
      .eq('is_archived', false); // Exclude archived carts

    if (isUserCart) {
      // For logged-in users: find carts where is_anonymous = false and user_id matches
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      // For anonymous users: find carts where is_anonymous = true and session_id matches
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: cart, error: findError } = await query.maybeSingle();

    if (findError) {
      console.error('Error finding cart:', findError);
      throw findError;
    }

    // Calculate delivery fee if not provided and method is delivery
    let finalDeliveryFee = deliveryFee;

    if (deliveryMethod === 'delivery' && (deliveryFee === undefined || deliveryFee === null)) {
      console.log(`💰 Calculating delivery fee for business ${businessId}...`);

      try {
        // Get business details for delivery calculation
        const { data: business, error: businessError } = await supabase
          .from('businesses')
          .select('delivery_fee_model, delivery_fee, delivery_fee_per_km, coordinates')
          .eq('id', businessId)
          .single();

        if (businessError || !business) {
          console.warn(`⚠️ Could not fetch business details for ${businessId}, using default fee`);
          finalDeliveryFee = 2.50; // Default delivery fee
        } else {
          // Use user location data passed from client (same as business page)
          const finalPostcode = userLocation?.postcode || 'JE1 1AA';
          const finalCoordinates = userLocation?.coordinates || [49.1858, -2.1054];

          console.log(`💰 Using user location: postcode=${finalPostcode}, coordinates=[${finalCoordinates[0]}, ${finalCoordinates[1]}]`);

          // Parse business coordinates (stored as string in database)
          let businessCoordinates = [49.1858, -2.1054]; // Default St. Helier coordinates
          if (business.coordinates) {
            try {
              if (typeof business.coordinates === 'string') {
                // Parse string format like "(-2.109951,49.185141)"
                const coordStr = business.coordinates.replace(/[()]/g, '');
                const [lng, lat] = coordStr.split(',').map(Number);
                businessCoordinates = [lng, lat];
              } else if (Array.isArray(business.coordinates)) {
                businessCoordinates = business.coordinates;
              }
            } catch (error) {
              console.warn(`⚠️ Could not parse business coordinates: ${business.coordinates}`);
            }
          }

          console.log(`💰 Using business coordinates: [${businessCoordinates[0]}, ${businessCoordinates[1]}]`);

          // Use the delivery calculation API for consistency with business page
          const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/delivery/calculate-fee`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              businessId: businessId,
              businessCoordinates: businessCoordinates,
              customerCoordinates: finalCoordinates, // Use actual user coordinates
              postcode: finalPostcode, // Use actual user postcode
              deliveryFeeModel: business.delivery_fee_model || 'mixed',
              deliveryFee: business.delivery_fee || 2.50,
              deliveryFeePerKm: business.delivery_fee_per_km || 0.50
            })
          });

          if (response.ok) {
            const result = await response.json();
            finalDeliveryFee = result.feeNumeric || result.fee || 2.50;
            console.log(`💰 Used delivery calculation API result: £${finalDeliveryFee.toFixed(2)}`);
          } else {
            console.warn('⚠️ Delivery calculation API failed, using business default fee');
            finalDeliveryFee = business.delivery_fee || 2.50;
          }

          console.log(`💰 Calculated delivery fee for business ${businessId}: £${finalDeliveryFee.toFixed(2)}`);
        }
      } catch (error) {
        console.error(`❌ Error calculating delivery fee for business ${businessId}:`, error);
        finalDeliveryFee = 2.50; // Fallback to default
      }
    } else if (deliveryMethod === 'pickup') {
      finalDeliveryFee = 0; // Pickup is always free
    } else if (deliveryFee !== undefined && deliveryFee !== null) {
      finalDeliveryFee = deliveryFee; // Use provided fee
    } else {
      finalDeliveryFee = 0; // Default fallback
    }

    if (!cart) {
      // Create a new cart for this business if it doesn't exist
      console.log(`📦 Creating new cart for business ${businessId} to set delivery method`);

      const cartData: any = {
        business_id: businessId,
        delivery_method: deliveryMethod,
        delivery_fee: finalDeliveryFee,
        is_anonymous: !isUserCart, // Set is_anonymous flag based on user type
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (isUserCart) {
        cartData.user_id = cartIdentifier;
        cartData.session_id = sessionId; // Always store session_id for order grouping
      } else {
        cartData.session_id = sessionId;
        cartData.user_id = null;
      }

      const { error: createError } = await supabase
        .from('user_carts')
        .insert(cartData);

      if (createError) {
        console.error('Error creating cart for delivery method:', createError);
        throw createError;
      }

      console.log(`✅ Created new cart for business ${businessId} with delivery method: ${deliveryMethod} and fee: £${finalDeliveryFee.toFixed(2)}`);
    } else {
      // Update existing cart's delivery method and fee
      const { error: updateError } = await supabase
        .from('user_carts')
        .update({
          delivery_method: deliveryMethod, // Store as TEXT
          delivery_fee: finalDeliveryFee,
          updated_at: new Date().toISOString()
        })
        .eq('id', cart.id);

      if (updateError) {
        console.error('Error updating delivery method:', updateError);
        throw updateError;
      }

      console.log(`✅ Updated delivery method for business ${businessId}: ${deliveryMethod} with fee: £${finalDeliveryFee.toFixed(2)}`);
    }

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error updating delivery method:', error);
    return NextResponse.json(
      { error: `Failed to update delivery method: ${error.message}` },
      { status: 500 }
    );
  }
}

// Update delivery fee only
async function handleUpdateDeliveryFee(body: any, isUserCart: boolean, cartIdentifier: string, sessionId: string): Promise<NextResponse> {
  const { businessId, deliveryFee } = body;

  if (!businessId || typeof deliveryFee !== 'number') {
    return NextResponse.json(
      { error: 'Invalid delivery fee data - businessId and deliveryFee (number) required' },
      { status: 400 }
    );
  }

  try {
    console.log(`🚚 Updating delivery fee for business ${businessId} to £${deliveryFee.toFixed(2)}`);

    // Find the cart for this business (exclude archived carts)
    let query = supabase
      .from('user_carts')
      .select('id, business_id, delivery_method, delivery_fee')
      .eq('business_id', businessId)
      .eq('is_archived', false); // Exclude archived carts

    if (isUserCart) {
      query = query.eq('user_id', cartIdentifier).eq('is_anonymous', false);
    } else {
      query = query.eq('session_id', cartIdentifier).eq('is_anonymous', true);
    }

    const { data: carts, error: cartError } = await query;

    if (cartError) {
      console.error('Error finding cart:', cartError);
      throw cartError;
    }

    if (!carts || carts.length === 0) {
      return NextResponse.json(
        { error: `No cart found for business ${businessId}` },
        { status: 404 }
      );
    }

    const cart = carts[0];

    // Update the delivery fee
    const { error: updateError } = await supabase
      .from('user_carts')
      .update({
        delivery_fee: deliveryFee,
        updated_at: new Date().toISOString()
      })
      .eq('id', cart.id);

    if (updateError) {
      console.error('Error updating delivery fee:', updateError);
      throw updateError;
    }

    console.log(`✅ Updated delivery fee for business ${businessId}: £${deliveryFee.toFixed(2)}`);

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error updating delivery fee:', error);
    throw error;
  }
}
