"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Package,
  DollarSign,
  MapPin,
  Clock,
  Star,
  Volume2,
  Vibrate,
  Wifi,
  HelpCircle,
  Phone,
  Mail
} from "lucide-react"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileNavigation } from "@/components/driver-mobile-navigation"
import Link from "next/link"

export default function DriverMobileHelpPage() {
  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      <div className="p-4 space-y-6">
        {/* Page Title */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Help & Support</h1>
          <p className="text-gray-600">Get help with the Loop driver app</p>
        </div>

        {/* Quick Help Topics */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">Quick Help</h2>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center">
                <Package className="h-5 w-5 mr-2 text-blue-600" />
                Order Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Accepting Orders</h4>
                <p className="text-sm text-gray-600">
                  Swipe right on order cards to accept, or swipe left to decline.
                  You'll see pickup location, delivery address, and estimated earnings.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Order Status</h4>
                <p className="text-sm text-gray-600">
                  Track your current delivery progress. Update status as you pick up and deliver orders.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                Earnings & Payments
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">How Earnings Work</h4>
                <p className="text-sm text-gray-600">
                  You earn a base delivery fee plus any tips from customers.
                  Earnings are calculated per delivery and paid weekly.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Payment Schedule</h4>
                <p className="text-sm text-gray-600">
                  Payments are processed weekly via Stripe. Check your earnings page for payout status.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center">
                <Wifi className="h-5 w-5 mr-2 text-yellow-600" />
                Real-time Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Connection Status</h4>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-green-50 text-green-700 border-green-200">
                      <Wifi className="h-3 w-3 mr-1" />
                      Live
                    </Badge>
                    <span className="text-sm text-gray-600">Connected and monitoring for orders</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-yellow-50 text-yellow-700 border-yellow-200">
                      <Wifi className="h-3 w-3 mr-1" />
                      Connecting...
                    </Badge>
                    <span className="text-sm text-gray-600">Establishing connection</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Last Activity</h4>
                <p className="text-sm text-gray-600">
                  Shows when Loop last offered you an order or when you last interacted with the app.
                  This helps you know if the system is actively monitoring for deliveries.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Notification Controls</h4>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <Volume2 className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600">Sound alerts</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Vibrate className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600">Vibration alerts</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Best Practices */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">Best Practices</h2>

          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <Star className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-sm text-blue-900">Maintain High Ratings</h4>
                    <p className="text-sm text-blue-700">
                      Communicate with customers, handle food carefully, and deliver on time.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <Clock className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-sm text-blue-900">Time Management</h4>
                    <p className="text-sm text-blue-700">
                      Plan your routes efficiently and update order status promptly.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-sm text-blue-900">Location Services</h4>
                    <p className="text-sm text-blue-700">
                      Keep location services enabled for accurate tracking and better order matching.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Support */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900">Need More Help?</h2>

          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 rounded-full p-2">
                    <Phone className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">Driver Support</h4>
                    <p className="text-sm text-gray-600">Call us for urgent delivery issues</p>
                    <p className="text-sm font-medium text-green-600">+44 1534 123 456</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 rounded-full p-2">
                    <Mail className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">Email Support</h4>
                    <p className="text-sm text-gray-600">For general questions and feedback</p>
                    <p className="text-sm font-medium text-blue-600"><EMAIL></p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Link */}
        <Card className="border-2 border-dashed border-gray-300">
          <CardContent className="p-4 text-center">
            <HelpCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900 mb-1">More Questions?</h3>
            <p className="text-sm text-gray-600 mb-3">
              Check our comprehensive FAQ section for detailed answers.
            </p>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/driver/help" target="_blank" rel="noopener noreferrer">
                View Full FAQ
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Bottom Spacing for Navigation */}
        <div className="h-20"></div>
      </div>

      <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
