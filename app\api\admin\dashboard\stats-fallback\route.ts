import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in fallback, using direct admin access");
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check");
    } else {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
  } else {
    console.log("Found authorization header in fallback, attempting to verify");

    // Extract the token
    const token = authorization.replace('Bearer ', '');

    // In development mode, accept a dummy token
    if (process.env.NODE_ENV === 'development' && token === 'development-mode-token') {
      console.log("Development mode: Using dummy token in fallback");
      // Skip verification and proceed with admin access
    } else {
      try {
        // Skip token verification in development mode if token is empty or invalid
        if (process.env.NODE_ENV === 'development' && (!token || token === 'undefined' || token === 'null')) {
          console.log("Development mode: Skipping token verification for empty/invalid token in fallback");
        } else {
          // Verify the token
          const { data: { user }, error } = await adminClient.auth.getUser(token);

          if (error || !user) {
            console.error("Invalid token in fallback:", error);

            // In development mode, continue anyway
            if (process.env.NODE_ENV === 'development') {
              console.log("Development mode: Continuing despite invalid token in fallback");
            } else {
              return NextResponse.json(
                { error: "Invalid authentication token" },
                { status: 401 }
              );
            }
          } else {
            console.log("Token verified for user in fallback:", user.email);
          }
        }
      } catch (authError) {
        console.error("Error verifying token in fallback:", authError);

        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          );
        } else {
          console.log("Development mode: Continuing despite auth error in fallback");
        }
      }
    }
  }
  
  try {
    console.log("Using fallback stats endpoint for admin dashboard");

    // Try to get real data from the database
    let stats = {
      totalBusinesses: 0,
      pendingBusinesses: 0,
      totalUsers: 0,
      totalOrders: 0
    };

    let pendingBusinesses = [];

    try {
      // Get total businesses
      const { count: totalBusinesses } = await adminClient
        .from("businesses")
        .select("*", { count: "exact", head: true });

      if (totalBusinesses) {
        stats.totalBusinesses = totalBusinesses;
        console.log("Fallback: Got total businesses:", totalBusinesses);
      }

      // Get pending businesses
      const { count: pendingCount } = await adminClient
        .from("businesses")
        .select("*", { count: "exact", head: true })
        .is("is_approved", null);

      if (pendingCount) {
        stats.pendingBusinesses = pendingCount;
        console.log("Fallback: Got pending businesses:", pendingCount);
      }

      // Get total users
      const { count: totalUsers } = await adminClient
        .from("users")
        .select("*", { count: "exact", head: true });

      if (totalUsers) {
        stats.totalUsers = totalUsers;
        console.log("Fallback: Got total users:", totalUsers);
      }

      // Get pending business details - first check if view exists
      try {
        // First check if the view exists
        const { error: viewCheckError } = await adminClient
          .from("business_details_view")
          .select("id")
          .limit(1);

        if (viewCheckError) {
          console.error("Fallback: business_details_view might not exist:", viewCheckError);
          // Try to get data directly from businesses table as fallback
          try {
            const { data: fallbackData, error: fallbackError } = await adminClient
              .from("businesses")
              .select("id, name, created_at, business_type_id")
              .is("is_approved", null)
              .order("created_at", { ascending: false })
              .limit(5);

            if (fallbackError) {
              console.error("Fallback: Error fetching from businesses table:", fallbackError);
            } else if (fallbackData) {
              // Map the data to match expected format
              pendingBusinesses = fallbackData.map(b => ({
                id: b.id,
                name: b.name,
                business_type: "Business", // Default since we don't have the type name
                created_at: b.created_at
              }));
              console.log("Fallback: Successfully fetched pending business details from fallback:", pendingBusinesses.length);
            }
          } catch (fallbackErr) {
            console.error("Fallback: Exception in fallback query:", fallbackErr);
          }
        } else {
          // View exists, proceed with original query
          const { data: pendingData } = await adminClient
            .from("business_details_view")
            .select("id, name, business_type, created_at")
            .is("is_approved", null)
            .order("created_at", { ascending: false })
            .limit(5);

          if (pendingData) {
            pendingBusinesses = pendingData;
            console.log("Fallback: Got pending business details:", pendingData.length);
          }
        }
      } catch (pendingErr) {
        console.error("Fallback: Error fetching pending business details:", pendingErr);
      }
    } catch (dbError) {
      console.error("Error fetching data in fallback endpoint:", dbError);
      // Continue with default values
    }

    return NextResponse.json({
      stats,
      pendingBusinesses
    });
  } catch (error: any) {
    console.error("Error in fallback stats endpoint:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
