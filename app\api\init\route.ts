import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  try {
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Check if the images bucket exists, create it if it doesn't
    const { data: buckets } = await adminClient.storage.listBuckets()
    const imagesBucketExists = buckets?.some(bucket => bucket.name === "images")

    if (!imagesBucketExists) {
      // Create the images bucket
      const { error: createBucketError } = await adminClient.storage.createBucket("images", {
        public: true,
        fileSizeLimit: 5 * 1024 * 1024 // 5MB
      })

      if (createBucketError) {
        console.error("Error creating images bucket:", createBucketError)
        return NextResponse.json(
          { error: "Failed to create images bucket" },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      status: "Initialization complete",
      buckets: buckets?.map(bucket => bucket.name) || []
    })
  } catch (error: any) {
    console.error("Error in GET /api/init:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
