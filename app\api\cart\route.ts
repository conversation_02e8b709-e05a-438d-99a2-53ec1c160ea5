import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// GET endpoint to fetch a user's cart
export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    const { data: { user } } = await supabaseClient.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get or create the user's cart
    let cartId;
    const { data: existingCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('user_id', userData.id)
      .single();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch user cart', details: cartError.message },
        { status: 500 }
      );
    }

    if (existingCart) {
      cartId = existingCart.id;
    } else {
      // Create a new cart for the user
      const { data: newCart, error: createCartError } = await supabase
        .from('user_carts')
        .insert([{ user_id: userData.id }])
        .select('id')
        .single();

      if (createCartError) {
        console.error('Error creating user cart:', createCartError);
        return NextResponse.json(
          { error: 'Failed to create user cart', details: createCartError.message },
          { status: 500 }
        );
      }

      cartId = newCart.id;
    }

    // Get the cart items
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId);

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart items', details: itemsError.message },
        { status: 500 }
      );
    }

    // Format the cart items for the response
    const formattedItems = cartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id,
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url,
      // Include delivery attributes
      weight_class_kg: item.weight_class_kg,
      thermal_requirement: item.thermal_requirement,
      size_category: item.size_category
    }));

    // Get business names
    const businessIds = [...new Set(cartItems.map(item => item.business_id))];
    const businessNames = {};

    for (const businessId of businessIds) {
      // Look up business by numeric ID
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('name, slug')
        .eq('id', businessId)
        .single();

      if (!businessError && business) {
        // Store the business name using the numeric ID as the key
        businessNames[businessId] = business.name;
        console.log(`Found business name for ID ${businessId}: ${business.name}`);
      } else {
        console.log(`Business not found for ID ${businessId}, using default name`);
        // Use a default name if the business is not found
        businessNames[businessId] = `Business ${businessId}`;
      }
    }

    // Return the cart data
    const data = {
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames
    };

    if (error) {
      console.error('Error fetching cart:', error);
      return NextResponse.json(
        { error: 'Failed to fetch cart', details: error.message },
        { status: 500 }
      );
    }

    // If no data returned, return empty cart
    if (!data) {
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {}
      });
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST endpoint to update a user's cart
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    const { data: { user } } = await supabaseClient.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    if (!body.cart || !body.cart.items || !Array.isArray(body.cart.items)) {
      return NextResponse.json(
        { error: 'Valid cart data with items array is required' },
        { status: 400 }
      );
    }

    // Process each item in the cart
    const results = [];
    const items = body.cart.items;

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get or create the user's cart
    let cartId;
    const { data: existingCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('user_id', userData.id)
      .single();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch user cart', details: cartError.message },
        { status: 500 }
      );
    }

    if (existingCart) {
      cartId = existingCart.id;
    } else {
      // Create a new cart for the user
      const { data: newCart, error: createCartError } = await supabase
        .from('user_carts')
        .insert([{ user_id: userData.id }])
        .select('id')
        .single();

      if (createCartError) {
        console.error('Error creating user cart:', createCartError);
        return NextResponse.json(
          { error: 'Failed to create user cart', details: createCartError.message },
          { status: 500 }
        );
      }

      cartId = newCart.id;
    }

    // First, clear existing cart if requested
    if (body.clearExisting) {
      const { error: clearError } = await supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cartId);

      if (clearError) {
        console.error('Error clearing cart:', clearError);
        return NextResponse.json(
          { error: 'Failed to clear cart', details: clearError.message },
          { status: 500 }
        );
      }
    }

    // Add or update each item
    for (const item of items) {
      // Check if the item already exists in the cart
      const { data: existingItems, error: existingItemError } = await supabase
        .from('cart_items')
        .select('id')
        .eq('cart_id', cartId)
        .eq('product_id', parseInt(item.id, 10) || 0)
        .is('variant_id', item.variantId || null);

      if (existingItemError) {
        console.error('Error checking for existing item:', existingItemError);
        results.push({
          item: item.id,
          success: false,
          error: `Failed to check for existing item: ${existingItemError.message}`
        });
        continue; // Skip to the next item
      }

      let itemResult;

      // If the item exists, update it
      if (existingItems && existingItems.length > 0) {
        const existingItemId = existingItems[0].id;

        const { data: updateResult, error: updateError } = await supabase
          .from('cart_items')
          .update({
            quantity: item.quantity,
            price: item.price,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingItemId)
          .select('id')
          .single();

        if (updateError) {
          console.error('Error updating cart item:', updateError);
          results.push({
            item: item.id,
            success: false,
            error: `Failed to update item: ${updateError.message}`
          });
          continue; // Skip to the next item
        }

        itemResult = {
          id: updateResult.id,
          action: 'updated'
        };
      } else {
        // Fetch delivery attributes from products table
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select('weight_class_kg, thermal_requirement, size_category')
          .eq('id', parseInt(item.id, 10) || 0)
          .single();

        if (productError) {
          console.warn(`⚠️ Could not fetch delivery attributes for product ${item.id}:`, productError);
        }

        // If the item doesn't exist, insert it with delivery attributes
        const { data: insertResult, error: insertError } = await supabase
          .from('cart_items')
          .insert([{
            cart_id: cartId,
            product_id: parseInt(item.id, 10) || 0,
            variant_id: item.variantId || null,
            quantity: item.quantity,
            business_id: item.businessId,
            business_type: item.businessType,
            name: item.name,
            price: item.price,
            image_url: item.imageUrl || null,
            weight_class_kg: productData?.weight_class_kg ?? null,
            thermal_requirement: productData?.thermal_requirement ?? null,
            size_category: productData?.size_category ?? null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select('id')
          .single();

        if (insertError) {
          console.error('Error inserting cart item:', insertError);
          results.push({
            item: item.id,
            success: false,
            error: `Failed to insert item: ${insertError.message}`
          });
          continue; // Skip to the next item
        }

        itemResult = {
          id: insertResult.id,
          action: 'inserted'
        };
      }

      // Add the result to the results array
      results.push({
        item: item.id,
        success: true,
        result: itemResult
      });
    }

    // Get the updated cart items
    const { data: updatedCartItems, error: updatedItemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId);

    if (updatedItemsError) {
      console.error('Error fetching updated cart items:', updatedItemsError);
      return NextResponse.json({
        success: results.every(r => r.success),
        results,
        error: 'Failed to fetch updated cart items'
      });
    }

    // Format the cart items for the response
    const formattedItems = updatedCartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id,
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url
    }));

    // Get business names
    const businessIds = [...new Set(updatedCartItems.map(item => item.business_id))];
    const updatedBusinessNames = {};

    for (const businessId of businessIds) {
      // Look up business by numeric ID
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('name, slug')
        .eq('id', businessId)
        .single();

      if (!businessError && business) {
        // Store the business name using the numeric ID as the key
        updatedBusinessNames[businessId] = business.name;
        console.log(`Found business name for ID ${businessId}: ${business.name}`);
      } else {
        console.log(`Business not found for ID ${businessId}, using default name`);
        // Use a default name if the business is not found
        updatedBusinessNames[businessId] = `Business ${businessId}`;
      }
    }

    // Create the updated cart data
    const updatedCart = {
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames: updatedBusinessNames
    };

    return NextResponse.json({
      success: results.every(r => r.success),
      results,
      cart: updatedCart.cart,
      businessNames: updatedCart.businessNames
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clear a user's cart
export async function DELETE(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    const { data: { user } } = await supabaseClient.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get the user's cart ID
    const { data: userCart, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('user_id', userData.id)
      .single();

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch user cart', details: cartError.message },
        { status: 500 }
      );
    }

    // If no cart exists, return success (nothing to clear)
    if (!userCart) {
      return NextResponse.json({
        success: true,
        cleared: 0
      });
    }

    // Clear the cart items
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('cart_id', userCart.id)
      .select('id');

    if (error) {
      console.error('Error clearing cart items:', error);
      return NextResponse.json(
        { error: 'Failed to clear cart items', details: error.message },
        { status: 500 }
      );
    }

    // Also clear the cart context values in user_carts
    const { error: updateError } = await supabase
      .from('user_carts')
      .update({
        delivery_method: {},
        delivery_type: {},
        delivery_fees: {},
        preparation_times: {},
        delivery_times: {},
        scheduled_times: {},
        updated_at: new Date().toISOString()
      })
      .eq('id', userCart.id);

    if (updateError) {
      console.error('Error clearing cart context values:', updateError);
      // Don't return error, just log it since cart items were cleared successfully
    }

    return NextResponse.json({
      success: true,
      cleared: data ? data.length : 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
