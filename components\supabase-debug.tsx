'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

export default function SupabaseDebug() {
  const [status, setStatus] = useState<string>('Checking connection...');
  const [error, setError] = useState<string | null>(null);
  const [connectionInfo, setConnectionInfo] = useState<any>({});

  useEffect(() => {
    async function checkConnection() {
      try {
        // Get environment variables
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;



        setConnectionInfo({
          url: supabaseUrl,
          keyPresent: !!supabaseAnonKey,
        });

        if (!supabaseUrl || !supabaseAnonKey) {
          setStatus('Missing environment variables');
          setError('Supabase URL or Anon Key is missing');
          return;
        }

        // Initialize Supabase client
        const supabase = createClient(supabaseUrl, supabaseAnonKey);

        // Try a simple query to check connection
        const { data, error } = await supabase.from('users').select('count', { count: 'exact' });

        if (error) {
          setStatus('Connection error');
          setError(error.message);
          return;
        }

        setStatus('Connected successfully');

        // Now try to access the restaurants table
        const { data: restaurantData, error: restaurantError } = await supabase
          .from('restaurants')
          .select('*');

        if (restaurantError) {
          setStatus('Connected, but restaurants table error');
          setError(restaurantError.message);
          return;
        }

        setStatus(`Connected and found ${restaurantData?.length || 0} restaurants`);

      } catch (err: any) {
        setStatus('Unexpected error');
        setError(err.message || 'Unknown error occurred');
      }
    }

    checkConnection();
  }, []);

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Supabase Connection Status</h2>

      <div className="mb-4">
        <div className="font-medium">Status:</div>
        <div className={`mt-1 ${status.includes('error') || error ? 'text-red-600' : 'text-green-600'}`}>
          {status}
        </div>
      </div>

      {error && (
        <div className="mb-4">
          <div className="font-medium">Error:</div>
          <div className="mt-1 text-red-600 text-sm bg-red-50 p-2 rounded">
            {error}
          </div>
        </div>
      )}

      <div className="mb-4">
        <div className="font-medium">Connection Info:</div>
        <div className="mt-1 text-sm bg-gray-50 p-2 rounded">
          <div>URL: {connectionInfo.url || 'Not available'}</div>
          <div>API Key: {connectionInfo.keyPresent ? 'Present' : 'Missing'}</div>
        </div>
      </div>

      <div className="text-xs text-gray-500 mt-4">
        Check the browser console for more detailed information.
      </div>
    </div>
  );
}
