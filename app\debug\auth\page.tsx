import { AuthStateCleaner } from "@/components/debug/auth-state-cleaner"

export default function AuthDebugPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Authentication Debug Tools
        </h1>
        
        <div className="space-y-6">
          <AuthStateCleaner />
          
          <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">
              Common Authentication Issues
            </h3>
            <ul className="text-sm text-blue-600 space-y-1">
              <li>• 403 Permission Denied errors</li>
              <li>• Can access protected pages but appear logged out</li>
              <li>• Authentication token expired or invalid</li>
              <li>• Inconsistent authentication state</li>
            </ul>
          </div>

          <div className="p-4 border border-green-200 rounded-lg bg-green-50">
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              After Clearing Authentication State
            </h3>
            <p className="text-sm text-green-600">
              The page will refresh automatically. You'll need to log in again to access protected areas.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
