import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface PushSubscriptionData {
  endpoint: string
  expirationTime?: number | null
  keys: {
    p256dh: string
    auth: string
  }
}

interface SubscribeRequest {
  subscription: PushSubscriptionData
  deviceType?: string
  browserName?: string
  userAgent?: string
  forceActivate?: boolean
  preferences?: {
    order_updates?: boolean
    delivery_updates?: boolean
    messages?: boolean
    marketing?: boolean
    quiet_hours?: {
      enabled?: boolean
      start?: string
      end?: string
    }
  }
}

// POST - Subscribe to push notifications
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const body: SubscribeRequest = await request.json()

    if (!body.subscription || !body.subscription.endpoint) {
      return NextResponse.json(
        { error: 'Valid push subscription is required' },
        { status: 400 }
      )
    }

    // Use the auth UUID directly since push_subscriptions.user_id references auth.users.id
    const authUserId = user.id

    // Default preferences
    const defaultPreferences = {
      order_updates: true,
      delivery_updates: true,
      messages: true,
      marketing: false,
      quiet_hours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      }
    }

    const preferences = { ...defaultPreferences, ...body.preferences }

    // If forceActivate is true, deactivate ALL existing subscriptions for this user
    // Otherwise, just deactivate subscriptions for this device type
    if (body.forceActivate) {
      console.log('🔄 Force activating - deactivating all existing subscriptions for user')
      await supabase
        .from('push_subscriptions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', authUserId)
    } else {
      // First, deactivate any existing subscriptions for this user and device type
      // This handles the case where a user might have multiple subscriptions
      await supabase
        .from('push_subscriptions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', authUserId)
        .eq('device_type', body.deviceType)
    }

    // Debug: Log the incoming subscription data
    console.log('📋 Incoming subscription data:', {
      type: typeof body.subscription,
      data: body.subscription,
      deviceType: body.deviceType
    })

    // Ensure subscription_data is properly formatted as JSON
    let subscriptionData
    try {
      // If it's already an object, use it directly
      // If it's a string, parse it
      subscriptionData = typeof body.subscription === 'string'
        ? JSON.parse(body.subscription)
        : body.subscription

      console.log('✅ Parsed subscription data:', subscriptionData)
    } catch (parseError) {
      console.error('❌ Error parsing subscription data:', parseError)
      console.error('Raw subscription data:', body.subscription)
      return NextResponse.json(
        { error: 'Invalid subscription data format' },
        { status: 400 }
      )
    }

    // Create new subscription (or update if endpoint already exists)
    const subscriptionRecord = {
      user_id: authUserId,
      subscription_data: subscriptionData,
      device_type: body.deviceType,
      browser_name: body.browserName,
      user_agent: body.userAgent,
      preferences,
      is_active: true,
      last_used_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Try to insert, if it fails due to unique constraint, update instead
    let { data, error } = await supabase
      .from('push_subscriptions')
      .insert(subscriptionRecord)
      .select()
      .single()

    if (error && error.code === '23505') {
      // Unique constraint violation - update existing record
      console.log('Subscription exists, updating instead...')
      const result = await supabase
        .from('push_subscriptions')
        .update({
          subscription_data: subscriptionData,
          device_type: body.deviceType,
          browser_name: body.browserName,
          user_agent: body.userAgent,
          preferences,
          is_active: true,
          last_used_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', authUserId)
        .eq('subscription_data->endpoint', subscriptionData.endpoint)
        .select()
        .single()

      data = result.data
      error = result.error
    }

    if (error) {
      console.error('Error creating/updating push subscription:', error)
      return NextResponse.json(
        { error: 'Failed to create push subscription', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Push subscription created successfully',
      subscription: data
    })

  } catch (error: any) {
    console.error('Error in push subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get user's push subscriptions
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user

    // Use the auth UUID directly since push_subscriptions.user_id references auth.users.id
    const authUserId = user.id

    // Get user's active subscriptions
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('id, device_type, browser_name, preferences, is_active, created_at, last_used_at')
      .eq('user_id', authUserId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching push subscriptions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch push subscriptions' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      subscriptions: subscriptions || []
    })

  } catch (error: any) {
    console.error('Error fetching push subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Unsubscribe from push notifications
export async function DELETE(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('id')
    const endpoint = searchParams.get('endpoint')

    if (!subscriptionId && !endpoint) {
      return NextResponse.json(
        { error: 'Subscription ID or endpoint is required' },
        { status: 400 }
      )
    }

    // Use the auth UUID directly since push_subscriptions.user_id references auth.users.id
    const authUserId = user.id

    let query = supabase
      .from('push_subscriptions')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('user_id', authUserId)

    if (subscriptionId) {
      query = query.eq('id', subscriptionId)
    } else if (endpoint === 'all') {
      // Special case: deactivate all subscriptions for this user
      // No additional filter needed - just the user_id filter above
    } else if (endpoint) {
      query = query.eq('subscription_data->endpoint', endpoint)
    }

    const { error } = await query

    if (error) {
      console.error('Error unsubscribing from push notifications:', error)
      return NextResponse.json(
        { error: 'Failed to unsubscribe from push notifications' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Successfully unsubscribed from push notifications'
    })

  } catch (error: any) {
    console.error('Error unsubscribing from push notifications:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
