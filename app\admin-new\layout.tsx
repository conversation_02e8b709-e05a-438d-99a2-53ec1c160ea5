"use client"

import { Inter } from "next/font/google"
import { AdminLayoutWrapper } from "@/components/admin/admin-layout-wrapper"
import { AuthProvider } from "@/context/unified-auth-context"
import { SupabaseProvider } from "@/components/providers/supabase-provider"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

export default function AdminNewLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className={`${inter.variable} font-sans`}>
      <SupabaseProvider>
        <AuthProvider>
          <AdminLayoutWrapper>
            {children}
          </AdminLayoutWrapper>
        </AuthProvider>
      </SupabaseProvider>
    </div>
  )
}
