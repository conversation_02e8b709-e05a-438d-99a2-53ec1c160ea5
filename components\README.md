# Loop Jersey Components

This directory contains all reusable UI components used throughout the Loop Jersey application.

## Directory Structure

```
components/
├── admin/             # Admin-specific components
├── auth/              # Authentication-related components
├── business/          # Business-specific components
├── delivery/          # Delivery-related components
├── filters/           # Search and filtering components
├── mobile-search/     # Mobile-specific search components
├── product/           # Product-related components
├── product-import-ai-tool/ # AI tool for product import
├── providers/         # Provider components
├── search/            # Search components
└── ui/                # Base UI components (shadcn/ui)
```

## Key Components

### Layout Components
- `header.tsx` - Main application header with navigation
- `footer.tsx` - Main application footer
- `theme-wrapper.tsx` - Theme provider wrapper

### Business Components
- `business-card-small.tsx` - Small business card component
- `portrait-business-card.tsx` - Portrait-oriented business card
- `square-business-card.tsx` - Square-oriented business card
- `restaurant-card.tsx` - Restaurant-specific card
- `shop-card.tsx` - Shop-specific card

### Product Components
- `menu-item.tsx` - Restaurant menu item component
- `product-item.tsx` - Generic product item component
- `pharmacy-product.tsx` - Pharmacy product component
- `cafe-item.tsx` - Cafe item component
- `add-to-cart-button.tsx` - Button for adding items to cart

### Cart Components
- `cart-sheet.tsx` - Slide-in cart component

### Authentication Components
- `auth-monitor.tsx` - Authentication state monitoring component
- `biometric-login.tsx` - Biometric login component
- `biometric-registration.tsx` - Biometric registration component

### Search Components
- `category-scroller.tsx` - Horizontal category scroller
- `home-filters.tsx` - Home page filter components
- `search-form.tsx` - Search form component
- `search-form-new.tsx` - Updated search form component

### Delivery Components
- `delivery-mode-toggle.tsx` - Toggle for delivery mode
- `delivery-time-estimator.tsx` - Delivery time estimation component
- `delivery-tracker.tsx` - Delivery tracking component
- `dynamic-delivery-time.tsx` - Dynamic delivery time calculation

### Map Components
- `restaurant-map.tsx` - Restaurant location map
- `restaurant-delivery-map.tsx` - Restaurant delivery area map
- `live-delivery-map.tsx` - Live delivery tracking map
- `static-map.tsx` - Static map component

## Usage Guidelines

### Component Naming Conventions
- Use PascalCase for component names
- Use descriptive names that indicate the component's purpose
- Suffix with the component type (e.g., Button, Card, Dialog)

### Component Structure
- Each component should have a clear, single responsibility
- Use TypeScript interfaces to define props
- Include JSDoc comments for complex components

### Example Component Usage

```tsx
import { PortraitBusinessCard } from "@/components/portrait-business-card";

// Usage in a component or page
<PortraitBusinessCard 
  business={business}
  showRating={true}
  showDeliveryInfo={true}
/>
```

### Adding New Components

When adding new components:
1. Follow the naming conventions
2. Place in the appropriate subdirectory
3. Use TypeScript for type safety
4. Include proper prop validation
5. Consider reusability and composability

## UI Component Library

The `ui/` directory contains base UI components built with [shadcn/ui](https://ui.shadcn.com/), which is a collection of reusable components built with Radix UI and Tailwind CSS.

These components provide a foundation for building consistent UIs across the application and should be used whenever possible before creating custom components.
