import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify super admin access
async function verifySuperAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user is super admin
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (profile.role !== 'super_admin') {
      return { authorized: false, error: 'Super admin access required', status: 403 }
    }

    return { authorized: true, user, profile }
  } catch (error) {
    console.error('Error verifying super admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// GET - Fetch all categories with business type information
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { data: categories, error } = await adminClient
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        parent_id,
        level,
        business_type_id,
        business_types!business_type_id (
          name
        ),
        is_active,
        display_order,
        category_purpose,
        created_at,
        updated_at
      `)
      .order('business_type_id', { ascending: true })
      .order('display_order', { ascending: true })
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching categories:', error)
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      )
    }

    // Transform the data to flatten business type information
    const transformedCategories = categories.map(category => ({
      ...category,
      business_type_name: category.business_types?.name || null,
      business_types: undefined, // Remove the nested object
      is_active: Boolean(category.is_active) // Ensure boolean type
    }))

    return NextResponse.json(transformedCategories)
  } catch (error) {
    console.error('Error in GET /api/super-admin/categories:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create a new category
export async function POST(request: NextRequest) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      slug,
      description,
      business_type_id,
      category_purpose,
      display_order,
      is_active
    } = body

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      )
    }

    // Check if slug already exists
    const { data: existingCategory } = await adminClient
      .from('categories')
      .select('id')
      .eq('slug', slug)
      .single()

    if (existingCategory) {
      return NextResponse.json(
        { error: 'A category with this slug already exists' },
        { status: 400 }
      )
    }

    const categoryData = {
      name,
      slug,
      description: description || null,
      parent_id: null, // All categories are top-level now
      level: 1, // All categories are level 1
      business_type_id: business_type_id || null,
      category_purpose: category_purpose || 'specialization',
      display_order: display_order || 0,
      is_active: is_active !== undefined ? is_active : true
    }

    const { data: category, error } = await adminClient
      .from('categories')
      .insert(categoryData)
      .select()
      .single()

    if (error) {
      console.error('Error creating category:', error)
      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      )
    }

    return NextResponse.json(category, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/super-admin/categories:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
