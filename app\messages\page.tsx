"use client"

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from "@/context/unified-auth-context"
import { MessagesInterface } from './components/MessagesInterface'
import { Loader2 } from "lucide-react"

export default function MessagesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, isLoading } = useAuth()

  // Extract context from URL parameters
  const context = {
    order_id: searchParams.get('order_id'),
    business_id: searchParams.get('business_id'),
    rider_id: searchParams.get('rider_id'),
    connection_id: searchParams.get('connection_id'),
    role: searchParams.get('role') as 'customer' | 'business' | 'rider' | null,
    channel: searchParams.get('channel') as string | null,
    continue: searchParams.get('continue') === 'true'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-600 mx-auto mb-4" />
          <p className="text-gray-500">Loading messages...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login?redirect=/messages')
    return null
  }

  return (
    <div className="min-h-screen bg-white">
      <MessagesInterface user={user} context={context} />
    </div>
  )
}
