import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Get Supabase URL and keys from environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    // Check if environment variables are set
    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
      return NextResponse.json({
        error: 'Supabase environment variables not set',
        environment: {
          supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
          supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Not set',
          supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        }
      }, { status: 500 });
    }
    
    // Create Supabase clients
    const anonClient = createClient(supabaseUrl, supabaseAnonKey);
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test anon client connection
    let anonResult;
    try {
      const { data, error } = await anonClient
        .from('business_types')
        .select('*')
        .limit(5);
      
      anonResult = {
        success: !error,
        error: error ? error.message : null,
        errorDetails: error,
        data: data,
      };
    } catch (e) {
      anonResult = {
        success: false,
        error: e.message,
        exception: true,
      };
    }
    
    // Test admin client connection
    let adminResult;
    try {
      const { data, error } = await adminClient
        .from('business_types')
        .select('*')
        .limit(5);
      
      adminResult = {
        success: !error,
        error: error ? error.message : null,
        errorDetails: error,
        data: data,
      };
    } catch (e) {
      adminResult = {
        success: false,
        error: e.message,
        exception: true,
      };
    }
    
    // Test admin client with a different table
    let businessesResult;
    try {
      const { data, error } = await adminClient
        .from('businesses')
        .select('*')
        .limit(5);
      
      businessesResult = {
        success: !error,
        error: error ? error.message : null,
        errorDetails: error,
        data: data,
      };
    } catch (e) {
      businessesResult = {
        success: false,
        error: e.message,
        exception: true,
      };
    }
    
    // Return results
    return NextResponse.json({
      status: 'Supabase debug check',
      timestamp: new Date().toISOString(),
      environment: {
        supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 15)}...` : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 5)}...` : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? `${supabaseServiceKey.substring(0, 5)}...` : 'Not set',
        nodeEnv: process.env.NODE_ENV,
      },
      anonClient: anonResult,
      adminClient: adminResult,
      businessesTable: businessesResult
    });
  } catch (error) {
    console.error('Error in debug-supabase API:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred', 
        details: error.message,
        stack: error.stack
      },
      { status: 500 }
    );
  }
}
