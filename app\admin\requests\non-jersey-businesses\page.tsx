"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { 
  Building2, 
  TrendingUp, 
  Users, 
  Heart, 
  MapPin, 
  Search, 
  CheckCircle, 
  XCircle, 
  Clock,
  Vote,
  Globe
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface NonJerseyBusinessRequest {
  id: number
  business_name: string
  business_type: string
  suggested_address: string
  customer_name: string
  customer_email: string
  vote_count: number
  status: string
  created_at: string
  notes?: string
  location: string
}

export default function AdminNonJerseyBusinessRequestsPage() {
  const [requests, setRequests] = useState<NonJerseyBusinessRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreatePlaceholderDialog, setShowCreatePlaceholderDialog] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<NonJerseyBusinessRequest | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/admin/non-jersey-business-requests')
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests)
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePlaceholder = async (request: NonJerseyBusinessRequest) => {
    setSelectedRequest(request)
    setShowCreatePlaceholderDialog(true)
  }

  const handleUpdateStatus = async (requestId: number, newStatus: string) => {
    try {
      const response = await fetch('/api/admin/non-jersey-business-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestId,
          status: newStatus
        })
      })

      if (response.ok) {
        toast({
          title: "Status updated",
          description: `Request has been ${newStatus}.`,
        })
        fetchRequests() // Refresh the list
      } else {
        throw new Error('Failed to update status')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update request status.",
        variant: "destructive"
      })
    }
  }

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.business_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.business_type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.customer_name?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || request.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-300">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-300">Approved</Badge>
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-300">Rejected</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const pendingRequests = requests.filter(r => r.status === 'pending')
  const totalVotes = requests.reduce((sum, r) => sum + r.vote_count, 0)
  const topVotes = requests.length > 0 ? Math.max(...requests.map(r => r.vote_count)) : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Non-Jersey Business Requests</h1>
        <p className="text-gray-600 mt-2">
          Manage requests for international chains and businesses not currently operating in Jersey
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">{requests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{pendingRequests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Votes</p>
                <p className="text-2xl font-bold text-gray-900">{totalVotes}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Top Votes</p>
                <p className="text-2xl font-bold text-gray-900">{topVotes}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search businesses, types, or customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Top Requests */}
      {pendingRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              Most Requested International Businesses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pendingRequests
                .sort((a, b) => b.vote_count - a.vote_count)
                .slice(0, 5)
                .map((request, index) => (
                  <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold ${
                        index === 0 ? 'bg-purple-400 text-purple-900' :
                        index < 3 ? 'bg-purple-500 text-white' : 'bg-gray-300 text-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{request.business_name}</p>
                        <p className="text-sm text-gray-600">{request.business_type}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Vote className="h-4 w-4 text-gray-500" />
                      <span className="font-bold text-purple-600">{request.vote_count}</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Requests List */}
      {loading ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading requests...</p>
          </CardContent>
        </Card>
      ) : filteredRequests.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== "all" ? 'No requests found' : 'No international business requests yet'}
            </h3>
            <p className="text-gray-500">
              {searchQuery || statusFilter !== "all" 
                ? 'Try adjusting your search or filter criteria'
                : 'International business requests will appear here when customers submit them'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{request.business_name}</h3>
                      {request.business_type && (
                        <Badge variant="outline">{request.business_type}</Badge>
                      )}
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                        <Globe className="h-3 w-3 mr-1" />
                        International
                      </Badge>
                      {getStatusBadge(request.status)}
                    </div>

                    {request.suggested_address && (
                      <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                        <MapPin className="h-4 w-4" />
                        {request.suggested_address}
                      </div>
                    )}

                    <div className="text-sm text-gray-600 mb-2">
                      <p><strong>Requested by:</strong> {request.customer_name} ({request.customer_email})</p>
                      <p><strong>Date:</strong> {formatDate(request.created_at)}</p>
                    </div>

                    {request.notes && (
                      <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg mb-2">
                        <strong>Notes:</strong> {request.notes}
                      </div>
                    )}

                    <div className="text-sm text-purple-600 bg-purple-50 p-3 rounded-lg">
                      <strong>Note:</strong> This business is not currently operating in Jersey. 
                      Consider creating a placeholder page to generate interest and potentially attract them to the island.
                    </div>
                  </div>

                  <div className="text-right ml-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Vote className="h-5 w-5 text-gray-500" />
                      <span className="text-2xl font-bold text-purple-600">{request.vote_count}</span>
                      <span className="text-sm text-gray-500">
                        {request.vote_count === 1 ? 'vote' : 'votes'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      {request.status === 'pending' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCreatePlaceholder(request)}
                            className="w-full text-purple-600 border-purple-200 hover:bg-purple-50"
                          >
                            <Building2 className="h-4 w-4 mr-1" />
                            Create Placeholder
                          </Button>
                          <div className="flex gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateStatus(request.id, 'approved')}
                              className="flex-1 text-green-600 border-green-200 hover:bg-green-50"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateStatus(request.id, 'rejected')}
                              className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Placeholder Dialog */}
      <Dialog open={showCreatePlaceholderDialog} onOpenChange={setShowCreatePlaceholderDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create Placeholder Business</DialogTitle>
            <DialogDescription>
              This will create a placeholder business page for {selectedRequest?.business_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Creating a placeholder will allow customers to see this international business in search results 
              and may encourage them to consider expanding to Jersey.
            </p>
            <div className="bg-purple-50 p-3 rounded-lg">
              <p className="text-sm text-purple-700">
                <strong>Note:</strong> This business is not currently in Jersey. The placeholder will clearly 
                indicate this and may help gauge local interest for potential expansion.
              </p>
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowCreatePlaceholderDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // TODO: Implement placeholder creation
                  toast({
                    title: "Feature coming soon",
                    description: "International business placeholder creation will be implemented soon.",
                  })
                  setShowCreatePlaceholderDialog(false)
                }}
                className="flex-1 bg-purple-600 hover:bg-purple-700"
              >
                Create Placeholder
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
