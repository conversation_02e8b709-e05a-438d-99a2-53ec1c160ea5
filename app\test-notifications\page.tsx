"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { notificationService } from '@/services/notification-service'

export default function TestNotificationsPage() {
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [subscription, setSubscription] = useState<any>(null)
  const [status, setStatus] = useState('Checking...')

  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // First register service worker if not already registered
        if ('serviceWorker' in navigator) {
          const registration = await navigator.serviceWorker.register('/sw.js')
          console.log('Service Worker registered:', registration)

          // Wait for service worker to be ready and active
          await navigator.serviceWorker.ready
          console.log('Service Worker is ready')

          // Force activation if needed
          if (registration.waiting) {
            registration.waiting.postMessage({ type: 'SKIP_WAITING' })
          }

          // Wait a bit more to ensure activation
          await new Promise(resolve => setTimeout(resolve, 1000))
          console.log('Service Worker activation complete')
        }

        await notificationService.initialize()
        const existingSub = await notificationService.getSubscription()
        if (existingSub) {
          setIsSubscribed(true)
          setSubscription(existingSub)
          setStatus('Already subscribed!')
        } else {
          setStatus('Not subscribed')
        }
      } catch (error) {
        console.error('Error checking subscription:', error)
        setStatus('Error checking subscription')
      }
    }

    initializeNotifications()
  }, [])

  const handleSubscribe = async () => {
    try {
      setStatus('Checking existing subscription...')

      // First check if we already have a subscription
      const existingSub = await notificationService.getSubscription()
      if (existingSub) {
        setIsSubscribed(true)
        setSubscription(existingSub)
        setStatus('Using existing subscription!')
        return
      }

      setStatus('Requesting permission...')
      const permission = await notificationService.requestPermission()
      if (permission !== 'granted') {
        setStatus('Permission denied')
        return
      }

      setStatus('Creating new subscription...')
      const sub = await notificationService.subscribe()
      if (sub) {
        setIsSubscribed(true)
        setSubscription(sub)
        setStatus('Subscribed successfully!')
      } else {
        setStatus('Failed to subscribe')
      }
    } catch (error) {
      console.error('Error subscribing:', error)
      setStatus(`Error: ${error}`)
    }
  }

  const sendTestNotification = async (type: string) => {
    try {
      setStatus('Sending test notification...')
      
      const response = await fetch('/api/test-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type })
      })

      const result = await response.json()
      
      if (response.ok) {
        setStatus('Test notification sent!')
      } else {
        setStatus(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error sending test notification:', error)
      setStatus(`Error: ${error}`)
    }
  }

  const sendLocalNotification = async () => {
    try {
      await notificationService.sendNotification({
        title: "Local Test Notification",
        body: "This is a test notification sent locally",
        icon: "/android-chrome-192x192.png",
        data: {
          type: "test",
          url: "/test-notifications"
        }
      })
      setStatus('Local notification sent!')
    } catch (error) {
      console.error('Error sending local notification:', error)
      setStatus(`Error: ${error}`)
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Notification Testing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-2">Status: {status}</p>
            <p className="text-sm text-gray-600 mb-4">
              Subscribed: {isSubscribed ? 'Yes' : 'No'}
            </p>
          </div>

          {!isSubscribed && (
            <Button onClick={handleSubscribe} className="w-full">
              Subscribe to Notifications
            </Button>
          )}

          {isSubscribed && (
            <div className="space-y-2">
              <Button 
                onClick={sendLocalNotification} 
                variant="outline" 
                className="w-full"
              >
                Send Local Test Notification
              </Button>
              
              <Button 
                onClick={() => sendTestNotification('driver_order')} 
                className="w-full"
              >
                Send Driver Order Notification
              </Button>
              
              <Button 
                onClick={() => sendTestNotification('customer_ready')} 
                variant="secondary" 
                className="w-full"
              >
                Send Customer Ready Notification
              </Button>
            </div>
          )}

          {subscription && (
            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <p><strong>Subscription Details:</strong></p>
              <p>Endpoint: {subscription.endpoint?.substring(0, 50)}...</p>
              <p>Keys: {subscription.keys ? 'Present' : 'Missing'}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
