"use client"

import { useAuth } from "@/context/unified-auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"

export default function AuthDebugPage() {
  const { user, userProfile, isLoading, refreshUserProfile, signOut } = useAuth()
  const router = useRouter()

  const handleRefreshProfile = async () => {
    console.log("Manually refreshing profile...")
    try {
      const profile = await refreshUserProfile()
      console.log("Profile refresh result:", profile)
    } catch (err) {
      console.error("Profile refresh error:", err)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
        
        <div className="grid gap-6">
          {/* Auth State */}
          <Card>
            <CardHeader>
              <CardTitle>Authentication State</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Loading:</strong> {isLoading ? "Yes" : "No"}
                </div>
                <div>
                  <strong>User:</strong> {user ? "Found" : "None"}
                </div>
                <div>
                  <strong>User Email:</strong> {user?.email || "N/A"}
                </div>
                <div>
                  <strong>User ID:</strong> {user?.id || "N/A"}
                </div>
                <div>
                  <strong>Profile:</strong> {userProfile ? "Found" : "None"}
                </div>
                <div>
                  <strong>Profile Role:</strong> {userProfile?.role || "N/A"}
                </div>
                <div>
                  <strong>Profile Name:</strong> {userProfile?.name || "N/A"}
                </div>
                <div>
                  <strong>Profile ID:</strong> {userProfile?.id || "N/A"}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Raw Data */}
          <Card>
            <CardHeader>
              <CardTitle>Raw Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <strong>User Object:</strong>
                  <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                    {JSON.stringify(user, null, 2)}
                  </pre>
                </div>
                <div>
                  <strong>Profile Object:</strong>
                  <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                    {JSON.stringify(userProfile, null, 2)}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button onClick={handleRefreshProfile}>
                  Refresh Profile
                </Button>
                <Button onClick={() => router.push("/login")} variant="outline">
                  Go to Login
                </Button>
                <Button onClick={() => router.push("/super-admin")} variant="outline">
                  Go to Super Admin
                </Button>
                {user && (
                  <Button onClick={signOut} variant="destructive">
                    Sign Out
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
