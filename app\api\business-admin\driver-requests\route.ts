import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log("=== DRIVER REQUESTS API CALLED ===")

    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Parse URL to get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')

    // Simple authentication check - for now, default to business ID 48 (has driver requests)
    // TODO: Add proper authentication based on user role and business association
    let businessId = businessIdParam ? parseInt(businessIdParam) : 48

    // If admin user requests a specific business, use that
    if (businessIdParam) {
      businessId = parseInt(businessIdParam)
      console.log(`Admin user requesting driver requests for business ID: ${businessId}`)
    } else {
      console.log(`Using default business ID: ${businessId}`)
    }

    console.log(`Fetching driver requests for business ID: ${businessId}`)

    // Fetch driver requests for this business (simplified query first)
    const { data: driverRequests, error } = await adminClient
      .from('driver_business_approvals')
      .select('*')
      .eq('business_id', businessId)
      .order('application_date', { ascending: false })

    console.log(`Driver requests query result:`, {
      data: driverRequests,
      error,
      count: driverRequests?.length || 0
    })

    if (error) {
      console.error("Error fetching driver requests:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: driverRequests || [],
      error: null,
      count: driverRequests?.length || 0
    })

  } catch (error: any) {
    console.error("Unexpected error in driver requests API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log("=== DRIVER REQUEST UPDATE API CALLED ===")

    const adminClient = createClient(supabaseUrl, supabaseServiceKey)
    const body = await request.json()
    const { requestId, status, businessId } = body

    if (!requestId || !status) {
      return NextResponse.json(
        { error: "Request ID and status are required" },
        { status: 400 }
      )
    }

    console.log(`Updating driver request ${requestId} to status: ${status}`)

    // Update the driver request status
    const { data, error } = await adminClient
      .from('driver_business_approvals')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()

    if (error) {
      console.error("Error updating driver request:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log("Driver request updated successfully:", data)

    return NextResponse.json({
      data: data[0],
      error: null,
      message: `Driver request ${status} successfully`
    })

  } catch (error: any) {
    console.error("Unexpected error updating driver request:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
