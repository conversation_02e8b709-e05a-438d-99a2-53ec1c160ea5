<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delivery Method Switch</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button {
            padding: 10px 20px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
            background: white;
        }
        .button.active {
            background: #007bff;
            color: white;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Test Delivery Method Switch</h1>
    
    <div class="test-section">
        <h2>Manual API Test</h2>
        <p>Test the delivery method switching API directly</p>
        
        <div>
            <label>Business ID: <input type="number" id="businessId" value="1"></label>
        </div>
        <div>
            <label>Session ID: <input type="text" id="sessionId" placeholder="Auto-generated"></label>
        </div>
        
        <div style="margin: 10px 0;">
            <button class="button" onclick="setDeliveryMethod('pickup')">Set Pickup</button>
            <button class="button" onclick="setDeliveryMethod('delivery')">Set Delivery</button>
            <button class="button" onclick="getCart()">Get Cart</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        // Generate a session ID if not provided
        let sessionId = localStorage.getItem('test-session-id');
        if (!sessionId) {
            sessionId = 'test-' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('test-session-id', sessionId);
        }
        document.getElementById('sessionId').value = sessionId;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function setDeliveryMethod(method) {
            const businessId = parseInt(document.getElementById('businessId').value);
            const sessionId = document.getElementById('sessionId').value;
            
            log(`Setting delivery method to ${method} for business ${businessId}...`);
            
            try {
                const response = await fetch('/api/cart-direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'updateDeliveryMethod',
                        businessId: businessId,
                        deliveryMethod: method,
                        sessionId: sessionId,
                        userLocation: {
                            postcode: 'JE2 3QN',
                            coordinates: [-2.1065, 49.1868]
                        }
                    })
                });

                log(`Response status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Success: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log(`Error: ${errorText}`);
                }
            } catch (error) {
                log(`Network error: ${error.message}`);
                console.error('Full error:', error);
            }
        }

        async function getCart() {
            const sessionId = document.getElementById('sessionId').value;
            
            log(`Getting cart for session ${sessionId}...`);
            
            try {
                const response = await fetch('/api/cart-direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get',
                        sessionId: sessionId
                    })
                });

                log(`Response status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Cart data: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log(`Error: ${errorText}`);
                }
            } catch (error) {
                log(`Network error: ${error.message}`);
                console.error('Full error:', error);
            }
        }

        log('Test page loaded. Session ID: ' + sessionId);
    </script>
</body>
</html>
