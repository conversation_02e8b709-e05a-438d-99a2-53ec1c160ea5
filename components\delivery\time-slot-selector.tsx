"use client"

import { useState, useEffect } from "react"
import { Calendar } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format, addDays, setHours, setMinutes, isBefore, getDay } from "date-fns"
import type { OpeningHours } from "@/types/business"

interface TimeSlotSelectorProps {
  onSelectTimeSlot: (date: Date) => void
  selectedTime?: Date | null
  className?: string
  businessHours?: OpeningHours | {
    open: string; // Format: "HH:MM" (24-hour)
    close: string; // Format: "HH:MM" (24-hour)
  }
}

// Map day of week number to day name
const dayOfWeekMap = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday'
];

export default function TimeSlotSelector({
  onSelectTimeSlot,
  selectedTime: externalSelectedTime,
  className = "",
  businessHours = { open: "09:00", close: "22:00" }
}: TimeSlotSelectorProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>("")
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [availableTimes, setAvailableTimes] = useState<string[]>([])

  // Generate available dates (today + next 6 days)
  useEffect(() => {
    const dates = []
    const now = new Date()

    // Add today
    dates.push(now)

    // Add next 6 days
    for (let i = 1; i <= 6; i++) {
      dates.push(addDays(now, i))
    }

    setAvailableDates(dates)
    setSelectedDate(now)
  }, [])

  // Generate available time slots based on selected date and business hours
  useEffect(() => {
    if (!selectedDate) return

    const times: string[] = []
    const now = new Date()
    const isToday = selectedDate.getDate() === now.getDate() &&
                    selectedDate.getMonth() === now.getMonth() &&
                    selectedDate.getFullYear() === now.getFullYear()

    // Get day of week for the selected date
    const dayOfWeek = getDay(selectedDate); // 0 = Sunday, 1 = Monday, etc.
    const dayName = dayOfWeekMap[dayOfWeek];

    // Get business hours for the selected day
    let openTime = "09:00";
    let closeTime = "22:00";

    if ('open' in businessHours && 'close' in businessHours) {
      // Simple hours format
      openTime = businessHours.open;
      closeTime = businessHours.close;
    } else if (typeof businessHours === 'object') {
      // Full opening hours by day
      const dayHours = businessHours[dayName as keyof OpeningHours];
      if (dayHours) {
        if (typeof dayHours === 'string') {
          // Handle case where day hours is a string like "Closed"
          if (dayHours.toLowerCase() !== 'closed') {
            const [open, close] = dayHours.split('-').map(t => t.trim());
            openTime = open;
            closeTime = close;
          } else {
            // Business is closed this day
            setAvailableTimes([]);
            return;
          }
        } else {
          // Handle case where day hours is an object with open/close
          openTime = dayHours.open;
          closeTime = dayHours.close;
        }
      }
    }

    // Parse business hours
    const [openHour, openMinute] = openTime.split(":").map(Number)
    const [closeHour, closeMinute] = closeTime.split(":").map(Number)

    // Create time slots in 30-minute intervals
    for (let hour = openHour; hour <= closeHour; hour++) {
      for (let minute of [0, 30]) {
        // Skip if it's the closing hour and we're past the closing minute
        if (hour === closeHour && minute > closeMinute) continue

        // Create the time slot
        const timeSlot = setHours(setMinutes(selectedDate, minute), hour)

        // Skip times in the past if it's today
        if (isToday && isBefore(timeSlot, now)) continue

        // Add the time slot
        times.push(format(timeSlot, "HH:mm"))
      }
    }

    setAvailableTimes(times)

    // Select the first available time slot by default
    if (times.length > 0 && (!selectedTime || !times.includes(selectedTime))) {
      setSelectedTime(times[0])
    }
  }, [selectedDate, businessHours])

  // When date or time changes, notify parent component
  useEffect(() => {
    if (selectedDate && selectedTime) {
      const [hours, minutes] = selectedTime.split(":").map(Number)
      const deliveryDate = new Date(selectedDate)
      deliveryDate.setHours(hours, minutes, 0, 0)
      onSelectTimeSlot(deliveryDate)
    }
  }, [selectedDate, selectedTime, onSelectTimeSlot])

  // Sync with external selected time if provided
  useEffect(() => {
    if (externalSelectedTime && availableDates.length > 0) {
      // Set the selected date
      const closestDate = availableDates.reduce((prev, curr) => {
        return Math.abs(curr.getTime() - externalSelectedTime.getTime()) <
               Math.abs(prev.getTime() - externalSelectedTime.getTime()) ? curr : prev;
      });

      setSelectedDate(closestDate);

      // Set the selected time
      const timeString = format(externalSelectedTime, "HH:mm");
      if (availableTimes.includes(timeString)) {
        setSelectedTime(timeString);
      }
    }
  }, [externalSelectedTime, availableDates, availableTimes]);

  return (
    <div className={`${className}`}>
      <div className="mb-4">
        <Label className="mb-2 block">Select Date</Label>
        <div className="grid grid-cols-4 gap-2">
          {availableDates.map((date, index) => {
            const isSelected = selectedDate &&
              date.getDate() === selectedDate.getDate() &&
              date.getMonth() === selectedDate.getMonth() &&
              date.getFullYear() === selectedDate.getFullYear();

            return (
              <Button
                key={index}
                type="button"
                variant={isSelected ? "default" : "outline"}
                className={`flex flex-col items-center justify-center h-20 p-1 rounded-md ${isSelected ? 'bg-emerald-600 text-white' : 'bg-white'}`}
                onClick={() => setSelectedDate(date)}
              >
                <span className="text-xs">{format(date, "EEE")}</span>
                <span className="text-xl font-semibold">{format(date, "d")}</span>
                <span className="text-xs">{format(date, "MMM")}</span>
              </Button>
            );
          })}
        </div>
      </div>

      <div className="mb-4">
        <Label htmlFor="time-select" className="mb-2 block">Select Time</Label>
        <Select value={selectedTime} onValueChange={setSelectedTime}>
          <SelectTrigger id="time-select" className="w-full">
            <SelectValue placeholder="Select a time slot" />
          </SelectTrigger>
          <SelectContent>
            {availableTimes.map((time) => (
              <SelectItem key={time} value={time}>
                {time}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {selectedDate && selectedTime && (
        <div className="p-4 bg-emerald-50 rounded-md border border-emerald-200">
          <div className="flex items-center text-emerald-700">
            <Calendar className="h-5 w-5 mr-2" />
            <span className="font-medium">
              Scheduled for {format(selectedDate, "EEEE, MMMM d")} at {selectedTime}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
