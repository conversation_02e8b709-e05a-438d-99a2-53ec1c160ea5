import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Get businesses this driver is approved for
    const { data: approvedBusinesses, error: approvalError } = await supabase
      .from('driver_business_approvals')
      .select('business_id')
      .eq('driver_id', driverProfile.id)
      .eq('status', 'approved')

    if (approvalError) {
      console.error('Error fetching business approvals:', approvalError)
      return NextResponse.json(
        { error: "Failed to check business approvals" },
        { status: 500 }
      )
    }

    // If driver is not approved for any businesses, return empty list
    if (!approvedBusinesses || approvedBusinesses.length === 0) {
      return NextResponse.json({
        availableOrders: [],
        currentOrder: null,
        isOnDelivery: false,
        message: "Driver not approved for any businesses yet"
      })
    }

    const approvedBusinessIds = approvedBusinesses.map(approval => approval.business_id)

    // Check if driver has any active orders (SINGLE SOURCE OF TRUTH: orders table)
    const { data: currentOrder, error: currentOrderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        customer_name,
        customer_phone,
        delivery_address,
        postcode,
        parish,
        delivery_type,
        total,
        status,
        preparation_time,
        estimated_delivery_time,
        ready_time,
        created_at,
        businesses!inner (
          id,
          name,
          address,
          phone,
          latitude,
          coordinates
        )
      `)
      .eq('driver_id', driverProfile.id)
      .eq('delivery_method', 'delivery')
      .in('status', ['assigned', 'picked_up', 'on_the_way'])  // Active delivery statuses
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (currentOrder && !currentOrderError) {
      // Driver has an active order - return it
      return NextResponse.json({
        availableOrders: [],
        currentOrder: currentOrder,
        isOnDelivery: true,
        message: `Driver is currently on a delivery (${currentOrder.status})`
      })
    }

    // Get orders this driver has declined in the last 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString()
    const { data: declinedOrders } = await supabase
      .from('driver_activity_log')
      .select('related_order_id')
      .eq('driver_id', driverProfile.id)
      .eq('activity_type', 'order_declined')
      .gte('timestamp', thirtyMinutesAgo)

    const declinedOrderIds = declinedOrders?.map(log => log.related_order_id) || []

    // Get available orders (SINGLE SOURCE OF TRUTH: orders table)
    // Rules for offering orders to drivers:
    // 1. Order status = 'offered' (available)
    // 2. No driver assigned (driver_id IS NULL)
    // 3. Delivery method = 'delivery' (not pickup)
    // 4. Business is in driver's approved list
    // 5. Driver doesn't have any active orders (checked above)
    let ordersQuery = supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        business_name,
        customer_name,
        customer_phone,
        delivery_address,
        postcode,
        parish,
        delivery_type,
        total,
        delivery_fee,
        status,
        preparation_time,
        estimated_delivery_time,
        delivery_distance_km,
        ready_time,
        created_at,
        cart_id,
        businesses!inner (
          id,
          name,
          address,
          location,
          phone,
          latitude,
          coordinates
        )
      `)
      .eq('status', 'offered')                              // 1. Available orders only
      .eq('delivery_method', 'delivery')                    // 3. Delivery orders only
      .is('driver_id', null)                               // 2. Unassigned orders only
      .in('business_id', approvedBusinessIds)              // 4. Approved businesses only
      .order('ready_time', { ascending: true })
      .limit(10)

    // Exclude recently declined orders
    if (declinedOrderIds.length > 0) {
      ordersQuery = ordersQuery.not('id', 'in', `(${declinedOrderIds.join(',')})`)
    }

    const { data: availableOrders, error: ordersError } = await ordersQuery

    if (ordersError) {
      console.error('Error fetching available orders:', ordersError)
      return NextResponse.json(
        { error: "Failed to fetch available orders" },
        { status: 500 }
      )
    }

    // Calculate distance from driver to each business (if driver location available)
    const { data: driverLocation } = await supabase
      .from('driver_status')
      .select('current_location_lat, current_location_lng')
      .eq('driver_id', driverProfile.id)
      .single()

    // Get item counts for each order
    const ordersWithItemCounts = await Promise.all(
      (availableOrders || []).map(async (order) => {
        // Get cart items count for this order
        const { data: cartItems, error: itemsError } = await supabase
          .from('cart_items')
          .select('id, quantity')
          .eq('cart_id', order.cart_id)

        let itemCount = 0
        let totalItems = 0

        if (!itemsError && cartItems) {
          itemCount = cartItems.length
          totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0)
        }

        let distance = null
        let estimatedPickupTime = null

        if (driverLocation?.current_location_lat &&
            driverLocation?.current_location_lng &&
            order.businesses?.latitude &&
            order.businesses?.coordinates) {

          // Extract longitude from coordinates point
          const coordsMatch = order.businesses.coordinates.match(/\(([^,]+),([^)]+)\)/)
          if (coordsMatch) {
            const businessLng = parseFloat(coordsMatch[1])
            const businessLat = parseFloat(coordsMatch[2])

            // Calculate distance using Haversine formula
            const R = 6371 // Earth's radius in kilometers
            const dLat = (businessLat - driverLocation.current_location_lat) * Math.PI / 180
            const dLng = (businessLng - driverLocation.current_location_lng) * Math.PI / 180
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                     Math.cos(driverLocation.current_location_lat * Math.PI / 180) *
                     Math.cos(businessLat * Math.PI / 180) *
                     Math.sin(dLng/2) * Math.sin(dLng/2)
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
            distance = R * c // Distance in kilometers

            // Estimate pickup time (assuming 30 km/h average speed)
            estimatedPickupTime = Math.round((distance / 30) * 60) // Minutes
          }
        }

        return {
          ...order,
          distance: distance ? Math.round(distance * 100) / 100 : null, // Round to 2 decimal places
          estimatedPickupTime,
          itemCount,
          totalItems
        }
      })
    )

    const ordersWithDistance = ordersWithItemCounts

    // Sort by distance if available, otherwise by ready_time
    ordersWithDistance.sort((a, b) => {
      if (a.distance !== null && b.distance !== null) {
        return a.distance - b.distance
      }
      return new Date(a.ready_time || a.created_at).getTime() -
             new Date(b.ready_time || b.created_at).getTime()
    })

    return NextResponse.json({
      availableOrders: ordersWithDistance,
      currentOrder: null,
      isOnDelivery: false,
      driverLocation: driverLocation ? {
        lat: driverLocation.current_location_lat,
        lng: driverLocation.current_location_lng
      } : null
    })

  } catch (error) {
    console.error('Error in available orders API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
