import { Receipt } from "lucide-react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { OrderDetails } from "../types"

interface OrderSummaryProps {
  orderDetails: OrderDetails
}

export function OrderSummary({ orderDetails }: OrderSummaryProps) {
  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 backdrop-blur-sm">
      <CardHeader className="pb-4 border-b border-gray-100">
        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
          <Receipt className="h-5 w-5 mr-2 text-blue-600" />
          Order Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex justify-between text-base">
            <span className="text-gray-700 font-medium">Businesses total</span>
            <span className="text-gray-900 font-semibold">£{(() => {
              // Calculate total from business totals (subtotal + delivery fee for each business)
              const calculatedBusinessesTotal = orderDetails.businesses.reduce((total, business) => {
                const businessTotal = business.subtotal + (business.deliveryMethod === 'delivery' ? business.deliveryFee : 0);
                return total + businessTotal;
              }, 0);
              return calculatedBusinessesTotal.toFixed(2);
            })()}</span>
          </div>
          <div className="flex justify-between text-base">
            <span className="text-gray-700 font-medium">Service Fee</span>
            <span className="text-gray-900 font-semibold">£{orderDetails.serviceFee.toFixed(2)}</span>
          </div>
          <Separator className="my-3 bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
          <div className="flex justify-between font-bold text-xl pt-2 bg-gradient-to-r from-blue-50 to-emerald-50 p-4 rounded-lg border border-blue-100">
            <span className="text-gray-900">Total</span>
            <span className="text-blue-700">£{(() => {
              // Calculate total from business subtotals + delivery fees + service fee
              const calculatedSubtotal = orderDetails.businesses.reduce((total, business) => total + business.subtotal, 0);
              const totalDeliveryFees = orderDetails.businesses.reduce((total, business) =>
                total + (business.deliveryMethod === 'delivery' ? business.deliveryFee : 0), 0);
              const calculatedTotal = calculatedSubtotal + totalDeliveryFees + orderDetails.serviceFee;
              return calculatedTotal.toFixed(2);
            })()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
