"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Building2, Users, ShoppingBag, AlertTriangle } from "lucide-react"
import { supabase } from "@/lib/supabase"

interface DashboardStats {
  totalBusinesses: number
  pendingBusinesses: number
  totalUsers: number
  totalOrders: number
}

interface PendingBusiness {
  id: number
  name: string
  business_type: string
  created_at: string
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBusinesses: 0,
    pendingBusinesses: 0,
    totalUsers: 0,
    totalOrders: 0
  })
  const [pendingBusinesses, setPendingBusinesses] = useState<PendingBusiness[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    setLoading(true)
    setError(null)

    try {
      // Get total businesses
      const { count: totalBusinesses, error: businessError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true })

      if (businessError) throw businessError

      // Get pending businesses count
      const { count: pendingBusinessesCount, error: pendingCountError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true })
        .is("is_approved", null)

      if (pendingCountError) throw pendingCountError

      // Get pending businesses details
      const { data: pendingBusinessesData, error: pendingDetailsError } = await supabase
        .from("business_details_view")
        .select("id, name, business_type, created_at")
        .is("is_approved", null)
        .order("created_at", { ascending: false })
        .limit(5)

      if (pendingDetailsError) throw pendingDetailsError

      // Get total users
      const { count: totalUsers, error: usersError } = await supabase
        .from("users")
        .select("*", { count: "exact", head: true })

      if (usersError) throw usersError

      // Get total orders
      const { count: totalOrders, error: ordersError } = await supabase
        .from("orders")
        .select("*", { count: "exact", head: true })

      if (ordersError) throw ordersError

      setStats({
        totalBusinesses: totalBusinesses || 0,
        pendingBusinesses: pendingBusinessesCount || 0,
        totalUsers: totalUsers || 0,
        totalOrders: totalOrders || 0
      })

      setPendingBusinesses(pendingBusinessesData || [])
    } catch (err: any) {
      console.error("Error fetching dashboard stats:", err)
      setError(err.message || "Failed to load dashboard statistics")
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div className="p-8 text-center">Loading dashboard statistics...</div>
  }

  if (error) {
    return <div className="p-8 text-center text-red-500">Error: {error}</div>
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
            <Building2 className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBusinesses}</div>
            <p className="text-xs text-gray-500">Registered businesses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <AlertTriangle className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingBusinesses}</div>
            <p className="text-xs text-gray-500">Businesses awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-gray-500">Registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingBag className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-gray-500">Orders processed</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Pending Business Approvals</CardTitle>
            <CardDescription>Businesses awaiting your approval</CardDescription>
          </CardHeader>
          <CardContent>
            {pendingBusinesses.length > 0 ? (
              <div className="space-y-4">
                {pendingBusinesses.map((business) => (
                  <div key={business.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div>
                      <p className="font-medium">{business.name}</p>
                      <div className="flex items-center text-sm text-gray-500">
                        <span className="mr-2">{business.business_type}</span>
                        <span>•</span>
                        <span className="ml-2">{new Date(business.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <a
                      href={`/admin/businesses/${business.id}`}
                      className="px-3 py-1 text-sm bg-emerald-100 text-emerald-700 rounded-md hover:bg-emerald-200 transition-colors"
                    >
                      Review
                    </a>
                  </div>
                ))}

                {stats.pendingBusinesses > 5 && (
                  <div className="text-center mt-2">
                    <a
                      href="/admin/businesses"
                      className="text-sm text-emerald-600 hover:text-emerald-800"
                    >
                      View all {stats.pendingBusinesses} pending approvals
                    </a>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-center text-gray-500 py-4">No pending approvals</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest orders placed on the platform</CardDescription>
          </CardHeader>
          <CardContent>
            {/* This would be populated with actual data in a real implementation */}
            <p className="text-center text-gray-500 py-4">No recent orders</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
