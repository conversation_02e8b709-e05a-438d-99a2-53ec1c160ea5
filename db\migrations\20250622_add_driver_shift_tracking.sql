-- Add driver shift tracking system - Raw data collection only
-- Focus on collecting source data, not calculating derivatives

-- 1. Add is_on_shift column to driver_status table
ALTER TABLE driver_status ADD COLUMN IF NOT EXISTS is_on_shift BOOLEAN DEFAULT FALSE;

-- 2. Create driver_shifts table - Core shift timing data
CREATE TABLE IF NOT EXISTS driver_shifts (
  id SERIAL PRIMARY KEY,
  driver_id UUID NOT NULL REFERENCES driver_profiles(id) ON DELETE CASCADE,
  
  -- Core timing data (SOURCE OF TRUTH for shift times)
  shift_start TIMESTAMP WITH TIME ZONE NOT NULL,
  shift_end TIMESTAMP WITH TIME ZONE,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_shift_duration CHECK (shift_end IS NULL OR shift_end > shift_start)
);

-- 3. Create driver_shift_orders table - Track all order interactions during shifts
CREATE TABLE IF NOT EXISTS driver_shift_orders (
  id SERIAL PRIMARY KEY,
  shift_id INTEGER NOT NULL REFERENCES driver_shifts(id) ON DELETE CASCADE,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  
  -- Order interaction data (SOURCE OF TRUTH for driver actions)
  offered_at TIMESTAMP WITH TIME ZONE NOT NULL,
  action_taken VARCHAR(20) NOT NULL CHECK (action_taken IN ('accepted', 'declined', 'expired')),
  action_taken_at TIMESTAMP WITH TIME ZONE,
  
  -- Snapshot of key order data at time of interaction
  -- (Prevents data loss if orders table changes later)
  business_id INTEGER NOT NULL,
  parish VARCHAR(100) NOT NULL,
  delivery_fee DECIMAL(10,2) NOT NULL, -- FROM orders.delivery_fee (SOURCE OF TRUTH)
  order_total DECIMAL(10,2) NOT NULL,  -- FROM orders.total (SOURCE OF TRUTH)
  distance_km DECIMAL(8,2),            -- FROM orders.delivery_distance_km (SOURCE OF TRUTH)
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one record per order per shift
  UNIQUE(shift_id, order_id)
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_driver_shifts_driver_id ON driver_shifts(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_shifts_start_time ON driver_shifts(shift_start);
CREATE INDEX IF NOT EXISTS idx_driver_shifts_active ON driver_shifts(driver_id, shift_end) WHERE shift_end IS NULL;

CREATE INDEX IF NOT EXISTS idx_driver_shift_orders_shift_id ON driver_shift_orders(shift_id);
CREATE INDEX IF NOT EXISTS idx_driver_shift_orders_order_id ON driver_shift_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_driver_shift_orders_action ON driver_shift_orders(action_taken);

-- 5. Create functions for shift management

-- Function to start a new shift
CREATE OR REPLACE FUNCTION start_driver_shift(p_driver_id UUID)
RETURNS INTEGER AS $$
DECLARE
  v_shift_id INTEGER;
  v_active_shift_count INTEGER;
BEGIN
  -- Check if driver already has an active shift
  SELECT COUNT(*) INTO v_active_shift_count
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_active_shift_count > 0 THEN
    RAISE EXCEPTION 'Driver already has an active shift';
  END IF;
  
  -- Create new shift record with start time
  INSERT INTO driver_shifts (driver_id, shift_start)
  VALUES (p_driver_id, NOW())
  RETURNING id INTO v_shift_id;
  
  -- Update driver status
  UPDATE driver_status
  SET is_on_shift = TRUE,
      last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN v_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Function to end a shift
CREATE OR REPLACE FUNCTION end_driver_shift(p_driver_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
BEGIN
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Set shift end time
  UPDATE driver_shifts
  SET 
    shift_end = NOW(),
    updated_at = NOW()
  WHERE id = v_shift_id;
  
  -- Update driver status
  UPDATE driver_status
  SET is_on_shift = FALSE,
      last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to record order offer during shift
CREATE OR REPLACE FUNCTION record_shift_order_offer(
  p_driver_id UUID,
  p_order_id INTEGER,
  p_action VARCHAR(20)
)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
  v_order_data RECORD;
BEGIN
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Get order details from SOURCE OF TRUTH (orders table)
  SELECT 
    o.id, 
    o.business_id, 
    o.parish,
    o.total,                    -- SOURCE OF TRUTH for order value
    o.delivery_fee,             -- SOURCE OF TRUTH for delivery fee
    o.delivery_distance_km      -- SOURCE OF TRUTH for distance
  INTO v_order_data
  FROM orders o
  WHERE o.id = p_order_id;
  
  IF v_order_data IS NULL THEN
    RAISE EXCEPTION 'Order not found: %', p_order_id;
  END IF;
  
  -- Insert or update shift order record with source data
  INSERT INTO driver_shift_orders (
    shift_id, 
    order_id, 
    offered_at, 
    action_taken, 
    action_taken_at,
    business_id, 
    parish, 
    order_total,              -- FROM orders.total
    delivery_fee,             -- FROM orders.delivery_fee  
    distance_km               -- FROM orders.delivery_distance_km
  )
  VALUES (
    v_shift_id, 
    p_order_id, 
    NOW(), 
    p_action, 
    NOW(),
    v_order_data.business_id, 
    v_order_data.parish,
    v_order_data.total,       -- SOURCE OF TRUTH
    v_order_data.delivery_fee, -- SOURCE OF TRUTH
    v_order_data.delivery_distance_km -- SOURCE OF TRUTH
  )
  ON CONFLICT (shift_id, order_id)
  DO UPDATE SET
    action_taken = p_action,
    action_taken_at = NOW(),
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 6. Create view for active shifts (no calculations, just raw data)
CREATE OR REPLACE VIEW active_driver_shifts AS
SELECT 
  ds.id,
  ds.driver_id,
  dp.user_id,
  u.name as driver_name,
  ds.shift_start,
  ds.created_at
FROM driver_shifts ds
JOIN driver_profiles dp ON ds.driver_id = dp.id
JOIN users u ON dp.user_id = u.id
WHERE ds.shift_end IS NULL;

-- Add comments for documentation
COMMENT ON TABLE driver_shifts IS 'Raw shift timing data - start and end times only';
COMMENT ON TABLE driver_shift_orders IS 'Raw order interaction data during shifts - captures source data from orders table';
COMMENT ON COLUMN driver_shift_orders.delivery_fee IS 'SOURCE OF TRUTH: Copied from orders.delivery_fee at time of interaction';
COMMENT ON COLUMN driver_shift_orders.order_total IS 'SOURCE OF TRUTH: Copied from orders.total at time of interaction';
COMMENT ON COLUMN driver_shift_orders.distance_km IS 'SOURCE OF TRUTH: Copied from orders.delivery_distance_km at time of interaction';
COMMENT ON FUNCTION start_driver_shift(UUID) IS 'Records shift start time and updates driver status';
COMMENT ON FUNCTION end_driver_shift(UUID) IS 'Records shift end time and updates driver status';
COMMENT ON FUNCTION record_shift_order_offer(UUID, INTEGER, VARCHAR) IS 'Records order interactions with source data from orders table';
