import { Progress } from "@/components/ui/progress"

export function SalesMetrics() {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">Conversion Rate</div>
          <div className="text-sm font-medium">24.8%</div>
        </div>
        <Progress value={24.8} className="h-2 bg-emerald-100" indicatorClassName="bg-emerald-500" />
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">Repeat Purchase Rate</div>
          <div className="text-sm font-medium">42.3%</div>
        </div>
        <Progress value={42.3} className="h-2 bg-emerald-100" indicatorClassName="bg-emerald-500" />
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">Cart Abandonment Rate</div>
          <div className="text-sm font-medium">18.5%</div>
        </div>
        <Progress value={18.5} className="h-2 bg-emerald-100" indicatorClassName="bg-emerald-500" />
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">Average Items Per Order</div>
          <div className="text-sm font-medium">3.2</div>
        </div>
        <Progress value={64} className="h-2 bg-emerald-100" indicatorClassName="bg-emerald-500" />
      </div>
    </div>
  )
}
