import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    // Get the user's session using the server client
    const supabase = await createServerSupabase()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      return NextResponse.json(
        { error: "Session error", details: sessionError },
        { status: 500 }
      )
    }

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    const email = session.user.email

    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      )
    }

    // Get user data
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("*")
      .eq("email", email)
      .single()

    if (userError) {
      return NextResponse.json(
        { error: "Error fetching user data", details: userError },
        { status: 500 }
      )
    }

    // Get business manager data
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("*")
      .eq("user_id", userData.id)

    // Get business data if manager data exists
    let businessData = null
    if (managerData && managerData.length > 0) {
      const { data: business, error: businessError } = await adminClient
        .from("businesses")
        .select("*")
        .eq("id", managerData[0].business_id)
        .single()

      if (!businessError) {
        businessData = business
      }
    }

    // Check if the user has business-admin access
    const allowedRoles = ['business_staff', 'business_manager', 'admin', 'super_admin']
    const hasAccess = allowedRoles.includes(userData.role)

    // Check if the user has a business manager record
    const hasBusinessManagerRecord = managerData && managerData.length > 0

    // Check if the business manager is approved
    const isApproved = hasBusinessManagerRecord ? managerData[0].is_approved : false

    // Return all the debug information
    return NextResponse.json({
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
      },
      businessManagerRecords: managerData,
      business: businessData,
      accessControl: {
        hasAccess: hasAccess,
        allowedRoles: allowedRoles,
        roleInAllowedRoles: allowedRoles.includes(userData.role),
        hasBusinessManagerRecord: hasBusinessManagerRecord,
        isApproved: isApproved,
      },
      authContext: {
        isAdmin: userData.role === 'admin' || userData.role === 'super_admin',
        isSuperAdmin: userData.role === 'super_admin',
        isBusinessManager: userData.role === 'business_manager',
        isBusinessStaff: userData.role === 'business_staff',
      }
    })
  } catch (error: any) {
    console.error("Unexpected error in GET /api/debug/business-admin-access:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
