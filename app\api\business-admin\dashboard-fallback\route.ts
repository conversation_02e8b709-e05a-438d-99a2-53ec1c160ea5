import { NextResponse } from "next/server"

export async function GET() {
  try {
    // Return default stats for the business admin dashboard
    // This endpoint doesn't check authentication and is meant as a fallback
    
    console.log("Using fallback stats endpoint for business admin dashboard")
    
    const defaultStats = {
      totalOrders: 0,
      pendingOrders: 0,
      completedOrders: 0,
      totalRevenue: 0,
      recentOrders: []
    }
    
    return NextResponse.json(defaultStats)
  } catch (error: any) {
    console.error("Error in fallback stats endpoint:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
