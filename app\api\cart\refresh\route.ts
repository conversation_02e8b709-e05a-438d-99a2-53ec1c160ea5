import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

/**
 * POST /api/cart/refresh
 * 
 * Refreshes cart data by validating items against current product information
 * Updates prices, removes unavailable items, and provides detailed feedback
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, userId } = body

    if (!sessionId && !userId) {
      return NextResponse.json(
        { error: 'Session ID or User ID is required' },
        { status: 400 }
      )
    }

    console.log('🔄 CART-REFRESH: Starting cart refresh for:', { sessionId, userId })

    // Get all carts for this session/user
    let cartsQuery = supabase
      .from('user_carts')
      .select(`
        id,
        business_id,
        session_id,
        created_at,
        businesses!inner (
          id,
          name,
          is_active
        )
      `)

    if (userId) {
      cartsQuery = cartsQuery.eq('user_id', userId)
    } else {
      cartsQuery = cartsQuery.eq('session_id', sessionId)
    }

    const { data: carts, error: cartsError } = await cartsQuery

    if (cartsError) {
      console.error('❌ CART-REFRESH: Error fetching carts:', cartsError)
      return NextResponse.json(
        { error: 'Failed to fetch cart data' },
        { status: 500 }
      )
    }

    if (!carts || carts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No carts found to refresh',
        changes: []
      })
    }

    const refreshResults = []

    // Process each cart
    for (const cart of carts) {
      console.log(`🔄 CART-REFRESH: Processing cart ${cart.id} for business ${cart.businesses?.name}`)

      // Get cart items with product information
      const { data: cartItems, error: itemsError } = await supabase
        .from('cart_items')
        .select(`
          id,
          product_id,
          quantity,
          price,
          created_at,
          products!inner (
            id,
            name,
            price,
            is_available,
            business_id
          )
        `)
        .eq('cart_id', cart.id)

      if (itemsError) {
        console.error(`❌ CART-REFRESH: Error fetching items for cart ${cart.id}:`, itemsError)
        refreshResults.push({
          businessName: cart.businesses?.name,
          success: false,
          error: 'Failed to fetch cart items'
        })
        continue
      }

      if (!cartItems || cartItems.length === 0) {
        refreshResults.push({
          businessName: cart.businesses?.name,
          success: true,
          message: 'No items in cart',
          changes: []
        })
        continue
      }

      const changes = []
      const itemsToUpdate = []
      const itemsToRemove = []

      // Check each item
      for (const item of cartItems) {
        const product = item.products

        // Check if product is still available
        if (!product?.is_available) {
          itemsToRemove.push(item.id)
          changes.push({
            type: 'removed',
            productName: product?.name || 'Unknown Product',
            reason: 'Product is no longer available'
          })
          continue
        }

        // Check if business is still active
        if (!cart.businesses?.is_active) {
          itemsToRemove.push(item.id)
          changes.push({
            type: 'removed',
            productName: product?.name || 'Unknown Product',
            reason: 'Business is temporarily closed'
          })
          continue
        }

        // Check for price changes
        const currentPrice = parseFloat(product.price)
        const cartPrice = parseFloat(item.price)

        if (Math.abs(currentPrice - cartPrice) > 0.01) { // Allow for small floating point differences
          itemsToUpdate.push({
            id: item.id,
            newPrice: currentPrice
          })
          changes.push({
            type: 'price_updated',
            productName: product.name,
            oldPrice: cartPrice,
            newPrice: currentPrice,
            reason: 'Product price has changed'
          })
        }
      }

      // Remove unavailable items
      if (itemsToRemove.length > 0) {
        const { error: removeError } = await supabase
          .from('cart_items')
          .delete()
          .in('id', itemsToRemove)

        if (removeError) {
          console.error(`❌ CART-REFRESH: Error removing items from cart ${cart.id}:`, removeError)
          refreshResults.push({
            businessName: cart.businesses?.name,
            success: false,
            error: 'Failed to remove unavailable items'
          })
          continue
        }
      }

      // Update prices for changed items
      for (const update of itemsToUpdate) {
        const { error: updateError } = await supabase
          .from('cart_items')
          .update({ price: update.newPrice })
          .eq('id', update.id)

        if (updateError) {
          console.error(`❌ CART-REFRESH: Error updating price for item ${update.id}:`, updateError)
          changes.push({
            type: 'error',
            reason: 'Failed to update item price'
          })
        }
      }

      // Update cart session ID if needed
      if (sessionId && cart.session_id !== sessionId) {
        const { error: sessionUpdateError } = await supabase
          .from('user_carts')
          .update({ session_id: sessionId })
          .eq('id', cart.id)

        if (sessionUpdateError) {
          console.error(`❌ CART-REFRESH: Error updating session ID for cart ${cart.id}:`, sessionUpdateError)
        } else {
          changes.push({
            type: 'session_updated',
            reason: 'Cart session synchronized'
          })
        }
      }

      refreshResults.push({
        businessName: cart.businesses?.name,
        success: true,
        changes: changes,
        itemsRemoved: itemsToRemove.length,
        itemsUpdated: itemsToUpdate.length
      })

      console.log(`✅ CART-REFRESH: Completed refresh for ${cart.businesses?.name} - ${changes.length} changes made`)
    }

    const totalChanges = refreshResults.reduce((sum, result) => sum + (result.changes?.length || 0), 0)

    return NextResponse.json({
      success: true,
      message: `Cart refresh completed. ${totalChanges} changes made across ${refreshResults.length} business(es).`,
      results: refreshResults,
      totalChanges
    })

  } catch (error) {
    console.error('❌ CART-REFRESH: Unexpected error:', error)
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred while refreshing cart',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
