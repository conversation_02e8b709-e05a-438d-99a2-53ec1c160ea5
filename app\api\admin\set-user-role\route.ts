import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: Request) {
  try {
    // Get the user's session using the server client
    const supabase = await createServerSupabase()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    // Check if the current user is an admin or super_admin
    const { data: currentUser, error: currentUserError } = await adminClient
      .from("users")
      .select("role")
      .eq("email", session.user.email)
      .single()

    if (currentUserError) {
      return NextResponse.json(
        { error: `Error fetching current user: ${currentUserError.message}` },
        { status: 500 }
      )
    }

    if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
      return NextResponse.json(
        { error: "Unauthorized. Only admins can set user roles." },
        { status: 403 }
      )
    }

    // Get request body
    const { email, role } = await request.json()

    if (!email || !role) {
      return NextResponse.json(
        { error: "Email and role are required" },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles = ['customer', 'business_staff', 'business_manager', 'admin', 'super_admin']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: `Invalid role. Must be one of: ${validRoles.join(', ')}` },
        { status: 400 }
      )
    }

    // Only super_admin can create other super_admins
    if (role === 'super_admin' && currentUser.role !== 'super_admin') {
      return NextResponse.json(
        { error: "Only super admins can create other super admins" },
        { status: 403 }
      )
    }

    // Update user role in the database
    const { data: userData, error: updateError } = await adminClient
      .from("users")
      .update({ role })
      .eq("email", email)
      .select()
      .single()

    if (updateError) {
      return NextResponse.json(
        { error: `Error updating user role: ${updateError.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: `User role updated successfully to ${role}`,
      user: userData
    })
  } catch (error: any) {
    console.error("Unexpected error in POST /api/admin/set-user-role:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
