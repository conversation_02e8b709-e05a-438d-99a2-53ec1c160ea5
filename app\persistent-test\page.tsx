'use client'

import { useState, useEffect } from 'react'

export default function PersistentNotificationTest() {
  const [status, setStatus] = useState<string>('')
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null)

  useEffect(() => {
    checkServiceWorker()
  }, [])

  const checkServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          setSwRegistration(registration)
          setStatus('✅ Service worker found')
        } else {
          setStatus('⚠️ No service worker registered')
        }
      } catch (error) {
        setStatus(`❌ Service worker error: ${error.message}`)
      }
    } else {
      setStatus('❌ Service workers not supported')
    }
  }

  const forceServiceWorkerUpdate = async () => {
    setStatus('🔄 Forcing service worker update...')
    
    try {
      // Unregister existing service worker
      const registrations = await navigator.serviceWorker.getRegistrations()
      for (const registration of registrations) {
        await registration.unregister()
        setStatus(prev => prev + '\n🗑️ Unregistered old service worker')
      }

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Register fresh service worker
      const registration = await navigator.serviceWorker.register('/sw.js', { 
        scope: '/',
        updateViaCache: 'none' // Force fresh download
      })
      
      await navigator.serviceWorker.ready
      setSwRegistration(registration)
      setStatus(prev => prev + '\n✅ Fresh service worker registered')

      // Force update
      await registration.update()
      setStatus(prev => prev + '\n🔄 Service worker updated')

    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
    }
  }

  const testPersistentBrowserNotification = async () => {
    setStatus(prev => prev + '\n🧪 Testing persistent browser notification...')

    try {
      const permission = await Notification.requestPermission()
      if (permission !== 'granted') {
        setStatus(prev => prev + `\n❌ Permission denied: ${permission}`)
        return
      }

      const notification = new Notification('🔒 PERSISTENT TEST', {
        body: 'This notification should stay visible until you click it or dismiss it manually. It should NOT auto-disappear.',
        icon: '/android-chrome-192x192.png',
        requireInteraction: true, // This makes it persistent
        tag: 'persistent-test',
        renotify: true,
        actions: [
          { action: 'test', title: '✅ Test Action' },
          { action: 'dismiss', title: '❌ Dismiss' }
        ]
      })

      notification.onshow = () => {
        setStatus(prev => prev + '\n📱 Persistent notification shown - should stay visible!')
      }

      notification.onclick = () => {
        setStatus(prev => prev + '\n🖱️ Notification clicked!')
        notification.close()
      }

      notification.onerror = (error) => {
        setStatus(prev => prev + `\n❌ Notification error: ${error}`)
      }

      setStatus(prev => prev + '\n📤 Persistent notification created')

    } catch (error) {
      setStatus(prev => prev + `\n❌ Error: ${error.message}`)
    }
  }

  const testServiceWorkerNotification = async () => {
    if (!swRegistration) {
      setStatus(prev => prev + '\n❌ No service worker registration')
      return
    }

    setStatus(prev => prev + '\n🚀 Testing service worker notification...')

    try {
      const permission = await Notification.requestPermission()
      if (permission !== 'granted') {
        setStatus(prev => prev + `\n❌ Permission denied: ${permission}`)
        return
      }

      // Use service worker to show notification
      await swRegistration.showNotification('🔒 SERVICE WORKER PERSISTENT TEST', {
        body: 'This is a persistent notification from the service worker. Should stay visible until interaction.',
        icon: '/android-chrome-192x192.png',
        badge: '/favicon-32x32.png',
        requireInteraction: true,
        tag: 'sw-persistent-test',
        renotify: true,
        vibrate: [200, 100, 200],
        actions: [
          { action: 'view', title: '👀 View' },
          { action: 'dismiss', title: '❌ Dismiss' }
        ],
        data: {
          type: 'test',
          timestamp: Date.now()
        }
      })

      setStatus(prev => prev + '\n✅ Service worker notification created - should be persistent!')

    } catch (error) {
      setStatus(prev => prev + `\n❌ Service worker notification error: ${error.message}`)
    }
  }

  const testPushNotification = async () => {
    setStatus(prev => prev + '\n🚀 Testing push notification with persistence...')

    try {
      const response = await fetch('/api/notifications/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: '🔒 PERSISTENT PUSH TEST',
          body: 'This push notification should be persistent and stay visible until you interact with it.',
          data: {
            type: 'persistent_test',
            timestamp: Date.now()
          }
        })
      })

      if (response.ok) {
        const result = await response.json()
        setStatus(prev => prev + '\n✅ Push notification sent successfully')
        setStatus(prev => prev + `\n📊 Sent to ${result.result?.sent || 0} devices`)
        setStatus(prev => prev + '\n🔔 Check for persistent notification on your devices!')
      } else {
        const errorText = await response.text()
        setStatus(prev => prev + `\n❌ Push notification failed: ${errorText}`)
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Push notification error: ${error.message}`)
    }
  }

  const clearAllNotifications = async () => {
    setStatus(prev => prev + '\n🧹 Clearing all notifications...')

    try {
      if (swRegistration) {
        const notifications = await swRegistration.getNotifications()
        notifications.forEach(notification => notification.close())
        setStatus(prev => prev + `\n✅ Cleared ${notifications.length} service worker notifications`)
      }
    } catch (error) {
      setStatus(prev => prev + `\n❌ Error clearing notifications: ${error.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          🔒 Persistent Notification Test
        </h1>

        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded p-4">
            <h3 className="font-semibold text-blue-800 mb-2">About Persistent Notifications:</h3>
            <p className="text-blue-700 text-sm">
              Persistent notifications use `requireInteraction: true` and should stay visible 
              until you click them or manually dismiss them. They should NOT auto-disappear after a few seconds.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={forceServiceWorkerUpdate}
              className="bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700"
            >
              🔄 Force Service Worker Update
            </button>

            <button
              onClick={testPersistentBrowserNotification}
              className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
            >
              🔒 Test Persistent Browser Notification
            </button>

            <button
              onClick={testServiceWorkerNotification}
              className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
              disabled={!swRegistration}
            >
              🔒 Test Service Worker Notification
            </button>

            <button
              onClick={testPushNotification}
              className="bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
            >
              🔒 Test Push Notification (Persistent)
            </button>

            <button
              onClick={clearAllNotifications}
              className="bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700"
            >
              🧹 Clear All Notifications
            </button>
          </div>

          {status && (
            <div className="mt-4">
              <h3 className="font-semibold text-gray-700 mb-2">Status:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm whitespace-pre-wrap max-h-64 overflow-auto">
                {status}
              </pre>
            </div>
          )}

          <div className="mt-6 p-4 bg-yellow-50 rounded">
            <h4 className="font-semibold text-yellow-800 mb-2">Testing Steps:</h4>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>First, click "Force Service Worker Update" to get the latest code</li>
              <li>Try "Test Persistent Browser Notification" - should stay visible</li>
              <li>Try "Test Service Worker Notification" - should also be persistent</li>
              <li>Finally, try "Test Push Notification" - the full system test</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
