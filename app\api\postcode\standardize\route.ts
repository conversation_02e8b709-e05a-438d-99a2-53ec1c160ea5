import { NextResponse } from 'next/server';
import { standardizeJerseyPostcode, standardizeAndGeocodePostcode, getDefaultCoordinatesForPostcode } from '@/lib/postcode-standardizer';

/**
 * API endpoint to standardize a Jersey postcode
 * @param request - The request object containing the postcode to standardize
 * @returns Standardized postcode information
 */
export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    const { postcode, geocode = false, timestamp } = body;

    // Validate required fields
    if (!postcode) {
      return NextResponse.json(
        { error: "Postcode is required" },
        { status: 400 }
      );
    }

    console.log(`API received postcode: "${postcode}", geocode: ${geocode}, timestamp: ${timestamp}`);

    // Standardize the postcode
    if (geocode) {
      try {
        console.log(`Attempting to standardize and geocode postcode: ${postcode}`);
        const result = await standardizeAndGeocodePostcode(postcode);

        // If we have a valid postcode but no coordinates, try to get default coordinates
        if (result.is_valid && !result.coordinates) {
          console.log(`No coordinates returned, trying default coordinates`);
          const defaultCoords = getDefaultCoordinatesForPostcode(result.standardized);

          if (defaultCoords) {
            console.log(`Using default coordinates: [${defaultCoords[0]}, ${defaultCoords[1]}]`);
            result.coordinates = defaultCoords;
            result.validation_message = 'Using approximate location for this postcode area.';
          }
        }

        const response = NextResponse.json({
          message: result.is_valid
            ? (result.coordinates
                ? "Postcode standardized and geocoded successfully"
                : "Postcode standardized but geocoding failed")
            : "Postcode standardization failed",
          result
        });

        // Add cache control headers
        response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');

        return response;
      } catch (geocodeError) {
        console.error('Error in geocoding postcode:', geocodeError);

        // If geocoding fails, still return the standardized postcode
        const standardized = standardizeJerseyPostcode(postcode);

        // Try to get default coordinates
        if (standardized.is_valid) {
          const defaultCoords = getDefaultCoordinatesForPostcode(standardized.standardized);

          if (defaultCoords) {
            standardized.coordinates = defaultCoords;
            standardized.validation_message = 'Using approximate location due to geocoding error.';
          } else {
            standardized.validation_message = 'Geocoding failed. Using default Jersey coordinates.';
          }
        }

        const response = NextResponse.json({
          message: "Geocoding failed but postcode was standardized",
          result: standardized
        });

        // Add cache control headers
        response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');

        return response;
      }
    } else {
      // Just standardize without geocoding
      const result = standardizeJerseyPostcode(postcode);
      const response = NextResponse.json({
        message: result.is_valid ? "Postcode standardized successfully" : "Postcode standardization failed",
        result
      });

      // Add cache control headers
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');

      return response;
    }
  } catch (error) {
    console.error('Error in postcode standardization API:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
