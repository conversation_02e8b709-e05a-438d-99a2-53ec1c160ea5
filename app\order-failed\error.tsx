"use client"

import { useEffect } from "react"
import Link from "next/link"
import { AlertCircle, ArrowRight, Home } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function OrderFailedErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to the console
    console.error("Order failed page error:", error)
  }, [error])

  return (
    <div className="container max-w-5xl py-8 mx-auto text-center">
      <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center mb-5 mx-auto">
        <AlertCircle className="h-10 w-10 text-red-600" />
      </div>
      <h1 className="text-3xl font-bold text-gray-800">Order Processing Failed</h1>
      <p className="text-gray-600 mt-3 max-w-lg mx-auto">
        We were unable to process your order. Please try again or contact customer support.
      </p>

      <Alert className="mt-6 max-w-lg mx-auto bg-red-50 border-red-200 text-red-700">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          There was a problem with your order. Your payment has not been processed, and no order has been created.
        </AlertDescription>
      </Alert>

      <div className="mt-8 flex flex-wrap justify-center gap-4">
        <Link href="/">
          <Button variant="outline" className="border-gray-300">
            <Home className="mr-2 h-4 w-4 text-gray-600" />
            Return Home
          </Button>
        </Link>
        <Link href="/checkout">
          <Button className="bg-blue-600 hover:bg-blue-700">
            Try Again
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
    </div>
  )
}
