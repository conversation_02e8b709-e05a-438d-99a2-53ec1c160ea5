'use client'

import { useState, useEffect } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Bell, BellOff, Settings, CheckCircle } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/context/unified-auth-context'
import { addAuthHeaders } from '@/utils/auth-token'

export function SimplePushNotifications() {
  const { user, isLoading: authLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [hasPermission, setHasPermission] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [preferences, setPreferences] = useState({
    orderUpdates: true,
    deliveryUpdates: true,
    promotions: false,
    messages: true
  })

  useEffect(() => {
    const checkStatus = async () => {
      try {
        // Wait for auth to finish loading
        if (authLoading) {
          return
        }

        // Check browser support
        const isSupported = 'Notification' in window
        if (!isSupported) {
          setIsLoading(false)
          return
        }

        // Check permission
        const permission = Notification.permission
        setHasPermission(permission === 'granted')

        // Only check subscriptions if user is authenticated
        if (user) {
          try {
            console.log('🔍 Checking existing subscriptions for authenticated user...')
            const response = await fetch('/api/notifications/subscribe', {
              headers: addAuthHeaders()
            })
            console.log('🔍 GET Response:', response.status, response.statusText)

            if (response.ok) {
              const data = await response.json()
              console.log('🔍 Existing subscriptions:', data)
              // Check for active subscriptions
              const activeSubscriptions = data.subscriptions?.filter(sub => sub.is_active) || []
              setIsSubscribed(activeSubscriptions.length > 0)
              console.log('🔍 Active subscriptions found:', activeSubscriptions.length)
            } else if (response.status === 401) {
              console.log('🔍 User authentication expired')
              setIsSubscribed(false)
            } else if (response.status === 500) {
              console.log('🔍 Server error - this is expected if user has no subscriptions yet')
              setIsSubscribed(false)
            } else {
              console.log('🔍 API error:', response.status, response.statusText)
              setIsSubscribed(false)
            }
          } catch (error) {
            console.log('🔍 Network error:', error.message)
            setIsSubscribed(false)
          }
        } else {
          console.log('🔍 User not authenticated - skipping subscription check')
          setIsSubscribed(false)
        }

      } catch (error) {
        console.error('Error checking notification status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkStatus()
  }, [authLoading, user])

  const handlePermissionRequest = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      setHasPermission(permission === 'granted')
      return permission === 'granted'
    }
    return false
  }

  const handleSubscriptionToggle = async () => {
    // Check if user is authenticated first
    if (!user) {
      alert('Please sign in to enable notifications')
      return
    }

    setIsLoading(true)
    try {
      if (isSubscribed) {
        // Unsubscribe - deactivate all subscriptions for this user
        // We'll use a special parameter to indicate we want to deactivate all subscriptions
        const response = await fetch('/api/notifications/subscribe?endpoint=all', {
          method: 'DELETE',
          headers: addAuthHeaders()
        })
        if (response.ok) {
          setIsSubscribed(false)
          console.log('✅ Successfully unsubscribed from notifications')

          // Show success notification
          if (Notification.permission === 'granted') {
            new Notification('Notifications Disabled', {
              body: 'You will no longer receive push notifications.',
              icon: '/wheel-logo.svg',
              badge: '/wheel-logo.svg'
            })
          }
        } else {
          console.error('❌ Failed to unsubscribe:', response.status, response.statusText)
          const errorText = await response.text()
          console.error('Error details:', errorText)
          alert('Failed to disable notifications. Please try again.')
        }
      } else {
        // Subscribe
        let permissionGranted = hasPermission
        if (!permissionGranted) {
          permissionGranted = await handlePermissionRequest()
        }

        if (permissionGranted) {
          // Try to create a real push subscription using service worker
          let subscriptionData

          try {
            // Check if service worker is supported
            if ('serviceWorker' in navigator && 'PushManager' in window) {
              console.log('🔧 Attempting to register service worker...')

              // Wait for any existing service worker to be ready
              let registration
              try {
                registration = await navigator.serviceWorker.register('/sw.js', {
                  scope: '/'
                })
                console.log('✅ Service worker registered:', registration)

                // Wait for the service worker to be ready
                const readyRegistration = await navigator.serviceWorker.ready
                console.log('✅ Service worker is ready')

                // Use the ready registration for push subscription
                registration = readyRegistration
              } catch (regError) {
                console.error('❌ Service worker registration failed:', regError)
                throw regError
              }

              // Create real push subscription
              try {
                console.log('🔑 Using VAPID public key for subscription...')
                const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
                console.log('🔍 VAPID key from env:', vapidPublicKey ? 'Found' : 'Not found')
                console.log('🔍 VAPID key length:', vapidPublicKey?.length)
                console.log('🔍 VAPID key preview:', vapidPublicKey?.substring(0, 20) + '...')
                console.log('🔍 All env vars:', Object.keys(process.env).filter(k => k.includes('VAPID')))

                if (!vapidPublicKey) {
                  throw new Error('VAPID public key not configured')
                }

                // Convert base64url VAPID key to Uint8Array
                const urlBase64ToUint8Array = (base64String: string) => {
                  // Remove any padding that might be included
                  const cleanBase64 = base64String.replace(/=+$/, '')

                  // Add proper padding
                  const padding = '='.repeat((4 - cleanBase64.length % 4) % 4)
                  const base64 = (cleanBase64 + padding)
                    .replace(/-/g, '+')
                    .replace(/_/g, '/')

                  const rawData = window.atob(base64)
                  const outputArray = new Uint8Array(rawData.length)

                  for (let i = 0; i < rawData.length; ++i) {
                    outputArray[i] = rawData.charCodeAt(i)
                  }

                  // Ensure exactly 65 bytes for P-256 key
                  if (outputArray.length === 66) {
                    // Check if it starts with 0x04 (uncompressed point indicator)
                    if (outputArray[0] === 0x04) {
                      // This is likely a padding issue, try removing the last byte
                      return outputArray.slice(0, 65)
                    } else if (outputArray[0] === 0) {
                      // Remove leading zero byte if present
                      return outputArray.slice(1)
                    } else {
                      throw new Error(`Invalid VAPID key: 66 bytes but doesn't start with 0x04 or 0x00`)
                    }
                  } else if (outputArray.length === 65) {
                    return outputArray
                  } else {
                    throw new Error(`Invalid VAPID key length: ${outputArray.length}, expected 65`)
                  }
                }

                const applicationServerKey = urlBase64ToUint8Array(vapidPublicKey)
                console.log('🔧 Converted VAPID key to Uint8Array, length:', applicationServerKey.length)
                console.log('🔧 First few bytes:', Array.from(applicationServerKey.slice(0, 10)).map(b => b.toString(16).padStart(2, '0')).join(' '))

                const subscription = await registration.pushManager.subscribe({
                  userVisibleOnly: true,
                  applicationServerKey: applicationServerKey
                })

                console.log('✅ Real push subscription created:', subscription)
                console.log('📱 Subscription endpoint:', subscription.endpoint)
                console.log('🔑 Subscription keys:', subscription.toJSON())

                subscriptionData = {
                  subscription: subscription.toJSON(),
                  deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
                  browserName: getBrowserName(),
                  userAgent: navigator.userAgent,
                  preferences: {
                    order_updates: preferences.orderUpdates,
                    delivery_updates: preferences.deliveryUpdates,
                    marketing: preferences.promotions,
                    messages: preferences.messages
                  }
                }
              } catch (subError) {
                console.error('❌ Push subscription failed:', subError)
                throw subError
              }
            } else {
              throw new Error('Service Worker or Push Manager not supported')
            }
          } catch (swError) {
            console.warn('⚠️ Service worker/push subscription failed, creating demo subscription:', swError)

            // Fallback to demo subscription for testing
            const uniqueEndpoint = `https://fcm.googleapis.com/fcm/send/demo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

            subscriptionData = {
              subscription: {
                endpoint: uniqueEndpoint,
                keys: {
                  p256dh: `demo-p256dh-key-${Date.now()}`,
                  auth: `demo-auth-key-${Date.now()}`
                }
              },
              deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
              browserName: getBrowserName(),
              userAgent: navigator.userAgent,
              preferences: {
                order_updates: preferences.orderUpdates,
                delivery_updates: preferences.deliveryUpdates,
                marketing: preferences.promotions,
                messages: preferences.messages
              }
            }
          }

          console.log('📤 Sending subscription data:', subscriptionData)
          console.log('📤 JSON payload:', JSON.stringify(subscriptionData, null, 2))

          // Create a basic subscription record
          const response = await fetch('/api/notifications/subscribe', {
            method: 'POST',
            headers: addAuthHeaders(),
            body: JSON.stringify(subscriptionData)
          })

          console.log('📊 API Response:', response.status, response.statusText)
          console.log('📊 Response URL:', response.url)
          console.log('📊 Response type:', response.type)

          if (response.ok) {
            try {
              const responseData = await response.json()
              console.log('✅ Subscription successful:', responseData)
              setIsSubscribed(true)

              // Show success notification
              if (Notification.permission === 'granted') {
                new Notification('Notifications Enabled! 🎉', {
                  body: 'You\'ll now receive updates about your orders.',
                  icon: '/wheel-logo.svg',
                  badge: '/wheel-logo.svg'
                })
              }
            } catch (jsonError) {
              console.error('Error parsing success response:', jsonError)
              setIsSubscribed(true) // Assume success if response was ok
            }
          } else {
            let errorDetails = {
              status: response.status,
              statusText: response.statusText,
              url: response.url,
              headers: {}
            }

            // Get response headers
            response.headers.forEach((value, key) => {
              errorDetails.headers[key] = value
            })

            try {
              const errorText = await response.text()
              errorDetails.errorText = errorText

              // Try to parse as JSON for more details
              try {
                const errorJson = JSON.parse(errorText)
                errorDetails.errorJson = errorJson
              } catch (e) {
                // Not JSON, that's fine
              }
            } catch (textError) {
              errorDetails.textError = textError.message
            }

            console.error('❌ Subscription failed - Full details:', errorDetails)

            // Show user-friendly error
            if (response.status === 401) {
              alert('Please sign in to enable notifications')
            } else if (response.status === 400) {
              alert('Invalid request. Please check your data and try again.')
            } else if (response.status === 500) {
              alert('Server error. Please try again later.')
            } else {
              alert(`Failed to enable notifications (${response.status}). Please try again.`)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error toggling subscription:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreferenceChange = async (key: keyof typeof preferences) => {
    if (!isSubscribed) return

    const newPreferences = {
      ...preferences,
      [key]: !preferences[key]
    }
    setPreferences(newPreferences)

    // Save to server - only update preferences, don't create new subscription
    try {
      // Get the current subscription first
      const statusResponse = await fetch('/api/notifications/status', {
        method: 'GET',
        headers: addAuthHeaders()
      })

      if (statusResponse.ok) {
        const statusData = await statusResponse.json()
        if (statusData.hasActiveSubscription) {
          // Update preferences for existing subscription
          const response = await fetch('/api/notifications/preferences', {
            method: 'PATCH',
            headers: addAuthHeaders(),
            body: JSON.stringify({
              preferences: {
                order_updates: newPreferences.orderUpdates,
                delivery_updates: newPreferences.deliveryUpdates,
                marketing: newPreferences.promotions,
                messages: newPreferences.messages
              }
            })
          })

          if (!response.ok) {
            // Revert on error
            setPreferences(preferences)
          }
        }
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      setPreferences(preferences)
    }
  }

  const testNotification = async () => {
    if (hasPermission && Notification.permission === 'granted') {
      try {
        console.log('🧪 Sending server-side test notification...')
        // Use server-side test notification for proper Loop branding
        const response = await fetch('/api/test-notification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'customer_test'
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Server test notification sent:', result)
          // Don't show browser notification - server notification should appear
        } else {
          const errorText = await response.text()
          console.error('❌ Server test notification failed:', response.status, errorText)
          // Fallback to browser notification
          new Notification('Test Notification (Fallback)', {
            body: 'Server notification failed - this is browser fallback',
            icon: '/wheel-logo.svg',
            badge: '/wheel-logo.svg'
          })
        }
      } catch (error) {
        console.error('❌ Error sending test notification:', error)
        // Fallback to browser notification
        new Notification('Test Notification (Error Fallback)', {
          body: 'Network error occurred - this is browser fallback',
          icon: '/wheel-logo.svg',
          badge: '/wheel-logo.svg'
        })
      }
    }
  }

  const getBrowserName = () => {
    const userAgent = navigator.userAgent
    if (userAgent.includes('Chrome')) return 'Chrome'
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari')) return 'Safari'
    if (userAgent.includes('Edge')) return 'Edge'
    return 'Unknown'
  }

  // Check if notifications are supported
  const isSupported = 'Notification' in window

  if (isLoading || authLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BellOff className="h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <BellOff className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              Push notifications are not supported in this browser
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500 mb-2">
              Sign in to enable push notifications
            </p>
            <p className="text-xs text-gray-400">
              Get instant updates about your orders and deliveries
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Push Notifications
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base font-medium">Enable Push Notifications</Label>
            <p className="text-sm text-gray-600">
              {isSubscribed
                ? "You'll receive real-time notifications about your orders"
                : "Get instant updates about your orders and deliveries"
              }
            </p>
            {isSubscribed && (
              <div className="flex items-center gap-1 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                Active
              </div>
            )}
          </div>
          <Switch
            checked={isSubscribed}
            onCheckedChange={handleSubscriptionToggle}
            disabled={isLoading}
          />
        </div>

        {/* Permission Status */}
        {!hasPermission && !isSubscribed && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <p className="text-sm text-amber-700">
              Browser permission required to enable notifications
            </p>
          </div>
        )}

        {/* Preferences */}
        {isSubscribed && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-4 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Notification Types
              </h4>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm font-medium">Order Updates</Label>
                    <p className="text-xs text-gray-500">
                      Status changes, confirmations, and ready notifications
                    </p>
                  </div>
                  <Switch
                    checked={preferences.orderUpdates}
                    onCheckedChange={() => handlePreferenceChange('orderUpdates')}
                    size="sm"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm font-medium">Delivery Updates</Label>
                    <p className="text-xs text-gray-500">
                      Driver assignment and delivery progress
                    </p>
                  </div>
                  <Switch
                    checked={preferences.deliveryUpdates}
                    onCheckedChange={() => handlePreferenceChange('deliveryUpdates')}
                    size="sm"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm font-medium">Messages</Label>
                    <p className="text-xs text-gray-500">
                      Messages from businesses and drivers
                    </p>
                  </div>
                  <Switch
                    checked={preferences.messages}
                    onCheckedChange={() => handlePreferenceChange('messages')}
                    size="sm"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm font-medium">Promotions & Offers</Label>
                    <p className="text-xs text-gray-500">
                      Special deals and discounts
                    </p>
                  </div>
                  <Switch
                    checked={preferences.promotions}
                    onCheckedChange={() => handlePreferenceChange('promotions')}
                    size="sm"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Test Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={testNotification}
              className="w-full"
              disabled={!hasPermission}
            >
              Send Test Notification
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
