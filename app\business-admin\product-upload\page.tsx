"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Upload, FileText, Database, Sparkles, Info, AlertCircle, Download, ExternalLink } from "lucide-react"

export default function ProductUploadPage() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Product Data Upload & Enhancement</h1>
          <p className="text-muted-foreground">
            Upload your product data and enhance it with AI-powered descriptions and images
          </p>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="upload">Upload Products</TabsTrigger>
          <TabsTrigger value="history">Upload History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Data Upload & Enhancement</CardTitle>
              <CardDescription>
                Streamline your product catalog management with flexible import options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Two Ways to Import Products</AlertTitle>
                <AlertDescription>
                  You can either use our AI-powered enhancement tool to generate rich product descriptions and attributes,
                  or directly import your own complete CSV file that follows our template format.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <Card className="border-emerald-200">
                  <CardHeader className="pb-2 bg-emerald-50 rounded-t-lg">
                    <CardTitle className="text-lg flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-emerald-600" />
                      Standard CSV Import
                    </CardTitle>
                    <CardDescription>
                      For businesses with complete product data
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p className="text-sm text-gray-600 mb-4">
                      If you already have complete product information, you can use our standard CSV import.
                      Simply download our template, fill it with your data, and upload.
                    </p>
                    <div className="flex flex-col space-y-3">
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">1</div>
                        <span>Download our CSV template</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">2</div>
                        <span>Fill it with your product data</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">3</div>
                        <span>Upload and import directly</span>
                      </div>
                    </div>
                    <div className="mt-4 space-y-4">
                      <a
                        href="/templates/product-import-template.csv"
                        download
                        className="inline-flex items-center px-4 py-2 border border-emerald-600 text-sm font-medium rounded-md text-emerald-600 bg-white hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download CSV Template
                      </a>

                      <div className="bg-blue-50 p-3 rounded-md mt-3">
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <Info className="h-5 w-5 text-blue-600" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-blue-800">Rainbow CSV Extension</h3>
                            <div className="mt-1 text-sm text-blue-700">
                              <p>
                                We recommend using the Rainbow CSV extension to make editing CSV files easier.
                                It colorizes columns and helps with alignment, making it much easier to prepare your product data.
                              </p>
                              <p className="mt-1">
                                <a
                                  href="https://marketplace.visualstudio.com/items?itemName=mechatroner.rainbow-csv"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-800 font-medium hover:underline inline-flex items-center"
                                >
                                  Get Rainbow CSV for VS Code
                                  <ExternalLink className="h-3 w-3 ml-1" />
                                </a>
                                {" or "}
                                <a
                                  href="https://open-vsx.org/extension/mechatroner/rainbow-csv"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-800 font-medium hover:underline inline-flex items-center"
                                >
                                  other editors
                                  <ExternalLink className="h-3 w-3 ml-1" />
                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Need more examples?</h4>
                        <p className="text-sm text-gray-600 mb-2">
                          We've created a detailed template with examples for different business types:
                        </p>
                        <a
                          href="/templates/product-import-template-detailed.csv"
                          download
                          className="text-emerald-600 hover:text-emerald-700 text-sm inline-flex items-center"
                        >
                          <Download className="mr-1 h-4 w-4" />
                          Download detailed template with examples
                        </a>
                      </div>

                      <div className="bg-amber-50 p-3 rounded-md mt-4">
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <AlertCircle className="h-5 w-5 text-amber-600" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-amber-800">Product Availability Management</h3>
                            <div className="mt-1 text-sm text-amber-700">
                              <p>
                                <strong>Coming Soon:</strong> We're developing features to let you mark products as "out of stock"
                                or set time-based availability (e.g., "Available after 6 PM").
                              </p>
                              <p className="mt-1">
                                After importing your products, you'll be able to manage their availability directly
                                from the product management interface in your business admin dashboard.
                              </p>
                              <p className="mt-1">
                                Until then, you can manually update product availability through your business admin
                                dashboard once your products are imported.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-emerald-200">
                  <CardHeader className="pb-2 bg-emerald-50 rounded-t-lg">
                    <CardTitle className="text-lg flex items-center">
                      <Sparkles className="h-5 w-5 mr-2 text-emerald-600" />
                      AI-Enhanced Import
                    </CardTitle>
                    <CardDescription>
                      For businesses that want enhanced product content
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p className="text-sm text-gray-600 mb-4">
                      Let our AI help you create rich product descriptions, suggest categories, and generate
                      detailed attributes based on minimal product information.
                    </p>
                    <div className="flex flex-col space-y-3">
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">1</div>
                        <span>Upload basic product data (name, price)</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">2</div>
                        <span>Our AI generates descriptions & attributes</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center mr-2 text-emerald-600 font-medium">3</div>
                        <span>Review, edit and approve the enhanced data</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-center mt-6">
                <Button
                  className="bg-emerald-600 hover:bg-emerald-700"
                  onClick={() => setActiveTab("upload")}
                >
                  Start Product Upload
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upload Your Products</CardTitle>
              <CardDescription>
                Upload a CSV file with your product data to get started
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Coming Soon</AlertTitle>
                <AlertDescription>
                  The product upload feature is currently under development. Check back soon!
                </AlertDescription>
              </Alert>

              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Choose Your Import Method</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4 hover:border-emerald-300 hover:bg-emerald-50 cursor-pointer transition-colors">
                    <div className="flex items-center mb-2">
                      <div className="h-5 w-5 rounded-full border-2 border-emerald-500 mr-2 flex items-center justify-center">
                        <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
                      </div>
                      <span className="font-medium">Standard CSV Import</span>
                    </div>
                    <p className="text-sm text-gray-600 ml-7">
                      Upload your own complete CSV file that follows our template format.
                      <a
                        href="/templates/product-import-template.csv"
                        download
                        className="text-emerald-600 hover:text-emerald-700 ml-1 inline-flex items-center"
                      >
                        Download template
                        <Download className="ml-1 h-3 w-3" />
                      </a>
                    </p>
                    <p className="text-xs text-gray-600 ml-7 mt-1 flex items-center">
                      <Info className="h-3 w-3 mr-1 flex-shrink-0" />
                      The product_id field is for your own internal reference and reconciliation
                    </p>
                    <p className="text-xs text-blue-600 ml-7 mt-1 flex items-center">
                      <Info className="h-3 w-3 mr-1 flex-shrink-0" />
                      We recommend using Rainbow CSV extension for easier editing
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 hover:border-emerald-300 hover:bg-emerald-50 cursor-pointer transition-colors">
                    <div className="flex items-center mb-2">
                      <div className="h-5 w-5 rounded-full border-2 border-gray-300 mr-2"></div>
                      <span className="font-medium">AI-Enhanced Import</span>
                    </div>
                    <p className="text-sm text-gray-600 ml-7">
                      Upload basic product data and let our AI enhance it with descriptions and attributes.
                    </p>
                  </div>
                </div>
              </div>

              <div className="border-2 border-dashed rounded-lg p-12 text-center transition-colors border-gray-300">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="rounded-full bg-emerald-100 p-4">
                    <Upload className="h-8 w-8 text-emerald-600" />
                  </div>
                  <div>
                    <p className="text-lg font-medium">Drag & drop your CSV file here</p>
                    <p className="text-sm text-gray-500">or</p>
                  </div>
                  <Button variant="outline" className="mt-2">
                    Browse Files
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upload History</CardTitle>
              <CardDescription>
                View your previous product uploads and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Coming Soon</AlertTitle>
                <AlertDescription>
                  The upload history feature is currently under development. Check back soon!
                </AlertDescription>
              </Alert>

              <div className="text-center py-12">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium">No Upload History</h3>
                <p className="text-sm text-gray-500 mt-1">
                  You haven't uploaded any product data yet.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
