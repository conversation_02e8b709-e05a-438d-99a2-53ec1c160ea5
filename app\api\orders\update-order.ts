// Helper function for updating order records
import { createClient } from '@supabase/supabase-js';

// Supabase client setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Updates the status of an order
 * 
 * @param orderId The ID of the order
 * @param status The new status to set
 * @returns An object with success flag and error message if applicable
 */
export async function updateOrderStatus(
  orderId: number,
  status: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // 1. Update the order status
    const { data: order, error: updateError } = await supabase
      .from('orders')
      .update({
        status,
        updated_at: new Date()
      })
      .eq('id', orderId)
      .select()
      .single();
    
    if (updateError) {
      return { success: false, error: `Failed to update order status: ${updateError.message}` };
    }
    
    // 2. Add a status history entry
    const historyEntry = {
      order_id: orderId,
      status,
      notes: `Status updated to ${status}`,
      created_at: new Date()
    };
    
    const { error: historyError } = await supabase
      .from('order_status_history')
      .insert(historyEntry);
    
    if (historyError) {
      console.error(`Warning: Failed to create status history entry: ${historyError.message}`);
      // Don't fail the whole operation if just the history entry fails
    }
    
    return { success: true, order };
    
  } catch (error: any) {
    return { success: false, error: `Unexpected error: ${error.message}` };
  }
}

/**
 * Gets an order by ID
 * 
 * @param orderId The ID of the order
 * @returns The order or an error
 */
export async function getOrder(orderId: number) {
  return await supabase
    .from('orders')
    .select(`
      *,
      order_items(*),
      order_status_history(*)
    `)
    .eq('id', orderId)
    .single();
}

/**
 * Gets related orders (for multi-business orders)
 * 
 * @param customerEmail The customer email to find related orders
 * @param createdAt The timestamp when the orders were created
 * @returns The related orders or an error
 */
export async function getRelatedOrders(customerEmail: string, createdAt: string) {
  return await supabase
    .from('orders')
    .select(`
      *,
      order_items(*)
    `)
    .eq('customer_email', customerEmail)
    .eq('created_at', createdAt);
}
