import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get Supabase URL and keys from environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({
        error: 'Supabase environment variables not set',
        environment: {
          supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
          supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Not set',
        }
      }, { status: 500 });
    }
    
    // Check if the Supabase project is running by making a simple health check request
    const healthCheckUrl = `${supabaseUrl}/rest/v1/`;
    
    const response = await fetch(healthCheckUrl, {
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`
      }
    });
    
    const isProjectRunning = response.ok;
    let responseData = null;
    
    try {
      responseData = await response.json();
    } catch (e) {
      // Ignore JSON parse errors
    }
    
    // Try to fetch the Supabase project status
    const projectStatusUrl = `${supabaseUrl}/rest/v1/`;
    
    const statusResponse = await fetch(projectStatusUrl, {
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`
      }
    });
    
    const isStatusOk = statusResponse.ok;
    let statusData = null;
    
    try {
      statusData = await statusResponse.json();
    } catch (e) {
      // Ignore JSON parse errors
    }
    
    return NextResponse.json({
      supabaseUrl,
      isProjectRunning,
      responseStatus: response.status,
      responseStatusText: response.statusText,
      responseData,
      isStatusOk,
      statusData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in check-supabase-status API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
