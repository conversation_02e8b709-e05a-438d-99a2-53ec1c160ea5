import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Verify super admin access
async function verifySuperAdminAccess(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { authorized: false, error: 'No authorization header', status: 401 }
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      return { authorized: false, error: 'Invalid authorization header format', status: 401 }
    }

    // Create client with user's token
    const userClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, {
      global: {
        headers: {
          Authorization: authHeader
        }
      }
    })

    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError || !user) {
      return { authorized: false, error: 'Invalid token', status: 401 }
    }

    // Check if user is super admin
    const { data: profile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (profileError || !profile) {
      return { authorized: false, error: 'User profile not found', status: 404 }
    }

    if (profile.role !== 'super_admin') {
      return { authorized: false, error: 'Super admin access required', status: 403 }
    }

    return { authorized: true, user, profile }
  } catch (error) {
    console.error('Error verifying super admin access:', error)
    return { authorized: false, error: 'Authentication error', status: 500 }
  }
}

// Define the categories to populate based on the current home page categories
const categoriesToPopulate = [
  // Restaurant Categories (business_type_id: 1)
  { name: 'Pizza', slug: 'pizza', description: 'Pizza restaurants and pizzerias', business_type_id: 1, category_purpose: 'specialization', display_order: 10, icon: '🍕' },
  { name: 'Burgers', slug: 'burgers', description: 'Burger joints and grill houses', business_type_id: 1, category_purpose: 'specialization', display_order: 20, icon: '🍔' },
  { name: 'Sushi', slug: 'sushi', description: 'Japanese sushi and sashimi', business_type_id: 1, category_purpose: 'specialization', display_order: 30, icon: '🍣' },
  { name: 'Chinese', slug: 'chinese', description: 'Chinese cuisine and takeaway', business_type_id: 1, category_purpose: 'specialization', display_order: 40, icon: '🥡' },
  { name: 'Italian', slug: 'italian', description: 'Italian restaurants and pasta', business_type_id: 1, category_purpose: 'specialization', display_order: 50, icon: '🍝' },
  { name: 'Mexican', slug: 'mexican', description: 'Mexican food and tacos', business_type_id: 1, category_purpose: 'specialization', display_order: 60, icon: '🌮' },
  { name: 'Indian', slug: 'indian', description: 'Indian curry and spice cuisine', business_type_id: 1, category_purpose: 'specialization', display_order: 70, icon: '🍛' },
  { name: 'Chicken', slug: 'chicken', description: 'Chicken restaurants and wings', business_type_id: 1, category_purpose: 'specialization', display_order: 80, icon: '🍗' },
  { name: 'Healthy', slug: 'healthy', description: 'Healthy and nutritious options', business_type_id: 1, category_purpose: 'specialization', display_order: 90, icon: '🥗' },
  { name: 'Desserts', slug: 'desserts', description: 'Desserts and sweet treats', business_type_id: 1, category_purpose: 'specialization', display_order: 100, icon: '🍰' },
  { name: 'Seafood', slug: 'seafood', description: 'Fresh seafood and fish', business_type_id: 1, category_purpose: 'specialization', display_order: 110, icon: '🍤' },
  { name: 'Fast Food', slug: 'fast-food', description: 'Quick service restaurants', business_type_id: 1, category_purpose: 'specialization', display_order: 120, icon: '🍟' },
  { name: 'BBQ', slug: 'bbq', description: 'Barbecue and grilled meats', business_type_id: 1, category_purpose: 'specialization', display_order: 130, icon: '🍖' },
  { name: 'Thai', slug: 'thai', description: 'Thai cuisine and curries', business_type_id: 1, category_purpose: 'specialization', display_order: 140, icon: '🍛' },
  { name: 'Japanese', slug: 'japanese', description: 'Japanese cuisine beyond sushi', business_type_id: 1, category_purpose: 'specialization', display_order: 150, icon: '🍱' },
  { name: 'Korean', slug: 'korean', description: 'Korean BBQ and kimchi', business_type_id: 1, category_purpose: 'specialization', display_order: 160, icon: '🍲' },
  { name: 'Vietnamese', slug: 'vietnamese', description: 'Vietnamese pho and fresh rolls', business_type_id: 1, category_purpose: 'specialization', display_order: 170, icon: '🍜' },
  { name: 'Mediterranean', slug: 'mediterranean', description: 'Mediterranean and Greek food', business_type_id: 1, category_purpose: 'specialization', display_order: 180, icon: '🫒' },
  { name: 'British', slug: 'british', description: 'Traditional British cuisine', business_type_id: 1, category_purpose: 'specialization', display_order: 190, icon: '🏴' },
  { name: 'French', slug: 'french', description: 'French cuisine and bistros', business_type_id: 1, category_purpose: 'specialization', display_order: 200, icon: '🥖' },

  // Cafe Categories (business_type_id: 4)
  { name: 'Coffee', slug: 'coffee', description: 'Coffee shops and cafes', business_type_id: 4, category_purpose: 'specialization', display_order: 10, icon: '☕' },
  { name: 'Pastries', slug: 'pastries', description: 'Bakery items and pastries', business_type_id: 4, category_purpose: 'specialization', display_order: 20, icon: '🥐' },
  { name: 'Breakfast', slug: 'breakfast', description: 'Breakfast and brunch items', business_type_id: 4, category_purpose: 'specialization', display_order: 30, icon: '🍳' },
  { name: 'Sandwiches', slug: 'sandwiches', description: 'Sandwiches and light meals', business_type_id: 4, category_purpose: 'specialization', display_order: 40, icon: '🥪' },
  { name: 'Tea', slug: 'tea', description: 'Tea varieties and bubble tea', business_type_id: 4, category_purpose: 'specialization', display_order: 50, icon: '🍵' },
  { name: 'Smoothies', slug: 'smoothies', description: 'Fresh smoothies and juices', business_type_id: 4, category_purpose: 'specialization', display_order: 60, icon: '🧃' },

  // Shop Categories (business_type_id: 2)
  { name: 'Groceries', slug: 'groceries', description: 'General grocery shopping', business_type_id: 2, category_purpose: 'specialization', display_order: 10, icon: '🛒' },
  { name: 'Fresh Produce', slug: 'fresh-produce', description: 'Fresh fruits and vegetables', business_type_id: 2, category_purpose: 'specialization', display_order: 20, icon: '🍎' },
  { name: 'Dairy & Eggs', slug: 'dairy-eggs', description: 'Dairy products and eggs', business_type_id: 2, category_purpose: 'specialization', display_order: 30, icon: '🥚' },
  { name: 'Meat & Seafood', slug: 'meat-seafood', description: 'Fresh meat and seafood', business_type_id: 2, category_purpose: 'specialization', display_order: 40, icon: '🥩' },
  { name: 'Bakery', slug: 'bakery', description: 'Fresh bread and baked goods', business_type_id: 2, category_purpose: 'specialization', display_order: 50, icon: '🥖' },
  { name: 'Snacks', slug: 'snacks', description: 'Snacks and confectionery', business_type_id: 2, category_purpose: 'specialization', display_order: 60, icon: '🍫' },
  { name: 'Beverages', slug: 'beverages', description: 'Drinks and soft drinks', business_type_id: 2, category_purpose: 'specialization', display_order: 70, icon: '🥤' },
  { name: 'Household', slug: 'household', description: 'Household essentials', business_type_id: 2, category_purpose: 'specialization', display_order: 80, icon: '🏠' },
  { name: 'Personal Care', slug: 'personal-care', description: 'Personal care products', business_type_id: 2, category_purpose: 'specialization', display_order: 90, icon: '🧼' },

  // Pharmacy Categories (business_type_id: 3)
  { name: 'Health', slug: 'health', description: 'Health and wellness products', business_type_id: 3, category_purpose: 'specialization', display_order: 10, icon: '🩺' },
  { name: 'Medications', slug: 'medications', description: 'Over-the-counter medications', business_type_id: 3, category_purpose: 'specialization', display_order: 20, icon: '💊' },
  { name: 'Beauty', slug: 'beauty', description: 'Beauty and cosmetic products', business_type_id: 3, category_purpose: 'specialization', display_order: 30, icon: '💄' },
  { name: 'First Aid', slug: 'first-aid', description: 'First aid supplies', business_type_id: 3, category_purpose: 'specialization', display_order: 40, icon: '🩹' },
  { name: 'Wellness', slug: 'wellness', description: 'Vitamins and supplements', business_type_id: 3, category_purpose: 'specialization', display_order: 50, icon: '🧘' },

  // Errand Categories (business_type_id: 38)
  { name: 'Delivery', slug: 'delivery', description: 'General delivery services', business_type_id: 38, category_purpose: 'specialization', display_order: 10, icon: '🚚' },
  { name: 'Shopping', slug: 'shopping', description: 'Personal shopping services', business_type_id: 38, category_purpose: 'specialization', display_order: 20, icon: '🛍️' },
  { name: 'Tasks', slug: 'tasks', description: 'General task assistance', business_type_id: 38, category_purpose: 'specialization', display_order: 30, icon: '📋' },
  { name: 'Pickup', slug: 'pickup', description: 'Pickup and collection services', business_type_id: 38, category_purpose: 'specialization', display_order: 40, icon: '📦' },

  // Lift Categories (business_type_id: 49)
  { name: 'Transport', slug: 'transport', description: 'Personal transportation services', business_type_id: 49, category_purpose: 'specialization', display_order: 10, icon: '🚗' },
  { name: 'Airport Transfer', slug: 'airport-transfer', description: 'Airport pickup and drop-off', business_type_id: 49, category_purpose: 'specialization', display_order: 20, icon: '✈️' },
  { name: 'Local Rides', slug: 'local-rides', description: 'Short distance rides around Jersey', business_type_id: 49, category_purpose: 'specialization', display_order: 30, icon: '🚙' },
]

// POST - Populate categories from the home page horizontal menu
export async function POST(request: NextRequest) {
  try {
    const accessCheck = await verifySuperAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    let insertedCount = 0
    let skippedCount = 0
    const errors: string[] = []

    for (const category of categoriesToPopulate) {
      try {
        // Check if category already exists
        const { data: existingCategory } = await adminClient
          .from('categories')
          .select('id')
          .eq('slug', category.slug)
          .single()

        if (existingCategory) {
          skippedCount++
          continue
        }

        // Insert the category
        const { error } = await adminClient
          .from('categories')
          .insert({
            name: category.name,
            slug: category.slug,
            description: category.description,
            business_type_id: category.business_type_id,
            category_purpose: category.category_purpose,
            display_order: category.display_order,
            level: 1, // All categories are level 1 now
            is_active: true
          })

        if (error) {
          errors.push(`Failed to insert ${category.name}: ${error.message}`)
        } else {
          insertedCount++
        }
      } catch (error) {
        errors.push(`Error processing ${category.name}: ${error}`)
      }
    }

    return NextResponse.json({
      message: 'Categories population completed',
      inserted: insertedCount,
      skipped: skippedCount,
      errors: errors,
      total: categoriesToPopulate.length
    })
  } catch (error) {
    console.error('Error in POST /api/super-admin/categories/populate:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
