"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export default function TerminalPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Function to fetch logs
  const fetchLogs = async () => {
    try {
      const response = await fetch('/api/debug/terminal')
      const data = await response.json()
      setLogs(data.logs || [])
    } catch (error) {
      console.error('Error fetching logs:', error)
    } finally {
      setLoading(false)
    }
  }

  // Function to clear logs
  const clearLogs = async () => {
    try {
      await fetch('/api/debug/terminal', { method: 'DELETE' })
      setLogs([])
    } catch (error) {
      console.error('Error clearing logs:', error)
    }
  }

  // Function to add a test log
  const addTestLog = async () => {
    try {
      await fetch('/api/debug/terminal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Test log message from terminal page'
        }),
      })
      fetchLogs()
    } catch (error) {
      console.error('Error adding test log:', error)
    }
  }

  // Fetch logs on component mount and when autoRefresh changes
  useEffect(() => {
    fetchLogs()

    // Set up auto-refresh if enabled
    let intervalId: NodeJS.Timeout | null = null
    if (autoRefresh) {
      intervalId = setInterval(fetchLogs, 2000) // Refresh every 2 seconds
    }

    // Clean up interval on component unmount
    return () => {
      if (intervalId) clearInterval(intervalId)
    }
  }, [autoRefresh])

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Terminal</h1>
      
      <div className="flex gap-4 mb-6">
        <Button onClick={fetchLogs} variant="outline">
          Refresh Logs
        </Button>
        <Button onClick={clearLogs} variant="destructive">
          Clear Logs
        </Button>
        <Button onClick={addTestLog} variant="default">
          Add Test Log
        </Button>
        <Button 
          onClick={() => setAutoRefresh(!autoRefresh)} 
          variant={autoRefresh ? "default" : "outline"}
        >
          {autoRefresh ? "Auto-Refresh: ON" : "Auto-Refresh: OFF"}
        </Button>
        <Link href="/debug-terminal">
          <Button variant="outline">
            Debug Terminal
          </Button>
        </Link>
        <Link href="/auth-debug/business-admin-test">
          <Button variant="outline">
            Business Admin Debug
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Terminal Output</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading logs...</p>
          ) : logs.length === 0 ? (
            <p>No logs available.</p>
          ) : (
            <div className="bg-black text-white p-4 rounded-md font-mono text-sm overflow-auto max-h-[600px]">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
