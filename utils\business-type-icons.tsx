import { 
  Utensils, 
  Package, 
  Pill, 
  Coffee, 
  Car, 
  CheckSquare, 
  Store,
  LucideIcon
} from "lucide-react"

/**
 * Get the appropriate icon component for a business type
 * @param businessType - The business type slug or name
 * @returns The corresponding Lucide icon component
 */
export function getBusinessTypeIcon(businessType?: string): LucideIcon {
  if (!businessType) return Store

  const type = businessType.toLowerCase()

  switch (type) {
    case 'restaurant':
      return Utensils
    case 'shop':
      return Package
    case 'pharmacy':
      return Pill
    case 'cafe':
      return Coffee
    case 'lift':
      return Car
    case 'errand':
      return CheckSquare
    default:
      return Store
  }
}

/**
 * Get the appropriate icon component with styling for a business type
 * @param businessType - The business type slug or name
 * @param className - CSS classes to apply to the icon
 * @returns JSX element with the icon and styling
 */
export function getBusinessTypeIconWithStyle(
  businessType?: string, 
  className: string = "h-5 w-5"
) {
  const IconComponent = getBusinessTypeIcon(businessType)
  return <IconComponent className={className} />
}
