"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Package,
  MapPin,
  Clock,
  DollarSign,
  Star,
  ChevronRight,
  RefreshCw,
  AlertCircle,
  Navigation,
  Bell,
  AlertTriangle,
  ChevronUp,
  ChevronDown,
  Truck,
  CheckCircle,
  User,
  Building2
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { SwipeableOrderCard } from "@/components/driver/swipeable-order-card"
import { OrderWeightChart } from "@/components/driver/order-weight-chart"
import { EnhancedLocationTracker } from "@/components/enhanced-location-tracker"
import { notificationService } from "@/services/notification-service"
import { toast } from "sonner"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"

interface DriverDashboardData {
  driver: {
    id: string
    authId: string
    name: string
    isVerified: boolean
    isActive: boolean
    vehicleType: string
    totalDeliveries: number
    averageRating: number
    memberSince: string
    averageDeliveriesPerDay: number
  }
  status: {
    isOnline: boolean
    isOnDelivery: boolean
    lastStatusChange: string | null
    hasLocation: boolean
    locationUpdatedAt: string | null
  }
  currentOrder: any | null
  earnings: {
    today: number
    thisWeek: number
    currency: string
  }
  stats: {
    todayDeliveries: number
    availableOrders: number
    totalDeliveries: number
    averageRating: number
  }
  recentDeliveries: any[]
  timestamp: string
}

interface AvailableOrder {
  id: number
  order_number: string
  business_name: string
  customer_name: string
  delivery_address: string
  postcode: string
  parish: string
  delivery_type: string
  total: number
  delivery_fee: number
  distance: number | null
  delivery_distance_km: number | null
  estimated_delivery_time: string | number | null
  estimatedPickupTime: number | null
  itemCount: number
  totalItems: number
  status: string
  created_at: string
  updated_at: string
  businesses: {
    name: string
    address: string
    phone: string
  }
}

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image_url?: string
  customizations?: any
  special_instructions?: string
  products?: {
    id: string
    name: string
    description: string
    category: string
  }
}

interface AvailableOrdersData {
  availableOrders: AvailableOrder[]
  currentOrder: any | null
  isOnDelivery: boolean
  driverLocation: {
    lat: number
    lng: number
  } | null
}

export default function DriverOrdersPage() {
  const [dashboardData, setDashboardData] = useState<DriverDashboardData | null>(null)
  const [availableOrders, setAvailableOrders] = useState<AvailableOrdersData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)
  const [isEnablingNotifications, setIsEnablingNotifications] = useState(false)
  const [expandedOrders, setExpandedOrders] = useState<Set<number>>(new Set())
  const [orderItems, setOrderItems] = useState<Record<number, OrderItem[]>>({})
  const [loadingItems, setLoadingItems] = useState<Set<number>>(new Set())
  const [processingOrders, setProcessingOrders] = useState<Set<number>>(new Set())

  // Real-time subscription for orders
  const {
    data: realtimeOrders,
    loading: realtimeLoading,
    error: realtimeError,
  } = useSupabaseRealtime<any>(
    'orders',
    `
      id,
      order_number,
      business_id,
      business_name,
      customer_name,
      customer_phone,
      delivery_address,
      postcode,
      parish,
      delivery_type,
      total,
      delivery_fee,
      status,
      driver_id,
      preparation_time,
      estimated_delivery_time,
      ready_time,
      created_at,
      cart_id,
      delivery_distance_km,
      businesses!inner (
        id,
        name,
        address,
        location,
        phone,
        latitude,
        coordinates
      )
    `,
    [
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.offered' },
      { table: 'orders', event: 'INSERT', filter: 'status=eq.offered' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.assigned' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.picked_up' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.out_for_delivery' },
      { table: 'orders', event: 'DELETE' },
    ],
    {
      orderBy: 'ready_time',
      orderDirection: 'asc',
      limit: 20
    }
  )

  // Update available orders when real-time data changes
  useEffect(() => {
    if (realtimeOrders && !realtimeLoading) {
      // Filter for offered orders with no driver assigned
      const offeredOrders = realtimeOrders.filter(order =>
        order.status === 'offered' && !order.driver_id
      )

      // Filter for current order (assigned to this driver)
      const currentOrder = realtimeOrders.find(order =>
        order.driver_id && ['assigned', 'picked_up', 'out_for_delivery'].includes(order.status)
      )

      // Update available orders state
      const newOrdersData: AvailableOrdersData = {
        availableOrders: offeredOrders.map(order => ({
          ...order,
          itemCount: 0, // Will be calculated when expanded
          totalItems: 0, // Will be calculated when expanded
          distance: order.delivery_distance_km || null,
          estimatedPickupTime: null // Will be calculated if driver location available
        })),
        currentOrder: currentOrder || null,
        isOnDelivery: !!currentOrder,
        driverLocation: null // Will be updated by location tracker
      }

      // Show toast for new orders (only if we already have data loaded)
      if (availableOrders && offeredOrders.length > (availableOrders.availableOrders?.length || 0)) {
        const newOrdersCount = offeredOrders.length - (availableOrders.availableOrders?.length || 0)
        if (newOrdersCount > 0) {
          toast.success(`${newOrdersCount} new order${newOrdersCount > 1 ? 's' : ''} available!`, {
            description: "Check the available orders section below.",
            duration: 4000,
          })
        }
      }

      setAvailableOrders(newOrdersData)
      setIsLoading(false)
      setError(null)
    }
  }, [realtimeOrders, realtimeLoading])

  // Handle realtime errors
  useEffect(() => {
    if (realtimeError) {
      console.error('Realtime error:', realtimeError)
      setError('Connection error. Trying to reconnect...')
      // Fallback to API fetch if realtime fails
      fetchAvailableOrders()
    }
  }, [realtimeError])

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [])

  // Check notification status on load
  useEffect(() => {
    const checkNotificationStatus = async () => {
      await notificationService.initialize()
      setNotificationsEnabled(notificationService.notificationsEnabled)
    }
    checkNotificationStatus()
  }, [])

  // Initial data fetch
  useEffect(() => {
    Promise.all([fetchDashboardData(), fetchAvailableOrders()])
  }, [])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/driver/dashboard')
      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.statusText}`)
      }
      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
    }
  }

  // Fetch available orders
  const fetchAvailableOrders = async () => {
    try {
      const response = await fetch('/api/driver/available-orders')
      if (!response.ok) {
        throw new Error(`Failed to fetch available orders: ${response.statusText}`)
      }
      const data = await response.json()
      setAvailableOrders(data)
    } catch (err) {
      console.error('Error fetching available orders:', err)
      // Don't set error for available orders as it's not critical
    } finally {
      setIsLoading(false)
    }
  }

  // Update driver status (online/offline)
  const updateDriverStatus = async (isOnline: boolean) => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch('/api/driver/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isOnline, action: 'toggle' }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`)
      }

      const result = await response.json()

      // Update local state
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          status: {
            ...dashboardData.status,
            isOnline: result.status.isOnline,
            lastStatusChange: result.status.lastStatusChange
          }
        })
      }

      // Refresh available orders when going online
      if (isOnline) {
        await fetchAvailableOrders()
      }

    } catch (err) {
      console.error('Error updating driver status:', err)
      setError(err instanceof Error ? err.message : 'Failed to update status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Enable notifications
  const enableNotifications = async () => {
    setIsEnablingNotifications(true)
    try {
      await notificationService.initialize()

      if (notificationService.permissionStatus !== 'granted') {
        const permission = await notificationService.requestPermission()
        if (permission !== 'granted') {
          throw new Error('Notification permission denied')
        }
      }

      // Subscribe to notifications
      await notificationService.subscribe()

      setNotificationsEnabled(notificationService.notificationsEnabled)

      // Show success message
      toast.success("Notifications enabled!", {
        description: "You'll receive updates about new delivery requests.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error enabling notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to enable notifications')
    } finally {
      setIsEnablingNotifications(false)
    }
  }

  // Toggle order expansion and fetch items if needed
  const toggleOrderExpansion = async (orderId: number) => {
    const newExpandedOrders = new Set(expandedOrders)

    if (expandedOrders.has(orderId)) {
      newExpandedOrders.delete(orderId)
    } else {
      newExpandedOrders.add(orderId)

      // Fetch order items if not already loaded
      if (!orderItems[orderId]) {
        const newLoadingItems = new Set(loadingItems)
        newLoadingItems.add(orderId)
        setLoadingItems(newLoadingItems)

        try {
          const response = await fetch(`/api/driver/orders/${orderId}/items`)
          if (response.ok) {
            const data = await response.json()
            setOrderItems(prev => ({
              ...prev,
              [orderId]: data.items || []
            }))
          } else {
            console.error('Failed to fetch order items:', response.statusText)
            toast.error("Failed to load order items", {
              description: "Please try again.",
              duration: 3000,
            })
          }
        } catch (err) {
          console.error('Error fetching order items:', err)
          toast.error("Failed to load order items", {
            description: "Please check your connection and try again.",
            duration: 3000,
          })
        } finally {
          const newLoadingItems = new Set(loadingItems)
          newLoadingItems.delete(orderId)
          setLoadingItems(newLoadingItems)
        }
      }
    }

    setExpandedOrders(newExpandedOrders)
  }

  // Accept an order
  const acceptOrder = async (orderId: number) => {
    const newProcessingOrders = new Set(processingOrders)
    newProcessingOrders.add(orderId)
    setProcessingOrders(newProcessingOrders)

    try {
      const response = await fetch('/api/driver/accept-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to accept order: ${response.statusText}`)
      }

      const result = await response.json()

      // Refresh dashboard and available orders
      await Promise.all([fetchDashboardData(), fetchAvailableOrders()])

      // Show success message
      toast.success("Order accepted!", {
        description: "You can now start the delivery.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error accepting order:', err)
      toast.error("Failed to accept order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    } finally {
      const newProcessingOrders = new Set(processingOrders)
      newProcessingOrders.delete(orderId)
      setProcessingOrders(newProcessingOrders)
    }
  }

  // Decline an order
  const declineOrder = async (orderId: number) => {
    const newProcessingOrders = new Set(processingOrders)
    newProcessingOrders.add(orderId)
    setProcessingOrders(newProcessingOrders)

    try {
      const response = await fetch('/api/driver/decline-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to decline order: ${response.statusText}`)
      }

      const result = await response.json()

      // Remove the declined order from the UI immediately
      if (availableOrders) {
        const updatedOrders = availableOrders.availableOrders.filter(order => order.id !== orderId)
        setAvailableOrders({
          ...availableOrders,
          availableOrders: updatedOrders
        })
      }

      // Also refresh from server to ensure consistency
      await fetchAvailableOrders()

      // Show success message
      toast.success("Order declined", {
        description: "The order has been returned to the pool.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error declining order:', err)
      toast.error("Failed to decline order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    } finally {
      const newProcessingOrders = new Set(processingOrders)
      newProcessingOrders.delete(orderId)
      setProcessingOrders(newProcessingOrders)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'offered':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'assigned':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'picked_up':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'out_for_delivery':
        return 'bg-orange-50 text-orange-700 border-orange-200'
      case 'delivered':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600">Available orders and current deliveries</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600">Available orders and current deliveries</p>
        </div>
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            {error}
          </AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Page
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
        <p className="text-gray-600">Available orders and current deliveries</p>
      </div>

      {/* Driver Status & Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Status Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Driver Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  dashboardData?.status.isOnline ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
                }`}></div>
                <Label htmlFor="online-status" className="font-medium">
                  {dashboardData?.status.isOnline ? 'Online' : 'Offline'}
                </Label>
              </div>
              <Switch
                id="online-status"
                checked={dashboardData?.status.isOnline || false}
                onCheckedChange={updateDriverStatus}
                disabled={isUpdatingStatus}
              />
            </div>

            {dashboardData?.status.isOnDelivery && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">Currently on delivery</p>
                <p className="text-xs text-blue-600">Complete your current delivery to accept new orders</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notifications Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Notifications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Bell className={`h-4 w-4 ${notificationsEnabled ? 'text-green-600' : 'text-gray-400'}`} />
                <Label className="font-medium">
                  {notificationsEnabled ? 'Enabled' : 'Disabled'}
                </Label>
              </div>
              {!notificationsEnabled && (
                <Button
                  size="sm"
                  onClick={enableNotifications}
                  disabled={isEnablingNotifications}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {isEnablingNotifications ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    'Enable'
                  )}
                </Button>
              )}
            </div>

            {!notificationsEnabled && (
              <p className="text-xs text-gray-500">
                Enable notifications to receive alerts for new orders
              </p>
            )}
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Today's Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Deliveries</span>
              <span className="font-medium">{dashboardData?.stats.todayDeliveries || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Earnings</span>
              <span className="font-medium">{formatCurrency(dashboardData?.earnings.today || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Available</span>
              <span className="font-medium">
                {availableOrders ? availableOrders.availableOrders.length : dashboardData?.stats.availableOrders || 0}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Order */}
      {availableOrders?.currentOrder && (
        <Card className="border-2 border-blue-500 bg-blue-50">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg text-blue-900">Current Delivery</CardTitle>
              <Badge className="bg-blue-600 text-white">
                {availableOrders.currentOrder.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <p className="text-sm text-blue-700 mb-1">Order</p>
                <p className="font-medium text-blue-900">#{availableOrders.currentOrder.order_number}</p>
                <p className="text-sm text-blue-800">{availableOrders.currentOrder.business_name}</p>
              </div>
              <div>
                <p className="text-sm text-blue-700 mb-1">Customer</p>
                <p className="font-medium text-blue-900">{availableOrders.currentOrder.customer_name}</p>
                <p className="text-sm text-blue-800">{availableOrders.currentOrder.parish}</p>
              </div>
              <div>
                <p className="text-sm text-blue-700 mb-1">Delivery Fee</p>
                <p className="font-medium text-blue-900">{formatCurrency(availableOrders.currentOrder.delivery_fee)}</p>
              </div>
            </div>
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium" asChild>
              <Link href={`/driver/delivery-history/${availableOrders.currentOrder.order_number}`}>
                <Package className="h-4 w-4 mr-2" />
                Continue Delivery
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Available Orders */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Available Orders</CardTitle>
              <CardDescription>
                Orders waiting for pickup
                {!realtimeLoading && !realtimeError && (
                  <span className="inline-flex items-center ml-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"></div>
                    <span className="text-xs text-green-600">Live</span>
                  </span>
                )}
              </CardDescription>
            </div>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              {availableOrders?.availableOrders.length || 0} available
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {availableOrders?.availableOrders && availableOrders.availableOrders.length > 0 ? (
            <div className="space-y-4">
              {availableOrders.availableOrders.map((order) => (
                <SwipeableOrderCard
                  key={order.id}
                  order={order}
                  isExpanded={expandedOrders.has(order.id)}
                  onToggleExpansion={() => toggleOrderExpansion(order.id)}
                  onAccept={() => acceptOrder(order.id)}
                  onDecline={() => declineOrder(order.id)}
                  orderItems={orderItems[order.id]}
                  loadingItems={loadingItems.has(order.id)}
                  formatCurrency={formatCurrency}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No orders available</h3>
              <p className="text-gray-500 mb-4">
                {dashboardData?.status.isOnline
                  ? "You'll be notified when new delivery requests come in"
                  : "Go online to start receiving delivery requests"
                }
              </p>
              {!dashboardData?.status.isOnline && (
                <Button
                  onClick={() => updateDriverStatus(true)}
                  disabled={isUpdatingStatus}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {isUpdatingStatus ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Go Online
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Location Tracker - Hidden but functional for desktop */}
      <div className="hidden">
        <EnhancedLocationTracker />
      </div>
    </div>
  )
}
