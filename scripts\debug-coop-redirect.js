/**
 * Debug why Jersey Co-op isn't redirecting to aisle layout
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugCoopRedirect() {
  try {
    console.log('🔍 Debugging Jersey Co-op redirect issue...\n');

    // Check Jersey Co-op business data
    const { data: coop, error } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-coop')
      .single();

    if (error) {
      console.error('❌ Error fetching Jersey Co-op:', error);
      return;
    }

    if (!coop) {
      console.error('❌ Jersey Co-op not found');
      return;
    }

    console.log('📋 Jersey Co-op business data:');
    console.log(`   ID: ${coop.id}`);
    console.log(`   Name: ${coop.name}`);
    console.log(`   Slug: ${coop.slug}`);
    console.log(`   Page Layout: ${coop.page_layout}`);
    console.log(`   Page Layout Type: ${typeof coop.page_layout}`);

    // Test the condition that should trigger redirect
    const shouldRedirect = coop.page_layout === 'aisle';
    console.log(`\n🔄 Redirect Logic:`);
    console.log(`   Should redirect: ${shouldRedirect}`);
    console.log(`   Condition: page_layout === 'aisle'`);
    console.log(`   Actual: '${coop.page_layout}' === 'aisle'`);

    // Also check Jersey Wings for comparison
    const { data: wings, error: wingsError } = await supabase
      .from('businesses')
      .select('id, name, slug, page_layout')
      .eq('slug', 'jersey-wings')
      .single();

    if (!wingsError && wings) {
      console.log(`\n📋 Jersey Wings for comparison:`);
      console.log(`   Page Layout: ${wings.page_layout || 'null'}`);
      console.log(`   Should redirect: ${wings.page_layout === 'aisle'}`);
    }

    // Test the generic business service
    console.log(`\n🧪 Testing getBusinessById function...`);
    
    // Simulate the API call that the frontend makes
    const testUrl = `${supabaseUrl}/rest/v1/businesses?select=*&slug=eq.jersey-coop`;
    const response = await fetch(testUrl, {
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data && data.length > 0) {
        const businessData = data[0];
        console.log(`   API Response page_layout: ${businessData.page_layout}`);
        console.log(`   API Response type: ${typeof businessData.page_layout}`);
      } else {
        console.log(`   ❌ No data returned from API`);
      }
    } else {
      console.log(`   ❌ API call failed: ${response.status}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

debugCoopRedirect();
