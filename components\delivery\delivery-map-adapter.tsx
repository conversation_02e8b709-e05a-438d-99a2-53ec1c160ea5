"use client"

import MultiBusinessDeliveryMap from './multi-business-delivery-map'

interface DeliveryMapAdapterProps {
  customerCoordinates: [number, number] | null
  businessCoordinates: Record<string, [number, number]>
  height?: string
  className?: string
  interactive?: boolean
  showRoutes?: boolean
}

export default function DeliveryMapAdapter({
  customerCoordinates,
  businessCoordinates,
  height = "400px",
  className = "",
  interactive = true,
  showRoutes = true
}: DeliveryMapAdapterProps) {
  // If no customer coordinates, don't render the map
  if (!customerCoordinates || !Array.isArray(customerCoordinates) || customerCoordinates.length !== 2) {
    return (
      <div className="p-4 text-center text-gray-500">
        Invalid customer location coordinates.
      </div>
    )
  }

  // Validate customer coordinates
  const [customerLng, customerLat] = customerCoordinates
  if (isNaN(customerLng) || isNaN(customerLat)) {
    return (
      <div className="p-4 text-center text-gray-500">
        Invalid customer location coordinates.
      </div>
    )
  }

  // Convert businessCoordinates object to array of BusinessLocation objects
  // and validate each business coordinate
  const businesses = Object.entries(businessCoordinates || {})
    .filter(([_, coordinates]) => {
      return coordinates &&
             Array.isArray(coordinates) &&
             coordinates.length === 2 &&
             !isNaN(coordinates[0]) &&
             !isNaN(coordinates[1])
    })
    .map(([id, coordinates]) => ({
      id,
      name: id, // Use ID as name since we don't have business names here
      coordinates
    }))

  // Only render the map if we have businesses
  if (businesses.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No business locations available to display on the map.
      </div>
    )
  }

  return (
    <MultiBusinessDeliveryMap
      businesses={businesses}
      customerLng={customerLng}
      customerLat={customerLat}
      height={height}
      className={className}
      interactive={interactive}
      showRoutes={showRoutes}
    />
  )
}
