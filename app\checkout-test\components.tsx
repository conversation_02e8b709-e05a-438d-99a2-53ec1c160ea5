"use client"

import React, { useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { FixedCheckbox } from "@/components/ui/fixed-checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Button } from "@/components/ui/button"

// Isolated Checkbox Component
export function IsolatedCheckbox() {
  const [isChecked, setIsChecked] = useState(false)

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Isolated Checkbox</h3>
      <div className="flex items-center">
        <Checkbox
          id="isolatedCheckbox"
          checked={isChecked}
          onCheckedChange={(checked) => {
            console.log("Isolated checkbox changed:", checked)
            setIsChecked(!!checked)
          }}
        />
        <Label htmlFor="isolatedCheckbox" className="ml-2">
          Test Checkbox
        </Label>
      </div>
      <p className="text-sm mt-2">Value: {isChecked ? "Checked" : "Unchecked"}</p>
    </div>
  )
}

// Checkbox with Conditional Rendering
export function ConditionalCheckbox() {
  const [isChecked, setIsChecked] = useState(false)
  const [showExtra, setShowExtra] = useState(false)

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Conditional Checkbox</h3>
      <div className="flex items-center">
        <Checkbox
          id="conditionalCheckbox"
          checked={isChecked}
          onCheckedChange={(checked) => {
            console.log("Conditional checkbox changed:", checked)
            setIsChecked(!!checked)
            setShowExtra(!!checked)
          }}
        />
        <Label htmlFor="conditionalCheckbox" className="ml-2">
          Show Extra Content
        </Label>
      </div>

      {showExtra && (
        <div className="mt-2 p-2 bg-gray-100 rounded">
          <p className="text-sm">Extra content that appears when checked</p>
        </div>
      )}
    </div>
  )
}

// Radio Group Component
export function TestRadioGroup() {
  const [value, setValue] = useState("option1")

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Radio Group Test</h3>
      <RadioGroup value={value} onValueChange={setValue}>
        <div className="flex items-center space-x-2 mb-2">
          <RadioGroupItem value="option1" id="option1" />
          <Label htmlFor="option1">Option 1</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="option2" id="option2" />
          <Label htmlFor="option2">Option 2</Label>
        </div>
      </RadioGroup>
      <p className="text-sm mt-2">Selected: {value}</p>
    </div>
  )
}

// Checkbox with useEffect
export function CheckboxWithEffect() {
  const [isChecked, setIsChecked] = useState(false)
  const [effectCount, setEffectCount] = useState(0)

  // This useEffect will run whenever isChecked changes
  React.useEffect(() => {
    console.log("Effect running due to checkbox change")
    setEffectCount(prev => prev + 1)
  }, [isChecked])

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Checkbox with Effect</h3>
      <div className="flex items-center">
        <Checkbox
          id="effectCheckbox"
          checked={isChecked}
          onCheckedChange={(checked) => {
            console.log("Effect checkbox changed:", checked)
            setIsChecked(!!checked)
          }}
        />
        <Label htmlFor="effectCheckbox" className="ml-2">
          Trigger Effect
        </Label>
      </div>
      <p className="text-sm mt-2">Effect count: {effectCount}</p>
      <p className="text-xs text-red-500">Note: If this number increases rapidly, there's an infinite loop</p>
    </div>
  )
}

// Checkbox with Ref
export function CheckboxWithRef() {
  const [isChecked, setIsChecked] = useState(false)
  const checkboxRef = React.useRef(null)

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Checkbox with Ref</h3>
      <div className="flex items-center">
        <Checkbox
          id="refCheckbox"
          ref={checkboxRef}
          checked={isChecked}
          onCheckedChange={(checked) => {
            console.log("Ref checkbox changed:", checked)
            setIsChecked(!!checked)
          }}
        />
        <Label htmlFor="refCheckbox" className="ml-2">
          Checkbox with Ref
        </Label>
      </div>
    </div>
  )
}

// Form with multiple checkboxes
export function CheckboxForm() {
  const [options, setOptions] = useState({
    option1: false,
    option2: false,
    option3: false
  })

  const handleOptionChange = (option: string, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      [option]: checked
    }))
  }

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Multiple Checkboxes</h3>

      <div className="space-y-2">
        <div className="flex items-center">
          <Checkbox
            id="option1"
            checked={options.option1}
            onCheckedChange={(checked) => handleOptionChange("option1", !!checked)}
          />
          <Label htmlFor="option1" className="ml-2">Option 1</Label>
        </div>

        <div className="flex items-center">
          <Checkbox
            id="option2"
            checked={options.option2}
            onCheckedChange={(checked) => handleOptionChange("option2", !!checked)}
          />
          <Label htmlFor="option2" className="ml-2">Option 2</Label>
        </div>

        <div className="flex items-center">
          <Checkbox
            id="option3"
            checked={options.option3}
            onCheckedChange={(checked) => handleOptionChange("option3", !!checked)}
          />
          <Label htmlFor="option3" className="ml-2">Option 3</Label>
        </div>
      </div>

      <div className="mt-2">
        <p className="text-sm">Selected options: {Object.entries(options)
          .filter(([_, value]) => value)
          .map(([key]) => key)
          .join(", ") || "None"}</p>
      </div>
    </div>
  )
}

// Native HTML checkbox for comparison
export function NativeCheckbox() {
  const [isChecked, setIsChecked] = useState(false)

  return (
    <div className="p-4 border rounded-md mb-4">
      <h3 className="font-medium mb-2">Native HTML Checkbox</h3>
      <div className="flex items-center">
        <input
          type="checkbox"
          id="nativeCheckbox"
          checked={isChecked}
          onChange={(e) => {
            console.log("Native checkbox changed:", e.target.checked)
            setIsChecked(e.target.checked)
          }}
          className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
        />
        <label htmlFor="nativeCheckbox" className="ml-2">
          Native Checkbox
        </label>
      </div>
      <p className="text-sm mt-2">Value: {isChecked ? "Checked" : "Unchecked"}</p>
    </div>
  )
}

// Problematic Checkbox - Attempts to reproduce the issue in the checkout page
export function ProblematicCheckbox() {
  const [saveAddress, setSaveAddress] = useState(false)
  const [hasSelectedSavedAddress, setHasSelectedSavedAddress] = useState(false)
  const [isAddressFormExpanded, setIsAddressFormExpanded] = useState(true)

  // This useEffect might be causing the infinite loop
  React.useEffect(() => {
    if (saveAddress) {
      console.log("Address will be saved")
    }
  }, [saveAddress])

  return (
    <div className="p-4 border rounded-md mb-4 border-red-200 bg-red-50">
      <h3 className="font-medium mb-2">Problematic Checkbox (Checkout Page Simulation)</h3>

      <div className="flex items-center p-3 bg-emerald-50 rounded-lg border border-emerald-100">
        <Checkbox
          id="saveAddressProblematic"
          checked={saveAddress}
          onCheckedChange={(checked) => {
            console.log("Problematic checkbox changed:", checked)
            setSaveAddress(!!checked)
          }}
          className="text-emerald-600"
        />
        <div className="ml-2">
          <Label htmlFor="saveAddressProblematic" className="cursor-pointer text-gray-800 font-medium">
            Save this address for future orders
          </Label>
          <p className="text-xs text-gray-600 mt-0.5">
            This will be added to your saved addresses for easy selection next time
          </p>
        </div>
      </div>

      <div className="mt-4">
        <p className="text-sm">Current value: {saveAddress ? "Checked" : "Unchecked"}</p>
        <p className="text-xs text-red-600 mt-1">This attempts to reproduce the issue in the checkout page</p>
      </div>
    </div>
  )
}

// Fixed version of the problematic checkbox
export function FixedProblematicCheckbox() {
  const [saveAddress, setSaveAddress] = useState(false)
  const [hasSelectedSavedAddress, setHasSelectedSavedAddress] = useState(false)
  const [isAddressFormExpanded, setIsAddressFormExpanded] = useState(true)

  // This useEffect might be causing the infinite loop
  React.useEffect(() => {
    if (saveAddress) {
      console.log("Address will be saved (fixed version)")
    }
  }, [saveAddress])

  return (
    <div className="p-4 border rounded-md mb-4 border-orange-200 bg-orange-50">
      <h3 className="font-medium mb-2">Fixed Version of Problematic Checkbox</h3>

      <div className="flex items-center p-3 bg-emerald-50 rounded-lg border border-emerald-100">
        <FixedCheckbox
          id="saveAddressFixed"
          checked={saveAddress}
          onCheckedChange={(checked) => {
            console.log("Fixed problematic checkbox changed:", checked)
            setSaveAddress(!!checked)
          }}
          className="text-emerald-600"
        />
        <div className="ml-2">
          <Label htmlFor="saveAddressFixed" className="cursor-pointer text-gray-800 font-medium">
            Save this address for future orders
          </Label>
          <p className="text-xs text-gray-600 mt-0.5">
            This will be added to your saved addresses for easy selection next time
          </p>
        </div>
      </div>

      <div className="mt-4">
        <p className="text-sm">Current value: {saveAddress ? "Checked" : "Unchecked"}</p>
        <p className="text-xs text-orange-600 mt-1">This uses our fixed checkbox implementation</p>
      </div>
    </div>
  )
}

// Fixed Checkbox Component Test
export function FixedCheckboxTest() {
  const [isChecked, setIsChecked] = useState(false)

  return (
    <div className="p-4 border rounded-md mb-4 border-green-200 bg-green-50">
      <h3 className="font-medium mb-2">Fixed Checkbox Implementation</h3>
      <div className="flex items-center">
        <FixedCheckbox
          id="fixedCheckbox"
          checked={isChecked}
          onCheckedChange={(checked) => {
            console.log("Fixed checkbox changed:", checked)
            setIsChecked(!!checked)
          }}
        />
        <Label htmlFor="fixedCheckbox" className="ml-2">
          Fixed Checkbox
        </Label>
      </div>
      <p className="text-sm mt-2">Value: {isChecked ? "Checked" : "Unchecked"}</p>
      <p className="text-xs text-green-600 mt-1">This uses a modified implementation to prevent infinite loops</p>
    </div>
  )
}

// Test component with all the test cases
export function CheckboxTestSuite() {
  return (
    <div className="space-y-4">
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md mb-6">
        <h3 className="font-medium">Testing Instructions</h3>
        <p className="text-sm mt-2">
          1. Open your browser console (F12) to see error messages<br />
          2. Try interacting with each checkbox below<br />
          3. If you see "Maximum update depth exceeded" errors, that component has the issue<br />
          4. The "Fixed Checkbox" implementation should work without errors
        </p>
      </div>

      <ProblematicCheckbox />
      <FixedProblematicCheckbox />
      <FixedCheckboxTest />
      <IsolatedCheckbox />
      <ConditionalCheckbox />
      <TestRadioGroup />
      <CheckboxWithEffect />
      <CheckboxWithRef />
      <CheckboxForm />
      <NativeCheckbox />

      <div className="mt-6">
        <Button onClick={() => window.location.reload()}>
          Reload Page
        </Button>
      </div>
    </div>
  )
}
