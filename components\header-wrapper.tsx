"use client"

import React from "react"
import { usePathname } from "next/navigation"
import dynamic from "next/dynamic"
import Footer from "@/components/footer"

// Import Header with dynamic loading to avoid SSR hydration mismatches
const Header = dynamic(() => import("@/components/header"), {
  ssr: false // Disable SSR for the header to avoid hydration mismatches
})

// This is a client component that handles the conditional header rendering
export default function HeaderWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // Hide header on checkout page, order confirmation page, business-admin pages, admin pages, all driver pages, and 404 page
  const showHeader = !(
    pathname === "/checkout" ||
    pathname === "/not-found" ||
    pathname?.startsWith("/order-confirmation") ||
    pathname?.startsWith("/business-admin") ||
    pathname?.startsWith("/admin") ||
    pathname?.startsWith("/super-admin") ||
    pathname?.startsWith("/driver")
  )

  // Hide footer on driver-mobile pages
  const showFooter = !pathname?.startsWith("/driver-mobile")

  return (
    <div className="flex flex-col min-h-screen w-full">
      {showHeader && <Header key="main-header" />}
      <main className="flex-grow w-full">{children}</main>
      {showFooter && <Footer />}
    </div>
  )
}
