import { NextRequest, NextResponse } from 'next/server'
import { 
  validateDriverOrderAssignment, 
  validateDriverStatusConsistency, 
  syncDriverStatus,
  validateOrderStatusChange 
} from '@/lib/driver-validation'

export async function POST(request: NextRequest) {
  try {
    const { action, driverId, orderId, newStatus } = await request.json()

    switch (action) {
      case 'validate-assignment':
        if (!driverId || !orderId) {
          return NextResponse.json(
            { error: 'driverId and orderId required for assignment validation' },
            { status: 400 }
          )
        }
        const assignmentResult = await validateDriverOrderAssignment(driverId, orderId)
        return NextResponse.json(assignmentResult)

      case 'validate-consistency':
        if (!driverId) {
          return NextResponse.json(
            { error: 'driverId required for consistency validation' },
            { status: 400 }
          )
        }
        const consistencyResult = await validateDriverStatusConsistency(driverId)
        return NextResponse.json(consistencyResult)

      case 'sync-status':
        if (!driverId) {
          return NextResponse.json(
            { error: 'driverId required for status sync' },
            { status: 400 }
          )
        }
        const syncResult = await syncDriverStatus(driverId)
        return NextResponse.json(syncResult)

      case 'validate-status-change':
        if (!orderId || !newStatus) {
          return NextResponse.json(
            { error: 'orderId and newStatus required for status change validation' },
            { status: 400 }
          )
        }
        const statusResult = await validateOrderStatusChange(orderId, newStatus, driverId)
        return NextResponse.json(statusResult)

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: validate-assignment, validate-consistency, sync-status, validate-status-change' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Validation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const driverId = searchParams.get('driverId')

    if (action === 'check-all-drivers') {
      // Run consistency checks on all drivers
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      const { data: drivers, error } = await supabase
        .from('driver_profiles')
        .select('id')
        .eq('is_active', true)

      if (error) {
        return NextResponse.json({ error: 'Failed to fetch drivers' }, { status: 500 })
      }

      const results = await Promise.all(
        drivers.map(async (driver) => {
          const consistency = await validateDriverStatusConsistency(driver.id)
          return {
            driverId: driver.id,
            ...consistency
          }
        })
      )

      return NextResponse.json({
        totalDrivers: drivers.length,
        results: results,
        summary: {
          valid: results.filter(r => r.isValid).length,
          invalid: results.filter(r => !r.isValid).length,
          warnings: results.filter(r => r.warnings.length > 0).length
        }
      })
    }

    if (action === 'sync-all-drivers') {
      // Sync all driver statuses
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      const { data: drivers, error } = await supabase
        .from('driver_profiles')
        .select('id')
        .eq('is_active', true)

      if (error) {
        return NextResponse.json({ error: 'Failed to fetch drivers' }, { status: 500 })
      }

      const results = await Promise.all(
        drivers.map(async (driver) => {
          const syncResult = await syncDriverStatus(driver.id)
          return {
            driverId: driver.id,
            ...syncResult
          }
        })
      )

      return NextResponse.json({
        totalDrivers: drivers.length,
        results: results,
        summary: {
          synced: results.filter(r => r.isValid).length,
          failed: results.filter(r => !r.isValid).length
        }
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Use: check-all-drivers, sync-all-drivers' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Validation GET API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
