'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { OrderDetails } from './types'
import { fetchOrderDetails } from './utils'
import {
  OrderHeader,
  BusinessCard,
  OrderSummary,
  OrderInformation,
  QuickActions,
  ThankYouMessage
} from './components'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { useRealtimeCart } from '@/context/realtime-cart-context'

export default function OrderConfirmationContent() {
  const searchParams = useSearchParams()
  const { setIsProcessingOrder } = useRealtimeCart()
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Reset order processing state when order confirmation loads
  useEffect(() => {
    setIsProcessingOrder(false)
  }, [setIsProcessingOrder])

  useEffect(() => {
    const loadOrderDetails = async () => {
      try {
        setLoading(true)
        setError(null)

        // PHASE 2 STEP 3: Get order identifier from URL params (sessionId, orderNumber, or orderId)
        const sessionIdParam = searchParams?.get('sessionId')
        const orderNumberParam = searchParams?.get('orderNumber')
        const orderIdParam = searchParams?.get('orderId')
        const orderIdentifier = sessionIdParam || orderNumberParam || orderIdParam

        if (!orderIdentifier) {
          setError('No order identifier provided')
          setLoading(false)
          return
        }

        console.log('🔍 ORDER CONFIRMATION: Loading order details for identifier:', orderIdentifier)

        // Use the new fetchOrderDetails function (sessionStorage first, API fallback)
        const orderDetails = await fetchOrderDetails(orderIdentifier)

        if (orderDetails) {
          console.log('✅ ORDER CONFIRMATION: Successfully loaded order details')
          setOrderDetails(orderDetails)
        } else {
          console.log('❌ ORDER CONFIRMATION: Failed to load order details from both sessionStorage and API')
          setError('Failed to load order details')
        }

      } catch (error) {
        console.error('❌ ORDER CONFIRMATION: Error loading order details:', error)
        setError('An error occurred while loading order details')
      } finally {
        setLoading(false)
      }
    }

    loadOrderDetails()
  }, [searchParams])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your order details...</p>
        </div>
      </div>
    )
  }

  if (error || !orderDetails) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error || 'Order details not found'}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Order Header */}
        <OrderHeader businessCount={orderDetails.businesses.length} />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Business Cards */}
            <div className="space-y-6">
              {orderDetails.businesses.map((business, businessIndex) => (
                <BusinessCard
                  key={`business-${business.id}-${businessIndex}`}
                  business={business}
                  businessIndex={businessIndex}
                  orderDetails={orderDetails}
                />
              ))}
            </div>

            {/* Order Summary */}
            <OrderSummary orderDetails={orderDetails} />
          </div>

          {/* Right Column - Order Information & Actions */}
          <div className="space-y-6">
            {/* Order Information */}
            <OrderInformation orderDetails={orderDetails} />

            {/* Quick Actions */}
            <QuickActions orderId={orderDetails.orderId} />

            {/* Thank You Message */}
            <ThankYouMessage />
          </div>
        </div>


      </div>
    </div>
  )
}
