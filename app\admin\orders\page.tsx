'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Loader2, Search } from 'lucide-react'

interface CartItem {
  id: string
  name: string
  quantity: number
  price: number
  product_id: number
  variant_id?: number
  image_url?: string
  business_id: number
}

interface Order {
  // Core fields
  id: number
  created_at: string
  updated_at: string

  // Customer details
  customer_name: string
  customer_email: string
  customer_phone: string
  delivery_address: string
  customer_coordinates: any
  delivery_instructions: string

  // Payment info
  payment_method: string
  payment_status: string

  // Financial info
  total: number
  order_number: string
  subtotal: number
  delivery_fee: number
  service_fee: number

  // Business info
  business_id: number
  business_name: string
  business_type: string
  business_slug: string

  // Order management
  status: string
  preparation_time: number
  estimated_delivery_time: number
  delivery_method: string
  scheduled_time: string
  delivery_type: string

  // System fields
  cart_id: string
  session_id: string
  parish: string
  postcode: string
  user_id: string

  // Driver info
  driver_id: string
  delivery_distance_km: number
  driver_pickup_confirmed: boolean
  business_pickup_confirmed: boolean
  delivery_fulfillment: string

  // Timing fields
  ready_time: string
  offered_at: string
  assigned_at: string
  picked_up_at: string
  out_for_delivery_at: string
  delivered_at: string
  cancelled_at: string
  driver_response_time_seconds: number
  expected_pickup_time: string
  pickup_delay_minutes: number
}

export default function AdminOrdersPage() {
  const [searchResults, setSearchResults] = useState<Order[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [searchLoading, setSearchLoading] = useState(false)
  const [searchError, setSearchError] = useState<string | null>(null)
  const [searchCount, setSearchCount] = useState(0)

  // Get status badge color - matching business-admin style
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'delivered':
        return "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100 hover:text-emerald-800 transition-colors"
      case 'processing':
      case 'preparing':
        return "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:text-blue-800 transition-colors"
      case 'ready':
        return "bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 hover:text-indigo-800 transition-colors"
      case 'out_for_delivery':
      case 'out for delivery':
        return "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:text-purple-800 transition-colors"
      case 'pending':
        return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 hover:text-amber-800 transition-colors"
      case 'confirmed':
        return "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100 hover:text-yellow-800 transition-colors"
      case 'cancelled':
        return "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800 transition-colors"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:text-slate-800 transition-colors"
    }
  }

  // Format status for display - matching business-admin style
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'out_for_delivery':
        return 'Out for Delivery'
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  // Search for orders by multiple criteria
  const searchOrders = async () => {
    if (!searchTerm.trim()) {
      setSearchError('Please enter a search term')
      return
    }

    try {
      setSearchLoading(true)
      setSearchError(null)
      setSearchResults([])
      setSearchCount(0)

      const response = await fetch(`/api/admin/order?search=${encodeURIComponent(searchTerm.trim())}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('No orders found')
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setSearchResults(data.orders || [])
      setSearchCount(data.count || 0)
    } catch (err) {
      console.error('Error searching for orders:', err)
      setSearchError(err instanceof Error ? err.message : 'Failed to search for orders')
      setSearchResults([])
      setSearchCount(0)
    } finally {
      setSearchLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Orders Dashboard</h1>
        <p className="text-gray-600">View order information</p>
      </div>

      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search Orders</CardTitle>
          <p className="text-sm text-gray-600">Search by order number, customer name, address, parish, or postcode</p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Enter search term (e.g., LJ-25-065497, John Smith, St. Helier, JE2 4AB)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && searchOrders()}
              className="flex-1"
            />
            <Button
              onClick={searchOrders}
              disabled={searchLoading}
              className="flex items-center gap-2"
            >
              {searchLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Search
            </Button>
          </div>
          {searchError && (
            <div className="text-red-600 text-sm mt-2">{searchError}</div>
          )}
          {searchCount > 0 && (
            <div className="text-green-600 text-sm mt-2">
              Found {searchCount} order{searchCount !== 1 ? 's' : ''}
            </div>
          )}
        </CardContent>
      </Card>







      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {searchResults.map((order, index) => (
            <div key={order.id} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {order.order_number || `#${order.id}`}
                  </h3>
                  <Badge variant="outline" className={getStatusBadgeClass(order.status)}>
                    {formatStatus(order.status)}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(order.created_at).toLocaleDateString()} at {new Date(order.created_at).toLocaleTimeString()}
                </p>
              </div>

              {/* Content */}
              <div className="p-4 space-y-4">
                {/* Customer Details */}
                <div className="bg-blue-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm text-blue-800 mb-2">Customer Details</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{order.customer_name || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="text-right max-w-[140px] break-words">{order.customer_email || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span>{order.customer_phone || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Address:</span>
                      <span className="text-right max-w-[140px] break-words">{order.delivery_address || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Parish:</span>
                      <span>{order.parish || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Postcode:</span>
                      <span>{order.postcode || 'N/A'}</span>
                    </div>
                  </div>
                </div>

                {/* Business Details */}
                <div className="bg-purple-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm text-purple-800 mb-2">Business Details</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">ID:</span>
                      <span>{order.business_id || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{order.business_name || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span className="capitalize">{order.business_type || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Method:</span>
                      <span className="capitalize">{order.delivery_method || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Prep Time:</span>
                      <span>{order.preparation_time ? `${order.preparation_time} min` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Business Pickup:</span>
                      <Badge variant={order.business_pickup_confirmed ? "default" : "secondary"} className="text-xs">
                        {order.business_pickup_confirmed ? 'Confirmed' : 'Not Confirmed'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Payment & Financial Details */}
                <div className="bg-yellow-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm text-yellow-800 mb-2">Payment & Financial</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Method:</span>
                      <span className="capitalize">{order.payment_method || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge variant={order.payment_status === 'paid' ? "default" : "destructive"} className="text-xs">
                        {order.payment_status || 'N/A'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal:</span>
                      <span>£{order.subtotal?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Delivery Fee:</span>
                      <span>£{order.delivery_fee?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Fee:</span>
                      <span>£{order.service_fee?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="flex justify-between border-t pt-1">
                      <span className="text-gray-600 font-medium">Total:</span>
                      <span className="font-semibold text-green-600">£{order.total?.toFixed(2) || '0.00'}</span>
                    </div>
                  </div>
                </div>

                {/* Driver & Delivery Details */}
                <div className="bg-orange-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm text-orange-800 mb-2">Driver & Delivery</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Driver ID:</span>
                      <span className="text-right max-w-[140px] break-words">{order.driver_id || 'Not Assigned'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Distance:</span>
                      <span>{order.delivery_distance_km ? `${order.delivery_distance_km} km` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Driver Pickup:</span>
                      <Badge variant={order.driver_pickup_confirmed ? "default" : "secondary"} className="text-xs">
                        {order.driver_pickup_confirmed ? 'Confirmed' : 'Not Confirmed'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fulfillment:</span>
                      <span className="capitalize">{order.delivery_fulfillment || 'N/A'}</span>
                    </div>
                  </div>
                </div>

                {/* Order Timeline & Status */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h4 className="font-medium text-sm text-gray-800 mb-2">Order Timeline</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Offered:</span>
                      <span className="text-right max-w-[140px] break-words">{order.offered_at ? new Date(order.offered_at).toLocaleDateString() : 'Not Offered'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Assigned:</span>
                      <span className="text-right max-w-[140px] break-words">{order.assigned_at ? new Date(order.assigned_at).toLocaleDateString() : 'Not Assigned'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Picked Up:</span>
                      <span className="text-right max-w-[140px] break-words">{order.picked_up_at ? new Date(order.picked_up_at).toLocaleDateString() : 'Not Picked Up'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Delivered:</span>
                      <span className="text-right max-w-[140px] break-words">{order.delivered_at ? new Date(order.delivered_at).toLocaleDateString() : 'Not Delivered'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer with Order ID and User ID */}
              <div className="bg-gray-50 px-4 py-2 border-t border-gray-200 rounded-b-lg">
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>Order ID: {order.id}</span>
                  <span>User ID: {order.user_id ? order.user_id.slice(0, 8) + '...' : 'N/A'}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
