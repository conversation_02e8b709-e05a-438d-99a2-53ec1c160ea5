"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Package, Thermometer, Scale, AlertTriangle } from 'lucide-react'

// Copy the interfaces from the API
interface WeightClassData {
  weight_class: string
  weight_class_kg: number | null
  hot_items: number
  cold_items: number
  none_items: number
  undeclared_thermal_items: number
  total_items: number
}

interface OrderAnalysisData {
  order_id: number
  order_number: string
  business_name: string
  total_items: number
  weight_distribution: WeightClassData[]
  thermal_summary: {
    hot_items: number
    cold_items: number
    none_items: number
    undeclared_thermal_items: number
    requires_thermal_bag: boolean
  }
  size_summary: {
    small_items: number
    medium_items: number
    large_items: number
    very_large_items: number
    undeclared_size_items: number
    largest_size: string
  }
}

// Standalone Weight Chart Component (simplified version)
function StandaloneWeightChart({ orderId }: { orderId: number }) {
  const [analysisData, setAnalysisData] = useState<OrderAnalysisData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchOrderAnalysis()
  }, [orderId])

  const fetchOrderAnalysis = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/driver/order-analysis/${orderId}`)
      const result = await response.json()
      
      if (response.ok && result.success) {
        setAnalysisData(result.data)
      } else {
        setError(result.error || 'Failed to load order analysis')
      }
    } catch (err) {
      console.error('Error fetching order analysis:', err)
      setError('Failed to load order analysis')
    } finally {
      setLoading(false)
    }
  }

  const renderWeightBar = (weightData: WeightClassData, maxItems: number) => {
    const { weight_class, hot_items, cold_items, none_items, undeclared_thermal_items, total_items } = weightData
    const barWidth = maxItems > 0 ? (total_items / maxItems) * 100 : 0

    return (
      <div key={weight_class} className="flex items-center space-x-3 mb-3">
        {/* Weight class label */}
        <div className="w-24 text-sm font-medium text-gray-700 text-right">
          {weight_class === 'undeclared' ? (
            <span className="text-amber-600 flex items-center justify-end">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Undeclared
            </span>
          ) : (
            weight_class
          )}
        </div>

        {/* Bar container */}
        <div className="flex-1 relative">
          <div className="h-8 bg-gray-100 rounded-md overflow-hidden relative border">
            {/* Stacked bar segments */}
            <div className="flex h-full" style={{ width: `${barWidth}%` }}>
              {/* Hot items */}
              {hot_items > 0 && (
                <div 
                  className="bg-red-500 h-full"
                  style={{ width: `${(hot_items / total_items) * 100}%` }}
                  title={`${hot_items} hot items`}
                />
              )}
              {/* Cold items */}
              {cold_items > 0 && (
                <div 
                  className="bg-blue-500 h-full"
                  style={{ width: `${(cold_items / total_items) * 100}%` }}
                  title={`${cold_items} cold items`}
                />
              )}
              {/* None thermal items */}
              {none_items > 0 && (
                <div 
                  className="bg-green-500 h-full"
                  style={{ width: `${(none_items / total_items) * 100}%` }}
                  title={`${none_items} no thermal items`}
                />
              )}
              {/* Undeclared thermal items */}
              {undeclared_thermal_items > 0 && (
                <div 
                  className="bg-gray-800 h-full"
                  style={{ width: `${(undeclared_thermal_items / total_items) * 100}%` }}
                  title={`${undeclared_thermal_items} undeclared thermal items`}
                />
              )}
            </div>
          </div>
        </div>

        {/* Item count */}
        <div className="w-12 text-sm font-bold text-gray-900 text-center">
          {total_items}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Order Analysis...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
                <div className="h-8 flex-1 bg-gray-200 rounded"></div>
                <div className="h-4 w-6 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !analysisData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-medium">Error Loading Chart</p>
            <p className="text-sm text-gray-600 mt-1">{error || 'No data available'}</p>
            <button 
              onClick={fetchOrderAnalysis}
              className="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const { weight_distribution, thermal_summary, total_items } = analysisData
  const maxItems = Math.max(...weight_distribution.map(w => w.total_items))

  return (
    <Card className="border-2 border-blue-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Scale className="h-5 w-5 text-blue-600" />
          <span>Order Weight Analysis</span>
        </CardTitle>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span className="font-medium">{total_items} total items</span>
          <span>Order #{analysisData.order_number}</span>
          <span>{analysisData.business_name}</span>
          {thermal_summary.requires_thermal_bag && (
            <Badge variant="outline" className="text-xs">
              <Thermometer className="h-3 w-3 mr-1" />
              Thermal Required
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Weight distribution chart */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-700">Weight Distribution by Thermal Requirement</h4>
            <div className="text-xs text-gray-500">← Items Count</div>
          </div>
          
          <div className="space-y-2">
            {weight_distribution.map(weightData => renderWeightBar(weightData, maxItems))}
          </div>
        </div>

        {/* Legend */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Thermal Requirements Legend</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm text-gray-600">Hot Thermal ({thermal_summary.hot_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Cold Thermal ({thermal_summary.cold_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-600">No Thermal ({thermal_summary.none_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-800 rounded"></div>
              <span className="text-sm text-gray-600">Undeclared ({thermal_summary.undeclared_thermal_items})</span>
            </div>
          </div>
        </div>

        {/* Warnings */}
        {(weight_distribution.some(w => w.weight_class === 'undeclared') || thermal_summary.undeclared_thermal_items > 0) && (
          <div className="mt-4 space-y-3">
            {weight_distribution.some(w => w.weight_class === 'undeclared') && (
              <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-amber-800">Undeclared Weight Items</p>
                    <p className="text-xs text-amber-700 mt-1">
                      Some items don't have specified weights. Be prepared for varying item sizes.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {thermal_summary.undeclared_thermal_items > 0 && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-gray-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Undeclared Thermal Requirements</p>
                    <p className="text-xs text-gray-700 mt-1">
                      {thermal_summary.undeclared_thermal_items} items don't have specified thermal requirements. Check with business if thermal bags are needed.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default function TestChartStandalonePage() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-900">
              📊 Standalone Weight Chart Test
            </CardTitle>
            <p className="text-gray-600">
              Direct test of the weight chart component with API data
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                This page tests the weight chart component directly without driver authentication.
              </p>
            </div>
          </CardContent>
        </Card>
        
        {/* Test Chart */}
        <StandaloneWeightChart orderId={550} />
        
        <Card>
          <CardHeader>
            <CardTitle>Expected Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>Total Items:</strong> 9 items</p>
              <p><strong>Weight Distribution:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• <span className="text-amber-600">Undeclared:</span> 2 items (mystery boxes) - black segments</li>
                <li>• <span className="text-blue-600">0kg:</span> 1 item (ice cream) - blue segment</li>
                <li>• <span className="text-red-600">1kg:</span> 2 items (pizzas) - red segments</li>
                <li>• <span className="text-green-600">2kg:</span> 3 items (soft drinks) - green segments</li>
                <li>• <span className="text-red-600">4kg:</span> 1 item (catering tray) - red segment</li>
              </ul>
              <p><strong>Thermal Summary:</strong> 3 hot, 1 cold, 3 none, 2 undeclared</p>
              <p><strong>Warnings:</strong> Should show warnings for undeclared weight and thermal items</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
