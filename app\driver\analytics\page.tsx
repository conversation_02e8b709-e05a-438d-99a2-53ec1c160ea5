"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { DriverAnalyticsDashboard } from "@/components/driver-analytics-dashboard"

export default function DriverAnalyticsPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [driverStatus, setDriverStatus] = useState<{
    isDriver: boolean
    isVerified: boolean
    isActive: boolean
  } | null>(null)

  useEffect(() => {
    const checkDriverStatus = async () => {
      if (!user) {
        router.push('/login?redirectTo=/driver/analytics')
        return
      }

      try {
        const response = await fetch(`/api/driver/verify?userId=${user.id}`)
        const status = await response.json()
        
        setDriverStatus(status)
        
        if (!status.isDriver) {
          router.push('/partners/riders/apply')
          return
        }
        
        if (!status.isVerified) {
          router.push('/driver/pending-verification')
          return
        }
      } catch (error) {
        console.error('Error checking driver status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkDriverStatus()
  }, [user, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (!driverStatus?.isDriver || !driverStatus?.isVerified) {
    return null // Will redirect
  }

  return <DriverAnalyticsDashboard />
}
