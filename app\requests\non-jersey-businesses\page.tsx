"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { 
  Globe, 
  TrendingUp, 
  Users, 
  Heart, 
  ArrowLeft, 
  Plus, 
  Vote,
  MapPin,
  Building2
} from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"

interface NonJerseyBusinessRequest {
  id: number
  business_name: string
  business_type: string
  suggested_address: string
  customer_name: string
  customer_email: string
  vote_count: number
  status: string
  created_at: string
  notes?: string
  location: string
}

export default function NonJerseyBusinessRequestsPage() {
  const [requests, setRequests] = useState<NonJerseyBusinessRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showRequestDialog, setShowRequestDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Form state
  const [businessName, setBusinessName] = useState("")
  const [businessType, setBusinessType] = useState("")
  const [suggestedAddress, setSuggestedAddress] = useState("")
  const [customerName, setCustomerName] = useState("")
  const [customerEmail, setCustomerEmail] = useState("")
  const [notes, setNotes] = useState("")

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      // For now, we'll use the same API but filter for non-jersey businesses
      const response = await fetch('/api/business-requests?limit=20')
      if (response.ok) {
        const data = await response.json()
        // Filter for non-jersey businesses
        const nonJerseyRequests = (data.requests || []).filter((req: NonJerseyBusinessRequest) => req.location === 'non_jersey')
        setRequests(nonJerseyRequests)
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitRequest = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/business-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName,
          businessType,
          suggestedAddress,
          customerName,
          customerEmail,
          notes,
          location: 'non_jersey' // Specify this is a non-Jersey business
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "International business request submitted!",
          description: data.message,
        })
        setShowRequestDialog(false)
        resetForm()
        fetchRequests() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to submit request",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVote = async (request: NonJerseyBusinessRequest) => {
    if (!customerName || !customerEmail) {
      toast({
        title: "Login required",
        description: "Please fill in your name and email to vote",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch('/api/business-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: request.business_name,
          businessType: request.business_type,
          suggestedAddress: request.suggested_address,
          customerName,
          customerEmail,
          notes: 'Vote for existing business',
          location: 'non_jersey'
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Vote added!",
          description: data.message,
        })
        fetchRequests() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to vote",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to vote. Please try again.",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setBusinessName("")
    setBusinessType("")
    setSuggestedAddress("")
    setCustomerName("")
    setCustomerEmail("")
    setNotes("")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const totalVotes = requests.reduce((sum, r) => sum + r.vote_count, 0)
  const topVotes = requests.length > 0 ? Math.max(...requests.map(r => r.vote_count)) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white">
        <div className="container mx-auto px-4 py-12">
          <div className="flex items-center gap-4 mb-6">
            <Link href="/requests">
              <Button variant="outline" size="sm" className="bg-white text-indigo-600 hover:bg-indigo-50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Requests
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Globe className="h-12 w-12" />
              <h1 className="text-4xl font-bold">International Chain Requests</h1>
            </div>
            <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
              Request popular international chains and brands that aren't yet available in Jersey
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{requests.length}</div>
                <div className="text-indigo-200">Chains Requested</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{totalVotes}</div>
                <div className="text-indigo-200">Total Votes</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{topVotes}</div>
                <div className="text-indigo-200">Top Votes</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Info Banner */}
        <Card className="mb-8 border-indigo-200 bg-indigo-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Globe className="h-6 w-6 text-indigo-600 mt-1" />
              <div>
                <h3 className="font-semibold text-indigo-900 mb-2">About International Chain Requests</h3>
                <p className="text-indigo-700 text-sm">
                  These are requests for popular international chains and brands that don't currently operate in Jersey. 
                  High-demand requests may encourage these businesses to consider expanding to the island, 
                  or help Loop create placeholder pages to gauge local interest.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Button */}
        <div className="text-center mb-12">
          <Button 
            onClick={() => setShowRequestDialog(true)}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 text-lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            Request International Chain
          </Button>
        </div>

        {/* Top Requests */}
        {requests.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-indigo-600" />
                Most Requested International Chains
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requests
                  .sort((a, b) => b.vote_count - a.vote_count)
                  .slice(0, 5)
                  .map((request, index) => (
                    <div key={request.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                          index === 0 ? 'bg-indigo-400 text-indigo-900' :
                          index < 3 ? 'bg-indigo-500 text-white' : 'bg-gray-300 text-gray-700'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-lg">{request.business_name}</p>
                          <p className="text-sm text-gray-600">{request.business_type}</p>
                        </div>
                        <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                          <Globe className="h-3 w-3 mr-1" />
                          International
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="flex items-center gap-1">
                            <Vote className="h-4 w-4 text-gray-500" />
                            <span className="font-bold text-indigo-600 text-lg">{request.vote_count}</span>
                          </div>
                          <div className="text-xs text-gray-500">votes</div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVote(request)}
                          className="text-indigo-600 border-indigo-200 hover:bg-indigo-50"
                        >
                          Vote
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* All Requests */}
        <Card>
          <CardHeader>
            <CardTitle>All International Chain Requests</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading international chain requests...</p>
              </div>
            ) : requests.length === 0 ? (
              <div className="text-center py-12">
                <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No international chain requests yet</h3>
                <p className="text-gray-500 mb-6">Be the first to suggest an international brand for Jersey!</p>
                <Button 
                  onClick={() => setShowRequestDialog(true)}
                  className="bg-indigo-600 hover:bg-indigo-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Request Chain
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {requests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{request.business_name}</h3>
                          <Badge variant="outline">{request.business_type}</Badge>
                          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                            <Globe className="h-3 w-3 mr-1" />
                            International
                          </Badge>
                        </div>
                        {request.suggested_address && (
                          <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                            <MapPin className="h-4 w-4" />
                            {request.suggested_address}
                          </div>
                        )}
                        {request.notes && (
                          <p className="text-gray-600 mb-3">{request.notes}</p>
                        )}
                        <div className="text-sm text-gray-500">
                          Requested by {request.customer_name} on {formatDate(request.created_at)}
                        </div>
                      </div>
                      <div className="text-right ml-6">
                        <div className="flex items-center gap-2 mb-3">
                          <Vote className="h-5 w-5 text-gray-500" />
                          <span className="text-2xl font-bold text-indigo-600">{request.vote_count}</span>
                          <span className="text-sm text-gray-500">
                            {request.vote_count === 1 ? 'vote' : 'votes'}
                          </span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVote(request)}
                          className="text-indigo-600 border-indigo-200 hover:bg-indigo-50"
                        >
                          Vote
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Request Dialog */}
      <Dialog open={showRequestDialog} onOpenChange={setShowRequestDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Request International Chain</DialogTitle>
            <DialogDescription>
              Suggest an international chain or brand that should come to Jersey
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRequest} className="space-y-4">
            <div>
              <Label htmlFor="businessName">Business Name *</Label>
              <Input
                id="businessName"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                placeholder="e.g., McDonald's, Starbucks, H&M"
                required
              />
            </div>

            <div>
              <Label htmlFor="businessType">Business Type</Label>
              <Select value={businessType} onValueChange={setBusinessType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select business type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Fast Food">Fast Food</SelectItem>
                  <SelectItem value="Coffee Shop">Coffee Shop</SelectItem>
                  <SelectItem value="Restaurant">Restaurant</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                  <SelectItem value="Supermarket">Supermarket</SelectItem>
                  <SelectItem value="Fashion">Fashion</SelectItem>
                  <SelectItem value="Electronics">Electronics</SelectItem>
                  <SelectItem value="Home & Garden">Home & Garden</SelectItem>
                  <SelectItem value="Health & Beauty">Health & Beauty</SelectItem>
                  <SelectItem value="Entertainment">Entertainment</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="suggestedAddress">Suggested Location in Jersey</Label>
              <Input
                id="suggestedAddress"
                value={suggestedAddress}
                onChange={(e) => setSuggestedAddress(e.target.value)}
                placeholder="e.g., King Street, St Helier"
              />
            </div>

            <div>
              <Label htmlFor="customerName">Your Name *</Label>
              <Input
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Your full name"
                required
              />
            </div>

            <div>
              <Label htmlFor="customerEmail">Your Email *</Label>
              <Input
                id="customerEmail"
                type="email"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="notes">Why would this business be valuable in Jersey?</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Explain why this international chain would be a great addition to Jersey..."
                rows={3}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowRequestDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-indigo-600 hover:bg-indigo-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
