import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    // Check if the environment variables are set
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    return NextResponse.json({
      supabaseUrl: supabaseUrl ? "Set" : "Not set",
      supabaseServiceRoleKey: supabaseServiceRoleKey ? "Set" : "Not set",
      supabaseUrlLength: supabaseUrl?.length || 0,
      supabaseServiceRoleKeyLength: supabaseServiceRoleKey?.length || 0
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
