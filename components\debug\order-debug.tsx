"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, RefreshCcw } from "lucide-react"

interface OrderDebugProps {
  error?: string
  orderId?: string
  orderIds?: string[]
  isMultiBusiness?: boolean
}

export function OrderDebug({ error, orderId, orderIds, isMultiBusiness }: OrderDebugProps) {
  // Auto-expand the debug panel if there's an error
  const [isExpanded, setIsExpanded] = useState(!!error)
  const [checkoutData, setCheckoutData] = useState<any>(null)
  const [dbData, setDbData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("checkout")

  // Load checkout data and fetch database data when component mounts
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    // Load checkout data from session storage
    try {
      // Try to get the last order details from session storage
      const orderDetails = sessionStorage.getItem('orderDetails')
      if (orderDetails) {
        console.log('Found order details in session storage')
        const parsedDetails = JSON.parse(orderDetails)
        setCheckoutData(parsedDetails)
        console.log('Parsed order details:', parsedDetails)
      } else {
        console.log('No order details in session storage, checking cart backup')
        // If no order details, try to get the cart backup
        const cartBackup = localStorage.getItem('loopJerseyCartBackup')
        if (cartBackup) {
          console.log('Found cart backup in local storage')
          const parsedCart = JSON.parse(cartBackup)

          // Try to reconstruct order details from cart
          const reconstructedDetails = {
            cart: parsedCart,
            businesses: extractBusinessesFromCart(parsedCart),
            items: extractItemsFromCart(parsedCart)
          }

          setCheckoutData(reconstructedDetails)
          console.log('Reconstructed order details from cart backup:', reconstructedDetails)
        } else {
          console.log('No cart backup, checking current cart')
          // Last resort, try to get the current cart
          const currentCart = localStorage.getItem('loopJerseyCart')
          if (currentCart) {
            console.log('Found current cart in local storage')
            const parsedCart = JSON.parse(currentCart)

            // Try to reconstruct order details from cart
            const reconstructedDetails = {
              cart: parsedCart,
              businesses: extractBusinessesFromCart(parsedCart),
              items: extractItemsFromCart(parsedCart)
            }

            setCheckoutData(reconstructedDetails)
            console.log('Reconstructed order details from current cart:', reconstructedDetails)
          } else {
            console.log('No cart data found in local storage')
          }
        }
      }
    } catch (error) {
      console.error('Error loading checkout data:', error)
    }

    // Automatically fetch database data when component mounts
    fetchOrderData()
  }, [])

  // Helper function to extract businesses from cart
  const extractBusinessesFromCart = (cart: any) => {
    if (!cart || !Array.isArray(cart)) return []

    // Group items by business
    const businessMap = new Map()

    cart.forEach(item => {
      const businessId = item.businessId
      if (!businessId) return

      if (!businessMap.has(businessId)) {
        businessMap.set(businessId, {
          business_id: businessId,
          business_name: item.businessName || 'Unknown Business',
          business_type: item.businessType || 'restaurant',
          items: [],
          subtotal: 0
        })
      }

      const business = businessMap.get(businessId)
      business.items.push(item)
      business.subtotal += (item.price || 0) * (item.quantity || 1)
    })

    return Array.from(businessMap.values())
  }

  // Helper function to extract items from cart
  const extractItemsFromCart = (cart: any) => {
    if (!cart || !Array.isArray(cart)) return []
    return cart.map(item => ({
      ...item,
      id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.quantity,
      businessId: item.businessId
    }))
  }

  // Function to fetch the latest order data from the database
  const fetchOrderData = async () => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    setIsLoading(true)
    try {
      // Check if we have multiple order IDs (multi-business order)
      if (orderIds && orderIds.length > 0) {
        console.log(`Fetching data for multi-business order with ${orderIds.length} orders`);

        // Fetch data for the first order ID
        const firstOrderId = orderIds[0];
        console.log(`Fetching data for first order ID: ${firstOrderId}`);

        const response = await fetch(`/api/debug/order-data?orderId=${firstOrderId}`);

        // Get the response text first
        const responseText = await response.text();
        console.log(`API response: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);

        // Try to parse the response as JSON
        let data;
        try {
          data = JSON.parse(responseText);
        } catch (parseError) {
          console.error('Failed to parse API response as JSON:', parseError);
          throw new Error(`Failed to parse API response: ${responseText.substring(0, 100)}...`);
        }

        // Check if the response contains an error
        if (data.error) {
          console.error('API returned an error:', data.error);
          throw new Error(`API error: ${data.error}`);
        }

        // Add multi-business flag to the data
        data.isMultiBusiness = true;
        data.allOrderIds = orderIds;

        // Set the data
        setDbData(data);

        return;
      }

      // Single order case
      // Get the order ID from props or URL
      let orderIdToUse = orderId;

      // If no order ID in props, try to get it from the URL
      if (!orderIdToUse && typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        orderIdToUse = urlParams.get('orderId');
      }

      if (!orderIdToUse) {
        console.log('No order ID found in props or URL, using checkout data only');
        // Instead of returning an error, just set empty database data
        setDbData({
          order: null,
          orderItems: [],
          orderBusinesses: [],
          debug: {
            error: 'No order ID found in props or URL',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV
          }
        });
        setIsLoading(false);
        return;
      }

      console.log(`Fetching order data for order ID: ${orderIdToUse}`);

      // Fetch order data from the API
      const response = await fetch(`/api/debug/order-data?orderId=${orderIdToUse}`);

      // Get the response text first
      const responseText = await response.text();
      console.log(`API response: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse API response as JSON:', parseError);
        throw new Error(`Failed to parse API response: ${responseText.substring(0, 100)}...`);
      }

      // Check if the response contains an error
      if (data.error) {
        console.error('API returned an error:', data.error);
        throw new Error(`API error: ${data.error}`);
      }

      // Check if we have valid data
      if (!data.order && !data.orderBusinesses && !data.orderItems) {
        console.warn('API returned empty data:', data);
      }

      // Set the data even if it's empty or has an error, so we can display it
      setDbData(data);
    } catch (error) {
      console.error('Error fetching order data:', error);
    } finally {
      setIsLoading(false);
    }
  }

  // Format JSON for display
  const formatJson = (data: any) => {
    return JSON.stringify(data, null, 2)
  }

  // Render a comparison table between checkout data and database data
  const renderComparisonTable = () => {
    if (!checkoutData || !dbData) {
      return (
        <div className="text-center p-4 text-gray-500">
          {!checkoutData && !dbData ? (
            "No data available for comparison"
          ) : !checkoutData ? (
            "Checkout data not available"
          ) : (
            "Database data not available. Click 'Refresh Data' to fetch."
          )}
        </div>
      )
    }

    // Extract business data for comparison
    // Ensure businesses is always an array
    let checkoutBusinesses = [];
    if (checkoutData.businesses) {
      // If it's already an array, use it
      if (Array.isArray(checkoutData.businesses)) {
        checkoutBusinesses = checkoutData.businesses;
      } else if (typeof checkoutData.businesses === 'object') {
        // If it's an object (like itemsByBusiness), convert to array
        checkoutBusinesses = Object.values(checkoutData.businesses);
      }
    }

    const dbBusinesses = dbData.orderBusinesses || []

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Order Comparison</h3>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/3">Field</TableHead>
                <TableHead className="w-1/3">Checkout Data</TableHead>
                <TableHead className="w-1/3">Database Data</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">Order ID</TableCell>
                <TableCell>{checkoutData.orderId || 'N/A'}</TableCell>
                <TableCell>{dbData.order?.id || 'N/A'}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Customer Name</TableCell>
                <TableCell>{checkoutData.customerName || 'N/A'}</TableCell>
                <TableCell>{dbData.order?.customer_name || 'N/A'}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Customer Email</TableCell>
                <TableCell>{checkoutData.customerEmail || 'N/A'}</TableCell>
                <TableCell>{dbData.order?.customer_email || 'N/A'}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Total</TableCell>
                <TableCell>£{checkoutData.total?.toFixed(2) || 'N/A'}</TableCell>
                <TableCell>£{dbData.order?.total?.toFixed(2) || 'N/A'}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Payment Method</TableCell>
                <TableCell>{checkoutData.paymentMethod || 'N/A'}</TableCell>
                <TableCell>{dbData.order?.payment_method || 'N/A'}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Status</TableCell>
                <TableCell>N/A (Not created)</TableCell>
                <TableCell>{dbData.order?.status || 'N/A'}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <h3 className="text-lg font-medium mt-6">Businesses Comparison</h3>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/4">Business</TableHead>
                <TableHead className="w-3/8">Checkout Data</TableHead>
                <TableHead className="w-3/8">Database Data</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {checkoutBusinesses.map((business: any, index: number) => {
                // Find matching business in DB data
                const dbBusiness = dbBusinesses.find((b: any) =>
                  b.business_id === business.business_id ||
                  b.business_name === business.business_name
                )

                return (
                  <TableRow key={index}>
                    <TableCell className="font-medium">Business {index + 1}</TableCell>
                    <TableCell>
                      <div className="text-xs whitespace-pre-wrap">
                        <strong>ID:</strong> {business.business_id || 'N/A'}<br />
                        <strong>Name:</strong> {business.business_name || 'N/A'}<br />
                        <strong>Type:</strong> {business.business_type || 'N/A'}<br />
                        <strong>Subtotal:</strong> £{business.subtotal?.toFixed(2) || 'N/A'}<br />
                        <strong>Items:</strong> {business.items?.length || 0}
                      </div>
                    </TableCell>
                    <TableCell>
                      {dbBusiness ? (
                        <div className="text-xs whitespace-pre-wrap">
                          <strong>ID:</strong> {dbBusiness.business_id || 'N/A'}<br />
                          <strong>Name:</strong> {dbBusiness.business_name || 'N/A'}<br />
                          <strong>Type:</strong> {dbBusiness.business_type || 'N/A'}<br />
                          <strong>Subtotal:</strong> £{dbBusiness.subtotal?.toFixed(2) || 'N/A'}<br />
                          <strong>Status:</strong> {dbBusiness.status || 'N/A'}
                        </div>
                      ) : (
                        <span className="text-red-500">Not found in database</span>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}

              {/* Show any extra businesses in the database that weren't in checkout */}
              {dbBusinesses.filter((dbBusiness: any) =>
                !checkoutBusinesses.some((b: any) =>
                  b.business_id === dbBusiness.business_id ||
                  b.business_name === dbBusiness.business_name
                )
              ).map((extraBusiness: any, index: number) => (
                <TableRow key={`extra-${index}`}>
                  <TableCell className="font-medium text-amber-600">
                    Extra Business {index + 1}
                  </TableCell>
                  <TableCell>
                    <span className="text-amber-600">Not in checkout data</span>
                  </TableCell>
                  <TableCell>
                    <div className="text-xs whitespace-pre-wrap">
                      <strong>ID:</strong> {extraBusiness.business_id || 'N/A'}<br />
                      <strong>Name:</strong> {extraBusiness.business_name || 'N/A'}<br />
                      <strong>Type:</strong> {extraBusiness.business_type || 'N/A'}<br />
                      <strong>Subtotal:</strong> £{extraBusiness.subtotal?.toFixed(2) || 'N/A'}<br />
                      <strong>Status:</strong> {extraBusiness.status || 'N/A'}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <h3 className="text-lg font-medium mt-6">Items Comparison</h3>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Checkout Data</TableHead>
                <TableHead>Database Data</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(checkoutData.items || []).map((item: any, index: number) => {
                // Find matching item in DB data
                const dbItem = (dbData.orderItems || []).find((i: any) =>
                  i.product_id === item.id || i.name === item.name
                )

                return (
                  <TableRow key={index}>
                    <TableCell className="font-medium">Item {index + 1}</TableCell>
                    <TableCell>
                      <div className="text-xs whitespace-pre-wrap">
                        <strong>ID:</strong> {item.id || 'N/A'}<br />
                        <strong>Name:</strong> {item.name || 'N/A'}<br />
                        <strong>Price:</strong> £{item.price?.toFixed(2) || 'N/A'}<br />
                        <strong>Quantity:</strong> {item.quantity || 'N/A'}<br />
                        <strong>Business ID:</strong> {item.businessId || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      {dbItem ? (
                        <div className="text-xs whitespace-pre-wrap">
                          <strong>ID:</strong> {dbItem.id || 'N/A'}<br />
                          <strong>Product ID:</strong> {dbItem.product_id || 'N/A'}<br />
                          <strong>Name:</strong> {dbItem.name || 'N/A'}<br />
                          <strong>Price:</strong> £{dbItem.price?.toFixed(2) || 'N/A'}<br />
                          <strong>Quantity:</strong> {dbItem.quantity || 'N/A'}<br />
                          <strong>Business ID:</strong> {dbItem.business_id || 'N/A'}
                        </div>
                      ) : (
                        <span className="text-red-500">Not found in database</span>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  return (
    <Collapsible
      open={isExpanded}
      onOpenChange={setIsExpanded}
      className="w-full border rounded-md mt-8 bg-gray-50"
    >
      <div className="flex items-center justify-between px-4 py-2 border-b">
        <h2 className="text-lg font-semibold text-gray-700">Order Debug Information</h2>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm">
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                Show Details
              </>
            )}
          </Button>
        </CollapsibleTrigger>
      </div>

      <CollapsibleContent>
        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Show API request details */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
            <h3 className="font-semibold mb-2">API Request Details</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="font-medium">Order ID:</div>
              <div>{orderId || 'Not provided'}</div>

              <div className="font-medium">Multi-Business:</div>
              <div>{isMultiBusiness ? 'Yes' : 'No'}</div>

              <div className="font-medium">Order IDs:</div>
              <div>{orderIds && orderIds.length > 0 ? orderIds.join(', ') : 'None'}</div>

              <div className="font-medium">URL:</div>
              <div className="break-all">{typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
            </div>
          </div>

          <div className="flex justify-between mb-4">
            <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList>
                <TabsTrigger value="comparison">Comparison</TabsTrigger>
                <TabsTrigger value="checkout">Checkout Data</TabsTrigger>
                <TabsTrigger value="database">Database Data</TabsTrigger>
              </TabsList>

              <div className="mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchOrderData}
                  disabled={isLoading}
                  className="mb-4"
                >
                  <RefreshCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  {isLoading ? 'Loading...' : 'Refresh Data'}
                </Button>
              </div>

              <TabsContent value="comparison" className="mt-2">
                {renderComparisonTable()}
              </TabsContent>

              <TabsContent value="checkout" className="mt-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Checkout Data</CardTitle>
                    <CardDescription>
                      Data from the checkout page that was used to create the order
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {checkoutData ? (
                      <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-xs max-h-96">
                        {formatJson(checkoutData)}
                      </pre>
                    ) : (
                      <div className="text-center p-4 text-gray-500">
                        No checkout data available
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="database" className="mt-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Database Data</CardTitle>
                    <CardDescription>
                      Data that was actually stored in the database
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {dbData ? (
                      <div className="space-y-4">
                        {/* Debug information */}
                        {dbData.debug && (
                          <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-4">
                            <h3 className="text-sm font-medium text-blue-800 mb-2">Debug Information</h3>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div className="font-medium">Requested Order ID:</div>
                              <div>{dbData.debug.requestedOrderId || 'N/A'}</div>

                              <div className="font-medium">Actual Order ID:</div>
                              <div>{dbData.debug.actualOrderId || 'N/A'}</div>

                              <div className="font-medium">Query ID Type:</div>
                              <div>{dbData.debug.isUuid ? 'UUID' : 'Number/String'}</div>

                              <div className="font-medium">Timestamp:</div>
                              <div>{dbData.debug.timestamp || 'N/A'}</div>

                              <div className="font-medium">Environment:</div>
                              <div>{dbData.debug.environment || 'N/A'}</div>

                              <div className="font-medium">Order Businesses:</div>
                              <div>{dbData.debug.counts?.orderBusinesses || 0}</div>

                              <div className="font-medium">Order Items:</div>
                              <div>{dbData.debug.counts?.orderItems || 0}</div>

                              <div className="font-medium">Status History:</div>
                              <div>{dbData.debug.counts?.statusHistory || 0}</div>

                              <div className="font-medium">Cart Items:</div>
                              <div>{dbData.debug.counts?.cartItems || 0}</div>
                            </div>
                          </div>
                        )}

                        {/* Full data */}
                        <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-xs max-h-96">
                          {formatJson(dbData)}
                        </pre>
                      </div>
                    ) : (
                      <div className="text-center p-4 text-gray-500">
                        {isLoading ? (
                          "Loading database data..."
                        ) : (
                          "No database data available. Click 'Refresh Data' to fetch."
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
