import { useState, useEffect, useRef, useCallback } from 'react'
import { useAuth } from '@/context/unified-auth-context'
import { useToast } from '@/components/ui/use-toast'
import { Order } from '@/components/orders/enhanced-orders-table'

interface UseOrdersRealtimeOptions {
  isAdminUser: boolean
  selectedBusinessId: number | null
  businessId: number | null
  onOrdersUpdate: () => void
}

export function useOrdersRealtime({
  isAdminUser,
  selectedBusinessId,
  businessId,
  onOrdersUpdate
}: UseOrdersRealtimeOptions) {
  const { user } = useAuth()
  const { toast } = useToast()

  // State
  const [realtimeEnabled, setRealtimeEnabled] = useState(true)
  const [newOrderCount, setNewOrderCount] = useState(0)

  // Keep track of existing order IDs to detect new orders
  const existingOrderIds = useRef(new Set<number>())

  // Update existing order IDs when orders change
  const updateExistingOrderIds = useCallback((orders: Order[]) => {
    if (orders.length > 0) {
      const orderIds = new Set(orders.map(order => order.id))
      existingOrderIds.current = orderIds
    }
  }, [])

  // Set up real-time subscription for orders
  useEffect(() => {
    if (!realtimeEnabled || !user) {
      console.log('🚫 Real-time subscription disabled or user not authenticated:', { 
        realtimeEnabled, 
        hasUser: !!user 
      })
      return
    }

    // Get effective business ID
    const effectiveBusinessId = (isAdminUser && selectedBusinessId) ? selectedBusinessId : businessId

    if (!effectiveBusinessId) {
      // Only log this as an error if we've had enough time for business data to load
      if (businessId !== null) {
        console.log('❌ No business ID available for real-time subscription:', {
          isAdminUser,
          selectedBusinessId,
          businessId
        })
      }
      return
    }

    // Import Supabase client
    const { createClientComponentClient } = require('@supabase/auth-helpers-nextjs')
    const supabase = createClientComponentClient()

    console.log('🚀 Setting up real-time subscription for business:', effectiveBusinessId)

    // Create a unique channel name
    const channelName = `orders-${effectiveBusinessId}-${Math.random().toString(36).substr(2, 9)}`

    // Create channel for real-time updates
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders'
        },
        (payload) => {
          console.log('🔴 REAL-TIME EVENT:', {
            event: payload.eventType,
            orderId: payload.new?.id || payload.old?.id,
            businessId: payload.new?.business_id || payload.old?.business_id,
            effectiveBusinessId,
            timestamp: new Date().toISOString()
          })

          // Check if this order belongs to our business
          const orderBusinessId = payload.new?.business_id || payload.old?.business_id
          if (orderBusinessId !== effectiveBusinessId) {
            console.log('⚠️ Order belongs to different business, ignoring')
            return
          }

          if (payload.eventType === 'INSERT') {
            const newOrder = payload.new as any

            if (newOrder && !existingOrderIds.current.has(newOrder.id)) {
              console.log('✅ Processing new order:', newOrder.id)
              setNewOrderCount(prev => prev + 1)

              // Refresh orders to get complete data with joins
              onOrdersUpdate()

              toast({
                title: "New Order Received",
                description: `Order #${newOrder.order_number || newOrder.id} has been placed`,
                duration: 5000,
              })
            }
          } else if (payload.eventType === 'UPDATE') {
            console.log('✅ Processing order update:', payload.new?.id)
            // Refresh orders to get updated data
            onOrdersUpdate()
          }
        }
      )
      .subscribe((status) => {
        console.log('📡 Real-time subscription status:', status)
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to real-time updates for business:', effectiveBusinessId)
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Real-time subscription error for business:', effectiveBusinessId)
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Real-time subscription timed out for business:', effectiveBusinessId)
        } else if (status === 'CLOSED') {
          console.log('🔒 Real-time subscription closed for business:', effectiveBusinessId)
        }
      })

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up real-time subscription for business:', effectiveBusinessId)
      supabase.removeChannel(channel)
    }
  }, [realtimeEnabled, user, isAdminUser, selectedBusinessId, businessId, onOrdersUpdate, toast])

  // Toggle real-time functionality
  const toggleRealtime = useCallback(() => {
    setRealtimeEnabled(!realtimeEnabled)
    // Reset new order count when toggling live mode
    setNewOrderCount(0)

    // If enabling real-time, refresh orders to get latest data
    if (!realtimeEnabled) {
      onOrdersUpdate()
    }
  }, [realtimeEnabled, onOrdersUpdate])

  // Reset new order count
  const resetNewOrderCount = useCallback(() => {
    setNewOrderCount(0)
  }, [])

  return {
    realtimeEnabled,
    newOrderCount,
    toggleRealtime,
    resetNewOrderCount,
    updateExistingOrderIds
  }
}
