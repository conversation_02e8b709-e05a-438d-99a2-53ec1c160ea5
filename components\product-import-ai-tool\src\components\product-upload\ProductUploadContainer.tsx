
import React from 'react';
import { useProductUpload } from '@/context/ProductUploadContext';
import { UploadStep } from './UploadStep';
import { MappingStep } from './MappingStep';
import { ProcessingStep } from './ProcessingStep';
import { ReviewStep } from './ReviewStep';
import { CompletionStep } from './CompletionStep';
import { Card } from "@/components/ui/card";
import { Check } from "lucide-react";

export const ProductUploadContainer: React.FC = () => {
  const { currentStep } = useProductUpload();
  
  const steps = [
    { id: 1, name: 'Upload CSV' },
    { id: 2, name: 'Map Columns' },
    { id: 3, name: 'Process' },
    { id: 4, name: 'Review & Edit' },
    { id: 5, name: 'Complete' },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <UploadStep />;
      case 2:
        return <MappingStep />;
      case 3:
        return <ProcessingStep />;
      case 4:
        return <ReviewStep />;
      case 5:
        return <CompletionStep />;
      default:
        return <UploadStep />;
    }
  };

  return (
    <div className="flex flex-col space-y-8">
      {/* Steps indicator */}
      <div className="flex justify-center">
        <ol className="flex items-center w-full max-w-4xl">
          {steps.map((step, index) => (
            <li 
              key={step.id} 
              className={`flex items-center ${
                index < steps.length - 1 ? 'w-full' : ''
              }`}
            >
              <div className="flex items-center justify-center">
                <div className={`
                  flex items-center justify-center w-8 h-8 rounded-full shrink-0
                  ${currentStep > step.id ? 'bg-app-blue text-white' : 
                    currentStep === step.id ? 'bg-app-blue bg-opacity-90 text-white' : 
                    'bg-gray-200 text-gray-600'}
                `}>
                  {currentStep > step.id ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span>{step.id}</span>
                  )}
                </div>
                <span className={`hidden sm:inline-flex ml-2 text-sm font-medium ${
                  currentStep >= step.id ? 'text-app-blue' : 'text-gray-500'
                }`}>
                  {step.name}
                </span>
              </div>
              
              {/* Connector line */}
              {index < steps.length - 1 && (
                <div className={`w-full bg-gray-200 h-0.5 mx-2 ${
                  currentStep > step.id ? 'bg-app-blue' : ''
                }`}></div>
              )}
            </li>
          ))}
        </ol>
      </div>
      
      {/* Step content */}
      <div className="w-full max-w-6xl mx-auto px-4">
        {renderStepContent()}
      </div>
    </div>
  );
};
