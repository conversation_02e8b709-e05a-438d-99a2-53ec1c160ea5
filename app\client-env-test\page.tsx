'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@supabase/supabase-js'

export default function ClientEnvTestPage() {
  const [envVars, setEnvVars] = useState<any>(null)
  const [clientError, setClientError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function testSupabaseClient() {
      try {
        // Get environment variables
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
        
        // Create a test client
        const testClient = createClient(supabaseUrl, supabaseAnonKey)
        
        // Test the client with a simple query
        await testClient.from('businesses').select('count(*)').limit(1)
        
        return null
      } catch (err: any) {
        return err.message || 'Unknown error creating Supabase client'
      }
    }
    
    async function loadData() {
      setIsLoading(true)
      
      // Get environment variables
      const vars = {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
        NEXT_PUBLIC_SUPABASE_URL_LENGTH: process.env.NEXT_PUBLIC_SUPABASE_URL?.length || 0,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
          `Set (${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10)}...)` : 'Not set',
        NEXT_PUBLIC_SUPABASE_ANON_KEY_LENGTH: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
        NODE_ENV: process.env.NODE_ENV || 'Not set',
        RUNTIME: typeof window === 'undefined' ? 'server' : 'client'
      }
      
      // Test Supabase client
      const error = await testSupabaseClient()
      
      setEnvVars(vars)
      setClientError(error)
      setIsLoading(false)
    }
    
    loadData()
  }, [])
  
  if (isLoading) {
    return <div className="p-8">Loading environment variables...</div>
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Client-Side Environment Variables Test</h1>
      
      <div className="bg-gray-100 p-4 rounded mb-4">
        <h2 className="text-xl font-semibold mb-2">Environment Variables</h2>
        <pre className="bg-white p-4 rounded overflow-auto">
          {JSON.stringify(envVars, null, 2)}
        </pre>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl font-semibold mb-2">Supabase Client Test</h2>
        {clientError ? (
          <div className="bg-red-100 text-red-800 p-4 rounded">
            <p className="font-semibold">Error:</p>
            <p>{clientError}</p>
          </div>
        ) : (
          <div className="bg-green-100 text-green-800 p-4 rounded">
            <p>Supabase client created and tested successfully!</p>
          </div>
        )}
      </div>
    </div>
  )
}
