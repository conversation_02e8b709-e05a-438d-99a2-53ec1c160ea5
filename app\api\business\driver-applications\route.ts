import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get business ID from user (assuming user is a business manager)
    // In a real implementation, you'd check business_managers table
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('business_id')
    
    if (!businessId) {
      return NextResponse.json(
        { error: "Business ID is required" },
        { status: 400 }
      )
    }

    // Verify user has access to this business
    const { data: businessAccess, error: accessError } = await supabase
      .from('business_managers')
      .select('id')
      .eq('user_id', user.id)
      .eq('business_id', businessId)
      .single()

    if (accessError || !businessAccess) {
      return NextResponse.json(
        { error: "Access denied to this business" },
        { status: 403 }
      )
    }

    // Get search parameters
    const status = searchParams.get('status') || 'pending' // pending, approved, rejected, banned, all
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query for driver applications
    let applicationsQuery = supabase
      .from('driver_business_approvals')
      .select(`
        id,
        driver_id,
        status,
        application_date,
        decision_date,
        decision_by,
        rejection_reason,
        ban_reason,
        notes,
        driver_profiles!inner (
          id,
          user_id,
          vehicle_type,
          vehicle_make,
          vehicle_model,
          vehicle_year,
          vehicle_color,
          cargo_capacity_category,
          max_weight_kg,
          has_thermal_bag,
          has_cooler_bag,
          has_large_bag,
          delivery_types,
          store_types,
          equipment_notes,
          license_number,
          insurance_number,
          vehicle_registration,
          is_verified,
          is_active,
          average_rating,
          total_deliveries,
          created_at,
          users!driver_profiles_user_id_fkey (
            id,
            name,
            first_name,
            last_name,
            email,
            phone
          )
        )
      `)
      .eq('business_id', businessId)
      .order('application_date', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply status filter
    if (status !== 'all') {
      applicationsQuery = applicationsQuery.eq('status', status)
    }

    const { data: applications, error: applicationsError } = await applicationsQuery

    if (applicationsError) {
      console.error('Error fetching driver applications:', applicationsError)
      return NextResponse.json(
        { error: "Failed to fetch driver applications" },
        { status: 500 }
      )
    }

    // Get summary statistics
    const { data: summaryData, error: summaryError } = await supabase
      .from('driver_business_approvals')
      .select('status')
      .eq('business_id', businessId)

    const summary = {
      total: summaryData?.length || 0,
      pending: summaryData?.filter(app => app.status === 'pending').length || 0,
      approved: summaryData?.filter(app => app.status === 'approved').length || 0,
      rejected: summaryData?.filter(app => app.status === 'rejected').length || 0,
      banned: summaryData?.filter(app => app.status === 'banned').length || 0
    }

    // Transform applications for response
    const transformedApplications = applications?.map(app => ({
      id: app.id,
      driver_id: app.driver_id,
      status: app.status,
      application_date: app.application_date,
      decision_date: app.decision_date,
      decision_by: app.decision_by,
      rejection_reason: app.rejection_reason,
      ban_reason: app.ban_reason,
      notes: app.notes,
      driver: {
        id: app.driver_profiles.id,
        user: {
          id: app.driver_profiles.users.id,
          name: app.driver_profiles.users.name,
          first_name: app.driver_profiles.users.first_name,
          last_name: app.driver_profiles.users.last_name,
          email: app.driver_profiles.users.email,
          phone: app.driver_profiles.users.phone
        },
        vehicle: {
          type: app.driver_profiles.vehicle_type,
          make: app.driver_profiles.vehicle_make,
          model: app.driver_profiles.vehicle_model,
          year: app.driver_profiles.vehicle_year,
          color: app.driver_profiles.vehicle_color,
          capacity_category: app.driver_profiles.cargo_capacity_category,
          max_weight_kg: app.driver_profiles.max_weight_kg,
          license_number: app.driver_profiles.license_number,
          insurance_number: app.driver_profiles.insurance_number,
          registration: app.driver_profiles.vehicle_registration
        },
        equipment: {
          has_thermal_bag: app.driver_profiles.has_thermal_bag,
          has_cooler_bag: app.driver_profiles.has_cooler_bag,
          has_large_bag: app.driver_profiles.has_large_bag,
          notes: app.driver_profiles.equipment_notes
        },
        capabilities: {
          delivery_types: app.driver_profiles.delivery_types,
          store_types: app.driver_profiles.store_types
        },
        performance: {
          average_rating: app.driver_profiles.average_rating,
          total_deliveries: app.driver_profiles.total_deliveries,
          is_verified: app.driver_profiles.is_verified,
          is_active: app.driver_profiles.is_active
        },
        joined_date: app.driver_profiles.created_at
      }
    }))

    return NextResponse.json({
      success: true,
      applications: transformedApplications,
      summary,
      pagination: {
        limit,
        offset,
        total: transformedApplications?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in business driver applications GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
