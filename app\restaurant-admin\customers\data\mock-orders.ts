export interface CustomerOrder {
  id: string
  customerId: number
  date: string
  items: {
    name: string
    quantity: number
    price: number
    options?: string[]
  }[]
  subtotal: number
  deliveryFee: number
  serviceFee: number
  total: number
  status: 'delivered' | 'cancelled' | 'preparing' | 'out_for_delivery' | 'new'
  paymentMethod: string
  notes?: string
  address: string
}

export const customerOrders: CustomerOrder[] = [
  // <PERSON>'s orders
  {
    id: "JE-5289",
    customerId: 1,
    date: "May 15, 2024",
    items: [
      { name: "Jersey Crab Cakes", quantity: 2, price: 9.95 },
      { name: "Grilled Sea Bass", quantity: 1, price: 22.95 },
    ],
    subtotal: 42.85,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 45.85,
    status: "delivered",
    paymentMethod: "Card",
    notes: "Please include extra sauce",
    address: "15 Beachfront, St Helier, JE2 3NG",
  },
  {
    id: "JE-5245",
    customerId: 1,
    date: "May 10, 2024",
    items: [
      { name: "Jersey Crab Cakes", quantity: 1, price: 9.95 },
      { name: "Seafood Linguine", quantity: 1, price: 18.95 },
    ],
    subtotal: 28.90,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 31.90,
    status: "delivered",
    paymentMethod: "Card",
    address: "15 Beachfront, St Helier, JE2 3NG",
  },
  {
    id: "JE-5198",
    customerId: 1,
    date: "May 3, 2024",
    items: [
      { name: "Grilled Sea Bass", quantity: 1, price: 22.95 },
      { name: "Jersey Cream Brûlée", quantity: 1, price: 7.95 },
    ],
    subtotal: 30.90,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 33.90,
    status: "delivered",
    paymentMethod: "Card",
    address: "15 Beachfront, St Helier, JE2 3NG",
  },
  
  // Michael Brown's orders
  {
    id: "JE-5288",
    customerId: 2,
    date: "May 12, 2024",
    items: [
      { name: "Jersey Beef Burger", quantity: 1, price: 16.95 },
      { name: "Apple Crumble", quantity: 1, price: 6.95 },
    ],
    subtotal: 23.90,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 26.90,
    status: "delivered",
    paymentMethod: "PayPal",
    address: "42 Roseville Street, St Helier, JE2 4PL",
  },
  
  // Emma Wilson's orders
  {
    id: "JE-5287",
    customerId: 3,
    date: "May 8, 2024",
    items: [
      { name: "Seared Jersey Scallops", quantity: 2, price: 11.5 },
      { name: "Jersey Lobster", quantity: 1, price: 34.95 },
    ],
    subtotal: 57.95,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 60.95,
    status: "delivered",
    paymentMethod: "Card",
    notes: "Doorbell doesn't work, please knock",
    address: "8 La Route de St Aubin, St Helier, JE2 3SF",
  },
  
  // James Taylor's orders
  {
    id: "JE-5286",
    customerId: 4,
    date: "May 14, 2024",
    items: [
      { name: "Jersey Cream Brûlée", quantity: 1, price: 7.95 },
      { name: "Classic Prawn Cocktail", quantity: 1, price: 8.95 },
    ],
    subtotal: 16.90,
    deliveryFee: 2.5,
    serviceFee: 0.5,
    total: 19.90,
    status: "delivered",
    paymentMethod: "Cash",
    address: "23 Colomberie, St Helier, JE2 4QA",
  }
]

export function getOrdersByCustomerId(customerId: number): CustomerOrder[] {
  return customerOrders.filter(order => order.customerId === customerId)
}
