import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'
    
    // For testing purposes, we'll use a hardcoded user email
    const userEmail = '<EMAIL>'

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, total_deliveries, average_rating, created_at')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
    }

    // Get orders for the period
    const { data: orders } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        total,
        delivery_fee,
        status,
        created_at,
        updated_at,
        delivery_distance_km,
        estimated_delivery_time,
        parish,
        businesses!inner (
          name,
          location
        )
      `)
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())

    // Get earnings for the period
    const { data: earnings } = await supabase
      .from('driver_earnings')
      .select('*')
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())

    // Calculate performance metrics
    const totalOrders = orders?.length || 0
    const completedOrders = orders?.filter(o => o.status === 'delivered').length || 0
    const totalEarnings = earnings?.reduce((sum, e) => sum + (e.total_amount || 0), 0) || 0
    const averagePerDelivery = completedOrders > 0 ? totalEarnings / completedOrders : 0

    // Calculate completion rate
    const completionRate = totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0

    // Calculate on-time delivery rate (mock data for now)
    const onTimeDeliveryRate = Math.round(85 + Math.random() * 15) // 85-100%

    // Calculate acceptance rate (mock data for now)
    const acceptanceRate = Math.round(80 + Math.random() * 20) // 80-100%

    // Calculate customer satisfaction (mock data for now)
    const customerSatisfaction = Math.round(85 + Math.random() * 15) // 85-100%

    // Generate daily earnings trend
    const dailyEarnings = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dayOrders = orders?.filter(o => {
        const orderDate = new Date(o.created_at)
        return orderDate.toDateString() === date.toDateString()
      }) || []
      
      const dayEarnings = dayOrders.reduce((sum, o) => sum + (o.delivery_fee || 0), 0)
      
      dailyEarnings.push({
        date: date.toLocaleDateString('en-GB', { month: 'short', day: 'numeric' }),
        earnings: Math.round(dayEarnings * 100) / 100,
        deliveries: dayOrders.length
      })
    }

    // Generate hourly activity (mock data)
    const hourlyActivity = []
    for (let hour = 0; hour < 24; hour++) {
      const baseOrders = hour >= 11 && hour <= 14 ? 8 : hour >= 17 && hour <= 21 ? 12 : 2
      const variance = Math.random() * 4
      const orders = Math.round(baseOrders + variance)
      const earnings = orders * (8 + Math.random() * 4)
      
      hourlyActivity.push({
        hour,
        orders,
        earnings: Math.round(earnings * 100) / 100
      })
    }

    // Generate business breakdown
    const businessMap = new Map()
    orders?.forEach(order => {
      const business = order.business_name || 'Unknown'
      if (!businessMap.has(business)) {
        businessMap.set(business, { orders: 0, earnings: 0 })
      }
      const data = businessMap.get(business)
      data.orders += 1
      data.earnings += order.delivery_fee || 0
    })

    const businessBreakdown = Array.from(businessMap.entries()).map(([business, data]) => ({
      business,
      orders: data.orders,
      earnings: Math.round(data.earnings * 100) / 100,
      percentage: Math.round((data.orders / totalOrders) * 100)
    })).slice(0, 6) // Top 6 businesses

    // Generate parish distribution
    const parishMap = new Map()
    orders?.forEach(order => {
      const parish = order.parish || 'Unknown'
      if (!parishMap.has(parish)) {
        parishMap.set(parish, 0)
      }
      parishMap.set(parish, parishMap.get(parish) + 1)
    })

    const parishDistribution = Array.from(parishMap.entries()).map(([parish, deliveries]) => ({
      parish,
      deliveries,
      percentage: Math.round((deliveries / totalOrders) * 100)
    })).sort((a, b) => b.deliveries - a.deliveries).slice(0, 8) // Top 8 parishes

    // Calculate efficiency metrics
    const averageDeliveryTime = Math.round(25 + Math.random() * 15) // 25-40 minutes
    const averageDistance = orders?.length > 0 
      ? Math.round((orders.reduce((sum, o) => sum + (o.delivery_distance_km || 0), 0) / orders.length) * 100) / 100
      : 0
    const fuelEfficiency = Math.round(85 + Math.random() * 15) // 85-100%
    const routeOptimization = Math.round(80 + Math.random() * 20) // 80-100%
    const idleTime = Math.round(5 + Math.random() * 10) // 5-15 minutes

    // Calculate trends
    const weeklyTrend = Math.round((Math.random() - 0.3) * 30) // -9% to +21%
    const monthlyTrend = Math.round((Math.random() - 0.2) * 25) // -5% to +20%

    const analyticsData = {
      performance: {
        totalDeliveries: completedOrders,
        completionRate,
        averageRating: driverProfile.average_rating || 4.5,
        onTimeDeliveryRate,
        acceptanceRate,
        customerSatisfaction
      },
      earnings: {
        totalEarnings: Math.round(totalEarnings * 100) / 100,
        averagePerDelivery: Math.round(averagePerDelivery * 100) / 100,
        weeklyTrend,
        monthlyTrend,
        topEarningDay: 'Saturday',
        peakHours: ['12:00-14:00', '18:00-20:00']
      },
      efficiency: {
        averageDeliveryTime,
        averageDistance,
        fuelEfficiency,
        routeOptimization,
        idleTime
      },
      trends: {
        dailyEarnings,
        hourlyActivity,
        businessBreakdown,
        parishDistribution
      }
    }

    return NextResponse.json(analyticsData)

  } catch (error) {
    console.error('Analytics API error:', error)
    return NextResponse.json(
      { error: "Failed to fetch analytics data" },
      { status: 500 }
    )
  }
}
