import { NextRequest, NextResponse } from 'next/server';
import { calculateBusinessDeliveryFee } from '@/lib/delivery-fee-calculator';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const requestBody = await request.json();
    const {
      businessId,
      businessCoordinates,
      customerCoordinates,
      postcode,
      deliveryFeeModel,
      deliveryFee,
      deliveryFeePerKm,
      preparationTimeMinutes
    } = requestBody;

    console.log(`🚀 API: Delivery fee calculation request for business ${businessId}:`, requestBody);

    // Validate required parameters
    if (!businessId || !businessCoordinates || !postcode) {
      console.error(`❌ API: Missing required parameters for business ${businessId}:`, {
        businessId: !!businessId,
        businessCoordinates: !!businessCoordinates,
        postcode: !!postcode
      });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Check if business offers delivery before calculating fee
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('delivery_available, name')
      .eq('id', businessId)
      .single();

    if (businessError) {
      console.error(`❌ API: Error fetching business ${businessId} delivery availability:`, businessError);
      // Continue with calculation if we can't check delivery availability
    } else if (business && business.delivery_available === false) {
      console.log(`🚫 API: Business ${businessId} (${business.name}) is pickup-only, returning zero delivery fee`);
      return NextResponse.json({
        fee: 0,
        distance: 0,
        calculationMethod: 'pickup-only',
        travelTimeMinutes: 0,
        totalTimeMinutes: 0,
        timeRange: 'Pickup only'
      });
    }

    // Parse business coordinates (they might come as strings from database)
    let parsedBusinessCoordinates: [number, number];
    try {
      if (typeof businessCoordinates === 'string') {
        // Parse string format like "(-2.109951,49.185141)"
        const coordStr = businessCoordinates.replace(/[()]/g, '');
        const [lng, lat] = coordStr.split(',').map(Number);
        parsedBusinessCoordinates = [lng, lat];
      } else if (Array.isArray(businessCoordinates)) {
        parsedBusinessCoordinates = businessCoordinates;
      } else {
        throw new Error('Invalid business coordinates format');
      }
    } catch (error) {
      console.error('Error parsing business coordinates:', businessCoordinates, error);
      return NextResponse.json(
        { error: 'Invalid business coordinates format' },
        { status: 400 }
      );
    }

    // Calculate delivery fee
    const result = await calculateBusinessDeliveryFee(
      {
        coordinates: parsedBusinessCoordinates,
        delivery_fee_model: deliveryFeeModel || 'fixed',
        delivery_fee: deliveryFee || 2.50,
        delivery_fee_per_km: deliveryFeePerKm || 0.50
      },
      postcode,
      customerCoordinates,
      true // isCheckout = true
    );

    // Calculate delivery time based on distance and preparation time
    const travelTimeMinutes = result.distance ? Math.ceil((result.distance / 30) * 60) : 10; // 30 km/h average speed
    const totalTimeMinutes = (preparationTimeMinutes || 15) + travelTimeMinutes;
    const minTime = Math.max(5, totalTimeMinutes - 5);
    const maxTime = totalTimeMinutes + 5;

    const apiResponse = {
      fee: result.fee,
      distance: result.distance,
      calculationMethod: result.calculationMethod,
      travelTimeMinutes,
      totalTimeMinutes,
      timeRange: `${minTime}-${maxTime} min`
    };

    console.log(`✅ API: Delivery fee calculation result for business ${businessId}:`, apiResponse);

    return NextResponse.json(apiResponse);
  } catch (error) {
    console.error(`❌ API: Error calculating delivery fee for business ${businessId || 'unknown'}:`, error);
    return NextResponse.json(
      { error: 'Failed to calculate delivery fee', details: error.message },
      { status: 500 }
    );
  }
}
