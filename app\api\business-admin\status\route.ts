import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * Update business temporary closure status
 * PATCH /api/business-admin/status
 */
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { businessId, isTemporarilyClosed, closureMessage } = body

    console.log('🏪 Business status update request:', {
      businessId,
      isTemporarilyClosed,
      closureMessage: closureMessage ? `"${closureMessage}"` : 'null'
    })

    // Validate input
    if (!businessId || typeof businessId !== 'number') {
      return NextResponse.json(
        { error: 'Valid business ID is required' },
        { status: 400 }
      )
    }

    if (typeof isTemporarilyClosed !== 'boolean') {
      return NextResponse.json(
        { error: 'isTemporarilyClosed must be a boolean' },
        { status: 400 }
      )
    }

    // Validate closure message length if provided
    if (closureMessage && typeof closureMessage === 'string' && closureMessage.length > 200) {
      return NextResponse.json(
        { error: 'Closure message cannot exceed 200 characters' },
        { status: 400 }
      )
    }

    // Create Supabase client with service role for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Prepare update data
    const updateData: any = {
      is_temporarily_closed: isTemporarilyClosed,
      updated_at: new Date().toISOString()
    }

    // Only update closure message if business is being closed, or clear it if opening
    if (isTemporarilyClosed) {
      updateData.closure_message = closureMessage || null
    } else {
      updateData.closure_message = null // Clear message when opening
    }

    console.log('📝 Prepared update data:', updateData)

    // Update the business status
    const { data, error } = await supabase
      .from('businesses')
      .update(updateData)
      .eq('id', businessId)
      .select('id, name, is_temporarily_closed, closure_message')
      .single()

    if (error) {
      console.error('❌ Error updating business status:', error)
      return NextResponse.json(
        { error: 'Failed to update business status' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    console.log('✅ Business status updated successfully:', {
      businessId: data.id,
      businessName: data.name,
      isTemporarilyClosed: data.is_temporarily_closed,
      closureMessage: data.closure_message
    })

    return NextResponse.json({
      success: true,
      data: {
        id: data.id,
        name: data.name,
        isTemporarilyClosed: data.is_temporarily_closed,
        closureMessage: data.closure_message
      }
    })

  } catch (error: any) {
    console.error('❌ Business status update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Get business status
 * GET /api/business-admin/status?businessId=123
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      )
    }

    const businessIdNum = parseInt(businessId, 10)
    if (isNaN(businessIdNum)) {
      return NextResponse.json(
        { error: 'Invalid business ID' },
        { status: 400 }
      )
    }

    // Create Supabase client with service role
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get business status
    const { data, error } = await supabase
      .from('businesses')
      .select('id, name, is_temporarily_closed, closure_message')
      .eq('id', businessIdNum)
      .single()

    if (error) {
      console.error('❌ Error fetching business status:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business status' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: data.id,
        name: data.name,
        isTemporarilyClosed: data.is_temporarily_closed || false,
        closureMessage: data.closure_message || ""
      }
    })

  } catch (error: any) {
    console.error('❌ Business status fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
