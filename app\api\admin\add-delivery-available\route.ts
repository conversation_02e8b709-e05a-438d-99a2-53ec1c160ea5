import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  try {
    console.log('Starting to add delivery_available column to businesses table...');
    
    // Check if the column already exists
    let columnExists = false;
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('delivery_available')
        .limit(1);
      
      columnExists = !error;
    } catch (e) {
      console.error('Error checking if column exists:', e);
    }
    
    console.log(`Column delivery_available ${columnExists ? 'exists' : 'does not exist'}`);
    
    if (!columnExists) {
      // Add the column using raw SQL
      try {
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: `ALTER TABLE businesses ADD COLUMN IF NOT EXISTS delivery_available BOOLEAN DEFAULT true NOT NULL;`
        });
        
        if (error) {
          console.error('Error adding column with RPC:', error);
          
          // Try direct SQL execution
          const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseServiceKey,
              'Authorization': `Bearer ${supabaseServiceKey}`
            },
            body: JSON.stringify({
              sql: `ALTER TABLE businesses ADD COLUMN IF NOT EXISTS delivery_available BOOLEAN DEFAULT true NOT NULL;`
            })
          });
          
          if (!response.ok) {
            const result = await response.json();
            console.error('Error adding column with REST API:', result);
            return NextResponse.json(
              { error: 'Failed to add column', details: result },
              { status: 500 }
            );
          }
        }
        
        console.log('Successfully added delivery_available column');
      } catch (e) {
        console.error('Error adding column:', e);
        return NextResponse.json(
          { error: 'Failed to add column', details: e },
          { status: 500 }
        );
      }
    }
    
    // Update all rows to set delivery_available to true
    try {
      const { data, error } = await supabase
        .from('businesses')
        .update({ delivery_available: true })
        .is('delivery_available', null);
      
      if (error) {
        console.error('Error updating values with client:', error);
        
        // Try with raw SQL
        const { data: sqlData, error: sqlError } = await supabase.rpc('exec_sql', {
          sql: `UPDATE businesses SET delivery_available = true WHERE delivery_available IS NULL;`
        });
        
        if (sqlError) {
          console.error('Error updating values with RPC:', sqlError);
          
          // Try with REST API
          const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseServiceKey,
              'Authorization': `Bearer ${supabaseServiceKey}`
            },
            body: JSON.stringify({
              sql: `UPDATE businesses SET delivery_available = true WHERE delivery_available IS NULL;`
            })
          });
          
          if (!response.ok) {
            const result = await response.json();
            console.error('Error updating values with REST API:', result);
            return NextResponse.json(
              { error: 'Failed to update values', details: result },
              { status: 500 }
            );
          }
        }
      }
      
      console.log('Successfully set delivery_available to true for all businesses');
    } catch (e) {
      console.error('Error updating values:', e);
      return NextResponse.json(
        { error: 'Failed to update values', details: e },
        { status: 500 }
      );
    }
    
    // Verify the changes
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('id, name, delivery_available')
        .limit(5);
      
      if (error) {
        console.error('Error verifying changes:', error);
        return NextResponse.json(
          { error: 'Failed to verify changes', details: error },
          { status: 500 }
        );
      }
      
      console.log('Sample data after changes:', data);
      
      return NextResponse.json({
        success: true,
        message: 'Successfully added delivery_available column and set values to true',
        sampleData: data
      });
    } catch (e) {
      console.error('Error verifying changes:', e);
      return NextResponse.json(
        { error: 'Failed to verify changes', details: e },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
