import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id, name')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Get query parameters for date range
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const period = searchParams.get('period') || 'week' // week, month, all

    // Calculate date range based on period
    let dateFilter = ''
    const now = new Date()
    
    if (startDate && endDate) {
      dateFilter = `shift_start >= '${startDate}' AND shift_start <= '${endDate}'`
    } else {
      switch (period) {
        case 'today':
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          const tomorrow = new Date(today)
          tomorrow.setDate(tomorrow.getDate() + 1)
          dateFilter = `shift_start >= '${today.toISOString()}' AND shift_start < '${tomorrow.toISOString()}'`
          break
        case 'week':
          const weekAgo = new Date()
          weekAgo.setDate(weekAgo.getDate() - 7)
          dateFilter = `shift_start >= '${weekAgo.toISOString()}'`
          break
        case 'month':
          const monthAgo = new Date()
          monthAgo.setMonth(monthAgo.getMonth() - 1)
          dateFilter = `shift_start >= '${monthAgo.toISOString()}'`
          break
        default:
          dateFilter = '1=1' // No filter for 'all'
      }
    }

    // Get completed shifts with raw data
    const { data: shifts, error: shiftsError } = await supabase
      .from('driver_shifts')
      .select(`
        id,
        shift_start,
        shift_end,
        created_at,
        updated_at
      `)
      .eq('driver_id', driverProfile.id)
      .not('shift_end', 'is', null) // Only completed shifts
      .order('shift_start', { ascending: false })

    if (shiftsError) {
      console.error('Error fetching shifts:', shiftsError)
      return NextResponse.json(
        { error: "Failed to fetch shift data" },
        { status: 500 }
      )
    }

    // Get shift order interactions for each shift
    const shiftIds = shifts?.map(shift => shift.id) || []
    let shiftOrders = []
    
    if (shiftIds.length > 0) {
      const { data: orderData, error: orderError } = await supabase
        .from('driver_shift_orders')
        .select(`
          shift_id,
          order_id,
          offered_at,
          action_taken,
          action_taken_at,
          business_id,
          parish,
          delivery_fee,
          order_total,
          distance_km
        `)
        .in('shift_id', shiftIds)
        .order('offered_at', { ascending: false })

      if (!orderError) {
        shiftOrders = orderData || []
      }
    }

    // Get current active shift if exists
    const { data: activeShift } = await supabase
      .from('driver_shifts')
      .select(`
        id,
        shift_start,
        created_at
      `)
      .eq('driver_id', driverProfile.id)
      .is('shift_end', null)
      .single()

    // Get active shift orders if there's an active shift
    let activeShiftOrders = []
    if (activeShift) {
      const { data: activeOrderData } = await supabase
        .from('driver_shift_orders')
        .select(`
          shift_id,
          order_id,
          offered_at,
          action_taken,
          action_taken_at,
          business_id,
          parish,
          delivery_fee,
          order_total,
          distance_km
        `)
        .eq('shift_id', activeShift.id)
        .order('offered_at', { ascending: false })

      activeShiftOrders = activeOrderData || []
    }

    // Return raw data for frontend calculations
    return NextResponse.json({
      shifts: shifts || [],
      shiftOrders,
      activeShift,
      activeShiftOrders,
      driver: {
        id: driverProfile.id,
        name: user.name
      },
      period,
      dateRange: {
        start: startDate,
        end: endDate
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in shift analytics API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
