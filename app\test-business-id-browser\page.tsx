"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import Script from 'next/script'

export default function TestBusinessIdBrowserPage() {
  const [testResults, setTestResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const runTest = () => {
    setIsLoading(true)
    setTestResults(null)
    
    // Run the test script
    // @ts-ignore
    window.testBusinessIdConversion().then((results: any) => {
      setTestResults(results)
      setIsLoading(false)
    }).catch((error: any) => {
      setTestResults({ error: error.message })
      setIsLoading(false)
    })
  }

  return (
    <div className="container mx-auto py-8">
      <Script src="/test-business-id.js" />
      
      <h1 className="text-2xl font-bold mb-4">Business ID Conversion Browser Test</h1>
      
      <Button 
        onClick={runTest} 
        disabled={isLoading}
        className="mb-6"
      >
        {isLoading ? 'Running Test...' : 'Run Test'}
      </Button>
      
      <div className="bg-gray-100 p-4 rounded-md">
        <h2 className="text-lg font-semibold mb-2">Test Results:</h2>
        {testResults ? (
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        ) : (
          <p>Click "Run Test" to start the test</p>
        )}
      </div>
      
      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-2">Test Description:</h2>
        <p className="mb-2">This test simulates the business ID conversion process:</p>
        <ol className="list-decimal list-inside space-y-1 ml-4">
          <li>Creates a mock cart item with a string business ID (slug)</li>
          <li>Attempts to convert the string business ID to a numeric ID</li>
          <li>Updates the cart item with the numeric business ID</li>
          <li>Simulates creating an order with the updated cart item</li>
          <li>Simulates the order API processing</li>
          <li>Simulates the transaction helper processing</li>
        </ol>
        <p className="mt-2">The test results show the business ID at each stage of the process.</p>
      </div>
    </div>
  )
}
