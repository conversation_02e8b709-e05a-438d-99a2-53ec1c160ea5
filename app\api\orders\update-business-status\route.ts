// API endpoint for updating the status of a business in an order
import { NextRequest, NextResponse } from 'next/server';
import { updateOrderStatus, updateBusinessOrderStatus } from '../transaction-helper';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId, businessId, status, updateAll, notes } = body;

    // Validate input
    if (!orderId) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }

    if (!status) {
      return NextResponse.json({ error: 'Status is required' }, { status: 400 });
    }

    // Convert orderId to number
    const orderIdNum = Number(orderId);

    if (isNaN(orderIdNum)) {
      return NextResponse.json({ error: 'Order ID must be a number' }, { status: 400 });
    }

    let updatedOrder;

    if (updateAll) {
      // Update the order status directly
      updatedOrder = await updateOrderStatus(orderIdNum, status, notes);
    } else {
      // Validate businessId
      if (!businessId) {
        return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
      }

      // Convert businessId to number
      const businessIdNum = Number(businessId);

      if (isNaN(businessIdNum)) {
        return NextResponse.json({ error: 'Business ID must be a number' }, { status: 400 });
      }

      // Update the specific business
      updatedOrder = await updateBusinessOrderStatus(orderIdNum, businessIdNum, status, notes);
    }

    if (!updatedOrder) {
      return NextResponse.json({ error: 'Failed to update order status' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: updateAll ? 'Order updated successfully' : 'Business status updated successfully',
      order: updatedOrder
    });

  } catch (error: any) {
    return NextResponse.json({ error: `Unexpected error: ${error.message}` }, { status: 500 });
  }
}
