"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Clock, Navigation, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import dynamic from 'next/dynamic'
import { geocodeAddress } from "@/lib/geocoding"

// Import Leaflet dynamically to avoid SSR issues
const LeafletMap = dynamic(
  () => import('./osm-restaurant-map-inner'),
  { ssr: false }
)

interface OSMRestaurantDeliveryMapProps {
  restaurantName: string
  restaurantLocation: string
  restaurantCoordinates: [number, number] // [longitude, latitude]
  deliveryRadius: number // in kilometers
  preparationTimeMinutes: number
}

export default function OSMRestaurantDeliveryMap({
  restaurantName,
  restaurantLocation,
  restaurantCoordinates,
  deliveryRadius,
  preparationTimeMinutes
}: OSMRestaurantDeliveryMapProps) {
  const [mapError, setMapError] = useState(false)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [userAddress, setUserAddress] = useState<string>("")
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<number | null>(null)
  const [estimatedDistance, setEstimatedDistance] = useState<number | null>(null)
  const [showAddressInput, setShowAddressInput] = useState(false)
  const [addressInput, setAddressInput] = useState("")
  const [isLoadingAddress, setIsLoadingAddress] = useState(false)

  // Get user's location
  const getUserLocation = () => {
    setIsGettingLocation(true)

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { longitude, latitude } = position.coords
          setUserLocation([longitude, latitude])
          setIsGettingLocation(false)

          // Calculate estimated delivery time and distance
          calculateEstimates([longitude, latitude])
        },
        (error) => {
          console.error("Error getting user location:", error)
          setIsGettingLocation(false)
          setShowAddressInput(true)
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
      )
    } else {
      console.error("Geolocation is not supported by this browser")
      setIsGettingLocation(false)
      setShowAddressInput(true)
    }
  }

  // Handle address input submission
  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!addressInput.trim()) return

    setIsLoadingAddress(true)
    try {
      // Add "Jersey" to the address if not already included
      let fullAddress = addressInput
      if (!fullAddress.toLowerCase().includes("jersey")) {
        fullAddress += ", Jersey"
      }

      const coordinates = await geocodeAddress(fullAddress)
      if (coordinates) {
        setUserLocation(coordinates)
        setUserAddress(fullAddress)
        calculateEstimates(coordinates)
      } else {
        alert("Could not find that address. Please try again.")
      }
    } catch (error) {
      console.error("Error geocoding address:", error)
      alert("Error finding address. Please try again.")
    } finally {
      setIsLoadingAddress(false)
    }
  }

  // Calculate estimated delivery time and distance
  const calculateEstimates = (userCoords: [number, number]) => {
    // Ensure coordinates are valid
    if (!restaurantCoordinates || !Array.isArray(restaurantCoordinates) || restaurantCoordinates.length !== 2) {
      console.error("Invalid restaurant coordinates for calculation:", restaurantCoordinates);
      return;
    }

    // Make sure coordinates are numbers
    const validRestCoords: [number, number] = [
      typeof restaurantCoordinates[0] === 'number' ? restaurantCoordinates[0] : -2.1053,
      typeof restaurantCoordinates[1] === 'number' ? restaurantCoordinates[1] : 49.1805
    ];

    // Ensure user coordinates are valid
    const validUserCoords: [number, number] = [
      typeof userCoords[0] === 'number' ? userCoords[0] : 0,
      typeof userCoords[1] === 'number' ? userCoords[1] : 0
    ];

    // Calculate distance using Haversine formula
    const [userLng, userLat] = validUserCoords;
    const [restLng, restLat] = validRestCoords;

    const R = 6371 // Earth's radius in km
    const dLat = (userLat - restLat) * Math.PI / 180
    const dLon = (userLng - restLng) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(restLat * Math.PI / 180) * Math.cos(userLat * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    const distance = R * c

    setEstimatedDistance(distance)

    // Calculate delivery time (prep time + travel time)
    // Assume average speed of 25 km/h for delivery
    const travelTimeMinutes = Math.round(distance / 25 * 60)
    const totalDeliveryTime = preparationTimeMinutes + travelTimeMinutes

    setEstimatedDeliveryTime(totalDeliveryTime)
  }

  return (
    <div className="space-y-4">
      {/* Delivery Information */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-2">
        <h3 className="font-semibold mb-2">Delivery Information</h3>

        <div className="space-y-3">
          <div className="flex items-start">
            <Clock className="h-4 w-4 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Preparation Time</p>
              <p className="text-gray-600 text-sm">{preparationTimeMinutes} minutes</p>
            </div>
          </div>

          <div className="flex items-start">
            <Navigation className="h-4 w-4 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Delivery Radius</p>
              <p className="text-gray-600 text-sm">
                {deliveryRadius} km from restaurant
              </p>
            </div>
          </div>

          {estimatedDistance !== null && (
            <div className="flex items-start">
              <Navigation className="h-4 w-4 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">Delivery Distance</p>
                <p className="text-gray-600 text-sm">
                  {estimatedDistance.toFixed(1)} km ({Math.round(estimatedDistance / 25 * 60)} min drive)
                </p>
                {estimatedDistance > deliveryRadius && (
                  <p className="text-orange-500 text-xs mt-1">
                    Outside standard delivery radius. Additional fee may apply.
                  </p>
                )}
              </div>
            </div>
          )}

          {estimatedDeliveryTime !== null && (
            <div className="flex items-start">
              <Clock className="h-4 w-4 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">Total Delivery Time</p>
                <p className="text-gray-600 text-sm">
                  Approximately {estimatedDeliveryTime} minutes
                </p>
              </div>
            </div>
          )}

          <div className="flex items-start">
            <MapPin className="h-4 w-4 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Restaurant Address</p>
              <p className="text-gray-600 text-sm">{restaurantLocation}, Jersey</p>
            </div>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="rounded-lg overflow-hidden border border-gray-200 shadow-sm">
        {mapError ? (
          <div className="bg-red-50 p-4 text-center text-red-800">
            <p>Could not load the map. Please try again later.</p>
          </div>
        ) : (
          <LeafletMap 
            restaurantName={restaurantName}
            restaurantLocation={restaurantLocation}
            restaurantCoordinates={restaurantCoordinates}
            userLocation={userLocation}
            userAddress={userAddress}
            deliveryRadius={deliveryRadius}
            estimatedDeliveryTime={estimatedDeliveryTime}
            estimatedDistance={estimatedDistance}
            preparationTimeMinutes={preparationTimeMinutes}
          />
        )}
      </div>

      {/* Location Controls */}
      <div className="space-y-3">
        {!userLocation && !showAddressInput && (
          <Button
            onClick={getUserLocation}
            className="w-full"
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Getting your location...
              </>
            ) : (
              <>
                <MapPin className="mr-2 h-4 w-4" />
                Use my current location
              </>
            )}
          </Button>
        )}

        {!userLocation && (
          <div className="text-center">
            <button
              onClick={() => setShowAddressInput(!showAddressInput)}
              className="text-emerald-600 text-sm hover:underline"
            >
              {showAddressInput ? "Use my current location instead" : "Enter address manually"}
            </button>
          </div>
        )}

        {showAddressInput && (
          <form onSubmit={handleAddressSubmit} className="space-y-2">
            <div className="flex gap-2">
              <input
                type="text"
                value={addressInput}
                onChange={(e) => setAddressInput(e.target.value)}
                placeholder="Enter your address in Jersey"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                disabled={isLoadingAddress}
              />
              <Button type="submit" disabled={isLoadingAddress || !addressInput.trim()}>
                {isLoadingAddress ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Find"
                )}
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Example: 15 Beachfront, St Helier, JE2 3NG
            </p>
          </form>
        )}

        {userLocation && (
          <Button
            variant="outline"
            onClick={() => {
              setUserLocation(null)
              setUserAddress("")
              setEstimatedDeliveryTime(null)
              setEstimatedDistance(null)
              setShowAddressInput(false)
              setAddressInput("")
            }}
            className="w-full"
          >
            Reset Location
          </Button>
        )}
      </div>

      <div className="text-xs text-gray-500">
        <p>Delivery times are estimates and may vary based on traffic and weather conditions.</p>
        <p>Delivery radius of {deliveryRadius} km shown in green. We may be able to deliver outside this area for an additional fee.</p>
      </div>
    </div>
  )
}
