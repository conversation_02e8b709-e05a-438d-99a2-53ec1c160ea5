// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function subscribeBusinesses() {
  try {
    console.log('=== SUBSCRIBING BUSINESSES TO CATEGORIES ===\n');

    // Better, more targeted subscription plan
    const smartSubscriptions = [
      // RESTAURANTS - More specific matching
      { businessId: 1, categoryIds: [232, 16] }, // Jersey Wings -> Burgers & Sandwiches, Pizza
      { businessId: 2, categoryIds: [241, 16] }, // Pasta Paradise -> Italian Cuisine, Pizza
      { businessId: 3, categoryIds: [242, 20] }, // Sushi Bay -> Asian Cuisine, Seafood
      { businessId: 4, categoryIds: [234, 20] }, // Jersey Grill -> Grilled & BBQ, Seafood
      { businessId: 5, categoryIds: [239, 20] }, // St. Brelade's Bistro -> British Cuisine, Seafood
      { businessId: 34, categoryIds: [232, 15] }, // McDonalds -> Burgers & Sandwiches, Burgers
      { businessId: 38, categoryIds: [232, 15] }, // Burger King -> Burgers & Sandwiches, Burgers
      { businessId: 39, categoryIds: [242] }, // Princess Garden -> Asian Cuisine
      { businessId: 40, categoryIds: [242, 245] }, // Bengal Spice -> Asian Cuisine, Indian & Curry
      { businessId: 41, categoryIds: [16] }, // Domino's -> Pizza
      { businessId: 42, categoryIds: [239, 246] }, // Robin Hood -> British Cuisine, Pub Food
      { businessId: 43, categoryIds: [20, 239] }, // Snow Hill Chip Shop -> Seafood, British Cuisine
      { businessId: 44, categoryIds: [239, 246] }, // Green Dragon -> British Cuisine, Pub Food
      { businessId: 45, categoryIds: [244] }, // Turkish Delight -> Mexican & Latin (closest to Turkish)
      { businessId: 47, categoryIds: [239] }, // Off The Rails -> British Cuisine
      { businessId: 48, categoryIds: [240] }, // Prefecture St Aubin -> French Cuisine
      { businessId: 49, categoryIds: [242, 245] }, // Saffron -> Asian Cuisine, Indian & Curry
      { businessId: 50, categoryIds: [242] }, // Lotus House -> Asian Cuisine
      { businessId: 51, categoryIds: [242] }, // Great Wall -> Asian Cuisine
      { businessId: 52, categoryIds: [244] }, // Arepera La Tricolor -> Mexican & Latin
      { businessId: 54, categoryIds: [241] }, // Casa Paco -> Italian Cuisine
      { businessId: 55, categoryIds: [242, 245] }, // Jaipur -> Asian Cuisine, Indian & Curry
      { businessId: 81, categoryIds: [232] }, // KFC -> Burgers & Sandwiches

      // SHOPS - Specific to their type
      { businessId: 26, categoryIds: [199, 255, 257] }, // Island Grocers -> Groceries, Fresh Produce, Dairy & Eggs
      { businessId: 27, categoryIds: [200, 268, 269] }, // Jersey Home Goods -> Home Goods, Home & Garden, Kitchen & Dining
      { businessId: 28, categoryIds: [201, 199, 262] }, // Coastal Convenience -> Convenience, Groceries, Beverages
      { businessId: 36, categoryIds: [199, 272, 265] }, // Marks & Spencer -> Groceries, Clothing & Accessories, Personal Care
      { businessId: 82, categoryIds: [199, 255, 257, 260] }, // Jersey Co-op -> Groceries, Fresh Produce, Dairy & Eggs, Frozen Foods

      // CAFES - Coffee focused
      { businessId: 31, categoryIds: [204, 253, 247] }, // Harbour Coffee -> Coffee & Drinks, Specialty Coffee, Hot Drinks
      { businessId: 32, categoryIds: [204, 253, 205] }, // Jersey Bean -> Coffee & Drinks, Specialty Coffee, Breakfast
      { businessId: 33, categoryIds: [204, 247, 248] }, // Coastal Brew -> Coffee & Drinks, Hot Drinks, Cold Drinks
      { businessId: 74, categoryIds: [204, 251, 250] }, // Rabbit Hole -> Coffee & Drinks, Pastries & Baked Goods, Light Lunch

      // PHARMACIES - Health focused
      { businessId: 29, categoryIds: [202, 274, 275] }, // Island Health Pharmacy -> Prescription, Prescription Medications, Over-the-Counter
      { businessId: 30, categoryIds: [203, 281, 285] }, // Jersey Wellness -> Wellness, Vitamins & Minerals, Skincare
      { businessId: 37, categoryIds: [202, 275, 276] }, // Boots Jersey -> Prescription, Over-the-Counter, Pain Relief

      // ERRANDS - Service focused
      { businessId: 16, categoryIds: [196, 295, 297] }, // Task Runners -> Shopping, Grocery Shopping, Retail Shopping
      { businessId: 17, categoryIds: [197, 300, 302] }, // Jersey Helpers -> Delivery, Package Delivery, Airport Runs
    ];

    console.log(`📋 Planning to create ${smartSubscriptions.reduce((sum, sub) => sum + sub.categoryIds.length, 0)} category subscriptions\n`);

    // Clear existing subscriptions first
    console.log('🧹 Clearing existing business category subscriptions...');
    const { error: clearError } = await supabase
      .from('business_categories')
      .delete()
      .neq('business_id', 0); // Delete all

    if (clearError) {
      console.error('Error clearing existing subscriptions:', clearError);
      return;
    }
    console.log('✅ Existing subscriptions cleared\n');

    // Create new subscriptions
    console.log('📝 Creating new category subscriptions...');
    
    let totalCreated = 0;
    for (const subscription of smartSubscriptions) {
      const { businessId, categoryIds } = subscription;
      
      // Get business name for logging
      const { data: business } = await supabase
        .from('businesses')
        .select('name')
        .eq('id', businessId)
        .single();

      const businessName = business?.name || `Business ${businessId}`;
      
      for (const categoryId of categoryIds) {
        // Get category name for logging
        const { data: category } = await supabase
          .from('categories')
          .select('name')
          .eq('id', categoryId)
          .single();

        const categoryName = category?.name || `Category ${categoryId}`;

        // Create subscription
        const { error: insertError } = await supabase
          .from('business_categories')
          .insert({
            business_id: businessId,
            category_id: categoryId,
            is_primary: categoryIds.indexOf(categoryId) === 0 // First category is primary
          });

        if (insertError) {
          console.error(`❌ Error subscribing ${businessName} to ${categoryName}:`, insertError);
        } else {
          console.log(`✅ ${businessName} -> ${categoryName}`);
          totalCreated++;
        }
      }
    }

    console.log(`\n📊 SUBSCRIPTION RESULTS`);
    console.log('='.repeat(50));
    console.log(`Total subscriptions created: ${totalCreated}`);

    // Verify subscriptions
    const { data: verifyData, error: verifyError } = await supabase
      .from('business_categories')
      .select('*', { count: 'exact' });

    if (verifyError) {
      console.error('Error verifying subscriptions:', verifyError);
    } else {
      console.log(`Verified subscriptions in database: ${verifyData.length}`);
    }

    // Show subscription summary by business type
    console.log('\n📋 SUBSCRIPTION SUMMARY BY BUSINESS TYPE');
    console.log('='.repeat(50));

    const { data: summaryData, error: summaryError } = await supabase
      .from('business_categories')
      .select(`
        business_id,
        businesses!inner(
          name,
          business_types!inner(
            name
          )
        ),
        categories!inner(
          name
        )
      `);

    if (summaryError) {
      console.error('Error getting summary:', summaryError);
    } else {
      const byType = {};
      summaryData.forEach(sub => {
        const typeName = sub.businesses.business_types.name;
        if (!byType[typeName]) byType[typeName] = [];
        byType[typeName].push(sub);
      });

      Object.keys(byType).forEach(type => {
        console.log(`\n🏢 ${type}: ${byType[type].length} subscriptions`);
        const businessGroups = {};
        byType[type].forEach(sub => {
          const businessName = sub.businesses.name;
          if (!businessGroups[businessName]) businessGroups[businessName] = [];
          businessGroups[businessName].push(sub.categories.name);
        });
        
        Object.keys(businessGroups).forEach(businessName => {
          console.log(`   • ${businessName}: ${businessGroups[businessName].join(', ')}`);
        });
      });
    }

  } catch (error) {
    console.error('Error subscribing businesses:', error);
  }
}

subscribeBusinesses()
  .then(() => {
    console.log('\n✨ Business subscription completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Subscription failed:', error);
    process.exit(1);
  });
