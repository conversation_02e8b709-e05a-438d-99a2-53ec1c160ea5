import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('business_id')

    if (!businessId) {
      return NextResponse.json(
        { error: "business_id is required" },
        { status: 400 }
      )
    }

    // Get the primary business manager for this business
    const { data: businessManager, error: managerError } = await supabase
      .from('business_managers')
      .select(`
        user_id,
        is_primary,
        users!inner(
          id,
          auth_id,
          email,
          first_name,
          last_name
        )
      `)
      .eq('business_id', businessId)
      .eq('is_primary', true)
      .single()

    if (managerError) {
      // If no primary manager found, get any manager
      const { data: anyManager, error: anyManagerError } = await supabase
        .from('business_managers')
        .select(`
          user_id,
          is_primary,
          users!inner(
            id,
            auth_id,
            email,
            first_name,
            last_name
          )
        `)
        .eq('business_id', businessId)
        .limit(1)
        .single()

      if (anyManagerError || !anyManager) {
        return NextResponse.json(
          { error: "No business manager found for this business" },
          { status: 404 }
        )
      }

      return NextResponse.json({
        user_id: anyManager.user_id,
        auth_id: anyManager.users.auth_id,
        is_primary: anyManager.is_primary,
        user: anyManager.users
      })
    }

    return NextResponse.json({
      user_id: businessManager.user_id,
      auth_id: businessManager.users.auth_id,
      is_primary: businessManager.is_primary,
      user: businessManager.users
    })

  } catch (error) {
    console.error('Error fetching business manager:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
