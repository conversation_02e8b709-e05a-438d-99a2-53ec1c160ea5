/**
 * Utility functions for highlighting search terms in text
 */

import React from 'react'

/**
 * Highlights search terms in text with HTML markup
 */
export function highlightSearchTerm(text: string, searchTerm: string): React.ReactNode {
  if (!searchTerm || !text) return text

  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  const parts = text.split(regex)

  return parts.map((part, index) => {
    if (regex.test(part)) {
      return (
        <mark 
          key={index} 
          className="bg-yellow-200 text-yellow-900 px-1 rounded"
        >
          {part}
        </mark>
      )
    }
    return part
  })
}

/**
 * Check if text contains search term (case insensitive)
 */
export function containsSearchTerm(text: string, searchTerm: string): boolean {
  if (!searchTerm || !text) return false
  return text.toLowerCase().includes(searchTerm.toLowerCase())
}

/**
 * Get search match score for sorting results by relevance
 */
export function getSearchScore(text: string, searchTerm: string): number {
  if (!searchTerm || !text) return 0
  
  const lowerText = text.toLowerCase()
  const lowerTerm = searchTerm.toLowerCase()
  
  // Exact match gets highest score
  if (lowerText === lowerTerm) return 100
  
  // Starts with search term gets high score
  if (lowerText.startsWith(lowerTerm)) return 80
  
  // Contains search term gets medium score
  if (lowerText.includes(lowerTerm)) return 60
  
  // No match
  return 0
}
