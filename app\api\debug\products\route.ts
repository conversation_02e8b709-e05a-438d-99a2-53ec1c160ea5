import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== DEBUG PRODUCTS API ===")
    
    // Parse URL to get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')
    console.log("businessIdParam:", businessIdParam)

    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()
    console.log("Session error:", sessionError)
    console.log("Session user:", session?.user?.email)

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json({
        error: "Authentication required",
        debug: { sessionError, hasSession: !!session }
      }, { status: 401 })
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role")
      .eq("auth_id", session.user.id)
      .single()

    console.log("User profile error:", userError)
    console.log("User profile:", userProfile)

    if (userError) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json({
        error: "User profile error",
        debug: { userError, authId: session.user.id }
      }, { status: 500 })
    }

    if (!userProfile) {
      console.error("User profile not found")
      return NextResponse.json({
        error: "User profile not found",
        debug: { authId: session.user.id }
      }, { status: 404 })
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    console.log("User roles:", { isBusinessManager, isAdmin, isSuperAdmin })

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json({
        error: "Insufficient permissions",
        debug: { role: userProfile.role }
      }, { status: 403 })
    }

    // Determine which business ID to use
    let businessId: number | null = null

    if (businessIdParam && (isAdmin || isSuperAdmin)) {
      businessId = parseInt(businessIdParam)
      console.log(`Admin user requesting products for business ID: ${businessId}`)
    } else if (isBusinessManager) {
      const { data: businessManager, error: bmError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()
      
      console.log("Business manager lookup:", { businessManager, bmError })
      
      if (bmError || !businessManager) {
        console.error("Business manager without a business association:", bmError)
        return NextResponse.json({
          error: "No business associated with this account",
          debug: { bmError, userId: userProfile.id }
        }, { status: 400 })
      }
      
      businessId = businessManager.business_id
      console.log(`Business manager requesting products for business ID: ${businessId}`)
    } else if (isAdmin || isSuperAdmin) {
      businessId = null
      console.log("Admin user requesting all products")
    }

    // Test direct product query
    console.log("Testing direct product query...")
    const { data: directProducts, error: directError } = await adminClient
      .from("products")
      .select("id, name, business_id")
      .eq("business_id", 4)
      .limit(5)

    console.log("Direct products query:", { directProducts, directError })

    // Test with business filter
    let query = adminClient
      .from("products")
      .select("id, name, business_id, price, is_available")
      .order("name")

    if (businessId) {
      query = query.eq("business_id", businessId)
    }

    const { data: products, error: productsError } = await query.limit(10)

    console.log("Filtered products query:", { products, productsError, businessId })

    return NextResponse.json({
      debug: {
        businessIdParam,
        userProfile,
        roles: { isBusinessManager, isAdmin, isSuperAdmin },
        businessId,
        directProducts,
        directError,
        products,
        productsError
      },
      products: products || []
    })

  } catch (error: any) {
    console.error("Error in debug products API:", error)
    return NextResponse.json({
      error: error.message,
      debug: { stack: error.stack }
    }, { status: 500 })
  }
}
