import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import OrderConfirmationClient from './client';
import { createClient } from '@supabase/supabase-js';

export const metadata: Metadata = {
  title: 'Order Confirmation | Loop Jersey',
  description: 'Thank you for your order with Loop Jersey.',
};

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Server component for order confirmation
 * Verifies the order exists before rendering the client component
 */
export default async function OrderConfirmationPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Await searchParams in Next.js 15
  const params = await searchParams;

  // PHASE 2 STEP 3: Support sessionId for multi-business orders
  const orderNumber = params.orderNumber as string;
  const orderId = params.orderId as string;
  const sessionId = params.sessionId as string;
  const orderIdentifier = sessionId || orderNumber || orderId;

  if (!orderIdentifier) {
    console.error('❌ ORDER CONFIRMATION: No order number, ID, or session ID provided in URL');
    redirect('/order-failed?error=No+order+identifier+provided');
  }

  try {
    console.log('🔍 ORDER CONFIRMATION: Verifying order on server side:', orderIdentifier);

    // PHASE 2 STEP 3: Handle session-based multi-business orders
    if (sessionId) {
      console.log('🔍 ORDER CONFIRMATION: Session-based multi-business order detected:', sessionId);

      // For session-based orders, verify at least one order exists with this session_id
      const { data: sessionOrders, error: sessionError } = await supabase
        .from('orders')
        .select('id, order_number, status, created_at, business_name')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (sessionError) {
        console.error('❌ ORDER CONFIRMATION: Error verifying session orders:', sessionError);
        redirect(`/order-failed?error=${encodeURIComponent(sessionError.message)}&orderIdentifier=${orderIdentifier}`);
      }

      if (!sessionOrders || sessionOrders.length === 0) {
        console.warn('⚠️ ORDER CONFIRMATION: No orders found for session:', sessionId);
        redirect(`/order-failed?error=${encodeURIComponent('No orders found for session')}&orderIdentifier=${orderIdentifier}`);
      }

      console.log(`✅ ORDER CONFIRMATION: Found ${sessionOrders.length} orders for session:`, sessionOrders.map(o => `${o.business_name} (${o.order_number})`));

      // Use the first order as the verified order for the client component
      const data = sessionOrders[0];
      return <OrderConfirmationClient orderIdentifier={orderIdentifier} verifiedOrder={data} isMultiBusiness={sessionOrders.length > 1} />;
    }

    // Single business order verification (existing logic)
    // If we have an order number, search by order_number, otherwise search by id
    let query = supabase
      .from('orders')
      .select('id, order_number, status, created_at');

    if (orderNumber) {
      query = query.eq('order_number', orderNumber);
    } else {
      query = query.eq('id', orderId);
    }

    const { data, error } = await query.single();

    if (error) {
      console.error('❌ ORDER CONFIRMATION: Error verifying order:', error);
      redirect(`/order-failed?error=${encodeURIComponent(error.message)}&orderIdentifier=${orderIdentifier}`);
    }

    if (!data) {
      console.warn('⚠️ ORDER CONFIRMATION: Order not found:', orderIdentifier);
      redirect(`/order-failed?error=${encodeURIComponent('Order not found')}&orderIdentifier=${orderIdentifier}`);
    }

    console.log('✅ ORDER CONFIRMATION: Order verified on server side:', {
      id: data.id,
      orderNumber: data.order_number,
      status: data.status,
      createdAt: data.created_at
    });

    // If we get here, the order exists, so render the client component
    return <OrderConfirmationClient orderIdentifier={orderIdentifier} verifiedOrder={data} isMultiBusiness={false} />;
  } catch (error: any) {
    console.error('❌ ORDER CONFIRMATION: Unexpected error:', error);
    redirect(`/order-failed?error=${encodeURIComponent(error?.message || 'Unknown error')}&orderIdentifier=${orderIdentifier}`);
  }
}
