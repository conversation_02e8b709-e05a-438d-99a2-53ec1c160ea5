"use client"

import React, { useState } from "react"
import {
  <PERSON><PERSON>in,
  <PERSON>ader2,
  AlertCircle,
  CheckCircle,
  ArrowLeft,
  User,
  Home
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { updateAllBusinessCoordinates } from "@/lib/address-utils"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

export default function UpdateCoordinatesPage() {
  const { toast } = useToast()

  // Business coordinates state
  const [isUpdating, setIsUpdating] = useState(false)
  const [results, setResults] = useState<{
    success: number;
    failed: number;
    total: number;
    failedBusinesses?: Array<{ id: string; address: string; reason: string }>;
  } | null>(null)
  const [progress, setProgress] = useState(0)

  // User coordinates state
  const [isUpdatingUsers, setIsUpdatingUsers] = useState(false)
  const [userResults, setUserResults] = useState<any>(null)
  const [userProgress, setUserProgress] = useState(0)

  // Address coordinates state
  const [isUpdatingAddresses, setIsUpdatingAddresses] = useState(false)
  const [addressResults, setAddressResults] = useState<any>(null)
  const [addressProgress, setAddressProgress] = useState(0)

  const handleUpdateAll = async () => {
    if (isUpdating) return

    setIsUpdating(true)
    setProgress(10) // Start progress

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 5
          return newProgress < 90 ? newProgress : prev
        })
      }, 500)

      // Run the batch update
      const results = await updateAllBusinessCoordinates()

      // Clear the interval and set final progress
      clearInterval(progressInterval)
      setProgress(100)

      // Set results
      setResults(results)

      // Show toast
      if (results.success > 0) {
        toast({
          title: "Coordinates updated",
          description: `Successfully updated ${results.success} out of ${results.total} businesses.`,
        })
      } else {
        toast({
          variant: "destructive",
          title: "Update failed",
          description: "Could not update any business coordinates. Please check the logs.",
        })
      }
    } catch (error: any) {
      console.error("Error in handleUpdateAll:", error)
      toast({
        variant: "destructive",
        title: "Error updating coordinates",
        description: error.message || "An unexpected error occurred",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleUpdateUserCoordinates = async () => {
    if (isUpdatingUsers) return

    setIsUpdatingUsers(true)
    setUserProgress(10) // Start progress

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUserProgress(prev => {
          const newProgress = prev + 5
          return newProgress < 90 ? newProgress : prev
        })
      }, 500)

      // Call the API to update user coordinates
      const response = await fetch('/api/admin/update-user-coordinates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      // Clear the interval and set final progress
      clearInterval(progressInterval)
      setUserProgress(100)

      // Set results
      setUserResults(data)

      // Show toast
      if (data.success > 0) {
        toast({
          title: "User coordinates updated",
          description: `Successfully updated ${data.success} out of ${data.total} users.`,
        })
      } else {
        toast({
          variant: "destructive",
          title: "User update failed",
          description: "Could not update any user coordinates. Please check the logs.",
        })
      }
    } catch (error: any) {
      console.error("Error updating user coordinates:", error)
      toast({
        variant: "destructive",
        title: "Error updating user coordinates",
        description: error.message || "An unexpected error occurred",
      })
    } finally {
      setIsUpdatingUsers(false)
    }
  }

  const handleUpdateAddressCoordinates = async () => {
    if (isUpdatingAddresses) return

    setIsUpdatingAddresses(true)
    setAddressProgress(10) // Start progress

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setAddressProgress(prev => {
          const newProgress = prev + 5
          return newProgress < 90 ? newProgress : prev
        })
      }, 500)

      // Call the API to update address coordinates
      const response = await fetch('/api/admin/update-user-addresses-coordinates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      // Clear the interval and set final progress
      clearInterval(progressInterval)
      setAddressProgress(100)

      // Set results
      setAddressResults(data)

      // Show toast
      if (data.success > 0) {
        toast({
          title: "Address coordinates updated",
          description: `Successfully updated ${data.success} out of ${data.total} addresses.`,
        })
      } else {
        toast({
          variant: "destructive",
          title: "Address update failed",
          description: "Could not update any address coordinates. Please check the logs.",
        })
      }
    } catch (error: any) {
      console.error("Error updating address coordinates:", error)
      toast({
        variant: "destructive",
        title: "Error updating address coordinates",
        description: error.message || "An unexpected error occurred",
      })
    } finally {
      setIsUpdatingAddresses(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Link href="/admin" className="mr-4">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Update Coordinates</h1>
          <p className="text-gray-500">Batch update coordinates for businesses, users, and addresses</p>
        </div>
      </div>

      <Tabs defaultValue="businesses" className="max-w-2xl mx-auto">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="businesses">
            <MapPin className="mr-2 h-4 w-4" />
            Businesses
          </TabsTrigger>
          <TabsTrigger value="users">
            <User className="mr-2 h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="addresses">
            <Home className="mr-2 h-4 w-4" />
            Addresses
          </TabsTrigger>
        </TabsList>

        {/* Business Coordinates Tab */}
        <TabsContent value="businesses">
          <Card>
            <CardHeader>
              <CardTitle>Batch Update Business Coordinates</CardTitle>
              <CardDescription>
                This tool will geocode addresses for all businesses in the database and update their coordinates.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Important</AlertTitle>
                <AlertDescription>
                  This process will attempt to geocode all business addresses in the database.
                  Make sure all businesses have valid addresses before proceeding.
                  This operation may take some time depending on the number of businesses.
                </AlertDescription>
              </Alert>

              {isUpdating && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Updating coordinates...</span>
                    <span className="text-sm text-gray-500">{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}

              {results && !isUpdating && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Results</h3>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-white p-3 rounded-md shadow-sm">
                      <p className="text-gray-500 text-sm">Total</p>
                      <p className="text-lg font-bold">{results.total}</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-md shadow-sm">
                      <p className="text-green-600 text-sm">Success</p>
                      <p className="text-lg font-bold text-green-600">{results.success}</p>
                    </div>
                    <div className="bg-red-50 p-3 rounded-md shadow-sm">
                      <p className="text-red-600 text-sm">Failed</p>
                      <p className="text-lg font-bold text-red-600">{results.failed}</p>
                    </div>
                  </div>

                  {results.failed > 0 && (
                    <div className="space-y-4 mt-4">
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Some updates failed</AlertTitle>
                        <AlertDescription>
                          {results.failed} businesses could not be updated. This may be due to invalid addresses or geocoding API limitations.
                        </AlertDescription>
                      </Alert>

                      {results.failedBusinesses && results.failedBusinesses.length > 0 && (
                        <div className="bg-white p-4 rounded-md border border-red-200 mt-4">
                          <h4 className="font-medium text-red-700 mb-2">Failed Businesses</h4>
                          <div className="max-h-60 overflow-y-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left">ID</th>
                                  <th className="px-4 py-2 text-left">Address</th>
                                  <th className="px-4 py-2 text-left">Reason</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-100">
                                {results.failedBusinesses.map((business, index) => (
                                  <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-4 py-2 font-mono text-xs">{business.id}</td>
                                    <td className="px-4 py-2">{business.address || '(empty)'}</td>
                                    <td className="px-4 py-2 text-red-600">{business.reason}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {results.success === results.total && (
                    <Alert className="mt-4">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertTitle>All updates successful</AlertTitle>
                      <AlertDescription>
                        All business coordinates have been successfully updated.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full bg-emerald-600 hover:bg-emerald-700"
                onClick={handleUpdateAll}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Business Coordinates...
                  </>
                ) : (
                  <>
                    <MapPin className="mr-2 h-4 w-4" />
                    Update All Business Coordinates
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* User Coordinates Tab */}
        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Batch Update User Coordinates</CardTitle>
              <CardDescription>
                This tool will geocode addresses for all users in the database and update their coordinates.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Important</AlertTitle>
                <AlertDescription>
                  This process will attempt to geocode all user addresses in the database.
                  Make sure users have valid addresses before proceeding.
                  This operation may take some time depending on the number of users.
                </AlertDescription>
              </Alert>

              {isUpdatingUsers && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Updating coordinates...</span>
                    <span className="text-sm text-gray-500">{userProgress}%</span>
                  </div>
                  <Progress value={userProgress} className="h-2" />
                </div>
              )}

              {userResults && !isUpdatingUsers && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Results</h3>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-white p-3 rounded-md shadow-sm">
                      <p className="text-gray-500 text-sm">Total</p>
                      <p className="text-lg font-bold">{userResults.total}</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-md shadow-sm">
                      <p className="text-green-600 text-sm">Success</p>
                      <p className="text-lg font-bold text-green-600">{userResults.success}</p>
                    </div>
                    <div className="bg-red-50 p-3 rounded-md shadow-sm">
                      <p className="text-red-600 text-sm">Failed</p>
                      <p className="text-lg font-bold text-red-600">{userResults.failed}</p>
                    </div>
                  </div>

                  {userResults.failed > 0 && (
                    <div className="space-y-4 mt-4">
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Some updates failed</AlertTitle>
                        <AlertDescription>
                          {userResults.failed} users could not be updated. This may be due to invalid addresses or geocoding API limitations.
                        </AlertDescription>
                      </Alert>

                      {userResults.failures && userResults.failures.length > 0 && (
                        <div className="bg-white p-4 rounded-md border border-red-200 mt-4">
                          <h4 className="font-medium text-red-700 mb-2">Failed Users</h4>
                          <div className="max-h-60 overflow-y-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left">ID</th>
                                  <th className="px-4 py-2 text-left">Email</th>
                                  <th className="px-4 py-2 text-left">Reason</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-100">
                                {userResults.failures.map((failure, index) => (
                                  <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-4 py-2 font-mono text-xs">{failure.id}</td>
                                    <td className="px-4 py-2">{failure.email || '(empty)'}</td>
                                    <td className="px-4 py-2 text-red-600">{failure.reason}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {userResults.success === userResults.total && userResults.total > 0 && (
                    <Alert className="mt-4">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertTitle>All updates successful</AlertTitle>
                      <AlertDescription>
                        All user coordinates have been successfully updated.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full bg-emerald-600 hover:bg-emerald-700"
                onClick={handleUpdateUserCoordinates}
                disabled={isUpdatingUsers}
              >
                {isUpdatingUsers ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating User Coordinates...
                  </>
                ) : (
                  <>
                    <User className="mr-2 h-4 w-4" />
                    Update All User Coordinates
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Address Coordinates Tab */}
        <TabsContent value="addresses">
          <Card>
            <CardHeader>
              <CardTitle>Batch Update Address Coordinates</CardTitle>
              <CardDescription>
                This tool will geocode all user addresses in the database and update their coordinates. It will also fix any addresses with incorrectly formatted coordinates.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Important</AlertTitle>
                <AlertDescription>
                  This process will:
                  <ul className="list-disc pl-5 mt-2">
                    <li>Fix addresses with incorrectly formatted coordinates (e.g., "POINT(-2.134973 49.246555)")</li>
                    <li>Extract latitude and longitude from existing coordinate strings</li>
                    <li>Geocode addresses that don't have coordinates</li>
                  </ul>
                  This operation may take some time depending on the number of addresses.
                </AlertDescription>
              </Alert>

              {isUpdatingAddresses && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Updating coordinates...</span>
                    <span className="text-sm text-gray-500">{addressProgress}%</span>
                  </div>
                  <Progress value={addressProgress} className="h-2" />
                </div>
              )}

              {addressResults && !isUpdatingAddresses && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Results</h3>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-white p-3 rounded-md shadow-sm">
                      <p className="text-gray-500 text-sm">Total</p>
                      <p className="text-lg font-bold">{addressResults.total}</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-md shadow-sm">
                      <p className="text-green-600 text-sm">Success</p>
                      <p className="text-lg font-bold text-green-600">{addressResults.success}</p>
                    </div>
                    <div className="bg-red-50 p-3 rounded-md shadow-sm">
                      <p className="text-red-600 text-sm">Failed</p>
                      <p className="text-lg font-bold text-red-600">{addressResults.failed}</p>
                    </div>
                  </div>

                  {addressResults.failed > 0 && (
                    <div className="space-y-4 mt-4">
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Some updates failed</AlertTitle>
                        <AlertDescription>
                          {addressResults.failed} addresses could not be updated. This may be due to invalid addresses or geocoding API limitations.
                        </AlertDescription>
                      </Alert>

                      {addressResults.failures && addressResults.failures.length > 0 && (
                        <div className="bg-white p-4 rounded-md border border-red-200 mt-4">
                          <h4 className="font-medium text-red-700 mb-2">Failed Addresses</h4>
                          <div className="max-h-60 overflow-y-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left">ID</th>
                                  <th className="px-4 py-2 text-left">Reason</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-100">
                                {addressResults.failures.map((failure, index) => (
                                  <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-4 py-2 font-mono text-xs">{failure.id}</td>
                                    <td className="px-4 py-2 text-red-600">{failure.reason}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {addressResults.success === addressResults.total && addressResults.total > 0 && (
                    <Alert className="mt-4">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertTitle>All updates successful</AlertTitle>
                      <AlertDescription>
                        All address coordinates have been successfully updated.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full bg-emerald-600 hover:bg-emerald-700"
                onClick={handleUpdateAddressCoordinates}
                disabled={isUpdatingAddresses}
              >
                {isUpdatingAddresses ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Address Coordinates...
                  </>
                ) : (
                  <>
                    <Home className="mr-2 h-4 w-4" />
                    Update All Address Coordinates
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
