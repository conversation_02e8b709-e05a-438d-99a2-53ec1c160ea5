"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Package, Thermometer, Scale, AlertTriangle } from 'lucide-react'
import { OrderAnalysisData, WeightClassData } from '@/app/api/driver/order-analysis/[orderId]/route'

interface OrderWeightChartProps {
  orderId: string | number
  className?: string
  compact?: boolean // For use in order lists vs detailed view
}

export function OrderWeightChart({ orderId, className = "", compact = false }: OrderWeightChartProps) {
  const [analysisData, setAnalysisData] = useState<OrderAnalysisData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchOrderAnalysis()
  }, [orderId])

  const fetchOrderAnalysis = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/driver/order-analysis/${orderId}`)
      const result = await response.json()

      if (response.ok && result.success) {
        setAnalysisData(result.data)
      } else {
        setError(result.error || 'Failed to load order analysis')
      }
    } catch (err) {
      console.error('Error fetching order analysis:', err)
      setError('Failed to load order analysis')
    } finally {
      setLoading(false)
    }
  }

  const getThermalColor = (thermal: 'hot' | 'cold' | 'none' | 'undeclared'): string => {
    switch (thermal) {
      case 'hot': return 'bg-red-500'
      case 'cold': return 'bg-blue-500'
      case 'none': return 'bg-green-500'
      case 'undeclared': return 'bg-gray-800'
      default: return 'bg-gray-800' // Default to undeclared
    }
  }

  const getThermalTextColor = (thermal: 'hot' | 'cold' | 'none' | 'undeclared'): string => {
    switch (thermal) {
      case 'hot': return 'text-red-700'
      case 'cold': return 'text-blue-700'
      case 'none': return 'text-green-700'
      case 'undeclared': return 'text-gray-700'
      default: return 'text-gray-700' // Default to undeclared
    }
  }

  const renderWeightBar = (weightData: WeightClassData, maxItems: number) => {
    const { weight_class, hot_items, cold_items, none_items, undeclared_thermal_items, total_items } = weightData
    const barWidth = maxItems > 0 ? (total_items / maxItems) * 100 : 0

    return (
      <div key={weight_class} className="flex items-center space-x-3 mb-2">
        {/* Weight class label */}
        <div className="w-20 text-sm font-medium text-gray-700 text-right">
          {weight_class === 'undeclared' ? (
            <span className="text-amber-600 flex items-center justify-end">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Undeclared
            </span>
          ) : (
            weight_class
          )}
        </div>

        {/* Bar container */}
        <div className="flex-1 relative">
          <div className="h-6 bg-gray-100 rounded-md overflow-hidden relative">
            {/* Stacked bar segments */}
            <div className="flex h-full" style={{ width: `${barWidth}%` }}>
              {/* Hot items */}
              {hot_items > 0 && (
                <div
                  className="bg-red-500 h-full relative flex"
                  style={{ width: `${(hot_items / total_items) * 100}%` }}
                  title={`${hot_items} hot items`}
                >
                  {/* Item dividers */}
                  {Array.from({ length: hot_items - 1 }, (_, i) => (
                    <div
                      key={i}
                      className="absolute top-0 bottom-0 w-0.5 bg-black"
                      style={{ left: `${((i + 1) / hot_items) * 100}%` }}
                    />
                  ))}
                </div>
              )}
              {/* Cold items */}
              {cold_items > 0 && (
                <div
                  className="bg-blue-500 h-full relative flex"
                  style={{ width: `${(cold_items / total_items) * 100}%` }}
                  title={`${cold_items} cold items`}
                >
                  {/* Item dividers */}
                  {Array.from({ length: cold_items - 1 }, (_, i) => (
                    <div
                      key={i}
                      className="absolute top-0 bottom-0 w-0.5 bg-black"
                      style={{ left: `${((i + 1) / cold_items) * 100}%` }}
                    />
                  ))}
                </div>
              )}
              {/* None thermal items */}
              {none_items > 0 && (
                <div
                  className="bg-green-500 h-full relative flex"
                  style={{ width: `${(none_items / total_items) * 100}%` }}
                  title={`${none_items} no thermal items`}
                >
                  {/* Item dividers */}
                  {Array.from({ length: none_items - 1 }, (_, i) => (
                    <div
                      key={i}
                      className="absolute top-0 bottom-0 w-0.5 bg-black"
                      style={{ left: `${((i + 1) / none_items) * 100}%` }}
                    />
                  ))}
                </div>
              )}
              {/* Undeclared thermal items */}
              {undeclared_thermal_items > 0 && (
                <div
                  className="bg-gray-800 h-full relative flex"
                  style={{ width: `${(undeclared_thermal_items / total_items) * 100}%` }}
                  title={`${undeclared_thermal_items} undeclared thermal items`}
                >
                  {/* Item dividers */}
                  {Array.from({ length: undeclared_thermal_items - 1 }, (_, i) => (
                    <div
                      key={i}
                      className="absolute top-0 bottom-0 w-0.5 bg-black"
                      style={{ left: `${((i + 1) / undeclared_thermal_items) * 100}%` }}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Item count */}
        <div className="w-8 text-sm font-medium text-gray-900 text-center">
          {total_items}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-6 flex-1" />
                <Skeleton className="h-4 w-6" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !analysisData) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center text-gray-500">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{error || 'No order data available'}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const { weight_distribution, thermal_summary, total_items } = analysisData
  const maxItems = Math.max(...weight_distribution.map(w => w.total_items))

  if (compact) {
    // Compact version for order lists
    return (
      <div className={`${className} p-3 bg-gray-50 rounded-lg`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium">{total_items} items</span>
          </div>
          {thermal_summary.requires_thermal_bag && (
            <Badge variant="outline" className="text-xs">
              <Thermometer className="h-3 w-3 mr-1" />
              Thermal
            </Badge>
          )}
        </div>

        <div className="space-y-1">
          {weight_distribution.slice(0, 3).map(weightData => renderWeightBar(weightData, maxItems))}
          {weight_distribution.length > 3 && (
            <div className="text-xs text-gray-500 text-center">
              +{weight_distribution.length - 3} more weight classes
            </div>
          )}
        </div>
      </div>
    )
  }

  // Full version for detailed order view
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Scale className="h-5 w-5 text-gray-600" />
          <span>Order Weight Analysis</span>
        </CardTitle>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>{total_items} total items</span>
          {thermal_summary.requires_thermal_bag && (
            <Badge variant="outline" className="text-xs">
              <Thermometer className="h-3 w-3 mr-1" />
              Thermal bag required
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* Weight distribution chart */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-700">Weight Distribution</h4>
            <div className="text-xs text-gray-500">Items →</div>
          </div>

          <div className="space-y-2">
            {weight_distribution.map(weightData => renderWeightBar(weightData, maxItems))}
          </div>
        </div>

        {/* Legend */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Thermal Requirements</h4>
          <div className="flex flex-wrap gap-3">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span className="text-sm text-gray-600">Hot ({thermal_summary.hot_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Cold ({thermal_summary.cold_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-600">None ({thermal_summary.none_items})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-800 rounded"></div>
              <span className="text-sm text-gray-600">Undeclared ({thermal_summary.undeclared_thermal_items})</span>
            </div>
          </div>
        </div>

        {/* Undeclared attributes warnings */}
        {(weight_distribution.some(w => w.weight_class === 'undeclared') || thermal_summary.undeclared_thermal_items > 0) && (
          <div className="mt-4 space-y-3">
            {weight_distribution.some(w => w.weight_class === 'undeclared') && (
              <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-amber-800">Undeclared Weight Items</p>
                    <p className="text-xs text-amber-700 mt-1">
                      Some items don't have specified weights. Be prepared for varying item sizes.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {thermal_summary.undeclared_thermal_items > 0 && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-gray-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Undeclared Thermal Requirements</p>
                    <p className="text-xs text-gray-700 mt-1">
                      {thermal_summary.undeclared_thermal_items} items don't have specified thermal requirements. Check with business if thermal bags are needed.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
