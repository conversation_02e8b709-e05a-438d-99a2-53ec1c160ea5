"use client"

import React, { useState } from "react"
import { format, formatDistanceToNow } from "date-fns"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Clock,
  Eye,
  MoreHorizontal,
  Printer,
  RefreshCcw,
  Truck,
  CheckCircle2,
  XCircle,
  AlertCircle,
  ChefHat,
  Bell,
  BellOff,
  Save,
  AlertTriangle,
  Edit,
  Check,
  X,
  BarChart3
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { PrintableOrderSlip } from "./printable-order-slip"
import { OrderWeightChart } from "@/components/driver/order-weight-chart"

// Helper function to format variant and customization details
const formatItemDetails = (item: OrderItem): string[] => {
  const details: string[] = [];

  // Add variant information (check both possible field names)
  const variantName = item.variant_name || item.variantName;
  if (variantName) {
    details.push(`Size: ${variantName}`);
  }

  // Add customization information
  if (item.customizations && Array.isArray(item.customizations)) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  return details;
};

// Define the Order type with enhanced fields
export interface OrderItem {
  id: number | string
  order_id?: number | string
  product_id?: number | string
  name?: string // cart_items.name column
  product_name?: string // fallback for compatibility
  product_description?: string
  product_image?: string
  image_url?: string // cart_items.image_url column
  variant_id?: number | string
  variant_name?: string // Now properly populated from product_variants table
  variantName?: string // Alternative field name for compatibility
  quantity: number
  unit_price?: number
  price?: number // cart_items.price column
  total_price?: number
  customizations?: Array<{
    groupName: string
    options: Array<{
      name: string
      price: number
    }>
  }> // Now properly structured customizations
  notes?: string
}

export interface Order {
  id: number | string
  order_id?: string
  order_number?: string
  customer_name?: string
  customer_email?: string
  customer_phone?: string
  customer_address?: string
  parish?: string
  status: string
  total?: number
  subtotal?: number
  delivery_fee?: number
  service_fee?: number
  created_at: string
  updated_at?: string
  business_id?: number
  business_name?: string
  business_type?: string
  delivery_type?: string
  delivery_address?: string
  delivery_instructions?: string
  items_count?: number
  total_items?: number
  preparation_time?: number | null
  ready_time?: string | null
  estimated_delivery_time?: string | number | null
  scheduled_time?: string | null
  scheduled_for?: string | null
  scheduled_delivery_time?: string | null
  delivery_timing?: string
  asap?: boolean
  priority_level?: number
  driver_id?: string | null
  driver_name?: string | null
  payment_method?: string
  payment_status?: string
  notification_status?: {
    customer_notified: boolean
    business_notified: boolean
    driver_notified: boolean
    last_notification_at?: string
  }
  items?: OrderItem[]
  // PHASE 3 STEP 8: Add delivery fulfillment information
  delivery_fulfillment?: string // 'loop' or 'internal'
}

interface EnhancedOrdersTableProps {
  orders: Order[]
  onViewOrder?: (order: Order) => void
  onUpdateStatus?: (order: Order, newStatus: string) => void
  onPrintOrder?: (order: Order) => void
  onRefreshOrders?: () => void
  onRefresh?: () => void
  isLoading?: boolean
  showBusinessColumn?: boolean
}

export function EnhancedOrdersTable({
  orders,
  onViewOrder,
  onUpdateStatus,
  onPrintOrder,
  onRefreshOrders,
  onRefresh,
  isLoading = false,
  showBusinessColumn = false
}: EnhancedOrdersTableProps) {
  const { toast } = useToast()
  const [expandedOrderId, setExpandedOrderId] = useState<string | number | null>(null)
  const [orderItems, setOrderItems] = useState<Record<string | number, OrderItem[]>>({})
  const [loadingItems, setLoadingItems] = useState<Record<string | number, boolean>>({})
  const [editingTimeEstimates, setEditingTimeEstimates] = useState<Record<string | number, boolean>>({})
  const [preparationTimes, setPreparationTimes] = useState<Record<string | number, number | string>>({})
  const [updatingTimes, setUpdatingTimes] = useState<Record<string | number, boolean>>({})
  const [updatingStatus, setUpdatingStatus] = useState<Record<string | number, boolean>>({})
  const [showPrintSlip, setShowPrintSlip] = useState<string | number | null>(null)
  const [showDeliveryMetrics, setShowDeliveryMetrics] = useState<string | number | null>(null)

  // Function to fetch order items
  const fetchOrderItems = async (orderId: string | number) => {
    try {
      setLoadingItems(prev => ({ ...prev, [orderId]: true }))

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch(`/api/business-admin/orders/${orderId}`, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error Response:', errorText)
        throw new Error(`Failed to fetch order items: ${response.status} - ${errorText}`)
      }

      const data = await response.json()

      if (data.items && Array.isArray(data.items)) {
        setOrderItems(prev => ({ ...prev, [orderId]: data.items }))
      }
    } catch (error) {
      console.error('Error fetching order items:', error)
      toast({
        title: "Error",
        description: "Failed to load order items",
        variant: "destructive"
      })
    } finally {
      setLoadingItems(prev => ({ ...prev, [orderId]: false }))
    }
  }

  // Function to enable editing ready time
  const enableTimeEstimateEditing = (order: Order) => {
    setEditingTimeEstimates(prev => ({ ...prev, [order.id]: true }))

    // Calculate current ready time - same logic as calculateReadyTime but without timezone adjustments
    const orderTime = new Date(order.created_at)
    const currentReadyTime = order.ready_time
      ? new Date(order.ready_time)
      : new Date(orderTime.getTime() + (order.preparation_time || 15) * 60000)

    // Format for datetime-local input in user's local timezone
    // This will automatically show the time in the user's local timezone
    const year = currentReadyTime.getFullYear()
    const month = String(currentReadyTime.getMonth() + 1).padStart(2, '0')
    const day = String(currentReadyTime.getDate()).padStart(2, '0')
    const hours = String(currentReadyTime.getHours()).padStart(2, '0')
    const minutes = String(currentReadyTime.getMinutes()).padStart(2, '0')

    const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`

    console.log('Setting up datetime input:', {
      orderId: order.id,
      originalReadyTime: currentReadyTime.toISOString(),
      localDisplayTime: currentReadyTime.toString(),
      formattedForInput: formattedTime
    })

    setPreparationTimes(prev => ({
      ...prev,
      [order.id]: formattedTime
    }))
  }

  // Function to cancel editing time estimates
  const cancelTimeEstimateEditing = (orderId: string | number) => {
    setEditingTimeEstimates(prev => ({ ...prev, [orderId]: false }))
  }

  // Function to update ready time
  const updatePreparationTime = async (order: Order) => {
    try {
      setUpdatingTimes(prev => ({ ...prev, [order.id]: true }))

      const readyTimeString = preparationTimes[order.id]

      // Validate input
      if (!readyTimeString) {
        toast({
          title: "Invalid Input",
          description: "Please select a ready time",
          variant: "destructive"
        })
        return
      }

      // Validate that the selected time is not in the past
      const selectedTime = new Date(readyTimeString)
      const now = new Date()

      if (selectedTime < now) {
        toast({
          title: "Invalid Time",
          description: "Ready time cannot be in the past",
          variant: "destructive"
        })
        return
      }

      // Validate that the selected time is not too far in the future (e.g., more than 24 hours)
      const maxTime = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24 hours from now
      if (selectedTime > maxTime) {
        toast({
          title: "Invalid Time",
          description: "Ready time cannot be more than 24 hours in the future",
          variant: "destructive"
        })
        return
      }

      // Convert the datetime-local string to ISO string
      // The datetime-local input gives us a string in the user's local timezone
      const readyTime = new Date(readyTimeString).toISOString()

      console.log('Updating ready time:', {
        orderId: order.id,
        originalInput: readyTimeString,
        convertedISO: readyTime,
        orderCreatedAt: order.created_at
      })

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/business-admin/orders/time-estimates', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          orderId: order.id,
          readyTime: readyTime,
          sendNotification: true
        })
      })

      if (!response.ok) {
        let errorData = {}
        let responseText = ''

        try {
          responseText = await response.text()
          errorData = JSON.parse(responseText)
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
          errorData = { error: responseText || 'Unknown error' }
        }

        const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`

        console.error('API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          url: response.url,
          responseText,
          errorData,
          requestBody: {
            orderId: order.id,
            readyTime: new Date(readyTimeString).toISOString(),
            sendNotification: true
          }
        })

        throw new Error(errorMessage)
      }

      const data = await response.json()
      console.log('Ready time update successful:', data)

      toast({
        title: "Success",
        description: "Ready time updated successfully",
        variant: "default"
      })

      // Disable editing mode
      setEditingTimeEstimates(prev => ({ ...prev, [order.id]: false }))

      // Refresh orders to get the updated data
      onRefreshOrders()

    } catch (error) {
      console.error('Error updating ready time:', error)

      // Provide more specific error messages
      let errorMessage = "Failed to update ready time"
      if (error instanceof Error) {
        if (error.message.includes('400')) {
          errorMessage = "Invalid request. Please check the selected time."
        } else if (error.message.includes('401')) {
          errorMessage = "Authentication failed. Please log in again."
        } else if (error.message.includes('404')) {
          errorMessage = "Order not found."
        } else if (error.message.includes('500')) {
          errorMessage = "Server error. Please try again later."
        } else if (error.message.length > 0 && error.message !== errorMessage) {
          errorMessage = error.message
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setUpdatingTimes(prev => ({ ...prev, [order.id]: false }))
    }
  }

  // Helper function to format time in Jersey timezone (BST/GMT)
  const formatJerseyTime = (utcDate: Date): string => {
    // Use Intl.DateTimeFormat with Europe/London timezone (same as Jersey)
    // This automatically handles BST/GMT transitions
    return new Intl.DateTimeFormat('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Europe/London'
    }).format(utcDate)
  }

  // Format currency helper
  const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined || amount === '') return "£0.00"

    try {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount

      if (isNaN(numAmount)) return "£0.00"

      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP',
        minimumFractionDigits: 2
      }).format(numAmount)
    } catch (err) {
      console.error("Error formatting currency:", err)
      return "£0.00"
    }
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    try {
      const date = new Date(dateString)
      return format(date, "MMM d, yyyy")
    } catch (err) {
      console.error("Error formatting date:", err)
      return "Invalid date"
    }
  }

  // Format time helper - displays time in Jersey timezone (BST/GMT)
  const formatTime = (dateString: string) => {
    if (!dateString) return ""
    try {
      const utcDate = new Date(dateString)
      return formatJerseyTime(utcDate)
    } catch (err) {
      console.error("Error formatting time:", err)
      return ""
    }
  }

  // Format relative time (e.g., "5 minutes ago")
  const formatRelativeTime = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (err) {
      console.error("Error formatting relative time:", err)
      return ""
    }
  }

  // Calculate time remaining until scheduled delivery
  const calculateTimeRemaining = (scheduledFor: string | null | undefined) => {
    if (!scheduledFor) return null

    try {
      const scheduledTime = new Date(scheduledFor)
      const now = new Date()

      // Compare UTC times directly - no timezone conversion needed for comparison
      if (scheduledTime < now) {
        return { overdue: true, timeString: formatDistanceToNow(scheduledTime) + " overdue" }
      }

      // If scheduled time is in the future
      return { overdue: false, timeString: formatDistanceToNow(scheduledTime) + " remaining" }
    } catch (err) {
      console.error("Error calculating time remaining:", err)
      return null
    }
  }

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'delivered':
        return "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100 hover:text-emerald-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'processing':
      case 'preparing':
        return "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:text-blue-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'ready':
        return "bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 hover:text-indigo-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'out_for_delivery':
      case 'out for delivery':
        return "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:text-purple-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'pending':
        return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 hover:text-amber-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'confirmed':
        return "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100 hover:text-yellow-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      case 'cancelled':
        return "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:text-slate-800 font-medium px-2 py-0.5 rounded-full flex items-center transition-colors"
    }
  }

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'out_for_delivery':
        return 'Out for Delivery'
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  // Format status for buttons (action text)
  const formatStatusAction = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'Confirm'
      case 'cancelled':
        return 'Cancel'
      case 'out_for_delivery':
        return 'Out for Delivery'
      case 'preparing':
        return 'Start Preparing'
      case 'ready':
        return 'Mark Ready'
      case 'delivered':
        return 'Mark Delivered'
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  // Get priority badge
  const getPriorityBadge = (priority: number | undefined) => {
    if (!priority) return null

    let color = ""
    let label = ""

    switch(priority) {
      case 1:
        color = "bg-red-100 text-red-800 border-red-200"
        label = "Urgent"
        break
      case 2:
        color = "bg-orange-100 text-orange-800 border-orange-200"
        label = "High"
        break
      case 3:
        color = "bg-blue-100 text-blue-800 border-blue-200"
        label = "Normal"
        break
      case 4:
        color = "bg-green-100 text-green-800 border-green-200"
        label = "Low"
        break
      case 5:
        color = "bg-gray-100 text-gray-800 border-gray-200"
        label = "Lowest"
        break
      default:
        color = "bg-blue-100 text-blue-800 border-blue-200"
        label = "Normal"
    }

    return (
      <Badge className={`${color} font-medium px-2 py-0.5 rounded-full text-xs`}>
        {label}
      </Badge>
    )
  }

  // Helper function to format the delivery type badge class
  const getDeliveryTypeBadgeClass = (type: string | undefined) => {
    if (!type) return "bg-green-50 text-green-700 border-green-200 rounded-full px-2 py-0.5"

    switch (type.toLowerCase()) {
      case "pickup":
        return "bg-blue-50 text-blue-700 border-blue-200 rounded-full px-2 py-0.5 flex items-center"
      case "delivery":
      case "asap":
      default:
        return "bg-green-50 text-green-700 border-green-200 rounded-full px-2 py-0.5 flex items-center"
    }
  }

  // Helper function to format the delivery type text
  const formatDeliveryType = (type: string | undefined) => {
    if (!type) return "Delivery"

    switch (type.toLowerCase()) {
      case "pickup":
        return "Pickup"
      case "scheduled":
        return "Scheduled"
      case "asap":
      case "delivery":
      default:
        return "Delivery"
    }
  }

  // Helper function to format the delivery timing (ASAP vs Scheduled)
  const formatDeliveryTiming = (timing: string | undefined, scheduledTime: string | null | undefined) => {
    if (scheduledTime) return "Scheduled"
    return timing || "ASAP"
  }

  // Get notification status icon
  const getNotificationIcon = (order: Order) => {
    if (!order.notification_status) return null

    const allNotified =
      order.notification_status.customer_notified &&
      order.notification_status.business_notified &&
      (order.notification_status.driver_notified || !order.driver_id)

    if (allNotified) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <Bell className="h-4 w-4 text-green-500" />
          </TooltipTrigger>
          <TooltipContent>
            <p>All parties notified</p>
            {order.notification_status.last_notification_at && (
              <p className="text-xs text-gray-500">
                {formatRelativeTime(order.notification_status.last_notification_at)}
              </p>
            )}
          </TooltipContent>
        </Tooltip>
      )
    }

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <BellOff className="h-4 w-4 text-amber-500" />
        </TooltipTrigger>
        <TooltipContent>
          <p>Notifications pending</p>
          <ul className="text-xs">
            {!order.notification_status.customer_notified && <li>• Customer not notified</li>}
            {!order.notification_status.business_notified && <li>• Business not notified</li>}
            {order.driver_id && !order.notification_status.driver_notified && <li>• Driver not notified</li>}
          </ul>
        </TooltipContent>
      </Tooltip>
    )
  }

  // Toggle expanded order details
  const toggleOrderDetails = (orderId: string | number) => {
    if (expandedOrderId === orderId) {
      setExpandedOrderId(null)
    } else {
      setExpandedOrderId(orderId)

      // Fetch order items if we don't have them yet
      if (!orderItems[orderId] && !loadingItems[orderId]) {
        fetchOrderItems(orderId)
      }
    }
  }

  // Handle status update with loading state
  const handleStatusUpdate = async (order: Order, status: string) => {
    try {
      setUpdatingStatus(prev => ({ ...prev, [order.id]: true }))
      await onUpdateStatus(order, status)
    } catch (error) {
      console.error('Error updating status:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to update order status: ${error instanceof Error ? error.message : 'Unknown error'}`,
      })
    } finally {
      setUpdatingStatus(prev => ({ ...prev, [order.id]: false }))
    }
  }

  // Handle print order slip
  const handlePrintOrder = (order: Order) => {
    // Fetch order items if we don't have them
    if (!orderItems[order.id] && !loadingItems[order.id]) {
      fetchOrderItems(order.id)
    }
    setShowPrintSlip(order.id)
  }

  const handleViewDeliveryMetrics = (order: Order) => {
    setShowDeliveryMetrics(order.id)
  }

  // Get next status options based on current status - only show the primary next action
  const getNextStatusOptions = (currentStatus: string) => {
    switch (currentStatus.toLowerCase()) {
      case 'pending':
        return ['confirmed'] // Only show Confirm button
      case 'confirmed':
        return ['preparing'] // Only show Start Preparing button
      case 'preparing':
        return ['ready'] // Only show Mark Ready button
      case 'ready':
        return ['out_for_delivery'] // Only show Out for Delivery button
      case 'out_for_delivery':
        return ['delivered'] // Only show Mark Delivered button
      case 'delivered':
      case 'completed':
      case 'cancelled':
        return [] // Terminal states
      default:
        return ['confirmed'] // Default to confirm for unknown states
    }
  }

  // Get cancel option separately - always available except for terminal states
  const canCancelOrder = (currentStatus: string) => {
    const terminalStates = ['delivered', 'completed', 'cancelled']
    return !terminalStates.includes(currentStatus.toLowerCase())
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Clock className="mr-1 h-3.5 w-3.5 text-yellow-600" />
      case 'confirmed':
        return <CheckCircle2 className="mr-1 h-3.5 w-3.5 text-yellow-600" />
      case 'preparing':
        return <ChefHat className="mr-1 h-3.5 w-3.5 text-blue-600" />
      case 'ready':
        return <CheckCircle2 className="mr-1 h-3.5 w-3.5 text-indigo-600" />
      case 'out_for_delivery':
        return <Truck className="mr-1 h-3.5 w-3.5 text-purple-600" />
      case 'delivered':
      case 'completed':
        return <CheckCircle2 className="mr-1 h-3.5 w-3.5 text-emerald-600" />
      case 'cancelled':
        return <XCircle className="mr-1 h-3.5 w-3.5 text-red-600" />
      default:
        return <AlertCircle className="mr-1 h-3.5 w-3.5 text-gray-600" />
    }
  }

  // Helper function to extract parish from address
  const extractParish = (address: string | undefined): string => {
    if (!address) return ''

    // Common Jersey parishes
    const parishes = ['St. Helier', 'St. Brelade', 'St. Clement', 'St. Lawrence', 'St. Martin', 'St. Ouen', 'St. Peter', 'St. Saviour', 'Grouville', 'Trinity', 'St. John', 'St. Mary']

    for (const parish of parishes) {
      if (address.toLowerCase().includes(parish.toLowerCase())) {
        return parish
      }
    }

    return ''
  }

  // Helper function to calculate ready time
  const calculateReadyTime = (order: Order): string => {
    // Use ready_time if available, otherwise calculate from preparation_time
    let readyTime: Date

    if (order.ready_time) {
      readyTime = new Date(order.ready_time)
    } else if (order.preparation_time) {
      const orderTime = new Date(order.created_at)
      readyTime = new Date(orderTime.getTime() + order.preparation_time * 60000)
    } else {
      return 'N/A'
    }

    // Format in Jersey timezone
    return formatJerseyTime(readyTime)
  }

  // Helper function to get time until ready
  const getTimeUntilReady = (order: Order): { text: string; isOverdue: boolean; isUrgent: boolean } => {
    // Use ready_time if available, otherwise calculate from preparation_time
    let readyTime: Date

    if (order.ready_time) {
      readyTime = new Date(order.ready_time)
    } else if (order.preparation_time) {
      const orderTime = new Date(order.created_at)
      readyTime = new Date(orderTime.getTime() + order.preparation_time * 60000)
    } else {
      return { text: 'N/A', isOverdue: false, isUrgent: false }
    }

    // Compare UTC times directly - no timezone conversion needed for comparison
    const now = new Date()
    const diffMs = readyTime.getTime() - now.getTime()
    const diffMins = Math.round(diffMs / 60000)

    if (diffMins < 0) {
      return { text: `${Math.abs(diffMins)}m overdue`, isOverdue: true, isUrgent: false }
    } else if (diffMins <= 5) {
      return { text: `${diffMins}m left`, isOverdue: false, isUrgent: true }
    } else {
      return { text: `${diffMins}m left`, isOverdue: false, isUrgent: false }
    }
  }

  return (
    <div className="rounded-md border bg-white shadow-sm">
      <div className="relative overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="w-[100px]">Order #</TableHead>
              {showBusinessColumn && (
                <TableHead className="w-[120px] text-center">Business</TableHead>
              )}
              <TableHead className="w-[80px] text-center">Items</TableHead>
              <TableHead className="w-[90px] text-center">Value</TableHead>
              <TableHead className="w-[90px] text-center">Delivery Fee</TableHead>
              <TableHead className="w-[180px] text-center">
                <div className="flex items-center justify-center">
                  <Clock className="mr-1 h-3.5 w-3.5" />
                  Ready By
                </div>
              </TableHead>
              <TableHead className="w-[140px] text-center">Method & Location</TableHead>
              <TableHead className="w-[140px] text-center">Delivery Type</TableHead>
              <TableHead className="w-[120px] text-center">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={showBusinessColumn ? 9 : 8} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <RefreshCcw className="h-5 w-5 animate-spin text-gray-400" />
                    <span className="ml-2">Loading orders...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : orders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showBusinessColumn ? 9 : 8} className="h-24 text-center">
                  No orders found.
                </TableCell>
              </TableRow>
            ) : (
              orders.map((order) => (
                <React.Fragment key={order.id}>
                  <TableRow
                    className={`${expandedOrderId === order.id ? 'bg-gray-50' : 'hover:bg-gray-50'} cursor-pointer`}
                    onClick={() => toggleOrderDetails(order.id)}
                  >
                    {/* Order # */}
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <span className="text-sm">{order.order_number || order.order_id || `#${order.id}`}</span>
                        {order.priority_level && order.priority_level < 3 && (
                          <AlertCircle className="ml-1 h-3.5 w-3.5 text-red-500" />
                        )}
                      </div>
                    </TableCell>

                    {/* Business Name */}
                    {showBusinessColumn && (
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center">
                          <span className="text-sm font-medium">{order.business_name || 'Unknown'}</span>
                          {order.business_type && (
                            <span className="text-xs text-gray-500">{order.business_type}</span>
                          )}
                        </div>
                      </TableCell>
                    )}

                    {/* Items Count */}
                    <TableCell className="text-center">
                      <span className="font-medium text-sm">{order.items_count || 0}</span>
                    </TableCell>

                    {/* Value */}
                    <TableCell className="text-center">
                      <span className="font-medium text-sm">{formatCurrency(order.subtotal || order.total)}</span>
                    </TableCell>

                    {/* Delivery Fee - only show for businesses that don't use Loop delivery */}
                    <TableCell className="text-center">
                      {order.delivery_fulfillment !== 'loop' && order.delivery_fee ? (
                        <span className="font-medium text-sm text-orange-600">{formatCurrency(order.delivery_fee)}</span>
                      ) : (
                        <span className="text-xs text-gray-400">-</span>
                      )}
                    </TableCell>

                    {/* Ready By Time with inline editing */}
                    <TableCell className="text-center">
                      <div className="flex flex-col items-center space-y-1">
                        <div className="flex items-center justify-center">
                          <span className="font-medium text-sm">{calculateReadyTime(order)}</span>
                          {!editingTimeEstimates[order.id] && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-1 h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                enableTimeEstimateEditing(order)
                              }}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        {/* Time until ready indicator */}
                        {(() => {
                          const timeInfo = getTimeUntilReady(order)
                          return (
                            <span className={`text-xs leading-tight ${
                              timeInfo.isOverdue ? 'text-red-600 font-bold' :
                              timeInfo.isUrgent ? 'text-orange-600 font-medium' :
                              'text-gray-500'
                            }`}>
                              {timeInfo.text}
                            </span>
                          )
                        })()}

                        {/* Inline ready time editing */}
                        {editingTimeEstimates[order.id] && (
                          <div className="flex flex-col items-center mt-1 gap-1">
                            <Input
                              type="datetime-local"
                              className="w-36 h-7 text-xs"
                              value={preparationTimes[order.id] || ''}
                              min={new Date().toISOString().slice(0, 16)}
                              onChange={(e) => {
                                e.stopPropagation()
                                setPreparationTimes(prev => ({
                                  ...prev,
                                  [order.id]: e.target.value
                                }))
                              }}
                              onClick={(e) => e.stopPropagation()}
                            />
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  updatePreparationTime(order)
                                }}
                                disabled={updatingTimes[order.id]}
                              >
                                {updatingTimes[order.id] ? (
                                  <RefreshCcw className="h-3 w-3 animate-spin" />
                                ) : (
                                  <Check className="h-3 w-3" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  cancelTimeEstimateEditing(order.id)
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Method & Location */}
                    <TableCell className="text-center">
                      <div className="flex flex-col items-center">
                        <Badge className={getDeliveryTypeBadgeClass(order.delivery_type)} size="sm">
                          {order.delivery_type === 'pickup' ? (
                            <Clock className="mr-1 h-3 w-3" />
                          ) : (
                            <Truck className="mr-1 h-3 w-3" />
                          )}
                          {formatDeliveryType(order.delivery_type)}
                        </Badge>
                        {order.delivery_type === 'delivery' && (
                          <span className="text-xs text-gray-500 mt-1">
                            {order.parish || extractParish(order.delivery_address) || 'Unknown Parish'}
                          </span>
                        )}
                      </div>
                    </TableCell>

                    {/* Delivery Type */}
                    <TableCell className="text-center">
                      <div className="flex flex-col items-center">
                        <span className="text-sm font-medium">
                          {formatDeliveryTiming(order.delivery_timing, order.scheduled_delivery_time)}
                        </span>
                        {order.scheduled_delivery_time && (
                          <span className="text-xs text-gray-500 mt-1">
                            {formatTime(order.scheduled_delivery_time)}
                          </span>
                        )}
                      </div>
                    </TableCell>

                    {/* Status */}
                    <TableCell className="text-center">
                      <Badge className={getStatusBadgeClass(order.status)} size="sm">
                        {getStatusIcon(order.status)}
                        {formatStatus(order.status)}
                      </Badge>
                    </TableCell>
                  </TableRow>

                  {/* Expanded Order Details */}
                  {expandedOrderId === order.id && (
                    <TableRow className="bg-gray-50">
                      <TableCell colSpan={7} className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Order Details */}
                          <div className="bg-white p-4 rounded-md shadow-sm">
                            <h3 className="font-medium text-sm mb-2">Order Details</h3>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-500">Business Name:</span>
                                <span className="font-medium">{order.business_name || 'Unknown Business'}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Order ID:</span>
                                <span>{order.order_number || order.order_id || `#${order.id}`}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Created:</span>
                                <span>{formatDate(order.created_at)} {formatTime(order.created_at)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Status:</span>
                                <Badge className={getStatusBadgeClass(order.status)}>
                                  {formatStatus(order.status)}
                                </Badge>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Priority:</span>
                                <span>{getPriorityBadge(order.priority_level)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Total:</span>
                                <span className="font-medium">{formatCurrency(order.total)}</span>
                              </div>
                            </div>
                          </div>

                          {/* Customer Details */}
                          <div className="bg-white p-4 rounded-md shadow-sm">
                            <h3 className="font-medium text-sm mb-2">Customer Details</h3>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-500">Name:</span>
                                <span>{order.customer_name || 'Guest'}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Email:</span>
                                <span>{order.customer_email || 'No email'}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Phone:</span>
                                <span>{order.customer_phone || 'No phone'}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Address:</span>
                                <span className="text-right">{order.delivery_address || 'No address'}</span>
                              </div>
                              {order.delivery_instructions && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Instructions:</span>
                                  <span className="text-right text-sm">{order.delivery_instructions}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Delivery Details */}
                          <div className="bg-white p-4 rounded-md shadow-sm">
                            <div className="flex justify-between items-center mb-2">
                              <h3 className="font-medium text-sm">Delivery Details</h3>
                            </div>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-500">Type:</span>
                                <Badge className={getDeliveryTypeBadgeClass(order.delivery_type)}>
                                  {formatDeliveryType(order.delivery_type)}
                                </Badge>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-500">Method:</span>
                                <span>{formatDeliveryTiming(order.delivery_timing, order.scheduled_delivery_time)}</span>
                              </div>
                              {/* PHASE 3 STEP 8: Display delivery fulfillment information */}
                              {order.delivery_type === 'delivery' && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Fulfillment:</span>
                                  <Badge
                                    className={
                                      order.delivery_fulfillment === 'internal'
                                        ? "bg-orange-50 text-orange-700 border-orange-200 rounded-full px-2 py-0.5"
                                        : "bg-blue-50 text-blue-700 border-blue-200 rounded-full px-2 py-0.5"
                                    }
                                  >
                                    {order.delivery_fulfillment === 'internal' ? 'Internal Delivery' : 'Loop Delivery'}
                                  </Badge>
                                </div>
                              )}
                              {order.scheduled_time && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Scheduled For:</span>
                                  <span className={calculateTimeRemaining(order.scheduled_time)?.overdue ? 'text-red-600 font-bold' : ''}>
                                    {formatTime(order.scheduled_time)}
                                  </span>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span className="text-gray-500">Order Time:</span>
                                <span>{formatTime(order.created_at)}</span>
                              </div>
                              {order.preparation_time && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Ready By:</span>
                                  <span className="font-medium text-blue-600">
                                    {formatTime(new Date(new Date(order.created_at).getTime() + order.preparation_time * 60000).toISOString())}
                                  </span>
                                </div>
                              )}

                              {/* Preparation Time - Display only */}
                              <div className="flex justify-between">
                                <span className="text-gray-500">Prep Time:</span>
                                <div className="text-right">
                                  <div>{order.preparation_time || 'N/A'} {order.preparation_time ? 'minutes' : ''}</div>
                                  {order.preparation_time && (
                                    <div className="text-xs text-gray-500">
                                      Ready: {formatTime(new Date(new Date(order.created_at).getTime() + order.preparation_time * 60000).toISOString())}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {order.driver_id && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Driver:</span>
                                  <span>{order.driver_name || 'Assigned'}</span>
                                </div>
                              )}

                              {order.estimated_delivery_time && order.delivery_type !== 'pickup' && (
                                <div className="flex justify-between">
                                  <span className="text-gray-500">Est. Delivery:</span>
                                  <div className="text-right">
                                    <div>
                                      {typeof order.estimated_delivery_time === 'string' && order.estimated_delivery_time.includes(':')
                                        ? formatTime(order.estimated_delivery_time)
                                        : `${order.estimated_delivery_time} minutes`}
                                    </div>
                                    {typeof order.estimated_delivery_time === 'number' && order.preparation_time && (
                                      <div className="text-xs text-gray-500">
                                        By: {formatTime(new Date(new Date(order.created_at).getTime() + (order.preparation_time || 0) * 60000 + order.estimated_delivery_time * 60000).toISOString())}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}


                            </div>
                          </div>

                          {/* Order Items */}
                          <div className="md:col-span-3 bg-white p-4 rounded-md shadow-sm mt-2">
                            <h3 className="font-medium text-sm mb-2">Order Items</h3>
                            {loadingItems[order.id] ? (
                              <div className="flex items-center justify-center py-4">
                                <RefreshCcw className="h-5 w-5 animate-spin text-gray-400" />
                                <span className="ml-2 text-sm text-gray-500">Loading items...</span>
                              </div>
                            ) : orderItems[order.id] && orderItems[order.id].length > 0 ? (
                              <div className="overflow-x-auto">
                                <table className="w-full text-sm">
                                  <thead>
                                    <tr className="border-b">
                                      <th className="text-left py-2 font-medium text-gray-500">Item</th>
                                      <th className="text-center py-2 font-medium text-gray-500">Quantity</th>
                                      <th className="text-right py-2 font-medium text-gray-500">Price</th>
                                      <th className="text-right py-2 font-medium text-gray-500">Total</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {orderItems[order.id].map((item) => (
                                      <tr key={item.id} className="border-b hover:bg-gray-50">
                                        <td className="py-2">
                                          <div className="font-medium">{item.name || item.product_name || 'Unknown Product'}</div>
                                          {(() => {
                                            const details = formatItemDetails(item);
                                            return details.length > 0 && (
                                              <div className="text-xs text-gray-500 mt-1">
                                                {details.join(", ")}
                                              </div>
                                            );
                                          })()}
                                          {item.notes && (
                                            <div className="text-xs text-gray-500 mt-1">
                                              Note: {item.notes}
                                            </div>
                                          )}
                                        </td>
                                        <td className="py-2 text-center">{item.quantity}</td>
                                        <td className="py-2 text-right">{formatCurrency(item.price || item.unit_price || 0)}</td>
                                        <td className="py-2 text-right font-medium">{formatCurrency(item.total_price || ((item.price || item.unit_price || 0) * item.quantity))}</td>
                                      </tr>
                                    ))}
                                    <tr className="border-t">
                                      <td colSpan={3} className="py-1 text-right text-sm">Subtotal:</td>
                                      <td className="py-1 text-right text-sm">{formatCurrency(order.subtotal || 0)}</td>
                                    </tr>
                                    {(order.delivery_fee !== undefined && order.delivery_fee !== null) && (
                                      <tr>
                                        <td colSpan={3} className="py-1 text-right text-sm">Delivery Fee:</td>
                                        <td className="py-1 text-right text-sm">{formatCurrency(order.delivery_fee)}</td>
                                      </tr>
                                    )}
                                    {order.service_fee && order.service_fee > 0 && (
                                      <tr>
                                        <td colSpan={3} className="py-1 text-right text-sm">Service Fee:</td>
                                        <td className="py-1 text-right text-sm">{formatCurrency(order.service_fee)}</td>
                                      </tr>
                                    )}
                                    <tr className="bg-gray-50 border-t">
                                      <td colSpan={3} className="py-2 text-right font-medium">Total:</td>
                                      <td className="py-2 text-right font-bold">{formatCurrency(order.total || 0)}</td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-500 py-2">No items found for this order.</div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="md:col-span-3 mt-2">
                            <div className="flex flex-wrap gap-2">
                              {/* Print Order Slip Button */}
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-gray-700 hover:bg-gray-50"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handlePrintOrder(order)
                                }}
                              >
                                <Printer className="mr-1 h-3.5 w-3.5" />
                                Print Order Slip
                              </Button>

                              {/* View Delivery Metrics Button */}
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-blue-200 text-blue-700 hover:bg-blue-50"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleViewDeliveryMetrics(order)
                                }}
                              >
                                <BarChart3 className="mr-1 h-3.5 w-3.5" />
                                Delivery Metrics
                              </Button>

                              {/* Primary Status Update Button */}
                              {getNextStatusOptions(order.status).map((status) => (
                                <Button
                                  key={status}
                                  variant="outline"
                                  size="sm"
                                  className={`
                                    ${status === 'confirmed' ? 'border-yellow-200 text-yellow-700 hover:bg-yellow-50' : ''}
                                    ${status === 'preparing' ? 'border-blue-200 text-blue-700 hover:bg-blue-50' : ''}
                                    ${status === 'ready' ? 'border-indigo-200 text-indigo-700 hover:bg-indigo-50' : ''}
                                    ${status === 'out_for_delivery' ? 'border-purple-200 text-purple-700 hover:bg-purple-50' : ''}
                                    ${status === 'delivered' ? 'border-emerald-200 text-emerald-700 hover:bg-emerald-50' : ''}
                                  `}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleStatusUpdate(order, status)
                                  }}
                                  disabled={updatingStatus[order.id]}
                                >
                                  {updatingStatus[order.id] ? (
                                    <RefreshCcw className="mr-1 h-3.5 w-3.5 animate-spin" />
                                  ) : (
                                    getStatusIcon(status)
                                  )}
                                  {formatStatusAction(status)}
                                </Button>
                              ))}

                              {/* Cancel Button - shown separately if order can be cancelled */}
                              {canCancelOrder(order.status) && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-red-200 text-red-700 hover:bg-red-50"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleStatusUpdate(order, 'cancelled')
                                  }}
                                  disabled={updatingStatus[order.id]}
                                >
                                  {updatingStatus[order.id] ? (
                                    <RefreshCcw className="mr-1 h-3.5 w-3.5 animate-spin" />
                                  ) : (
                                    getStatusIcon('cancelled')
                                  )}
                                  {formatStatusAction('cancelled')}
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Printable Order Slip Modal */}
      {showPrintSlip && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Order Slip</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPrintSlip(null)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {(() => {
              const order = orders.find(o => o.id === showPrintSlip)
              if (!order) return <div>Order not found</div>

              return (
                <PrintableOrderSlip
                  order={order}
                  orderItems={orderItems[showPrintSlip] || []}
                  businessName={order.business_name || "Your Business"}
                />
              )
            })()}
          </div>
        </div>
      )}

      {/* Delivery Metrics Modal */}
      {showDeliveryMetrics && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Delivery Metrics</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDeliveryMetrics(null)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {(() => {
              const order = orders.find(o => o.id === showDeliveryMetrics)
              if (!order) return <div>Order not found</div>

              return (
                <div className="space-y-4">
                  {/* Order Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Order:</span>
                        <div className="font-medium">{order.order_number || `#${order.id}`}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Items:</span>
                        <div className="font-medium">{order.items_count || 0}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Value:</span>
                        <div className="font-medium">{formatCurrency(order.total)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Delivery:</span>
                        <div className="font-medium capitalize">{order.delivery_type || 'Unknown'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Weight Chart */}
                  <div className="border rounded-lg p-4">
                    <h3 className="text-lg font-medium mb-3">Order Weight & Thermal Analysis</h3>
                    <OrderWeightChart orderId={Number(order.id)} />
                  </div>
                </div>
              )
            })()}
          </div>
        </div>
      )}
    </div>
  )
}
