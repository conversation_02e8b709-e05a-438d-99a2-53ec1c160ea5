// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? 'Key is set' : 'Key is not set');

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function queryDatabase() {
  try {
    console.log('=== CHECKING BUSINESS CATEGORY SUBSCRIPTIONS ===\n');

    // Check business_categories table
    console.log('1. Checking business_categories table...');
    const { data: businessCategories, error: businessCategoriesError } = await supabase
      .from('business_categories')
      .select('business_id, category_id, is_primary')
      .limit(20);

    if (businessCategoriesError) {
      if (businessCategoriesError.code === '42P01') {
        console.log('❌ business_categories table does NOT exist');
      } else {
        console.error('Error querying business_categories:', businessCategoriesError);
      }
    } else {
      console.log('✅ business_categories table exists');
      console.log(`📊 Found ${businessCategories?.length || 0} business category subscriptions`);

      if (businessCategories && businessCategories.length > 0) {
        console.log('\n🏪 Business Category Subscriptions:');
        businessCategories.forEach(sub => {
          const isPrimary = sub.is_primary ? ' (PRIMARY)' : '';
          console.log(`   • Business ID ${sub.business_id} → Category ID ${sub.category_id}${isPrimary}`);
        });
      } else {
        console.log('❌ No businesses are currently subscribed to any categories');
      }
    }

    console.log('\n2. Checking categories that businesses are subscribed to...');
    const subscribedCategoryIds = [...new Set(businessCategories?.map(sub => sub.category_id) || [])];
    console.log('📋 Subscribed category IDs:', subscribedCategoryIds);

    if (subscribedCategoryIds.length > 0) {
      const { data: subscribedCategories, error: subscribedCategoriesError } = await supabase
        .from('categories')
        .select('id, name, slug, level, category_purpose, business_type_id')
        .in('id', subscribedCategoryIds);

      if (subscribedCategoriesError) {
        console.error('Error querying subscribed categories:', subscribedCategoriesError);
      } else {
        console.log(`✅ Found ${subscribedCategories?.length || 0} subscribed categories`);
        if (subscribedCategories && subscribedCategories.length > 0) {
          console.log('\n📋 Categories that businesses are subscribed to:');
          subscribedCategories.forEach(cat => {
            console.log(`   • ${cat.name} (${cat.slug}) - ID: ${cat.id}, Level: ${cat.level}, Purpose: ${cat.category_purpose}`);
          });
        }
      }
    }

    console.log('\n3. Checking Level 1 specialization categories (what shows on search page)...');
    const { data: searchPageCategories, error: searchPageCategoriesError } = await supabase
      .from('categories')
      .select('id, name, slug, level, category_purpose, business_type_id')
      .eq('is_active', true)
      .eq('level', 1)
      .eq('category_purpose', 'specialization')
      .limit(20);

    if (searchPageCategoriesError) {
      console.error('Error querying search page categories:', searchPageCategoriesError);
    } else {
      console.log(`✅ Found ${searchPageCategories?.length || 0} Level 1 specialization categories`);
      if (searchPageCategories && searchPageCategories.length > 0) {
        console.log('\n📋 Categories shown on search page:');
        searchPageCategories.forEach(cat => {
          console.log(`   • ${cat.name} (${cat.slug}) - ID: ${cat.id}, Level: ${cat.level}, Purpose: ${cat.category_purpose}`);
        });
      }
    }

    console.log('\n4. Checking for category mismatch...');
    if (subscribedCategoryIds.length > 0 && searchPageCategories && searchPageCategories.length > 0) {
      const searchPageCategoryIds = searchPageCategories.map(cat => cat.id);
      const overlap = subscribedCategoryIds.filter(id => searchPageCategoryIds.includes(id));

      console.log('🔍 Category ID overlap analysis:');
      console.log(`   • Businesses subscribed to ${subscribedCategoryIds.length} different categories`);
      console.log(`   • Search page shows ${searchPageCategoryIds.length} categories`);
      console.log(`   • Overlap: ${overlap.length} categories`);

      if (overlap.length === 0) {
        console.log('❌ PROBLEM: No overlap between subscribed categories and search page categories!');
        console.log('   This explains why category filtering is not working.');
      } else {
        console.log('✅ Some categories overlap - filtering should work for these.');
        console.log('   Overlapping category IDs:', overlap);
      }
    }

    console.log('\n5. Checking which businesses should appear when filtering...');
    const subscribedBusinessIds = [...new Set(businessCategories?.map(sub => sub.business_id) || [])];
    console.log('📋 Business IDs with category subscriptions:', subscribedBusinessIds);

    // Get details of subscribed businesses
    if (subscribedBusinessIds.length > 0) {
      const { data: subscribedBusinesses, error: subscribedBusinessesError } = await supabase
        .from('businesses')
        .select('id, name, slug, is_approved')
        .in('id', subscribedBusinessIds);

      if (subscribedBusinessesError) {
        console.error('Error querying subscribed businesses:', subscribedBusinessesError);
      } else {
        console.log(`✅ Found ${subscribedBusinesses?.length || 0} businesses with category subscriptions`);
        if (subscribedBusinesses && subscribedBusinesses.length > 0) {
          console.log('\n🏪 Businesses that should appear when filtering:');
          subscribedBusinesses.forEach(biz => {
            const isApproved = biz.is_approved ? '✅' : '❌';
            console.log(`   ${isApproved} ${biz.name} (${biz.slug}) - ID: ${biz.id}`);
          });
        }
      }
    }

  } catch (error) {
    console.error('❌ Database query failed:', error);
  }
}

queryDatabase();
