"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { MapP<PERSON>, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

import { JerseyAddressInput } from "@/components/delivery";
import { useCheckout } from "../checkout-context";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import { isValidJerseyPostcodeFormat, standardizeJerseyPostcodeFormat } from "@/lib/jersey-postcodes";

export const DeliveryAddress: React.FC = () => {
  const {
    address,
    setAddress,
    parish,
    setParish,
    postcode,
    setPostcode,
    instructions,
    setInstructions,

    customerCoords,
    setCustomerCoords,

    stepsCompleted,
    goToNextStep,
    goToPrevStep,
    user,
    itemsByBusiness
  } = useCheckout();

  const { getDeliveryMethod } = useRealtimeCart();

  // State for postcode validation
  const [postcodeError, setPostcodeError] = useState<string | null>(null);
  const [isProcessingPostcode, setIsProcessingPostcode] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to get parish from postcode using the Jersey postcode system
  const getParishFromPostcode = async (postcode: string): Promise<string | null> => {
    try {
      const response = await fetch(`/api/postcodes?action=parish&postcode=${encodeURIComponent(postcode)}`);
      const data = await response.json();
      return data.parish || null;
    } catch (error) {
      console.error('Error getting parish from postcode:', error);
      return null;
    }
  };

  // Function to geocode using Jersey postcode system only
  const geocodeWithPostcode = async (currentPostcode: string) => {
    try {
      setIsProcessingPostcode(true);

      // Get coordinates from Jersey postcode system
      const response = await fetch(`/api/postcodes?action=coordinates&postcode=${encodeURIComponent(currentPostcode)}`);
      const data = await response.json();

      if (data.coordinates) {
        console.log('Got coordinates from Jersey postcode system:', data.coordinates);
        setCustomerCoords(data.coordinates);
      } else {
        console.log('No coordinates found for postcode:', currentPostcode);
      }
    } catch (error) {
      console.error('Error geocoding with postcode:', error);
    } finally {
      setIsProcessingPostcode(false);
    }
  };

  // Debounced postcode processing function
  const processPostcodeWithDebounce = useCallback((postcode: string) => {
    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new timer
    debounceTimerRef.current = setTimeout(async () => {
      if (!postcode.trim()) return;

      // Validate postcode format
      if (!isValidJerseyPostcodeFormat(postcode)) {
        setPostcodeError("Please enter a valid Jersey postcode (e.g., JE2 3NG)");
        return;
      }

      // Clear any existing error
      setPostcodeError(null);

      // Standardize the postcode format
      const standardized = standardizeJerseyPostcodeFormat(postcode);
      const finalPostcode = standardized || postcode;

      if (standardized && standardized !== postcode) {
        setPostcode(standardized);
      }

      // Auto-populate parish from postcode
      const detectedParish = await getParishFromPostcode(finalPostcode);
      if (detectedParish) {
        console.log('Setting parish to:', detectedParish);
        setParish(detectedParish);
      }

      // Trigger geocoding with the postcode
      await geocodeWithPostcode(finalPostcode);
    }, 1500); // 1.5 second debounce
  }, [setPostcode, setParish]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Function to handle postcode input changes
  const handlePostcodeChange = (value: string) => {
    // Update the postcode value immediately for UI responsiveness
    setPostcode(value);

    // Clear any existing error when user starts typing
    if (postcodeError) {
      setPostcodeError(null);
    }

    // Only process if the postcode looks complete (6-7 characters)
    if (value.trim().length >= 6) {
      processPostcodeWithDebounce(value);
    }
  };

  // Check if any business requires delivery
  const requiresDeliveryAddress = React.useMemo(() => {
    const businessIds = Object.keys(itemsByBusiness);
    return businessIds.some(businessId => {
      const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));
      return deliveryMethod === 'delivery';
    });
  }, [itemsByBusiness, getDeliveryMethod]);



  return (
    <>
      {/* Show pickup-only message if no delivery is required */}
      {!requiresDeliveryAddress && (
        <div className="bg-orange-50 p-6 rounded-lg border border-orange-200 mb-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-2 rounded-full mr-3">
              <MapPin className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h3 className="font-medium text-lg text-orange-800">Pickup Only Orders</h3>
              <p className="text-sm text-orange-700 mt-1">
                All items in your cart are set for pickup. No delivery address is required.
              </p>
            </div>
          </div>
        </div>
      )}



      {requiresDeliveryAddress && (
        <div className="border-t border-dashed border-gray-200 pt-6 mt-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="bg-emerald-100 p-2 rounded-full mr-3">
              <MapPin className="h-5 w-5 text-emerald-600" />
            </div>
            <h3 className="font-medium text-lg text-gray-800">Delivery Address</h3>
          </div>
        </div>

          <div className="bg-white border border-gray-200 rounded-lg p-5 mb-6 shadow-sm">
            <div className="mb-4">
              <Label htmlFor="postcode" className="text-gray-700 font-medium">Postcode <span className="text-red-500">*</span></Label>
              <div className="relative">
                <Input
                  id="postcode"
                  value={postcode}
                  onChange={(e) => handlePostcodeChange(e.target.value)}
                  required
                  placeholder="JE2 3NG"
                  className={`mt-1 ${postcodeError ? 'border-red-500 focus:border-red-500' : ''} ${isProcessingPostcode ? 'pr-10' : ''}`}
                />
                {isProcessingPostcode && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 mt-0.5">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-600"></div>
                  </div>
                )}
              </div>
              {postcodeError && (
                <div className="flex items-center mt-1 text-sm text-red-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {postcodeError}
                </div>
              )}
              {isProcessingPostcode && !postcodeError && (
                <div className="flex items-center mt-1 text-sm text-emerald-600">
                  <div className="h-4 w-4 mr-1 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-emerald-600"></div>
                  </div>
                  Looking up postcode location...
                </div>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Jersey postcodes must be in the format JE2, JE3, or JE5 followed by a space, a digit, and two letters (e.g., JE2 3NG)
              </p>
            </div>

            <div className="mb-4">
              <Label htmlFor="address" className="text-gray-700 font-medium">Street Address <span className="text-red-500">*</span></Label>
              <JerseyAddressInput
                value={address}
                parish={parish}
                onChange={(value, coords, selectedParish) => {
                  // Update state in a single batch to minimize re-renders
                  const hasChanges = value !== address ||
                      (coords && (!customerCoords ||
                                coords[0] !== customerCoords[0] ||
                                coords[1] !== customerCoords[1])) ||
                      (selectedParish && selectedParish !== parish);

                  if (hasChanges) {
                    // Update all values at once
                    setAddress(value);

                    // If we have coordinates from the JerseyAddressInput, use them
                    // Otherwise, if we have a postcode, try geocoding with the Jersey postcode system
                    if (coords) {
                      setCustomerCoords(coords);
                    } else if (postcode && value && isValidJerseyPostcodeFormat(postcode)) {
                      // Re-geocode with postcode using the improved system
                      geocodeWithPostcode(postcode);
                    }

                    if (selectedParish && selectedParish !== parish) {
                      setParish(selectedParish);

                      // If we have a postcode and address, re-geocode with the Jersey system
                      if (postcode && value && isValidJerseyPostcodeFormat(postcode)) {
                        geocodeWithPostcode(postcode);
                      }
                    }
                  }
                }}
                onParishChange={(selectedParish) => {
                  // Only update if there's a real change
                  if (selectedParish !== parish) {
                    setParish(selectedParish);

                    // If we have address and postcode, re-geocode with the Jersey system
                    if (address && postcode && isValidJerseyPostcodeFormat(postcode)) {
                      geocodeWithPostcode(postcode);
                    }
                  }
                }}
                required
                placeholder="Enter your delivery address"
                className="mt-1"
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="instructions" className="text-gray-700 font-medium">Delivery Instructions (Optional)</Label>
              <Textarea
                id="instructions"
                value={instructions}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value.length <= 5000) {
                    setInstructions(value);
                  }
                }}
                placeholder="Any special instructions for delivery (e.g., gate code, landmark, etc.)"
                className="min-h-24 max-h-48 mt-1 resize-y"
                maxLength={5000}
              />
              <div className="flex justify-between items-center mt-1">
                <div className="text-sm text-gray-500">
                  {instructions.length}/5,000 characters
                </div>
                {instructions.length > 4500 && (
                  <div className="text-sm text-amber-600">
                    Approaching character limit
                  </div>
                )}
              </div>
            </div>


          </div>
        </div>
      )}

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={goToPrevStep}
        >
          Back
        </Button>
        <Button
          type="button"
          onClick={goToNextStep}
          className={`${
            stepsCompleted.deliveryAddress
              ? 'bg-emerald-600 hover:bg-emerald-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          disabled={!stepsCompleted.deliveryAddress}
        >
          {stepsCompleted.deliveryAddress ? (
            "Continue to Payment"
          ) : (
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Complete required fields
            </div>
          )}
        </Button>
      </div>
    </>
  );
};
