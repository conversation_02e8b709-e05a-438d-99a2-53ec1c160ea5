// PHASE 6 STEP 13: Business Delivery Metrics Component
// Shows delivery metrics to business admins

"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Package, Users, Clock, DollarSign, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BusinessDeliveryInfo {
  pendingOrders: number;
  availableDrivers: number;
  busyDrivers: number;
  averageOrderValue: number;
  message: string;
}

interface BusinessDeliveryMetricsProps {
  businessId: number;
  className?: string;
}

export function BusinessDeliveryMetrics({ businessId, className }: BusinessDeliveryMetricsProps) {
  const [deliveryInfo, setDeliveryInfo] = useState<BusinessDeliveryInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDeliveryInfo();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchDeliveryInfo, 30000);
    return () => clearInterval(interval);
  }, [businessId]);

  const fetchDeliveryInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/delivery-metrics?type=business&businessId=${businessId}`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDeliveryInfo(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching business delivery info:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !deliveryInfo) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading delivery metrics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!deliveryInfo) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Delivery metrics unavailable</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getOrdersStatusColor = () => {
    if (deliveryInfo.pendingOrders === 0) return 'secondary';
    if (deliveryInfo.pendingOrders > deliveryInfo.availableDrivers) return 'destructive';
    return 'default';
  };

  const getDriversStatusColor = () => {
    if (deliveryInfo.availableDrivers === 0) return 'destructive';
    if (deliveryInfo.availableDrivers < 3) return 'secondary';
    return 'default';
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center">
            <Package className="h-4 w-4 mr-2" />
            Delivery Overview
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchDeliveryInfo}
            disabled={loading}
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          {/* Pending Orders */}
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Pending Orders</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-lg font-semibold">{deliveryInfo.pendingOrders}</span>
              <Badge variant={getOrdersStatusColor()} className="text-xs">
                {deliveryInfo.pendingOrders === 0 ? 'None' : 
                 deliveryInfo.pendingOrders > deliveryInfo.availableDrivers ? 'High' : 'Normal'}
              </Badge>
            </div>
          </div>

          {/* Available Drivers */}
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Available Drivers</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-lg font-semibold">{deliveryInfo.availableDrivers}</span>
              <Badge variant={getDriversStatusColor()} className="text-xs">
                {deliveryInfo.availableDrivers === 0 ? 'None' : 
                 deliveryInfo.availableDrivers < 3 ? 'Low' : 'Good'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Additional Metrics */}
        <div className="space-y-2 pt-2 border-t">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Busy drivers</span>
            <span className="text-sm font-medium">{deliveryInfo.busyDrivers}</span>
          </div>
          
          {deliveryInfo.averageOrderValue > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Avg. order value</span>
              <span className="text-sm font-medium">£{deliveryInfo.averageOrderValue.toFixed(2)}</span>
            </div>
          )}
        </div>

        {/* Status Message */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            {deliveryInfo.message}
          </p>
        </div>

        {/* Quick Actions */}
        {deliveryInfo.pendingOrders > 0 && (
          <div className="pt-2 border-t">
            <p className="text-xs text-blue-600">
              💡 Tip: Update preparation times to help drivers plan their routes better
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
