'use client'

import { useState, useEffect } from 'react'
import { getAllRestaurants, getAllShops, getAllBusinessesByType } from '@/services/business-service-direct'

export default function DirectSearchPage() {
  const [businesses, setBusinesses] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeType, setActiveType] = useState('restaurant')

  // Fetch businesses on mount and when type changes
  useEffect(() => {
    async function fetchBusinesses() {
      setLoading(true)
      setError(null)

      try {
        let results: any[] = []

        if (activeType === 'all') {
          // Fetch all business types
          const restaurants = await getAllRestaurants()
          const shops = await getAllShops()
          const pharmacies = await getAllBusinessesByType('pharmacy' as any)
          const cafes = await getAllBusinessesByType('cafe' as any)
          const errands = await getAllBusinessesByType('errand')

          results = [
            ...restaurants,
            ...shops,
            ...pharmacies,
            ...cafes,
            ...errands
          ]
        } else if (activeType === 'restaurant') {
          results = await getAllRestaurants()
        } else if (activeType === 'shop') {
          results = await getAllShops()
        } else if (['pharmacy', 'cafe', 'errand'].includes(activeType)) {
          results = await getAllBusinessesByType(activeType as any)
        }

        setBusinesses(results)
      } catch (err) {
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchBusinesses()
  }, [activeType])

  return (
    <div className="container-fluid py-4">
      <h1 className="text-2xl font-bold mb-4">Direct Search Test</h1>

      <div className="mb-4">
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {['all', 'restaurant', 'shop', 'pharmacy', 'cafe', 'errand'].map(type => (
            <button
              key={type}
              onClick={() => setActiveType(type)}
              className={`px-4 py-2 rounded-full ${
                activeType === type
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {loading && <p>Loading businesses...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}

      <div className="mt-4">
        <h2 className="text-xl font-semibold mb-2">Businesses ({businesses.length})</h2>

        {businesses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {businesses.map(business => (
              <div key={business.id} className="border p-4 rounded shadow-sm">
                <h3 className="font-bold">{business.name}</h3>
                <p className="text-sm text-gray-600">{business.location}</p>
                <div className="flex items-center mt-1">
                  <span className="text-yellow-500">★</span>
                  <span className="ml-1">{business.rating || 'N/A'}</span>
                </div>
                <p className="text-sm mt-2">
                  {business.cuisines ? `Cuisines: ${business.cuisines.join(', ')}` :
                   business.storeTypes ? `Store Types: ${business.storeTypes.join(', ')}` :
                   business.pharmacyTypes ? `Pharmacy Types: ${business.pharmacyTypes.join(', ')}` :
                   business.cafeTypes ? `Cafe Types: ${business.cafeTypes.join(', ')}` :
                   business.errandTypes ? `Errand Types: ${business.errandTypes.join(', ')}` : ''}
                </p>
                <div className="mt-2 flex items-center text-sm">
                  <span className="bg-gray-100 px-2 py-1 rounded-full">
                    {business.deliveryTime} min
                  </span>
                  <span className="ml-2 bg-gray-100 px-2 py-1 rounded-full">
                    £{business.deliveryFee.toFixed(2)} delivery
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          !loading && <p>No businesses found</p>
        )}
      </div>
    </div>
  )
}
