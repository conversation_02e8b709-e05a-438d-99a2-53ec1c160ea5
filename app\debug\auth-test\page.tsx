"use client"

import { useAuth } from "@/context/unified-auth-context"
import { useEffect, useState } from "react"

export default function AuthTestPage() {
  const { user, userProfile, isAdmin, isSuperAdmin, isBusinessManager, isBusinessStaff, session, userRole } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>(null)

  useEffect(() => {
    const gatherDebugInfo = async () => {
      try {
        // Test API calls to see what works
        const profileResponse = await fetch('/api/user/profile')
        const profileData = profileResponse.ok ? await profileResponse.json() : { error: 'Failed to fetch profile' }

        const verifyResponse = await fetch('/api/auth/verify-token')
        const verifyData = verifyResponse.ok ? await verifyResponse.json() : { error: 'Failed to verify token' }

        setDebugInfo({
          profileApi: profileData,
          verifyApi: verifyData,
          localStorage: {
            token: localStorage.getItem('loop_jersey_auth_token')?.substring(0, 20) + '...',
            userEmail: localStorage.getItem('loop_jersey_user_email')
          }
        })
      } catch (error) {
        setDebugInfo({ error: String(error) })
      }
    }

    if (user) {
      gatherDebugInfo()
    }
  }, [user])

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Auth Context State</h2>
          <div className="space-y-2 text-sm">
            <div><strong>User:</strong> {user ? user.email : 'None'}</div>
            <div><strong>User Role:</strong> {userRole || 'None'}</div>
            <div><strong>User Profile:</strong> {userProfile ? userProfile.name : 'None'}</div>
            <div><strong>Session:</strong> {session ? 'Present' : 'None'}</div>
            <div><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</div>
            <div><strong>Is Super Admin:</strong> {isSuperAdmin ? 'Yes' : 'No'}</div>
            <div><strong>Is Business Manager:</strong> {isBusinessManager ? 'Yes' : 'No'}</div>
            <div><strong>Is Business Staff:</strong> {isBusinessStaff ? 'Yes' : 'No'}</div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">API Test Results</h2>
          <div className="text-sm">
            <pre className="whitespace-pre-wrap overflow-auto max-h-64">
              {debugInfo ? JSON.stringify(debugInfo, null, 2) : 'Loading...'}
            </pre>
          </div>
        </div>
      </div>

      <div className="mt-6 space-y-4">
        <h2 className="text-lg font-semibold">Test Links</h2>
        <div className="space-x-4">
          <a href="/admin-new" className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Test Admin Access
          </a>
          <a href="/super-admin" className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
            Test Super Admin Access
          </a>
          <a href="/business-admin/dashboard" className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
            Test Business Admin Access
          </a>
        </div>
      </div>
    </div>
  )
}
