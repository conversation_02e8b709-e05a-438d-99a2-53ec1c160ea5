"use client"

import { useState, useEffect } from "react"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { NotificationPermission } from "@/components/notification-permission"
import { NotificationDemo } from "@/components/notification-demo"
import { notificationService } from "@/services/notification-service"

export default function NotificationSettings() {
  const [hasPermission, setHasPermission] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [settings, setSettings] = useState({
    newOrders: true,
    orderUpdates: true,
    earnings: true,
    promotions: false,
    appUpdates: true,
  })

  useEffect(() => {
    const checkStatus = async () => {
      await notificationService.initialize()
      setHasPermission(notificationService.permissionStatus === "granted")

      const subscription = await notificationService.getSubscription()
      setIsSubscribed(!!subscription)
    }

    checkStatus()

    // Subscribe to changes
    const unsubscribe = notificationService.onSubscriptionChange((subscription) => {
      setIsSubscribed(!!subscription)
    })

    return unsubscribe
  }, [])

  const handlePermissionChange = (permission: NotificationPermission) => {
    setHasPermission(permission === "granted")
  }

  const handleSubscriptionToggle = async () => {
    if (isSubscribed) {
      await notificationService.unsubscribe()
    } else {
      await notificationService.subscribe()
    }
  }

  const handleSettingChange = (setting: keyof typeof settings) => {
    setSettings({
      ...settings,
      [setting]: !settings[setting],
    })
  }

  return (
    <div className="p-4 space-y-6">
      <div className="flex items-center mb-4">
        <Link href="/driver-mobile/settings" className="mr-2">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-xl font-bold">Notification Settings</h1>
      </div>

      <NotificationPermission onPermissionChange={handlePermissionChange} />

      {hasPermission && (
        <>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Push Notifications</p>
                  <p className="text-sm text-gray-500">
                    {isSubscribed ? "You are subscribed to push notifications" : "Subscribe to push notifications"}
                  </p>
                </div>
                <Switch checked={isSubscribed} onCheckedChange={handleSubscriptionToggle} id="push-notifications" />
              </div>
            </CardContent>
          </Card>

          <div className="space-y-4">
            <h2 className="text-lg font-medium">Notification Types</h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="new-orders" className="font-medium">
                    New Orders
                  </Label>
                  <p className="text-sm text-gray-500">Get notified about new delivery requests</p>
                </div>
                <Switch
                  id="new-orders"
                  checked={settings.newOrders}
                  onCheckedChange={() => handleSettingChange("newOrders")}
                  disabled={!isSubscribed}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="order-updates" className="font-medium">
                    Order Updates
                  </Label>
                  <p className="text-sm text-gray-500">Get notified when an order is ready for pickup</p>
                </div>
                <Switch
                  id="order-updates"
                  checked={settings.orderUpdates}
                  onCheckedChange={() => handleSettingChange("orderUpdates")}
                  disabled={!isSubscribed}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="earnings" className="font-medium">
                    Earnings
                  </Label>
                  <p className="text-sm text-gray-500">Get notified about your earnings</p>
                </div>
                <Switch
                  id="earnings"
                  checked={settings.earnings}
                  onCheckedChange={() => handleSettingChange("earnings")}
                  disabled={!isSubscribed}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="promotions" className="font-medium">
                    Promotions
                  </Label>
                  <p className="text-sm text-gray-500">Get notified about promotions and bonuses</p>
                </div>
                <Switch
                  id="promotions"
                  checked={settings.promotions}
                  onCheckedChange={() => handleSettingChange("promotions")}
                  disabled={!isSubscribed}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="app-updates" className="font-medium">
                    App Updates
                  </Label>
                  <p className="text-sm text-gray-500">Get notified about app updates and new features</p>
                </div>
                <Switch
                  id="app-updates"
                  checked={settings.appUpdates}
                  onCheckedChange={() => handleSettingChange("appUpdates")}
                  disabled={!isSubscribed}
                />
              </div>
            </div>
          </div>

          <NotificationDemo />
        </>
      )}
    </div>
  )
}
