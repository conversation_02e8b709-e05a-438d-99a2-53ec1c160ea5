import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderNumber: string }> }
) {
  try {
    const { orderNumber } = await params

    if (!orderNumber) {
      return NextResponse.json(
        { error: "Order number is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Check if driver is verified and active (Loop approval)
    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not authorized by Loop" },
        { status: 403 }
      )
    }

    // Get order details first to check business_id
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        cart_id,
        customer_name,
        customer_phone,
        customer_email,
        delivery_address,
        postcode,
        parish,
        delivery_instructions,
        delivery_fee,
        service_fee,
        subtotal,
        total,
        status,
        payment_method,
        payment_status,
        created_at,
        updated_at,
        ready_time,
        scheduled_time,
        estimated_delivery_time,
        delivery_distance_km,
        driver_pickup_confirmed,
        business_pickup_confirmed,
        businesses!inner (
          id,
          name,
          address,
          postcode,
          phone
        )
      `)
      .eq('order_number', orderNumber)
      .eq('driver_id', driverProfile.id)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found or not assigned to this driver" },
        { status: 404 }
      )
    }

    // Check if driver is approved by the business for this order
    const { data: businessApproval, error: approvalError } = await supabase
      .from('driver_business_approvals')
      .select('status')
      .eq('driver_id', driverProfile.id)
      .eq('business_id', order.business_id)
      .eq('status', 'approved')
      .single()

    if (approvalError || !businessApproval) {
      return NextResponse.json(
        { error: "Driver not approved by this business" },
        { status: 403 }
      )
    }

    // Get order items (if cart_id exists)
    let orderItems = null
    if (order.cart_id) {
      const { data: items, error: itemsError } = await supabase
        .from('cart_items')
        .select(`
          id,
          product_id,
          quantity,
          unit_price,
          total_price,
          special_instructions,
          products!inner (
            id,
            name,
            description,
            image_url
          )
        `)
        .eq('cart_id', order.cart_id)

      orderItems = items
    }

    // Get order status history
    const { data: statusHistory, error: historyError } = await supabase
      .from('order_status_history')
      .select(`
        id,
        status,
        notes,
        changed_by,
        changed_by_type,
        created_at
      `)
      .eq('order_id', order.id)
      .order('created_at', { ascending: true })

    // Get driver activity for this order
    const { data: driverActivity, error: activityError } = await supabase
      .from('driver_activity_log')
      .select(`
        id,
        activity_type,
        details,
        created_at
      `)
      .eq('driver_id', driverProfile.id)
      .contains('details', { order_id: order.id })
      .order('created_at', { ascending: true })

    // Format the response
    const orderDetails = {
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,

      // Business information
      business: {
        id: order.businesses.id,
        name: order.businesses.name,
        address: order.businesses.address,
        postcode: order.businesses.postcode,
        phone: order.businesses.phone
      },

      // Customer information
      customer: {
        name: order.customer_name,
        phone: order.customer_phone,
        email: order.customer_email,
        deliveryAddress: order.delivery_address,
        postcode: order.postcode,
        parish: order.parish,
        instructions: order.delivery_instructions
      },

      // Order details
      items: orderItems?.map(item => ({
        id: item.id,
        productId: item.product_id,
        name: item.products.name,
        description: item.products.description,
        imageUrl: item.products.image_url,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        totalPrice: item.total_price,
        specialInstructions: item.special_instructions
      })) || [],

      // Financial information
      pricing: {
        subtotal: order.subtotal || 0,
        deliveryFee: order.delivery_fee || 0,
        serviceFee: order.service_fee || 0,
        total: order.total || 0
      },

      // Payment information
      payment: {
        method: order.payment_method,
        status: order.payment_status
      },

      // Timing information
      timing: {
        createdAt: order.created_at,
        updatedAt: order.updated_at,
        readyTime: order.ready_time,
        scheduledTime: order.scheduled_time,
        estimatedDeliveryTime: order.estimated_delivery_time
      },

      // Delivery information
      delivery: {
        distance: order.delivery_distance_km,
        driverPickupConfirmed: order.driver_pickup_confirmed,
        businessPickupConfirmed: order.business_pickup_confirmed
      },

      // Status history
      statusHistory: statusHistory?.map(history => ({
        id: history.id,
        status: history.status,
        notes: history.notes,
        changedBy: history.changed_by,
        changedByType: history.changed_by_type,
        timestamp: history.created_at
      })) || [],

      // Driver activity
      driverActivity: driverActivity?.map(activity => ({
        id: activity.id,
        type: activity.activity_type,
        details: activity.details,
        timestamp: activity.created_at
      })) || []
    }

    return NextResponse.json({
      success: true,
      order: orderDetails
    })

  } catch (error) {
    console.error('Error in driver order details API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
