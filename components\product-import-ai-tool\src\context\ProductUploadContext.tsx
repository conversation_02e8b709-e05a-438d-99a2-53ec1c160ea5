
import React, { createContext, useContext, useState, ReactNode } from 'react';

export type ColumnMappingType = {
  [key: string]: {
    sourceColumn: string;
    useAI: boolean;
  };
};

export type ProductType = {
  id: string;
  productId?: string; // Added product ID as an optional field
  name: string;
  description: string;
  categories: string[];
  attributes: { [key: string]: string };
  price: string;
  images: string[];
  imageGenerationStatus?: 'idle' | 'generating' | 'completed' | 'failed';
  newCategory?: string;
  newAttributeKey?: string;
  newAttributeValue?: string;
};

type ProductUploadContextType = {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  csvData: string[][];
  setCsvData: (data: string[][]) => void;
  headers: string[];
  setHeaders: (headers: string[]) => void;
  columnMapping: ColumnMappingType;
  setColumnMapping: (mapping: ColumnMappingType) => void;
  products: ProductType[];
  setProducts: (products: ProductType[]) => void;
  updateProduct: (id: string, updatedFields: Partial<ProductType>) => void;
  processingStatus: 'idle' | 'processing' | 'completed' | 'failed';
  setProcessingStatus: (status: 'idle' | 'processing' | 'completed' | 'failed') => void;
  generateProductDescription: (productName: string) => Promise<string>;
  generateProductCategories: (productName: string, description: string) => Promise<string[]>;
  generateProductAttributes: (productName: string, description: string) => Promise<{ [key: string]: string }>;
  requestImageGeneration: (productId: string, prompt: string) => Promise<void>;
};

const initialContext: ProductUploadContextType = {
  currentStep: 1,
  setCurrentStep: () => {},
  csvData: [],
  setCsvData: () => {},
  headers: [],
  setHeaders: () => {},
  columnMapping: {},
  setColumnMapping: () => {},
  products: [],
  setProducts: () => {},
  updateProduct: () => {},
  processingStatus: 'idle',
  setProcessingStatus: () => {},
  generateProductDescription: async () => '',
  generateProductCategories: async () => [],
  generateProductAttributes: async () => ({}),
  requestImageGeneration: async () => {},
};

const ProductUploadContext = createContext<ProductUploadContextType>(initialContext);

export const useProductUpload = () => useContext(ProductUploadContext);

export const ProductUploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [csvData, setCsvData] = useState<string[][]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<ColumnMappingType>({});
  const [products, setProducts] = useState<ProductType[]>([]);
  const [processingStatus, setProcessingStatus] = useState<'idle' | 'processing' | 'completed' | 'failed'>('idle');

  // Mock AI functionality (replace with real AI API calls later)
  const generateProductDescription = async (productName: string): Promise<string> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return `This ${productName} is a high-quality product that will meet all your needs. It's designed for durability and performance, making it ideal for both home and professional use.`;
  };

  const generateProductCategories = async (productName: string, description: string): Promise<string[]> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Extract keywords from product name
    const keywords = productName.toLowerCase().split(' ');
    
    if (keywords.some(k => k.includes('chair') || k.includes('seat'))) {
      return ['Furniture', 'Seating', 'Home Decor'];
    } 
    else if (keywords.some(k => k.includes('phone') || k.includes('mobile'))) {
      return ['Electronics', 'Smartphones', 'Gadgets'];
    }
    else if (keywords.some(k => k.includes('shirt') || k.includes('jacket'))) {
      return ['Apparel', 'Tops', 'Fashion'];
    }
    else {
      return ['General Products', 'Merchandise'];
    }
  };

  const generateProductAttributes = async (productName: string, description: string): Promise<{ [key: string]: string }> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    const keywords = productName.toLowerCase().split(' ');
    
    // Default attributes
    const attributes: { [key: string]: string } = {
      'Brand': 'Acme Co',
      'Material': 'Premium Materials',
      'Condition': 'New',
    };
    
    // Add specific attributes based on keywords
    if (keywords.some(k => k.includes('chair') || k.includes('seat'))) {
      attributes['Weight Capacity'] = '250 lbs';
      attributes['Assembly Required'] = 'Yes';
      attributes['Color'] = 'Natural Wood';
    } 
    else if (keywords.some(k => k.includes('phone') || k.includes('mobile'))) {
      attributes['Storage'] = '128GB';
      attributes['Screen Size'] = '6.1 inches';
      attributes['Battery Life'] = '12 hours';
    }
    else if (keywords.some(k => k.includes('shirt') || k.includes('jacket'))) {
      attributes['Size'] = 'Medium';
      attributes['Color'] = 'Blue';
      attributes['Care'] = 'Machine Wash';
    }
    
    return attributes;
  };

  const requestImageGeneration = async (productId: string, prompt: string): Promise<void> => {
    // Update product status
    setProducts(prevProducts => 
      prevProducts.map(product => 
        product.id === productId 
          ? { ...product, imageGenerationStatus: 'generating' } 
          : product
      )
    );
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real app, this would get an actual generated image URL from an AI service
      const placeholderImageUrl = `https://source.unsplash.com/random/300x300/?${encodeURIComponent(prompt)}`;
      
      setProducts(prevProducts => 
        prevProducts.map(product => 
          product.id === productId 
            ? { 
                ...product, 
                images: [...product.images, placeholderImageUrl],
                imageGenerationStatus: 'completed' 
              } 
            : product
        )
      );
    } catch (error) {
      setProducts(prevProducts => 
        prevProducts.map(product => 
          product.id === productId 
            ? { ...product, imageGenerationStatus: 'failed' } 
            : product
        )
      );
      console.error('Failed to generate image:', error);
    }
  };

  const updateProduct = (id: string, updatedFields: Partial<ProductType>) => {
    setProducts(prevProducts => 
      prevProducts.map(product => 
        product.id === id 
          ? { ...product, ...updatedFields } 
          : product
      )
    );
  };

  return (
    <ProductUploadContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        csvData,
        setCsvData,
        headers,
        setHeaders,
        columnMapping,
        setColumnMapping,
        products,
        setProducts,
        updateProduct,
        processingStatus,
        setProcessingStatus,
        generateProductDescription,
        generateProductCategories,
        generateProductAttributes,
        requestImageGeneration,
      }}
    >
      {children}
    </ProductUploadContext.Provider>
  );
};
