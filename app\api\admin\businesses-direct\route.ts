import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { headers, cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Starting admin businesses API request")

    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    console.log("Auth check result:", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    // In development mode, use a default admin email if we don't have a user
    if (!userEmail && process.env.NODE_ENV === 'development') {
      userEmail = '<EMAIL>';
      console.log("Development mode: Using default admin email");
    } else if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", userEmail)
      .single();

    // In development mode, continue even if there's no user profile
    if ((profileError || !userProfile) && process.env.NODE_ENV !== 'development') {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    // Check if the user has admin or super_admin role
    if (userProfile && userProfile.role !== 'admin' && userProfile.role !== 'super_admin' && process.env.NODE_ENV !== 'development') {
      console.error("Unauthorized access attempt with role:", userProfile.role);
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      );
    }

    console.log("Admin access verified for user:", userEmail);

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch all businesses with all fields needed for admin page
    const { data: businesses, error, status } = await supabase
      .from("businesses")
      .select(`
        id,
        slug,
        name,
        address,
        postcode,
        phone,
        description,
        is_approved,
        created_at,
        updated_at,
        business_type_id,
        business_types(name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching businesses:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      businesses,
      error: null,
      status
    })
  } catch (error: any) {
    console.error("Unexpected error in businesses API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
