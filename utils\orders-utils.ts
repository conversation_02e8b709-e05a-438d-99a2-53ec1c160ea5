import { OrderFilters } from '@/components/orders/enhanced-order-filters'

// Status mapping for tab filters
export const getStatusFilterForTab = (tabValue: string): string[] => {
  switch (tabValue) {
    case "pending":
      return ["pending", "confirmed"]
    case "processing":
      return ["preparing", "ready", "out_for_delivery"]
    case "completed":
      return ["delivered"]
    case "cancelled":
      return ["cancelled"]
    default: // "all"
      return []
  }
}

// Format status for display messages
export const formatStatusForMessage = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'confirmed':
      return 'confirmed'
    case 'cancelled':
      return 'cancelled'
    case 'preparing':
      return 'preparing'
    case 'ready':
      return 'ready for pickup/delivery'
    case 'out_for_delivery':
      return 'out for delivery'
    case 'delivered':
      return 'delivered'
    default:
      return status
  }
}

// Get authentication headers
export const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem('loop_jersey_auth_token') || ''
  return {
    "Content-Type": "application/json",
    'Authorization': token ? `Bearer ${token}` : '',
  }
}

// Handle print order
export const handlePrintOrder = (orderId: number): boolean => {
  const printWindow = window.open(`/business-admin/orders/${orderId}/print`, '_blank')
  if (printWindow) {
    printWindow.focus()
    return true
  }
  return false
}

// Build API URL with parameters
export const buildOrdersApiUrl = (
  page: number,
  pageSize: number,
  searchQuery: string,
  dateRange: { from?: Date; to?: Date } | undefined,
  activeFilters: OrderFilters,
  selectedBusinessId?: number | null,
  isAdminUser?: boolean
): string => {
  const params = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    sortBy: "created_at",
    sortOrder: "desc"
  })

  if (searchQuery.trim()) {
    params.append("search", searchQuery.trim())
  }

  if (dateRange?.from) {
    params.append("startDate", dateRange.from.toISOString())
  }
  if (dateRange?.to) {
    params.append("endDate", dateRange.to.toISOString())
  }

  if (activeFilters.status.length > 0) {
    activeFilters.status.forEach(status => {
      params.append("status", status)
    })
  }

  if (activeFilters.priority.length > 0) {
    params.append("priority", activeFilters.priority.join(','))
  }

  if (activeFilters.deliveryType.length > 0) {
    params.append("deliveryType", activeFilters.deliveryType.join(','))
  }

  if (isAdminUser && selectedBusinessId) {
    params.append("businessId", selectedBusinessId.toString())
  }

  return `/api/business-admin/orders?${params.toString()}`
}

// Default order stats
export const getDefaultOrderStats = () => ({
  total: 0,
  pending: 0,
  confirmed: 0,
  preparing: 0,
  ready: 0,
  outForDelivery: 0,
  completed: 0,
  cancelled: 0,
  averageDeliveryTime: "0 min",
  overdueOrders: 0,
  highPriorityOrders: 0
})

// Safe number conversion utility
export const safeNumber = (value: any): number => {
  const num = Number(value)
  return isNaN(num) || !isFinite(num) ? 0 : num
}

// Safe stats conversion utility
export const sanitizeOrderStats = (stats: any) => ({
  total: safeNumber(stats?.total),
  pending: safeNumber(stats?.pending),
  confirmed: safeNumber(stats?.confirmed),
  preparing: safeNumber(stats?.preparing),
  ready: safeNumber(stats?.ready),
  outForDelivery: safeNumber(stats?.outForDelivery || stats?.out_for_delivery),
  completed: safeNumber(stats?.completed || stats?.delivered),
  cancelled: safeNumber(stats?.cancelled),
  averageDeliveryTime: stats?.averageDeliveryTime || "0 min",
  overdueOrders: safeNumber(stats?.overdueOrders),
  highPriorityOrders: safeNumber(stats?.highPriorityOrders)
})

// Default date range (current month)
export const getDefaultDateRange = () => ({
  from: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // First day of current month
  to: new Date(),
})

// Default filters
export const getDefaultFilters = (): OrderFilters => ({
  status: [],
  priority: [],
  deliveryType: [],
  paymentStatus: 'all',
  overdueOnly: false,
  notifiedStatus: 'all'
})

// Validate business ID from localStorage
export const getValidBusinessIdFromStorage = (): number | null => {
  try {
    const savedBusinessId = localStorage.getItem('loop_jersey_selected_business_id')
    if (savedBusinessId) {
      const businessId = parseInt(savedBusinessId)
      return isNaN(businessId) ? null : businessId
    }
  } catch (e) {
    console.error("Error loading selected business ID from localStorage:", e)
  }
  return null
}

// Save business ID to localStorage
export const saveBusinessIdToStorage = (businessId: number): void => {
  try {
    localStorage.setItem('loop_jersey_selected_business_id', businessId.toString())
  } catch (e) {
    console.error("Error storing selected business ID:", e)
  }
}

// Error handling utility
export const handleApiError = (error: any, response?: Response): string => {
  if (response && !response.ok) {
    return `HTTP ${response.status}: ${response.statusText}`
  }
  return error?.message || "An unexpected error occurred"
}

// Parse API response safely
export const parseApiResponse = async (response: Response): Promise<any> => {
  try {
    return await response.json()
  } catch (jsonError) {
    console.error("Failed to parse response as JSON:", jsonError)
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
}
