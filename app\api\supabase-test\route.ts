import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    // Check if environment variables are set
    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json(
        { 
          error: 'Missing Supabase environment variables',
          env: {
            supabaseUrl: !!supabaseUrl,
            supabaseAnonKey: !!supabaseAnonKey
          }
        },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Check users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });
      
    // Check restaurants table
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .select('*');
      
    return NextResponse.json({
      connection: 'success',
      users: {
        success: !userError,
        error: userError ? userError.message : null,
        data: userData
      },
      restaurants: {
        success: !restaurantError,
        error: restaurantError ? restaurantError.message : null,
        count: restaurantData ? restaurantData.length : 0,
        data: restaurantData
      }
    });
    
  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
