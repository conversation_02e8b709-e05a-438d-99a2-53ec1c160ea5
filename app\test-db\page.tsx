'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

export default function TestDbPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'success' | 'failed'>('checking');
  const [ipAddress, setIpAddress] = useState<string | null>(null);

  useEffect(() => {
    async function checkConnection() {
      try {
        // Get public IP address
        const ipResponse = await fetch('https://api.ipify.org?format=json');
        const ipData = await ipResponse.json();
        setIpAddress(ipData.ip);

        // Create Supabase client
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseAnonKey) {
          throw new Error('Supabase environment variables not set');
        }
        
        const supabase = createClient(supabaseUrl, supabaseAnonKey);
        
        // Test connection by fetching businesses
        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .limit(5);
        
        if (error) {
          throw error;
        }
        
        setBusinesses(data || []);
        setConnectionStatus('success');
      } catch (err: any) {
        console.error('Database connection error:', err);
        setError(err.message || 'Unknown error');
        setConnectionStatus('failed');
      } finally {
        setLoading(false);
      }
    }
    
    checkConnection();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Connection Test</h1>
      
      <div className="mb-4 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Connection Status</h2>
        {loading ? (
          <p>Checking connection...</p>
        ) : connectionStatus === 'success' ? (
          <p className="text-green-600">✅ Connected successfully to Supabase!</p>
        ) : (
          <p className="text-red-600">❌ Connection failed: {error}</p>
        )}
        
        {ipAddress && (
          <p className="mt-2">Your IP Address: {ipAddress}</p>
        )}
      </div>
      
      <div className="mb-4 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Environment Variables</h2>
        <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Not set'}</p>
        <p>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Not set'}</p>
      </div>
      
      {connectionStatus === 'success' && (
        <div className="p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">Businesses Data</h2>
          {businesses.length > 0 ? (
            <ul className="list-disc pl-5">
              {businesses.map((business) => (
                <li key={business.id} className="mb-2">
                  <strong>{business.name}</strong> - {business.description}
                </li>
              ))}
            </ul>
          ) : (
            <p>No businesses found</p>
          )}
        </div>
      )}
      
      <div className="mt-6 p-4 border rounded bg-gray-50">
        <h2 className="text-lg font-semibold mb-2">Troubleshooting Tips</h2>
        <ul className="list-disc pl-5">
          <li>Check if your IP address is allowed in Supabase dashboard</li>
          <li>Verify that your API keys are correct and not expired</li>
          <li>Check if there are any network restrictions or firewalls blocking the connection</li>
          <li>Try restarting your development server</li>
          <li>Check if Supabase is experiencing any outages</li>
        </ul>
      </div>
    </div>
  );
}
