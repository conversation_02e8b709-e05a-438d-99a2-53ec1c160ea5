import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  try {
    const results: Record<string, any> = {
      environment: {
        supabaseUrl: supabaseUrl ? `Set (${supabaseUrl.substring(0, 15)}...)` : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? `Set (${supabaseAnonKey.length} chars)` : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? `Set (${supabaseServiceKey.length} chars)` : 'Not set',
        nodeEnv: process.env.NODE_ENV || 'Not set'
      },
      tests: {}
    };

    // Test 1: Check if we can connect to Supabase with anon key
    try {
      const anonClient = createClient(supabaseUrl, supabaseAnonKey);
      const { data: anonData, error: anonError } = await anonClient
        .from('users')
        .select('count', { count: 'exact', head: true });

      results.tests.anonConnection = {
        success: !anonError,
        error: anonError ? anonError.message : null,
        data: anonData ? 'Data received' : 'No data'
      };
    } catch (error: any) {
      results.tests.anonConnection = {
        success: false,
        error: error.message || 'Unknown error',
        data: null
      };
    }

    // Test 2: Check if we can connect to Supabase with service role key
    try {
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from('users')
        .select('count', { count: 'exact', head: true });

      results.tests.adminConnection = {
        success: !adminError,
        error: adminError ? adminError.message : null,
        data: adminData ? 'Data received' : 'No data'
      };
    } catch (error: any) {
      results.tests.adminConnection = {
        success: false,
        error: error.message || 'Unknown error',
        data: null
      };
    }

    // Test 3: Check if we can get the authenticated user
    try {
      const cookieStore = cookies();
      const authClient = createClient(supabaseUrl, supabaseAnonKey, {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      });

      const { data: authData, error: authError } = await authClient.auth.getUser();

      results.tests.authUser = {
        success: !authError && !!authData.user,
        error: authError ? authError.message : null,
        data: authData.user ? `User found (${authData.user.email})` : 'No user found'
      };
    } catch (error: any) {
      results.tests.authUser = {
        success: false,
        error: error.message || 'Unknown error',
        data: null
      };
    }

    // Test 4: Check if exec_sql function exists
    try {
      const { data: execData, error: execError } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'SELECT 1 as test'
      });

      results.tests.execSql = {
        success: !execError,
        error: execError ? execError.message : null,
        data: execData ? 'Function exists' : 'No data'
      };
    } catch (error: any) {
      results.tests.execSql = {
        success: false,
        error: error.message || 'Unknown error',
        data: null
      };
    }

    // Test 5: Check if we can list tables
    try {
      const { data: tablesData, error: tablesError } = await supabaseAdmin
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .limit(10);

      results.tests.listTables = {
        success: !tablesError,
        error: tablesError ? tablesError.message : null,
        data: tablesData ? `Found ${tablesData.length} tables` : 'No tables found',
        tables: tablesData ? tablesData.map((t: any) => t.table_name).join(', ') : null
      };
    } catch (error: any) {
      results.tests.listTables = {
        success: false,
        error: error.message || 'Unknown error',
        data: null
      };
    }

    // Test 6: Check if specific tables exist
    const tablesToCheck = ['users', 'businesses', 'products', 'orders', 'order_items'];
    results.tests.specificTables = {};

    for (const table of tablesToCheck) {
      try {
        const { count, error } = await supabaseAdmin
          .from(table)
          .select('*', { count: 'exact', head: true });

        results.tests.specificTables[table] = {
          exists: !error,
          error: error ? error.message : null,
          count: count || 0
        };
      } catch (error: any) {
        results.tests.specificTables[table] = {
          exists: false,
          error: error.message || 'Unknown error',
          count: 0
        };
      }
    }

    return NextResponse.json(results);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
