import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Haversine formula to calculate distance between two coordinates
function calculateDistance(
  businessCoords: [number, number],
  customerCoords: [number, number]
): number {
  const [businessLng, businessLat] = businessCoords
  const [customerLng, customerLat] = customerCoords

  const R = 6371 // Earth's radius in km
  const dLat = (customerLat - businessLat) * Math.PI / 180
  const dLon = (customerLng - businessLng) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(businessLat * Math.PI / 180) * Math.cos(customerLat * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  const distanceKm = R * c * 1.3 // Apply road factor (same as checkout)

  return distanceKm
}

export async function POST(request: NextRequest) {
  try {
    // Simple authentication check - only allow for super admin
    const userEmail = '<EMAIL>' // Hardcoded for security

    console.log('🚀 Starting delivery distance backfill...')

    // Get all delivery orders without distance data
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        customer_coordinates,
        delivery_method,
        businesses!inner (
          id,
          coordinates
        )
      `)
      .eq('delivery_method', 'delivery')
      .is('delivery_distance_km', null)
      .not('customer_coordinates', 'is', null)
      .not('businesses.coordinates', 'is', null)

    if (ordersError) {
      console.error('❌ Error fetching orders:', ordersError)
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: ordersError },
        { status: 500 }
      )
    }

    if (!orders || orders.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No orders need distance backfill',
        updated: 0,
        errors: 0
      })
    }

    console.log(`📊 Found ${orders.length} orders that need distance calculation`)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // Process orders
    for (const order of orders) {
      try {
        // Validate coordinates
        if (!order.customer_coordinates || !order.businesses?.coordinates) {
          const errorMsg = `Order ${order.order_number}: Missing coordinates`
          console.warn(`⚠️ ${errorMsg}`)
          errors.push(errorMsg)
          errorCount++
          continue
        }

        // Parse coordinates
        const customerCoords = order.customer_coordinates as [number, number]
        const businessCoords = order.businesses.coordinates as [number, number]

        // Validate coordinate format
        if (!Array.isArray(customerCoords) || customerCoords.length !== 2 ||
            !Array.isArray(businessCoords) || businessCoords.length !== 2) {
          const errorMsg = `Order ${order.order_number}: Invalid coordinate format`
          console.warn(`⚠️ ${errorMsg}`)
          errors.push(errorMsg)
          errorCount++
          continue
        }

        // Calculate distance
        const distance = calculateDistance(businessCoords, customerCoords)

        // Update the order with calculated distance
        const { error: updateError } = await supabase
          .from('orders')
          .update({ delivery_distance_km: distance })
          .eq('id', order.id)

        if (updateError) {
          const errorMsg = `Order ${order.order_number}: Update failed - ${updateError.message}`
          console.error(`❌ ${errorMsg}`)
          errors.push(errorMsg)
          errorCount++
        } else {
          console.log(`✅ Order ${order.order_number}: Distance updated to ${distance.toFixed(2)} km`)
          successCount++
        }

      } catch (error) {
        const errorMsg = `Order ${order.order_number}: Calculation failed - ${error.message}`
        console.error(`❌ ${errorMsg}`)
        errors.push(errorMsg)
        errorCount++
      }
    }

    console.log('\n🎉 Backfill completed!')
    console.log(`✅ Successfully updated: ${successCount} orders`)
    console.log(`❌ Failed to update: ${errorCount} orders`)

    return NextResponse.json({
      success: true,
      message: 'Backfill completed',
      totalProcessed: orders.length,
      updated: successCount,
      errors: errorCount,
      errorDetails: errors.slice(0, 10) // Return first 10 errors for debugging
    })

  } catch (error) {
    console.error('💥 Fatal error during backfill:', error)
    return NextResponse.json(
      { error: 'Backfill failed', details: error.message },
      { status: 500 }
    )
  }
}
