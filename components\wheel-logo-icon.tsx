"use client"

import React from 'react'

interface WheelLogoIconProps {
  className?: string
  size?: number
  color?: string
}

export default function WheelLogoIcon({ 
  className = "", 
  size = 16, 
  color = "currentColor" 
}: WheelLogoIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Outer wheel circle with green fill */}
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke={color}
        strokeWidth="2"
        fill="#059669"
      />

      {/* Inner hub circle */}
      <circle
        cx="12"
        cy="12"
        r="2.5"
        stroke={color}
        strokeWidth="1.5"
        fill="none"
      />

      {/* Wheel spokes */}
      <path
        d="M12 9.5V4"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M12 20V14.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M9.5 12H4"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M20 12H14.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />

      {/* Diagonal spokes */}
      <path
        d="M10.1 10.1L6.5 6.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M17.5 17.5L13.9 13.9"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M10.1 13.9L6.5 17.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M17.5 6.5L13.9 10.1"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  )
}
