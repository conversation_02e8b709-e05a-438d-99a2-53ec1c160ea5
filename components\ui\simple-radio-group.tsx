"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface SimpleRadioGroupProps {
  value?: string
  onValueChange?: (value: string) => void
  className?: string
  children?: React.ReactNode
}

interface SimpleRadioGroupItemProps {
  value: string
  id?: string
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

const SimpleRadioGroupContext = React.createContext<{
  value?: string
  onValueChange?: (value: string) => void
}>({})

const SimpleRadioGroup = React.forwardRef<
  HTMLDivElement,
  SimpleRadioGroupProps
>(({ value, onValueChange, className, children, ...props }, ref) => {
  return (
    <SimpleRadioGroupContext.Provider value={{ value, onValueChange }}>
      <div
        ref={ref}
        role="radiogroup"
        className={cn("space-y-2", className)}
        {...props}
      >
        {children}
      </div>
    </SimpleRadioGroupContext.Provider>
  )
})
SimpleRadioGroup.displayName = "SimpleRadioGroup"

const SimpleRadioGroupItem = React.forwardRef<
  HTMLDivElement,
  SimpleRadioGroupItemProps
>(({ value, id, disabled = false, className, children, ...props }, ref) => {
  const { value: groupValue, onValueChange } = React.useContext(SimpleRadioGroupContext)
  const checked = value === groupValue
  
  const handleClick = () => {
    if (!disabled && onValueChange) {
      onValueChange(value)
    }
  }

  return (
    <div
      ref={ref}
      id={id}
      role="radio"
      aria-checked={checked}
      aria-disabled={disabled}
      tabIndex={disabled ? -1 : 0}
      className={cn(
        "flex h-4 w-4 shrink-0 items-center justify-center rounded-full border border-primary",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:cursor-not-allowed disabled:opacity-50",
        checked ? "border-2 border-primary" : "border border-primary",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      onKeyDown={(e) => {
        if (e.key === " " || e.key === "Enter") {
          e.preventDefault()
          handleClick()
        }
      }}
      {...props}
    >
      {checked && (
        <div className="h-2 w-2 rounded-full bg-primary" />
      )}
      {children}
    </div>
  )
})
SimpleRadioGroupItem.displayName = "SimpleRadioGroupItem"

export { SimpleRadioGroup, SimpleRadioGroupItem }
