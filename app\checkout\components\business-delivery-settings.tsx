"use client";

import React, { useState, useEffect } from "react";
import { Truck, Store, Clock, AlertTriangle, ChevronDown, ChevronUp, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SimpleRadioGroup, SimpleRadioGroupItem } from "@/components/ui/simple-radio-group";
import { BasicTimeSelector } from "./basic-time-selector";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import { format } from "date-fns";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface BusinessDeliverySettingsProps {
  businessId: string;
  businessName: string;
  preparationTime: number;
  deliveryAvailable: boolean; // Keep for backward compatibility
  defaultOpen?: boolean;
  businessType?: string;
  // PHASE 3 STEP 7: Add granular delivery configuration props
  businessDetails?: {
    pickup_available?: boolean;
    pickup_asap_available?: boolean;
    pickup_scheduled_time_available?: boolean;
    pickup_scheduled_period_available?: boolean;
    delivery_asap_available?: boolean;
    delivery_scheduled_time_available?: boolean;
    delivery_scheduled_period_available?: boolean;
    min_advance_booking_minutes?: number;
    max_advance_booking_days?: number;
  };
}

export function BusinessDeliverySettings({
  businessId,
  businessName,
  preparationTime,
  deliveryAvailable,
  defaultOpen = false,
  businessType,
  businessDetails
}: BusinessDeliverySettingsProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const {
    getDeliveryMethod,
    setDeliveryMethod,
    getDeliveryType,
    setDeliveryType,
    getScheduledTime,
    setScheduledTime
  } = useRealtimeCart();

  // Check if this is an errand business
  const isErrandBusiness = businessType === 'errand';

  // PHASE 3 STEP 7: Determine available delivery options based on business configuration
  const pickupAvailable = businessDetails?.pickup_available !== false; // Default to true if not specified
  const deliveryOptionsAvailable = deliveryAvailable && (
    businessDetails?.delivery_asap_available ||
    businessDetails?.delivery_scheduled_time_available ||
    businessDetails?.delivery_scheduled_period_available
  );

  // Determine available timing options for current delivery method
  const currentDeliveryMethod = getDeliveryMethod(businessId);
  const isPickupMethod = currentDeliveryMethod === 'pickup';
  const isDeliveryMethod = currentDeliveryMethod === 'delivery';

  const asapAvailable = isPickupMethod
    ? businessDetails?.pickup_asap_available !== false // Default to true for pickup
    : businessDetails?.delivery_asap_available === true; // Must be explicitly enabled for delivery

  const scheduledTimeAvailable = isPickupMethod
    ? businessDetails?.pickup_scheduled_time_available === true
    : businessDetails?.delivery_scheduled_time_available === true;

  const scheduledPeriodAvailable = isPickupMethod
    ? businessDetails?.pickup_scheduled_period_available === true
    : businessDetails?.delivery_scheduled_period_available === true;

  // For errand businesses, automatically set delivery method and mark as delivery-only
  useEffect(() => {
    if (isErrandBusiness) {
      console.log(`📋 Errand business detected in checkout (${businessName}), setting delivery method to 'delivery'`);
      setDeliveryMethod(businessId, 'delivery');
    }
  }, [isErrandBusiness, businessId, businessName, setDeliveryMethod]);

  // PHASE 3 STEP 7: Auto-select appropriate delivery method based on business configuration
  useEffect(() => {
    const currentMethod = getDeliveryMethod(businessId);

    // If current method is not available, switch to an available option
    if (currentMethod === 'delivery' && !deliveryOptionsAvailable && pickupAvailable) {
      console.log(`🔄 Business ${businessName}: Delivery not available, switching to pickup`);
      setDeliveryMethod(businessId, 'pickup');
    } else if (currentMethod === 'pickup' && !pickupAvailable && deliveryOptionsAvailable) {
      console.log(`🔄 Business ${businessName}: Pickup not available, switching to delivery`);
      setDeliveryMethod(businessId, 'delivery');
    } else if (!currentMethod) {
      // No method set, choose the first available option
      if (pickupAvailable) {
        console.log(`🔄 Business ${businessName}: No method set, defaulting to pickup`);
        setDeliveryMethod(businessId, 'pickup');
      } else if (deliveryOptionsAvailable) {
        console.log(`🔄 Business ${businessName}: No method set, defaulting to delivery`);
        setDeliveryMethod(businessId, 'delivery');
      }
    }
  }, [businessId, businessName, deliveryOptionsAvailable, pickupAvailable, getDeliveryMethod, setDeliveryMethod]);

  // Get current delivery type for this business
  const currentDeliveryType = getDeliveryType(businessId);

  // Get current scheduled time for this business
  const currentScheduledTimeString = getScheduledTime(businessId);
  const [scheduledTime, setLocalScheduledTime] = useState<Date | null>(
    currentScheduledTimeString ? new Date(currentScheduledTimeString) : null
  );

  // Calculate the earliest available time based on preparation time
  const calculateEarliestAvailableTime = () => {
    const now = new Date();

    // Add the preparation time to the current time
    const earliestTime = new Date(now.getTime() + preparationTime * 60 * 1000);

    // Round up to the nearest 15 minutes
    const minutes = earliestTime.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 15) * 15;
    earliestTime.setMinutes(roundedMinutes);
    earliestTime.setSeconds(0);
    earliestTime.setMilliseconds(0);

    return earliestTime;
  };

  const earliestAvailableTime = calculateEarliestAvailableTime();

  // Handle delivery method change
  const handleDeliveryMethodChange = (method: 'delivery' | 'pickup') => {
    setDeliveryMethod(businessId, method);
  };

  // Handle delivery type change
  const handleDeliveryTypeChange = (type: 'asap' | 'scheduled') => {
    setDeliveryType(businessId, type);

    // If changing to asap, clear the scheduled time
    if (type === 'asap') {
      setLocalScheduledTime(null);
      setScheduledTime(businessId, '');
    }
  };

  // Handle scheduled time change
  const handleScheduledTimeChange = (date: Date) => {
    // Only update if the time has actually changed by more than 1 minute
    if (!scheduledTime || Math.abs(date.getTime() - scheduledTime.getTime()) > 60000) {
      setLocalScheduledTime(date);
      setScheduledTime(businessId, date.toISOString());
    }
  };

  // Update local state when cart context changes
  useEffect(() => {
    if (currentScheduledTimeString && (!scheduledTime ||
        new Date(currentScheduledTimeString).getTime() !== scheduledTime.getTime())) {
      setLocalScheduledTime(new Date(currentScheduledTimeString));
    }
  }, [currentScheduledTimeString]);

  // Determine if the delivery settings are complete
  const isComplete = () => {
    if (currentDeliveryType === 'scheduled' && !scheduledTime) {
      return false;
    }
    return true;
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="mb-6 border border-gray-200 rounded-lg overflow-hidden"
    >
      <div className={cn(
        "p-4 border-b border-gray-200 cursor-pointer transition-colors",
        isOpen ? "bg-white" : "bg-gray-50 hover:bg-gray-100"
      )}>
        <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
          <div className="flex items-center">
            {isComplete() ? (
              <CheckCircle className="h-5 w-5 text-emerald-600 mr-3" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-3" />
            )}
            <div>
              <div className="font-medium text-lg text-gray-800">{businessName}</div>
              <div className="text-sm text-gray-500">
                {currentDeliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'} -
                {currentDeliveryType === 'asap'
                  ? ' As soon as possible'
                  : scheduledTime
                    ? ` ${format(scheduledTime, "EEE, MMM d 'at' h:mm a")}`
                    : ' Schedule not set'}
              </div>
            </div>
          </div>
          <div className="text-gray-500">
            {isOpen ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </div>
        </CollapsibleTrigger>
      </div>

      <CollapsibleContent>
        <div className="p-4">
          {/* Delivery Method Selection - Hide for errand businesses */}
          {!isErrandBusiness && (
            <div className="mb-4">
              <Label className="mb-2 block text-sm font-medium">Delivery Method</Label>
              <div className="grid grid-cols-2 gap-2">
                {/* Show delivery option based on business configuration */}
                {deliveryOptionsAvailable ? (
                  <Button
                    type="button"
                    variant={currentDeliveryMethod === 'delivery' ? "default" : "outline"}
                    className={`flex items-center justify-center p-3 ${
                      currentDeliveryMethod === 'delivery' ? 'bg-emerald-600 text-white' : 'bg-white'
                    }`}
                    onClick={() => handleDeliveryMethodChange('delivery')}
                  >
                    <Truck className="h-4 w-4 mr-2" />
                    <span>Delivery</span>
                  </Button>
                ) : (
                  <div className="flex items-center justify-center h-10 p-3 border border-gray-200 rounded-md bg-gray-100 text-gray-500">
                    <Truck className="h-4 w-4 mr-2" />
                    <span>Delivery Unavailable</span>
                  </div>
                )}

                {/* Show pickup option based on business configuration */}
                {pickupAvailable ? (
                  <Button
                    type="button"
                    variant={currentDeliveryMethod === 'pickup' ? "default" : "outline"}
                    className={`flex items-center justify-center p-3 ${
                      currentDeliveryMethod === 'pickup' ? 'bg-emerald-600 text-white' : 'bg-white'
                    }`}
                    onClick={() => handleDeliveryMethodChange('pickup')}
                  >
                    <Store className="h-4 w-4 mr-2" />
                    <span>Pickup</span>
                  </Button>
                ) : (
                  <div className="flex items-center justify-center h-10 p-3 border border-gray-200 rounded-md bg-gray-100 text-gray-500">
                    <Store className="h-4 w-4 mr-2" />
                    <span>Pickup Unavailable</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* For errand businesses, show delivery-only badge */}
          {isErrandBusiness && (
            <div className="mb-4">
              <Label className="mb-2 block text-sm font-medium">Delivery Method</Label>
              <div className="flex justify-center">
                <div className="flex items-center bg-blue-50 text-blue-600 border border-blue-200 rounded-lg px-4 py-3 w-full justify-center">
                  <Truck className="h-4 w-4 mr-2" />
                  <span className="font-medium">Delivery Service Only</span>
                </div>
              </div>
            </div>
          )}

          {/* Delivery/Pickup Time Selection */}
          <div>
            <Label className="mb-2 block text-sm font-medium">
              {currentDeliveryMethod === 'delivery' ? 'Delivery Time' : 'Pickup Time'}
            </Label>

            <SimpleRadioGroup
              value={currentDeliveryType}
              onValueChange={handleDeliveryTypeChange}
              className="space-y-2 mb-3"
            >
              {/* ASAP option - only show if available for current delivery method */}
              {asapAvailable && (
                <div className="flex items-center space-x-2 p-2 bg-white rounded-md border border-gray-200 hover:border-emerald-200 transition-colors">
                  <SimpleRadioGroupItem value="asap" id={`asap-${businessId}`} className="text-emerald-600" />
                  <Label htmlFor={`asap-${businessId}`} className="cursor-pointer flex-1">
                    <div className="font-medium">As soon as possible</div>
                    <div className="text-xs text-gray-500">
                      {currentDeliveryMethod === 'delivery'
                        ? 'Delivery will begin after preparation'
                        : 'Pickup will be ready after preparation'}
                    </div>
                  </Label>
                </div>
              )}

              {/* Scheduled option - only show if any scheduled option is available */}
              {(scheduledTimeAvailable || scheduledPeriodAvailable) && (
                <div className="flex items-center space-x-2 p-2 bg-white rounded-md border border-gray-200 hover:border-emerald-200 transition-colors">
                  <SimpleRadioGroupItem value="scheduled" id={`scheduled-${businessId}`} className="text-emerald-600" />
                  <Label htmlFor={`scheduled-${businessId}`} className="cursor-pointer flex-1">
                    <div className="font-medium">Schedule for later</div>
                    <div className="text-xs text-gray-500">
                      Choose a specific time for {currentDeliveryMethod === 'delivery' ? 'delivery' : 'pickup'}
                    </div>
                  </Label>
                </div>
              )}

              {/* Show message if no timing options are available */}
              {!asapAvailable && !scheduledTimeAvailable && !scheduledPeriodAvailable && (
                <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="text-amber-800 text-sm font-medium">
                    No timing options available for {currentDeliveryMethod}
                  </div>
                  <div className="text-amber-600 text-xs mt-1">
                    Please contact the business directly or try a different delivery method.
                  </div>
                </div>
              )}
            </SimpleRadioGroup>

            {currentDeliveryType === "scheduled" && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-800 mb-2 text-sm">
                  Select {currentDeliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'} Time
                </h4>
                <BasicTimeSelector
                  selectedTime={scheduledTime}
                  onSelectTime={handleScheduledTimeChange}
                />
              </div>
            )}
          </div>

          {/* Preparation Time Info */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
            <div className="flex items-center text-blue-800 text-sm font-medium mb-1">
              <Clock className="h-4 w-4 mr-1 text-blue-600" />
              <span>Preparation Time: {preparationTime} minutes</span>
            </div>
            <p className="text-xs text-blue-600">
              {currentDeliveryMethod === 'delivery'
                ? 'Delivery will begin after the order is prepared.'
                : 'Your order will be ready for pickup after this preparation time.'}
            </p>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
