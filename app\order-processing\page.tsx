'use client'

import { useEffect, useState } from 'react'
import { Clock } from 'lucide-react'
import { OrderDebug } from '@/components/debug/order-debug'

export default function OrderProcessingPage() {
  const [processingTime, setProcessingTime] = useState(0)
  const [isDevelopment, setIsDevelopment] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  useEffect(() => {
    // Check if we're in development mode
    setIsDevelopment(process.env.NODE_ENV === 'development')

    // Get order ID and error from URL query parameters
    const urlParams = new URLSearchParams(window.location.search)
    const orderId = urlParams.get('orderId') || ''
    const error = urlParams.get('error')

    if (error) {
      setErrorMessage(decodeURIComponent(error))
    }

    // Check if order details exist in sessionStorage
    const orderDetailsStr = sessionStorage.getItem('orderDetails')

    if (!orderDetailsStr) {
      console.warn('No order details found in sessionStorage during processing')
      // We'll continue anyway, but log a warning
    } else {
      try {
        // Parse the order details to check if they're valid
        const orderDetails = JSON.parse(orderDetailsStr)
        console.log('ORDER PROCESSING: Order details found in sessionStorage:', {
          orderId: orderDetails.orderId,
          businessCount: orderDetails.businesses?.length || 0,
          itemCount: orderDetails.items?.length || 0
        })

        // Log business details if available
        if (orderDetails.businesses && orderDetails.businesses.length > 0) {
          orderDetails.businesses.forEach((business, index) => {
            console.log(`ORDER PROCESSING: Business ${index + 1}:`, {
              id: business.id,
              name: business.name,
              itemCount: business.items?.length || 0
            })
          })
        }
      } catch (error) {
        console.error('ORDER PROCESSING: Error parsing order details:', error)
      }
    }

    // Verify the order exists in the database before redirecting
    const verifyOrder = async (id: string) => {
      try {
        console.log('ORDER PROCESSING: Verifying order in database:', id);

        // Use the dedicated verification API endpoint
        const response = await fetch(`/api/orders/verify/${id}`);

        if (!response.ok) {
          console.error('❌ ORDER PROCESSING: Verification API error:', response.status, response.statusText);

          // If the API fails, try the fallback method
          return await fallbackVerifyOrder(id);
        }

        const data = await response.json();

        if (data.exists) {
          console.log('✅ ORDER PROCESSING: Order verified via API:', {
            id: data.orderId,
            orderNumber: data.orderNumber || 'Not available',
            status: data.status || 'Not available'
          });
          return true;
        } else {
          console.warn('⚠️ ORDER PROCESSING: Order verification failed via API:', data.message || 'Unknown reason');

          // If the API says the order doesn't exist, try the fallback method
          return await fallbackVerifyOrder(id);
        }
      } catch (error) {
        console.error('❌ ORDER PROCESSING: Error verifying order via API:', error);

        // If the API call fails, try the fallback method
        return await fallbackVerifyOrder(id);
      }
    }

    // Fallback verification method using OrderService
    const fallbackVerifyOrder = async (id: string) => {
      try {
        console.log('ORDER PROCESSING: Using fallback verification for order:', id);

        // Import the order service
        const orderService = await import('@/services/order-service')

        // Try to fetch the order from the database
        const orderDetails = await orderService.default.getOrder(parseInt(id))

        if (orderDetails) {
          console.log('✅ ORDER PROCESSING: Order verified via fallback method:', id);

          // Check if the order has the expected data
          if (orderDetails.id) {
            console.log('✅ ORDER PROCESSING: Order data is valid:', {
              id: orderDetails.id,
              orderNumber: orderDetails.order_number || 'Not available',
              status: orderDetails.status || 'Not available'
            });
            return true;
          } else {
            console.warn('⚠️ ORDER PROCESSING: Order exists but has incomplete data:', orderDetails);
            // Still return true since the order exists in the database
            return true;
          }
        } else {
          console.warn('⚠️ ORDER PROCESSING: Order not found via fallback method:', id);

          // Try to restore the cart from backup
          try {
            const backupCart = localStorage.getItem("loopJerseyCartBackup");
            if (backupCart) {
              console.log("🔄 ORDER PROCESSING: Restoring cart from backup");
              localStorage.setItem("loopJerseyCart", backupCart);
            }
          } catch (backupError) {
            console.error("Error restoring cart from backup:", backupError);
          }

          // Check if we have order details in sessionStorage
          const orderDetailsStr = sessionStorage.getItem('orderDetails');
          if (orderDetailsStr) {
            try {
              const parsedDetails = JSON.parse(orderDetailsStr);
              if (parsedDetails && parsedDetails.orderId) {
                console.log('✅ ORDER PROCESSING: Order details found in sessionStorage, proceeding with confirmation');
                return true;
              }
            } catch (parseError) {
              console.error('Error parsing order details from sessionStorage:', parseError);
            }
          }

          // If we have an order ID from the URL, assume it's valid even if we can't verify it
          if (id) {
            console.log('✅ ORDER PROCESSING: Proceeding with order confirmation using order ID from URL');
            return true;
          }

          return false;
        }
      } catch (error) {
        console.error('❌ ORDER PROCESSING: Error in fallback verification:', error);

        // Check if we have order details in sessionStorage as a fallback
        const orderDetailsStr = sessionStorage.getItem('orderDetails');
        if (orderDetailsStr) {
          try {
            const parsedDetails = JSON.parse(orderDetailsStr);
            if (parsedDetails && parsedDetails.orderId) {
              console.log('✅ ORDER PROCESSING: Order details found in sessionStorage despite API error, proceeding with confirmation');
              return true;
            }
          } catch (parseError) {
            console.error('Error parsing order details from sessionStorage:', parseError);
          }
        }

        // If we have an order ID from the URL, assume it's valid even if we can't verify it
        if (id) {
          console.log('✅ ORDER PROCESSING: Proceeding with order confirmation using order ID from URL despite API error');
          return true;
        }

        return false;
      }
    }

    // Set up a timer to show processing for at least 3 seconds
    const timer = setInterval(async () => {
      setProcessingTime(prev => {
        const newTime = prev + 1

        // After 3 seconds, verify the order and redirect to confirmation
        if (newTime >= 3) {
          clearInterval(timer)

          // If we have an order ID, verify it exists in the database
          if (orderId) {
            console.log('ORDER PROCESSING: Verifying order after processing time:', orderId);

            verifyOrder(orderId).then(exists => {
              if (exists) {
                console.log('✅ ORDER PROCESSING: Order verified, redirecting to confirmation page');

                // Clear the cart backup since the order was successful
                try {
                  // Create a backup of the cart for the order confirmation page to use if needed
                  const cartData = localStorage.getItem("loopJerseyCart");
                  if (cartData) {
                    localStorage.setItem("loopJerseyLastCart", cartData);
                  }

                  // Now remove the cart backup
                  localStorage.removeItem("loopJerseyCartBackup");
                } catch (error) {
                  console.error("Error handling cart backup:", error);
                }

                // Use window.location for navigation instead of router
                window.location.href = `/order-confirmation?orderId=${orderId}`;
              } else {
                console.error('❌ ORDER PROCESSING: Order verification failed, redirecting to failed page');

                // If verification fails completely (both primary and fallback methods),
                // redirect to the order-failed page
                const errorMsg = `Order verification failed: Order ID ${orderId} could not be found in the database`;
                window.location.href = `/order-failed?orderId=${orderId}&error=${encodeURIComponent(errorMsg)}`;
              }
            }).catch(error => {
              console.error('❌ ORDER PROCESSING: Error during order verification:', error);

              // Create a detailed error message with debugging information
              let errorMsg = error.message || "Unknown error";

              // Get the order ID from the URL
              const urlParams = new URLSearchParams(window.location.search);
              const orderId = urlParams.get('orderId');

              if (process.env.NODE_ENV === 'development') {
                // In development, add more details to the error message
                errorMsg += ` (Order ID: ${orderId})`;
                if (error.stack) {
                  console.error('Error stack:', error.stack);
                }
              }

              // If there's an error during verification, redirect to the order-failed page
              window.location.href = `/order-failed?orderId=${orderId}&error=${encodeURIComponent(errorMsg)}`;
            });
          } else {
            console.error('❌ ORDER PROCESSING: No order ID provided');
            // No order ID, redirect to order failed page
            window.location.href = '/order-failed?error=no_order_id';
          }
        }

        return newTime
      })
    }, 1000)

    // Clean up timer on unmount
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="container max-w-5xl py-16 mx-auto text-center">
      <div className="h-24 w-24 rounded-full bg-blue-100 flex items-center justify-center mb-6 mx-auto">
        <Clock className="h-12 w-12 text-blue-600 animate-pulse" />
      </div>
      <h1 className="text-3xl font-bold text-gray-800 mb-4">Processing Your Order</h1>
      <p className="text-gray-600 max-w-lg mx-auto">
        Please wait while we process your order. You will be redirected to the confirmation page shortly.
      </p>

      {/* Debug information - only shown in development mode */}
      {isDevelopment && (
        <OrderDebug
          error={errorMessage || undefined}
          orderId={urlParams.get('orderId') || undefined}
        />
      )}
    </div>
  )
}
