import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    console.log("Business registration API called");

    // Parse the request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (parseError: any) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: `Invalid request format: ${parseError.message}` },
        { status: 400 }
      );
    }

    const {
      email,
      password,
      name,
      phone,
      businessName,
      businessDescription,
      businessAddress,
      businessPostcode,
      businessPhone,
      businessTypeId,
      deliveryFee,
      minimumOrderAmount,
      selectedCategories
    } = requestBody;

    // Validate required fields
    if (!email || !password || !name || !businessName || !businessAddress || !businessPostcode || !businessPhone) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Validate business type ID
    if (!businessTypeId || isNaN(Number(businessTypeId))) {
      console.error("Invalid business type ID:", businessTypeId);
      return NextResponse.json(
        { error: "Invalid business type ID" },
        { status: 400 }
      )
    }

    // Create a Supabase client with the service role key
    console.log("Creating Supabase client with service role key");

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase URL or service role key");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    console.log("Supabase client created successfully");

    // 1. Check if user already exists in auth
    let userId;
    let authUserId;

    // Try to get user by email
    const { data: existingUser, error: getUserError } = await supabase
      .from("users")
      .select("id, email, role")
      .eq("email", email)
      .maybeSingle();

    if (existingUser) {
      console.log("User already exists, using existing user:", existingUser.id);
      userId = existingUser.id;

      // Update the user's role to business_manager if they're not already an admin
      if (existingUser.role !== 'admin' && existingUser.role !== 'super_admin' && existingUser.role !== 'business_manager') {
        const { error: updateRoleError } = await supabase
          .from("users")
          .update({
            role: "business_manager",
            name: name || existingUser.name,
            phone: phone || existingUser.phone,
            updated_at: new Date().toISOString()
          })
          .eq("id", userId);

        if (updateRoleError) {
          console.error("Error updating user role:", updateRoleError);
          // Continue anyway, as this is not critical
        }
      }

      // Check if the user exists in auth
      const { data: authUser, error: authCheckError } = await supabase.auth.admin.listUsers({
        filters: {
          email: email
        }
      });

      if (authCheckError) {
        console.error("Error checking auth user:", authCheckError);
      } else if (authUser && authUser.users && authUser.users.length > 0) {
        authUserId = authUser.users[0].id;
        console.log("Found existing auth user:", authUserId);
      } else {
        // Create auth user if they don't exist
        const { data: newAuthUser, error: authCreateError } = await supabase.auth.admin.createUser({
          email,
          password,
          email_confirm: true
        });

        if (authCreateError) {
          console.error("Error creating auth user:", authCreateError);
        } else if (newAuthUser && newAuthUser.user) {
          authUserId = newAuthUser.user.id;
          console.log("Created new auth user:", authUserId);
        }
      }
    } else {
      // User doesn't exist, create a new one
      console.log("Creating new user with email:", email);

      try {
        // 1. Register the user with Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: {
            name: name,
            phone: phone
          }
        });

        if (authError) {
          console.error("Auth error:", authError);
          return NextResponse.json(
            { error: `Authentication error: ${authError.message}` },
            { status: 400 }
          );
        }

        if (authData && authData.user) {
          authUserId = authData.user.id;
          console.log("Created auth user with ID:", authUserId);
        } else {
          console.error("No auth user data returned");
          return NextResponse.json(
            { error: "Failed to create authentication user" },
            { status: 500 }
          );
        }
      } catch (authCreateError: any) {
        console.error("Exception creating auth user:", authCreateError);
        return NextResponse.json(
          { error: `Exception creating auth user: ${authCreateError.message || "Unknown error"}` },
          { status: 500 }
        );
      }

      // 2. Create a user record in the users table
      console.log("Creating user record in users table");

      try {
        // First check if the user already exists (might have been created by a trigger)
        const { data: existingUserData, error: existingUserError } = await supabase
          .from("users")
          .select("id")
          .eq("email", email)
          .maybeSingle();

        if (!existingUserError && existingUserData) {
          console.log("User already exists in users table:", existingUserData.id);
          userId = existingUserData.id;
        } else {
          // Create the user record
          const { data: userData, error: userError } = await supabase
            .from("users")
            .insert([{
              id: authUserId, // Use the auth user ID if available
              name,
              email,
              phone,
              role: "business_manager",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }])
            .select();

          if (userError) {
            console.error("User creation error:", userError);
            return NextResponse.json(
              { error: `User creation error: ${userError.message}` },
              { status: 500 }
            );
          }

          if (!userData || userData.length === 0) {
            console.error("No user data returned");
            return NextResponse.json(
              { error: "Failed to create user record" },
              { status: 500 }
            );
          }

          userId = userData[0].id;
          console.log("Created user record with ID:", userId);
        }
      } catch (userCreateError: any) {
        console.error("Exception creating user record:", userCreateError);
        return NextResponse.json(
          { error: `Exception creating user record: ${userCreateError.message || "Unknown error"}` },
          { status: 500 }
        );
      }
    }

    // 3. Create a business registration record
    let registrationId: number | null = null;

    try {
      const { data: registrationData, error: registrationError } = await supabase
        .from('business_registrations')
        .insert({
          user_id: userId,
          business_type_id: businessTypeId,
          name: businessName,
          description: businessDescription || '',
          address: businessAddress,
          postcode: businessPostcode,
          phone: businessPhone,
          delivery_fee: deliveryFee,
          minimum_order_amount: minimumOrderAmount,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      if (registrationError) {
        console.error("Business registration error:", registrationError)
        return NextResponse.json(
          { error: `Business registration error: ${registrationError.message || "Unknown error creating business registration"}` },
          { status: 500 }
        )
      }

      if (!registrationData || registrationData.length === 0) {
        console.error("No registration data returned")
        return NextResponse.json(
          { error: "No registration data returned from database" },
          { status: 500 }
        )
      }

      // Store the registration ID
      registrationId = registrationData[0].id;
      console.log("Created business registration with ID:", registrationId);
    } catch (regError: any) {
      console.error("Exception in business registration:", regError)
      return NextResponse.json(
        { error: `Exception in business registration: ${regError.message || "Unknown error"}` },
        { status: 500 }
      )
    }

    // Create a business record directly in the businesses table
    let businessData = null
    try {
      // First, create a slug from the business name
      const slug = businessName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Insert the business record
      const { data: businessRecord, error: businessError } = await supabase
        .from('businesses')
        .insert({
          name: businessName,
          slug: slug,
          description: businessDescription || '',
          address: businessAddress,
          location: businessPostcode,
          phone: businessPhone,
          business_type_id: businessTypeId,
          delivery_fee: deliveryFee,
          minimum_order_amount: minimumOrderAmount,
          is_approved: false, // Set to false initially
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      if (businessError) {
        console.error("Error creating business record:", businessError)
      } else if (businessRecord && businessRecord.length > 0) {
        businessData = businessRecord[0].id

        // Create a business manager record
        const { error: managerError } = await supabase
          .from('business_managers')
          .insert({
            user_id: userId,
            business_id: businessData,
            is_primary: true,
            is_approved: false // Set to false initially
          })

        if (managerError) {
          console.error("Error creating business manager record:", managerError)
        }
      }
    } catch (error) {
      console.error("Error in business creation process:", error)
    }

    // For backward compatibility, also try the register_business function if it exists
    if (!businessData) {
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('register_business', {
            user_id: userId,
            business_type_id: businessTypeId,
            name: businessName,
            description: businessDescription || '',
            address: businessAddress,
            postcode: businessPostcode,
            phone: businessPhone,
            logo_url: null,
            banner_url: null,
            delivery_fee: deliveryFee,
            minimum_order_amount: minimumOrderAmount
          })

        if (!rpcError) {
          businessData = rpcData
        } else {
          console.log("Note: register_business RPC failed:", rpcError)
        }
      } catch (rpcError) {
        console.log("Note: register_business RPC not available:", rpcError)
      }
    }

    // We already have a registration record, so we don't need to fail if business creation failed

    // 4. Add business categories if any were selected
    if (selectedCategories && selectedCategories.length > 0) {
      // Store categories in the registration_categories table
      if (registrationId) {
        const registrationCategoryInserts = selectedCategories.map((categoryId: number, index: number) => ({
          registration_id: registrationId,
          category_id: categoryId,
          is_primary: index === 0 // First category is primary
        }))

        const { error: registrationCategoriesError } = await supabase
          .from("registration_categories")
          .insert(registrationCategoryInserts)

        if (registrationCategoriesError) {
          console.error("Registration categories error:", registrationCategoriesError)
          // Don't fail the whole registration for this
        }
      }

      // Also add to business_categories if we have a business ID (for backward compatibility)
      if (businessData) {
        const businessId = businessData

        const categoryInserts = selectedCategories.map((categoryId: number, index: number) => ({
          business_id: businessId,
          category_id: categoryId,
          is_primary: index === 0 // First category is primary
        }))

        const { error: categoriesError } = await supabase
          .from("business_categories")
          .insert(categoryInserts)

        if (categoriesError) {
          console.error("Categories error:", categoriesError)
          // Don't fail the whole registration for this
        }
      }
    }

    // Log the final response data
    const responseData = {
      success: true,
      registrationId: registrationId,
      businessId: businessData,
      message: "Business registration submitted successfully and is pending approval"
    };

    console.log("Business registration successful, returning:", responseData);

    return NextResponse.json(responseData)
  } catch (error: any) {
    console.error("Unexpected error in business registration:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
