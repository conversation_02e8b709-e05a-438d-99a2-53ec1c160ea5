"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/context/unified-auth-context"
import {
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  Package,
  Clock,
  MapPin,
  BarChart3,
  Star,
  AlertCircle,
  User,
  Building2,
  Truck,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface DashboardData {
  driver: {
    id: string
    name: string
    isVerified: boolean
    isActive: boolean
    vehicleType: string
    totalDeliveries: number
    averageRating: number
    memberSince: string
    averageDeliveriesPerDay: number
  }
  status: {
    isOnline: boolean
    isOnDelivery: boolean
    lastStatusChange: string
    hasLocation: boolean
    locationUpdatedAt: string
  }
  currentOrder: any
  earnings: {
    today: number
    thisWeek: number
    currency: string
  }
  stats: {
    todayDeliveries: number
    availableOrders: number
    totalDeliveries: number
    averageRating: number
  }
  recentDeliveries: any[]
  timestamp: string
}

interface DeliveryRequest {
  id: string
  orderNumber: string
  businessName: string
  pickupAddress: string
  deliveryAddress: string
  estimatedEarnings: number
  expiresAt: string
  status: string
}

export default function DriverDashboard() {
  const { user, isLoading: authLoading } = useAuth()
  const router = useRouter()

  const [driverStatus, setDriverStatus] = useState<{
    isDriver: boolean
    isVerified: boolean
    isActive: boolean
    approvedBusinesses: number
  } | null>(null)
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [deliveryRequests, setDeliveryRequests] = useState<DeliveryRequest[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingRequests, setIsLoadingRequests] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/driver/dashboard')
      if (response.ok) {
        const data = await response.json()
        setDashboardData(data)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }

  // Update driver online status
  const updateDriverStatus = async (isOnline: boolean) => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch('/api/driver/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isOnline, action: 'toggle' }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`)
      }

      const result = await response.json()

      // Update local state
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          status: {
            ...dashboardData.status,
            isOnline: result.status.isOnline,
            lastStatusChange: result.status.lastStatusChange
          }
        })
      }

      // Refresh delivery requests when going online
      if (isOnline) {
        await fetchDeliveryRequests()
      }
    } catch (error) {
      console.error('Error updating driver status:', error)
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Fetch delivery requests
  const fetchDeliveryRequests = async () => {
    setIsLoadingRequests(true)
    try {
      const response = await fetch('/api/driver/deliveries')
      if (response.ok) {
        const data = await response.json()
        setDeliveryRequests(data.deliveries || [])
      }
    } catch (error) {
      console.error('Error fetching delivery requests:', error)
    } finally {
      setIsLoadingRequests(false)
    }
  }

  // Accept delivery request
  const handleAcceptDelivery = async (orderId: string) => {
    try {
      const response = await fetch(`/api/driver/deliveries/${orderId}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        // Refresh delivery requests and dashboard data
        await Promise.all([fetchDeliveryRequests(), fetchDashboardData()])
      } else {
        console.error('Failed to accept delivery')
      }
    } catch (error) {
      console.error('Error accepting delivery:', error)
    }
  }

  // Decline delivery request
  const handleDeclineDelivery = async (orderId: string) => {
    try {
      const response = await fetch(`/api/driver/deliveries/${orderId}/decline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        // Refresh delivery requests
        await fetchDeliveryRequests()
      } else {
        console.error('Failed to decline delivery')
      }
    } catch (error) {
      console.error('Error declining delivery:', error)
    }
  }

  useEffect(() => {
    const checkDriverStatus = async () => {
      // Wait for auth to finish loading before checking user
      if (authLoading) {
        return
      }

      if (!user) {
        // User is not logged in at all - redirect to main login
        router.push('/login?redirectTo=/driver/dashboard')
        return
      }

      try {
        console.log('Checking driver status for user:', user.id, user.email)
        const response = await fetch(`/api/driver/verify?userId=${user.id}`)
        const status = await response.json()
        console.log('Driver status response:', status)

        setDriverStatus(status)

        if (!status.isDriver) {
          // User is logged in but doesn't have a driver profile - redirect to application page
          router.push('/partners/riders/apply')
          return
        }

        if (!status.isVerified) {
          // User has driver profile but not verified - redirect to pending verification page
          router.push('/driver/pending-verification')
          return
        }

        // If driver is verified, fetch dashboard data
        await Promise.all([fetchDashboardData(), fetchDeliveryRequests()])
      } catch (error) {
        console.error('Error checking driver status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkDriverStatus()
  }, [user, router, authLoading])

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading driver dashboard...</p>
        </div>
      </div>
    )
  }

  if (!driverStatus?.isDriver) {
    return null // Will redirect
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Verification Status Alert */}
      {!driverStatus?.isVerified && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Account Pending Verification</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Your driver application is being reviewed. You'll be able to receive deliveries once verified.
              </p>
            </div>
          </div>
        </div>
      )}

      {dashboardData?.status.approvedBusinesses === 0 && driverStatus?.isVerified && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-blue-800">No Business Approvals</h3>
              <p className="text-sm text-blue-700 mt-1">
                You need to apply to businesses to start receiving deliveries.
                <Link href="/driver/profile" className="underline ml-1">Apply to businesses</Link>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Status Toggle */}
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-sm text-gray-500">
            {dashboardData?.status.isOnline ? 'You are online and available for deliveries' : 'You are offline - toggle to start receiving orders'}
          </p>
        </div>

        <div className="flex items-center space-x-3 bg-gray-50 px-4 py-3 rounded-lg border">
          <Label htmlFor="online-toggle" className="text-sm font-medium text-gray-700">
            Available for Deliveries
          </Label>
          <Switch
            id="online-toggle"
            checked={dashboardData?.status.isOnline || false}
            onCheckedChange={updateDriverStatus}
            disabled={isUpdatingStatus}
            className="data-[state=checked]:bg-green-600"
          />
          <div className="flex items-center">
            <span className={`h-2.5 w-2.5 rounded-full mr-2 ${
              dashboardData?.status.isOnline ? 'bg-green-500' : 'bg-gray-400'
            }`}></span>
            <span className={`text-sm font-medium ${
              dashboardData?.status.isOnline ? 'text-green-700' : 'text-gray-500'
            }`}>
              {dashboardData?.status.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <Package className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                New
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Today's Deliveries</p>
              <h3 className="text-2xl font-bold">{dashboardData?.stats.todayDeliveries || 0}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              {dashboardData?.stats.availableOrders || 0} available orders
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                Today
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Today's Earnings</p>
              <h3 className="text-2xl font-bold">
                £{dashboardData?.earnings.today?.toFixed(2) || '0.00'}
              </h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              This week: £{dashboardData?.earnings.thisWeek?.toFixed(2) || '0.00'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-orange-100 p-2 rounded-lg">
                <Building2 className="h-6 w-6 text-orange-600" />
              </div>
              <Badge variant="outline" className="bg-orange-50 text-orange-700 hover:bg-orange-50">
                <Building2 className="mr-1 h-3 w-3" />
                Active
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Business Approvals</p>
              <h3 className="text-2xl font-bold">{dashboardData?.status.approvedBusinesses || 0}</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Approved to deliver for these businesses
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 p-2 rounded-lg">
                <Star className="h-6 w-6 text-purple-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <Star className="mr-1 h-3 w-3" />
                Rating
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <h3 className="text-2xl font-bold">
                {dashboardData?.stats.averageRating?.toFixed(2) || '0.00'}
              </h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Vehicle: {dashboardData?.driver.vehicleType || 'Not set'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Requests & Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div>
              <CardTitle>Delivery Requests</CardTitle>
              <CardDescription>New and upcoming delivery requests</CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="new">
              <TabsList className="mb-4">
                <TabsTrigger value="new">New</TabsTrigger>
                <TabsTrigger value="historic">Historic</TabsTrigger>
              </TabsList>

              <TabsContent value="new" className="space-y-4 mt-0">
                {isLoadingRequests ? (
                  <div className="text-center py-6">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-2"></div>
                    <p className="text-gray-500">Loading delivery requests...</p>
                  </div>
                ) : deliveryRequests.length > 0 ? (
                  deliveryRequests.map((request) => (
                    <Card key={request.id} className="border-2 border-emerald-200 shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className="bg-emerald-100 text-emerald-800 text-xs font-medium px-2.5 py-0.5 rounded">
                              New Request
                            </div>
                            <span className="ml-2 text-sm font-medium">Order #{request.orderNumber}</span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {request.expiresAt ? `Expires ${new Date(request.expiresAt).toLocaleTimeString()}` : 'Available'}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Pickup</p>
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                              <p className="text-sm">{request.businessName}, {request.pickupAddress}</p>
                            </div>
                          </div>

                          <div>
                            <p className="text-xs text-gray-500 mb-1">Dropoff</p>
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                              <p className="text-sm">{request.deliveryAddress}</p>
                            </div>
                          </div>

                          <div className="flex flex-col items-end justify-between">
                            <div className="text-right">
                              <p className="text-sm text-gray-500">Estimated Earnings</p>
                              <p className="text-xl font-bold">£{request.estimatedEarnings.toFixed(2)}</p>
                            </div>

                            <div className="flex mt-2 space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeclineDelivery(request.id)}
                              >
                                Decline
                              </Button>
                              <Button
                                size="sm"
                                className="bg-emerald-600 hover:bg-emerald-700"
                                onClick={() => handleAcceptDelivery(request.id)}
                              >
                                Accept
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <Package className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No new delivery requests at the moment</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={fetchDeliveryRequests}
                    >
                      Refresh
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="historic" className="space-y-4 mt-0">
                {dashboardData?.recentDeliveries && dashboardData.recentDeliveries.length > 0 ? (
                  dashboardData.recentDeliveries.map((delivery) => (
                    <Card key={delivery.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className={`text-xs font-medium px-2.5 py-0.5 rounded ${
                              delivery.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                              delivery.status === 'picked_up' ? 'bg-orange-100 text-orange-800' :
                              delivery.status === 'out_for_delivery' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {delivery.status === 'assigned' ? 'Ready for Pickup' :
                               delivery.status === 'picked_up' ? 'Picked Up' :
                               delivery.status === 'out_for_delivery' ? 'Out for Delivery' :
                               delivery.status}
                            </div>
                            <span className="ml-2 text-sm font-medium">Order #{delivery.order_number}</span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {delivery.ready_time ?
                              `Ready: ${new Date(delivery.ready_time).toLocaleTimeString()}` :
                              new Date(delivery.updated_at).toLocaleTimeString()
                            }
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Pickup</p>
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                              <p className="text-sm">{delivery.business_name}</p>
                            </div>
                          </div>

                          <div>
                            <p className="text-xs text-gray-500 mb-1">Delivery</p>
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-1 flex-shrink-0" />
                              <p className="text-sm">{delivery.delivery_address}</p>
                            </div>
                          </div>

                          <div className="flex flex-col items-end justify-between">
                            <div className="text-right">
                              <p className="text-sm text-gray-500">Delivery Fee</p>
                              <p className="text-xl font-bold">£{delivery.delivery_fee?.toFixed(2) || '0.00'}</p>
                            </div>

                            <Link href={`/driver/delivery-history/${delivery.order_number}`}>
                              <Button size="sm" className="mt-2 bg-emerald-600 hover:bg-emerald-700">
                                {delivery.status === 'assigned' ? 'Start Pickup' :
                                 delivery.status === 'picked_up' ? 'Start Delivery' :
                                 delivery.status === 'out_for_delivery' ? 'Complete Delivery' :
                                 'View Details'}
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No delivery history</p>
                    <p className="text-sm mt-1">Your completed deliveries will appear here</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance</CardTitle>
            <CardDescription>Your delivery metrics for today</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Average Rating</p>
                  <p className="text-sm font-medium">
                    {dashboardData?.driver.averageRating?.toFixed(2) || '0.00'}/5
                  </p>
                </div>
                <Progress
                  value={(dashboardData?.driver.averageRating || 0) * 20}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Total Deliveries</p>
                  <p className="text-sm font-medium">{dashboardData?.driver.totalDeliveries || 0}</p>
                </div>
                <Progress
                  value={Math.min((dashboardData?.driver.totalDeliveries || 0) / 10 * 100, 100)}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Online Status</p>
                  <p className="text-sm font-medium">
                    {dashboardData?.status.isOnline ? 'Online' : 'Offline'}
                  </p>
                </div>
                <Progress
                  value={dashboardData?.status.isOnline ? 100 : 0}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium">Driver Info</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Vehicle Type</span>
                    <span className="font-medium">{dashboardData?.driver.vehicleType || 'Not set'}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Member Since</span>
                    <span className="font-medium">
                      {dashboardData?.driver.memberSince ?
                        new Date(dashboardData.driver.memberSince).toLocaleDateString() :
                        'Unknown'
                      }
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Avg Deliveries/Day</span>
                    <span className="font-medium">
                      {dashboardData?.driver.averageDeliveriesPerDay?.toFixed(1) || '0.0'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="pt-4">
                <Link href="/driver/analytics">
                  <Button variant="outline" className="w-full">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    View Detailed Analytics
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and navigation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/driver/orders">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                  <Package className="h-6 w-6" />
                  <span className="text-sm">Orders</span>
                </Button>
              </Link>
              <Link href="/driver/delivery-history">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                  <Truck className="h-6 w-6" />
                  <span className="text-sm">Delivery History</span>
                </Button>
              </Link>
              <Link href="/driver/earnings">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                  <DollarSign className="h-6 w-6" />
                  <span className="text-sm">Earnings</span>
                </Button>
              </Link>
              <Link href="/driver/businesses">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                  <Building2 className="h-6 w-6" />
                  <span className="text-sm">Businesses</span>
                </Button>
              </Link>
              <Link href="/driver/analytics">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                  <BarChart3 className="h-6 w-6" />
                  <span className="text-sm">Analytics</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
