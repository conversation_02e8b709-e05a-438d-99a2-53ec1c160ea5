"use client"

import { useState } from "react"
import { X, HelpCircle, CheckCircle, AlertCircle, Info } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface WizardFieldDialogProps {
  isOpen: boolean
  onClose: () => void
  field: {
    id: string
    label: string
    description: string
    required: boolean
    type: string
    placeholder?: string
    tips?: string[]
    examples?: string[]
    validation?: string
    section: string
  }
  onNext?: () => void
  onPrevious?: () => void
  currentStep: number
  totalSteps: number
}

export default function WizardFieldDialog({
  isOpen,
  onClose,
  field,
  onNext,
  onPrevious,
  currentStep,
  totalSteps
}: WizardFieldDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 rounded-full p-2">
                <HelpCircle className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-lg font-semibold">
                  {field.label}
                  {field.required && (
                    <Badge variant="outline" className="ml-2 bg-orange-100 text-orange-700 border-orange-300 text-xs">
                      Required
                    </Badge>
                  )}
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600">
                  Step {currentStep} of {totalSteps} • {field.section}
                </DialogDescription>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Field Description */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">What is this field for?</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              {field.description}
            </p>
          </div>

          {/* Field Type and Validation */}
          {field.validation && (
            <Alert className="bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <strong>Validation:</strong> {field.validation}
              </AlertDescription>
            </Alert>
          )}

          {/* Tips Section */}
          {field.tips && field.tips.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Tips for Success
              </h3>
              <ul className="space-y-2">
                {field.tips.map((tip, index) => (
                  <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Examples Section */}
          {field.examples && field.examples.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                Examples
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                {field.examples.map((example, index) => (
                  <div key={index} className="text-sm text-gray-700 font-mono bg-white rounded px-3 py-2 border">
                    {example}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Placeholder Information */}
          {field.placeholder && (
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">Expected Format</h3>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-600 italic">
                  Placeholder: "{field.placeholder}"
                </p>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={onPrevious}
              disabled={currentStep === 1}
            >
              Previous Field
            </Button>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Close Help
              </Button>
              <Button
                onClick={onNext}
                disabled={currentStep === totalSteps}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Next Field
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
