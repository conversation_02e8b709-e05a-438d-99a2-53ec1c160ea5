
import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, FileText, Check, AlertCircle, X } from "lucide-react";
import { useProductUpload } from '@/context/ProductUploadContext';
import { parseCSV, extractHeaders, validateCSV } from '@/utils/csvUtils';

export const UploadStep: React.FC = () => {
  const { setCsvData, setHeaders, setCurrentStep } = useProductUpload();
  const [isDragging, setIsDragging] = useState(false);
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const processFile = (file: File) => {
    setFileName(file.name);
    setError(null);
    
    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (extension !== 'csv') {
      setError('Please upload a CSV file.');
      setIsValid(false);
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const parsedData = parseCSV(content);
        const headers = extractHeaders(parsedData);
        
        // Validate CSV
        const validation = validateCSV(parsedData);
        
        if (!validation.valid) {
          setError(validation.errors.join(' '));
          setIsValid(false);
          return;
        }
        
        setCsvData(parsedData);
        setHeaders(headers);
        setIsValid(true);
      } catch (err) {
        setError('Failed to parse CSV file. Please check the format.');
        setIsValid(false);
      }
    };
    
    reader.readAsText(file);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      processFile(file);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleContinue = () => {
    if (isValid) {
      setCurrentStep(2);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Upload Product CSV</CardTitle>
        <CardDescription>
          Upload a CSV file containing your product data. The file should include columns for product names, descriptions, prices, and image URLs.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div 
          className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
            isDragging ? 'border-app-blue bg-app-blue-light' : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-app-blue-light p-4">
              <Upload className="h-8 w-8 text-app-blue" />
            </div>
            <div>
              <p className="text-lg font-medium">Drag & drop your CSV file here</p>
              <p className="text-sm text-gray-500">or</p>
            </div>
            <Button onClick={handleBrowseClick} variant="outline" className="mt-2">
              Browse Files
            </Button>
            <input 
              ref={fileInputRef}
              type="file" 
              accept=".csv" 
              className="hidden" 
              onChange={handleFileChange}
            />
            {fileName && (
              <div className="flex items-center space-x-2 mt-4 p-2 bg-gray-50 rounded w-full max-w-md">
                <FileText className="h-5 w-5 text-gray-500" />
                <span className="text-sm truncate flex-1">{fileName}</span>
                {isValid ? (
                  <Check className="h-5 w-5 text-green-500" />
                ) : (
                  <X className="h-5 w-5 text-red-500" />
                )}
              </div>
            )}
          </div>
        </div>
        
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
        
        {isValid && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-green-700">CSV file is valid and ready for mapping!</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" disabled>Back</Button>
        <Button onClick={handleContinue} disabled={!isValid}>Continue</Button>
      </CardFooter>
    </Card>
  );
};
