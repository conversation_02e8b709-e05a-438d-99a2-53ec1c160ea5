import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check")
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        )
      }
    } else {
      // Extract the token
      const token = authorization.replace('Bearer ', '');

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          return NextResponse.json(
            { error: "You do not have permission to access this resource" },
            { status: 403 }
          )
        }
      } catch (authError) {
        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          )
        }
      }
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'vote_count'
    const order = searchParams.get('order') || 'desc'
    const status = searchParams.get('status')

    // Build query using adminClient
    let query = adminClient
      .from('feature_requests')
      .select('*', { count: 'exact' })
      .order(sortBy, { ascending: order === 'asc' })
      .range(offset, offset + limit - 1)

    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    // Get feature requests with proper filtering
    const { data: requests, error, count } = await query

    if (error) {
      console.error('Error fetching feature requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch feature requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      requests: requests || [],
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in admin feature requests API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        )
      }
    } else {
      // Extract the token
      const token = authorization.replace('Bearer ', '');

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          return NextResponse.json(
            { error: "You do not have permission to access this resource" },
            { status: 403 }
          )
        }
      } catch (authError) {
        // Continue anyway in development mode
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          )
        }
      }
    }

    const body = await request.json()
    const { requestId, status } = body

    if (!requestId || !status) {
      return NextResponse.json(
        { error: 'Request ID and status are required' },
        { status: 400 }
      )
    }

    // Update the feature request status using adminClient
    const { data: updatedRequest, error } = await adminClient
      .from('feature_requests')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()

    if (error) {
      console.error('Error updating feature request:', error)
      return NextResponse.json(
        { error: 'Failed to update feature request' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Feature request updated successfully',
      request: updatedRequest
    })

  } catch (error) {
    console.error('Error in admin feature requests PATCH API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
