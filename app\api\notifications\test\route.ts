import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // For testing, we'll send a notification to your known subscription
    const testUserId = '43560081-d469-4d4e-85e2-457bda286397' // Your auth_id
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/send-push-notification`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: testUserId,
        title: body.title || 'Test Notification',
        body: body.body || 'This is a test notification',
        data: body.data || { type: 'test' },
        icon: '/wheel-logo.svg',
        badge: '/wheel-logo.svg'
      })
    })

    if (response.ok) {
      const result = await response.json()
      return NextResponse.json({
        success: true,
        message: 'Test notification sent successfully',
        result
      })
    } else {
      const errorText = await response.text()
      return NextResponse.json({
        success: false,
        error: 'Failed to send notification',
        details: errorText
      }, { status: 500 })
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Server error',
      details: error.message
    }, { status: 500 })
  }
}
