import { NextResponse } from "next/server"

export async function GET() {
  try {
    // Return default users data
    // This endpoint doesn't check authentication and is meant as a fallback
    
    console.log("Using fallback users endpoint")
    
    const defaultUsers = {
      users: []
    }
    
    return NextResponse.json(defaultUsers)
  } catch (error: any) {
    console.error("Error in fallback users endpoint:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
