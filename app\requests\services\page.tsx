"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { 
  Truck, 
  TrendingUp, 
  Users, 
  Heart, 
  ArrowLeft, 
  Plus, 
  Vote,
  Tag
} from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"

interface ServiceRequest {
  id: number
  service_name: string
  service_category: string
  description: string
  customer_name: string
  customer_email: string
  vote_count: number
  status: string
  created_at: string
  notes?: string
}

export default function ServiceRequestsPage() {
  const [requests, setRequests] = useState<ServiceRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [showRequestDialog, setShowRequestDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Form state
  const [serviceName, setServiceName] = useState("")
  const [serviceCategory, setServiceCategory] = useState("")
  const [description, setDescription] = useState("")
  const [customerName, setCustomerName] = useState("")
  const [customerEmail, setCustomerEmail] = useState("")
  const [notes, setNotes] = useState("")

  useEffect(() => {
    fetchRequests()
  }, [])

  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/service-requests?limit=20')
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitRequest = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/service-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceName,
          serviceCategory,
          description,
          customerName,
          customerEmail,
          notes
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Service request submitted!",
          description: data.message,
        })
        setShowRequestDialog(false)
        resetForm()
        fetchRequests() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to submit request",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVote = async (request: ServiceRequest) => {
    if (!customerName || !customerEmail) {
      toast({
        title: "Login required",
        description: "Please fill in your name and email to vote",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch('/api/service-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceName: request.service_name,
          serviceCategory: request.service_category,
          description: `Vote for existing service: ${request.description}`,
          customerName,
          customerEmail,
          notes: 'Vote for existing service'
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Vote added!",
          description: data.message,
        })
        fetchRequests() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to vote",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to vote. Please try again.",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setServiceName("")
    setServiceCategory("")
    setDescription("")
    setCustomerName("")
    setCustomerEmail("")
    setNotes("")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const totalVotes = requests.reduce((sum, r) => sum + r.vote_count, 0)
  const topVotes = requests.length > 0 ? Math.max(...requests.map(r => r.vote_count)) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white">
        <div className="container mx-auto px-4 py-12">
          <div className="flex items-center gap-4 mb-6">
            <Link href="/requests">
              <Button variant="outline" size="sm" className="bg-white text-purple-600 hover:bg-purple-50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Requests
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Truck className="h-12 w-12" />
              <h1 className="text-4xl font-bold">Service Requests</h1>
            </div>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Suggest new types of delivery services like grocery, pharmacy, laundry, and more
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{requests.length}</div>
                <div className="text-purple-200">Services Requested</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{totalVotes}</div>
                <div className="text-purple-200">Total Votes</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{topVotes}</div>
                <div className="text-purple-200">Top Votes</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Action Button */}
        <div className="text-center mb-12">
          <Button 
            onClick={() => setShowRequestDialog(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            Request New Service
          </Button>
        </div>

        {/* Top Requests */}
        {requests.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Most Requested Services
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requests
                  .sort((a, b) => b.vote_count - a.vote_count)
                  .slice(0, 5)
                  .map((request, index) => (
                    <div key={request.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                          index === 0 ? 'bg-purple-400 text-purple-900' :
                          index < 3 ? 'bg-purple-500 text-white' : 'bg-gray-300 text-gray-700'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-lg">{request.service_name}</p>
                          <p className="text-sm text-gray-600">{request.service_category}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="flex items-center gap-1">
                            <Vote className="h-4 w-4 text-gray-500" />
                            <span className="font-bold text-purple-600 text-lg">{request.vote_count}</span>
                          </div>
                          <div className="text-xs text-gray-500">votes</div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVote(request)}
                          className="text-purple-600 border-purple-200 hover:bg-purple-50"
                        >
                          Vote
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* All Requests */}
        <Card>
          <CardHeader>
            <CardTitle>All Service Requests</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading service requests...</p>
              </div>
            ) : requests.length === 0 ? (
              <div className="text-center py-12">
                <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No service requests yet</h3>
                <p className="text-gray-500 mb-6">Be the first to suggest a new delivery service!</p>
                <Button 
                  onClick={() => setShowRequestDialog(true)}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Request Service
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {requests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{request.service_name}</h3>
                          {request.service_category && (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Tag className="h-3 w-3" />
                              {request.service_category}
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-600 mb-3">{request.description}</p>
                        <div className="text-sm text-gray-500">
                          Requested by {request.customer_name} on {formatDate(request.created_at)}
                        </div>
                      </div>
                      <div className="text-right ml-6">
                        <div className="flex items-center gap-2 mb-3">
                          <Vote className="h-5 w-5 text-gray-500" />
                          <span className="text-2xl font-bold text-purple-600">{request.vote_count}</span>
                          <span className="text-sm text-gray-500">
                            {request.vote_count === 1 ? 'vote' : 'votes'}
                          </span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVote(request)}
                          className="text-purple-600 border-purple-200 hover:bg-purple-50"
                        >
                          Vote
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Request Dialog */}
      <Dialog open={showRequestDialog} onOpenChange={setShowRequestDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Request New Service</DialogTitle>
            <DialogDescription>
              Suggest a new type of delivery service for Loop Jersey
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRequest} className="space-y-4">
            <div>
              <Label htmlFor="serviceName">Service Name *</Label>
              <Input
                id="serviceName"
                value={serviceName}
                onChange={(e) => setServiceName(e.target.value)}
                placeholder="e.g., Grocery Delivery, Pharmacy Delivery"
                required
              />
            </div>

            <div>
              <Label htmlFor="serviceCategory">Category</Label>
              <Select value={serviceCategory} onValueChange={setServiceCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Delivery">Delivery</SelectItem>
                  <SelectItem value="Healthcare">Healthcare</SelectItem>
                  <SelectItem value="Personal Services">Personal Services</SelectItem>
                  <SelectItem value="Pet Services">Pet Services</SelectItem>
                  <SelectItem value="Gifts & Flowers">Gifts & Flowers</SelectItem>
                  <SelectItem value="Business Services">Business Services</SelectItem>
                  <SelectItem value="Beverages">Beverages</SelectItem>
                  <SelectItem value="Education">Education</SelectItem>
                  <SelectItem value="Technology">Technology</SelectItem>
                  <SelectItem value="Food & Beverages">Food & Beverages</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the service and why it would be valuable..."
                required
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="customerName">Your Name *</Label>
              <Input
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Your full name"
                required
              />
            </div>

            <div>
              <Label htmlFor="customerEmail">Your Email *</Label>
              <Input
                id="customerEmail"
                type="email"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Any additional information..."
                rows={2}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowRequestDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-purple-600 hover:bg-purple-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
