import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  try {
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check database tables
    const { data: tables } = await adminClient
      .from("pg_tables")
      .select("tablename, schemaname")
      .eq("schemaname", "public")

    // Check if the user is authenticated
    const isAuthenticated = !!session && !sessionError

    // Get user info if authenticated
    let userInfo = null
    if (isAuthenticated) {
      const { data: userProfile } = await authClient
        .from("users")
        .select("id, role, business_id, email, name")
        .eq("id", session.user.id)
        .single()

      userInfo = userProfile
    }

    // Check if categories table exists
    const categoriesTableExists = tables?.some(table => table.tablename === "categories")

    // Check if products table exists
    const productsTableExists = tables?.some(table => table.tablename === "products")

    // Check if users table exists
    const usersTableExists = tables?.some(table => table.tablename === "users")

    // Check if businesses table exists
    const businessesTableExists = tables?.some(table => table.tablename === "businesses")

    // Check if product_variants table exists
    const productVariantsTableExists = tables?.some(table => table.tablename === "product_variants")

    // Get environment info
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? "Set" : "Not set",
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? "Set" : "Not set"
    }

    return NextResponse.json({
      status: "API is running",
      isAuthenticated,
      userInfo,
      tables: {
        all: tables,
        categories: categoriesTableExists,
        products: productsTableExists,
        users: usersTableExists,
        businesses: businessesTableExists,
        product_variants: productVariantsTableExists
      },
      environment: envInfo,
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    console.error("Error in GET /api/debug:", error)
    return NextResponse.json(
      { 
        error: error.message || "An unexpected error occurred",
        stack: error.stack
      },
      { status: 500 }
    )
  }
}
