import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// Helper function to create a server-side Supabase client
async function createServerSupabase() {
  const cookieStore = cookies();
  return createServerComponentClient({ cookies: () => cookieStore });
}

export async function POST(request: Request) {
  try {
    console.log("Starting create user profile API request");

    // Get the request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.email || !body.auth_id) {
      return NextResponse.json(
        { error: "Email and auth_id are required" },
        { status: 400 }
      );
    }

    // Create a server-side Supabase client
    const supabase = await createServerSupabase();
    
    // Verify the user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    
    // Only allow creating profiles during signup (when the auth_id matches the session user)
    // or for admins creating users
    let isAuthorized = false;
    
    if (session) {
      // Check if this is the same user (signup flow)
      if (session.user.id === body.auth_id) {
        isAuthorized = true;
      } else {
        // Check if the user is an admin
        const { data: userData } = await adminClient
          .from("users")
          .select("role")
          .eq("auth_id", session.user.id)
          .single();
          
        if (userData && (userData.role === "admin" || userData.role === "super_admin")) {
          isAuthorized = true;
        }
      }
    }
    
    if (!isAuthorized) {
      return NextResponse.json(
        { error: "Not authorized to create user profiles" },
        { status: 403 }
      );
    }
    
    // Create the user profile using the admin client
    const { data, error } = await adminClient
      .from("users")
      .insert([
        {
          email: body.email.toLowerCase(),
          name: body.name || body.email.split("@")[0],
          phone: body.phone || null,
          role: body.role || "customer",
          auth_id: body.auth_id,
          first_name: body.first_name || null,
          last_name: body.last_name || null,
        },
      ])
      .select()
      .single();
      
    if (error) {
      console.error("Error creating user profile:", error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { 
        message: "User profile created successfully",
        data
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error("Unexpected error in create-profile API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
