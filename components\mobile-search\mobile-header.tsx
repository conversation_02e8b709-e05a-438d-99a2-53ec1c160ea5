'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDown, MapPin, Clock, User, Heart, Menu } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileHeaderProps {
  location?: string
  deliveryTime?: string
  className?: string
}

export default function MobileHeader({
  location = 'Jersey',
  deliveryTime = 'Now',
  className
}: MobileHeaderProps) {
  return (
    <div className={cn("sticky top-0 z-50", className)}>
      {/* Main Header - Green Background */}
      <div className="bg-[#22c55e] py-3 px-4 flex items-center justify-between">
        <Link href="/" className="flex items-center">
          <div className="relative w-8 h-8 mr-2">
            <Image
              src="/wheel-logo.svg"
              alt="Loop"
              fill
              className="object-contain animate-spin-slow"
            />
          </div>
          <span className="text-white text-xl font-bold">Loop</span>
        </Link>

        <div className="flex items-center">
          <Link href="/cart" className="relative mr-3">
            <div className="w-8 h-8 flex items-center justify-center bg-white/20 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <path d="M16 10a4 4 0 0 1-8 0"></path>
              </svg>
              <span className="absolute -top-1 -right-1 bg-white text-[#22c55e] text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center">0</span>
            </div>
          </Link>

          <Link href="/account">
            <div className="w-8 h-8 flex items-center justify-center bg-white/20 rounded-full">
              <User className="h-5 w-5 text-white" />
            </div>
          </Link>

          <button className="ml-3">
            <Menu className="h-6 w-6 text-white" />
          </button>
        </div>
      </div>

      {/* Delivery Info */}
      <div className="bg-white border-b border-gray-100 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm">
            <div className="flex items-center mr-2 text-gray-700">
              <Clock className="h-4 w-4 mr-1" />
              <span>Delivery · {deliveryTime}</span>
            </div>
            <div className="flex items-center text-gray-900 font-medium">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{location}</span>
              <ChevronDown className="h-4 w-4 ml-1" />
            </div>
          </div>

          <Link href="/saved">
            <Heart className="h-5 w-5 text-gray-400" />
          </Link>
        </div>
      </div>
    </div>
  )
}
