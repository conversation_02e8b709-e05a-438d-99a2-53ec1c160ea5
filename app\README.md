# Loop Jersey App Directory

This directory contains all the Next.js application routes using the App Router architecture. This is where the main application pages and API routes are defined.

## Directory Structure

```
app/
├── admin/             # Admin dashboard
├── admin-new/         # New admin dashboard (in development)
├── api/               # API routes
├── auth/              # Authentication routes
├── auth-debug/        # Authentication debugging tools
├── business/          # Business pages
├── business-admin/    # Business admin dashboard
├── cafes/             # Cafe-specific pages
├── checkout/          # Checkout flow
├── components/        # App-specific components
├── customers/         # Customer management
├── debug-terminal/    # Debug terminal
├── driver/            # Driver pages
├── driver-mobile/     # Mobile driver app
├── orders/            # Order management
├── pharmacies/        # Pharmacy-specific pages
├── products/          # Product management
├── profile/           # User profile
├── restaurants/       # Restaurant-specific pages
├── search/            # Search functionality
├── shops/             # Shop-specific pages
├── styles/            # App-specific styles
├── super-admin/       # Super admin dashboard
├── error.tsx          # Error handling
├── global-error.tsx   # Global error handling
├── globals.css        # Global CSS
├── layout.tsx         # Root layout
├── loading.tsx        # Loading state
├── middleware.ts      # Middleware
├── not-found.tsx      # 404 page
└── page.tsx           # Home page
```

## Key Routes

### Main Pages
- `/` - Home page
- `/search` - Search page
- `/profile` - User profile
- `/checkout` - Checkout flow

### Business Pages
- `/businesses/[id]` - Individual business page
- `/restaurants` - Restaurant listing
- `/shops` - Shop listing
- `/pharmacies` - Pharmacy listing
- `/cafes` - Cafe listing

### Authentication
- `/auth/login` - Login page
- `/auth/register` - Registration page
- `/auth/forgot-password` - Password recovery

### Admin Areas
- `/admin` - Admin dashboard
- `/business-admin` - Business admin dashboard
- `/super-admin` - Super admin dashboard

### API Routes
- `/api/auth/*` - Authentication endpoints
- `/api/businesses/*` - Business endpoints
- `/api/products/*` - Product endpoints
- `/api/orders/*` - Order endpoints
- `/api/users/*` - User endpoints

## App Router Architecture

Loop Jersey uses Next.js App Router, which provides:

- **Server Components**: Most components are server components by default
- **Client Components**: Client-side interactivity with the `"use client"` directive
- **Route Groups**: Logical grouping of routes without affecting URL structure
- **Layouts**: Shared layouts across routes
- **Loading States**: Built-in loading states with `loading.tsx`
- **Error Handling**: Centralized error handling with `error.tsx` and `global-error.tsx`
- **Not Found Pages**: Custom 404 pages with `not-found.tsx`

## Development Guidelines

### Creating New Routes

1. Create a new directory for the route
2. Add a `page.tsx` file for the main content
3. Add a `layout.tsx` file if needed for route-specific layouts
4. Add `loading.tsx` and `error.tsx` for loading states and error handling

### Example Route Structure

```
app/
└── businesses/
    ├── [id]/
    │   ├── page.tsx       # Business detail page
    │   ├── layout.tsx     # Business detail layout
    │   ├── loading.tsx    # Loading state
    │   └── error.tsx      # Error handling
    ├── page.tsx           # Businesses listing page
    └── layout.tsx         # Businesses layout
```

### API Routes

API routes are defined in the `app/api` directory and follow a similar structure:

```
app/api/
└── businesses/
    ├── [id]/
    │   └── route.ts       # GET, PATCH, DELETE for a specific business
    └── route.ts           # GET, POST for businesses collection
```

Each API route exports HTTP method handlers:

```typescript
// app/api/businesses/route.ts
export async function GET(request: Request) {
  // Handle GET request
}

export async function POST(request: Request) {
  // Handle POST request
}
```

## Middleware

The `middleware.ts` file in the app directory handles:

- Authentication checks
- Redirects for unauthenticated users
- Postcode validation
- Role-based access control

## Root Layout

The `layout.tsx` file in the app directory defines the root layout for the entire application, including:

- HTML structure
- Global providers (Auth, Cart, Location)
- Header and Footer components
- Global styles
