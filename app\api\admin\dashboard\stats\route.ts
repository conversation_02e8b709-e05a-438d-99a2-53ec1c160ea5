import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { headers } from "next/headers";

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    console.log("Starting admin dashboard stats API request");

    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      console.log("No authorization header found, using direct admin access");
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check");
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
    } else {
      console.log("Found authorization header, attempting to verify");

      // Extract the token
      const token = authorization.replace('Bearer ', '');
      
      // In development mode, accept a dummy token
      if (process.env.NODE_ENV === 'development' && token === 'development-mode-token') {
        console.log("Development mode: Using dummy token");
        // Skip verification and proceed with admin access
      } else {
        try {
          // Skip token verification in development mode if token is empty or invalid
          if (process.env.NODE_ENV === 'development' && (!token || token === 'undefined' || token === 'null')) {
            console.log("Development mode: Skipping token verification for empty/invalid token");
          } else {
            // Verify the token
            const { data: { user }, error } = await adminClient.auth.getUser(token);

            if (error || !user) {
              console.error("Invalid token:", error);
              
              // In development mode, continue anyway
              if (process.env.NODE_ENV === 'development') {
                console.log("Development mode: Continuing despite invalid token");
              } else {
                return NextResponse.json(
                  { error: "Invalid authentication token" },
                  { status: 401 }
                );
              }
            } else {
              console.log("Token verified for user:", user.email);
            }
          }
        } catch (authError) {
          console.error("Error verifying token:", authError);
          
          // Continue anyway in development mode
          if (process.env.NODE_ENV !== 'development') {
            return NextResponse.json(
              { error: "Authentication error" },
              { status: 401 }
            );
          } else {
            console.log("Development mode: Continuing despite auth error");
          }
        }
      }

      // Check if the user has admin permissions - in development mode, we'll use a default admin user
      let userEmail = null;
      
      // In development mode, use a default admin email if we don't have a user
      if (process.env.NODE_ENV === 'development') {
        try {
          // Try to get the user from the token if available
          if (token && token !== 'development-mode-token') {
            const { data } = await adminClient.auth.getUser(token);
            userEmail = data?.user?.email;
          }
        } catch (e) {
          console.error("Error getting user from token:", e);
        }
        
        // If we still don't have a user email, use a default
        if (!userEmail) {
          userEmail = '<EMAIL>';
          console.log("Development mode: Using default admin email");
        }
      } else {
        // In production, we need a valid user
        try {
          const { data } = await adminClient.auth.getUser(token);
          userEmail = data?.user?.email;
          
          if (!userEmail) {
            return NextResponse.json(
              { error: "Invalid user" },
              { status: 401 }
            );
          }
        } catch (e) {
          console.error("Error getting user from token:", e);
          return NextResponse.json(
            { error: "Authentication error" },
            { status: 401 }
          );
        }
      }
      
      // Now check if the user has admin permissions
      try {
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", userEmail)
          .single();
          
        if (profileError || !userProfile) {
          console.error("Error fetching user profile:", profileError);
          
          // In development mode, continue anyway
          if (process.env.NODE_ENV === 'development') {
            console.log("Development mode: Continuing despite missing user profile");
          } else {
            return NextResponse.json(
              { error: "User profile not found" },
              { status: 403 }
            );
          }
        } else {
          // Check if the user has admin or super_admin role
          if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
            console.error("Unauthorized access attempt with role:", userProfile.role);
            
            // In development mode, continue anyway
            if (process.env.NODE_ENV === 'development') {
              console.log("Development mode: Continuing despite insufficient permissions");
            } else {
              return NextResponse.json(
                { error: "You do not have permission to access this resource" },
                { status: 403 }
              );
            }
          } else {
            console.log("Admin access verified for user:", userEmail, "with role:", userProfile.role);
          }
        }
      } catch (profileErr) {
        console.error("Exception checking user profile:", profileErr);
        
        // In development mode, continue anyway
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { error: "Error checking permissions" },
            { status: 500 }
          );
        } else {
          console.log("Development mode: Continuing despite profile check error");
        }
      }
    }

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get total businesses
    let totalBusinesses = 0;
    try {
      const { count, error: businessError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true });

      if (businessError) {
        console.error("Error fetching businesses:", businessError);
        // Continue with default value (0)
      } else {
        totalBusinesses = count || 0;
        console.log("Successfully fetched total businesses count:", totalBusinesses);
      }
    } catch (err) {
      console.error("Exception fetching businesses:", err);
      // Continue with default value (0)
    }

    // Get pending businesses count
    let pendingBusinessesCount = 0;
    try {
      const { count, error: pendingError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true })
        .is("is_approved", null);

      if (pendingError) {
        console.error("Error fetching pending businesses:", pendingError);
      } else {
        pendingBusinessesCount = count || 0;
        console.log("Successfully fetched pending businesses count:", pendingBusinessesCount);
      }
    } catch (err) {
      console.error("Exception fetching pending businesses:", err);
    }

    // Get pending businesses details
    let pendingBusinessesData = [];
    try {
      // First check if the view exists
      const { error: viewCheckError } = await supabase
        .from("business_details_view")
        .select("id")
        .limit(1);

      if (viewCheckError) {
        console.error("business_details_view might not exist:", viewCheckError);
        // Try to get data directly from businesses table as fallback
        try {
          const { data: fallbackData, error: fallbackError } = await supabase
            .from("businesses")
            .select("id, name, created_at, business_type_id")
            .is("is_approved", null)
            .order("created_at", { ascending: false })
            .limit(5);

          if (fallbackError) {
            console.error("Error fetching from businesses table:", fallbackError);
          } else if (fallbackData) {
            // Map the data to match expected format
            pendingBusinessesData = fallbackData.map(b => ({
              id: b.id,
              name: b.name,
              business_type: "Business", // Default since we don't have the type name
              created_at: b.created_at
            }));
            console.log("Successfully fetched pending business details from fallback:", pendingBusinessesData.length);
          }
        } catch (fallbackErr) {
          console.error("Exception in fallback query:", fallbackErr);
        }
      } else {
        // View exists, proceed with original query
        const { data, error: pendingDetailsError } = await supabase
          .from("business_details_view")
          .select("id, name, business_type, created_at")
          .is("is_approved", null)
          .order("created_at", { ascending: false })
          .limit(5);

        if (pendingDetailsError) {
          console.error("Error fetching pending business details:", pendingDetailsError);
        } else {
          pendingBusinessesData = data || [];
          console.log("Successfully fetched pending business details:", pendingBusinessesData.length);
        }
      }
    } catch (err) {
      console.error("Exception fetching pending business details:", err);
    }

    // Get total users
    let totalUsers = 0;
    try {
      const { count, error: usersError } = await supabase
        .from("users")
        .select("*", { count: "exact", head: true });

      if (usersError) {
        console.error("Error fetching users:", usersError);
      } else {
        totalUsers = count || 0;
        console.log("Successfully fetched total users count:", totalUsers);
      }
    } catch (err) {
      console.error("Exception fetching users:", err);
    }

    // Get total orders
    let totalOrders = 0;
    try {
      const { count, error: ordersError } = await supabase
        .from("orders")
        .select("*", { count: "exact", head: true });

      if (ordersError) {
        console.error("Error fetching orders:", ordersError);
        // Don't return an error for orders, just log it and continue
        // This allows the dashboard to still work even if orders table doesn't exist yet
      } else {
        totalOrders = count || 0;
        console.log("Successfully fetched total orders count:", totalOrders);
      }
    } catch (err) {
      console.error("Exception fetching orders:", err);
    }

    return NextResponse.json({
      stats: {
        totalBusinesses: totalBusinesses || 0,
        pendingBusinesses: pendingBusinessesCount || 0,
        totalUsers: totalUsers || 0,
        totalOrders: totalOrders || 0
      },
      pendingBusinesses: pendingBusinessesData || []
    });
  } catch (error: any) {
    console.error("Unexpected error in dashboard stats API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
