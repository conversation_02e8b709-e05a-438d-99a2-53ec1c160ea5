"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Download, RefreshCcw, Bell, HelpCircle } from "lucide-react"

// Import our custom components
import { DateRange } from "@/components/date-range-picker"
import { BusinessSelector } from "@/components/orders/business-selector"
import { Order } from "@/components/orders/enhanced-orders-table"
import { EnhancedOrderStats } from "@/components/orders/enhanced-order-stats"
import { EnhancedOrderFilters, OrderFilters } from "@/components/orders/enhanced-order-filters"
import { OrdersTabContent } from "@/components/orders/orders-tab-content"

// Import custom hooks
import { useOrders } from "@/hooks/use-orders"
import { useBusinessData } from "@/hooks/use-business-data"
import { useOrdersRealtime } from "@/hooks/use-orders-realtime"

// Import utilities
import {
  getStatusFilterForTab,
  formatStatusForMessage,
  handlePrintOrder,
  getDefaultDateRange,
  getDefaultFilters
} from "@/utils/orders-utils"

// Import our custom styles
import "@/styles/business-admin.css"

export default function BusinessAdminOrdersEnhanced() {
  const router = useRouter()
  const { toast } = useToast()

  // Page state
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(getDefaultDateRange())
  const [activeFilters, setActiveFilters] = useState<OrderFilters>(getDefaultFilters())
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)

  // Custom hooks
  const {
    business,
    availableBusinesses,
    selectedBusinessId,
    isAdminUser,
    isLoading: businessLoading,
    error: businessError,
    isPendingApproval,
    handleBusinessChange,
  } = useBusinessData()

  const {
    orders,
    totalPages,
    isLoading: ordersLoading,
    error: ordersError,
    orderStats,
    fetchOrders,
    debouncedFetchOrders,
    updateOrderStatus,
    cleanup: cleanupOrders
  } = useOrders({ selectedBusinessId, isAdminUser })

  const {
    realtimeEnabled,
    newOrderCount,
    toggleRealtime,
    resetNewOrderCount,
    updateExistingOrderIds
  } = useOrdersRealtime({
    isAdminUser,
    selectedBusinessId,
    businessId: business?.id || null,
    onOrdersUpdate: () => debouncedFetchOrders(page, pageSize, searchQuery, dateRange, activeFilters)
  })

  // Derived state
  const isLoading = businessLoading || ordersLoading
  const error = businessError || ordersError

  // Update existing order IDs when orders change
  useEffect(() => {
    updateExistingOrderIds(orders)
  }, [orders, updateExistingOrderIds])


  // Fetch orders when dependencies change
  useEffect(() => {
    fetchOrders(page, pageSize, searchQuery, dateRange, activeFilters)
  }, [page, pageSize, searchQuery, dateRange, activeFilters, fetchOrders])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupOrders()
    }
  }, [cleanupOrders])

  // Handle tab change
  const handleTabChange = (value: string) => {
    console.log("Tab changed to:", value)
    setActiveTab(value)

    // Update status filter based on tab using utility function
    const newStatusFilter = getStatusFilterForTab(value)

    setActiveFilters({
      ...activeFilters,
      status: newStatusFilter
    })
  }

  // Handle search with debouncing
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    setPage(1) // Reset to first page when searching

    // Use debounced fetch to avoid excessive API calls
    debouncedFetchOrders(1, pageSize, query, dateRange, activeFilters)
  }, [debouncedFetchOrders, pageSize, dateRange, activeFilters])

  // Memoized refresh function to avoid recreating on every render
  const refreshOrders = useCallback(() => {
    resetNewOrderCount()
    fetchOrders(page, pageSize, searchQuery, dateRange, activeFilters)
  }, [resetNewOrderCount, fetchOrders, page, pageSize, searchQuery, dateRange, activeFilters])

  // Handle filter change
  const handleFilterChange = useCallback((filters: OrderFilters) => {
    setActiveFilters(filters)
    setPage(1) // Reset to first page when changing filters
  }, [])

  // Handle date range change
  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    setDateRange(range)
    setPage(1) // Reset to first page when changing date range
  }, [])

  // Handle pagination
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage)
  }, [])

  // Handle view order
  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/business-admin/orders/${order.id}`)
  }, [router])

  // Handle update order status
  const handleUpdateStatus = useCallback(async (order: Order, newStatus: string) => {
    const success = await updateOrderStatus(order, newStatus)
    if (success) {
      // Refresh orders after a short delay to allow the server to process the update
      setTimeout(() => {
        fetchOrders(page, pageSize, searchQuery, dateRange, activeFilters)
      }, 1000)
    }
  }, [updateOrderStatus, fetchOrders, page, pageSize, searchQuery, dateRange, activeFilters])

  // Handle print order
  const handlePrintOrder = useCallback((order: Order) => {
    const printWindow = window.open(`/business-admin/orders/${order.id}/print`, '_blank')
    if (printWindow) {
      printWindow.focus()
    } else {
      toast({
        variant: "destructive",
        title: "Popup Blocked",
        description: "Please allow popups to print orders",
      })
    }
  }, [toast])

  // Loading state
  if (isLoading && orders.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading orders...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Orders</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={refreshOrders}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 business-admin-container bg-white">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-4 rounded-lg shadow-sm">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            Manage and track your customer orders
          </p>
        </div>
        <div className="flex flex-col md:flex-row items-end md:items-center gap-2 mt-4 md:mt-0">
          {/* Business Selector for Admin Users */}
          {isAdminUser && availableBusinesses.length > 0 && (
            <BusinessSelector
              businesses={availableBusinesses}
              selectedBusinessId={selectedBusinessId}
              onBusinessChange={handleBusinessChange}
            />
          )}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-8 admin-button">
              <Download className="mr-2 h-3.5 w-3.5" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8 relative hover:bg-slate-100 transition-colors admin-button"
              onClick={refreshOrders}
            >
              <RefreshCcw className="h-3.5 w-3.5 mr-2 group-hover:rotate-180 transition-transform duration-300" />
              Refresh
              {newOrderCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse shadow-sm">
                  {newOrderCount}
                </span>
              )}
            </Button>
            <Button
              variant={realtimeEnabled ? "default" : "outline"}
              size="sm"
              className={`h-8 relative admin-button ${realtimeEnabled ? "admin-button-primary bg-emerald-600 hover:bg-emerald-700" : ""}`}
              onClick={toggleRealtime}
            >
              {realtimeEnabled && newOrderCount === 0 ? (
                <>
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-white mr-1 animate-pulse"></span>
                  </div>
                  <Bell className="h-3.5 w-3.5 mr-2 ml-1" />
                </>
              ) : realtimeEnabled ? (
                <>
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-white mr-1 animate-pulse"></span>
                  </div>
                  <Bell className="h-3.5 w-3.5 mr-2 ml-1" />
                </>
              ) : (
                <Bell className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
              )}
              {realtimeEnabled ? "Live" : "Paused"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsHelpDialogOpen(true)}
              className="h-8 flex items-center gap-2 admin-button"
            >
              <HelpCircle className="h-3.5 w-3.5" />
              Help
            </Button>
          </div>
        </div>
      </div>

      {/* Order Stats */}
      <EnhancedOrderStats stats={orderStats} />

      {/* Order Filters */}
      <EnhancedOrderFilters
        onSearch={handleSearch}
        onFilterChange={handleFilterChange}
        onDateRangeChange={handleDateRangeChange}
        dateRange={dateRange}
        activeFilters={activeFilters}
      />

      {/* Tabs */}
      <Tabs defaultValue="all" className="space-y-4" onValueChange={handleTabChange} value={activeTab}>
        <div className="flex items-center justify-between">
          <TabsList className="bg-white p-1 rounded-lg admin-tabs shadow-sm">
            <TabsTrigger
              value="all"
              className={`admin-tab ${activeTab === "all"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-emerald-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}`}
            >
              All Orders
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className={activeTab === "pending"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-yellow-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "pending" ? "bg-yellow-500" : "bg-yellow-400/50"}`}></span>
                Pending
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="processing"
              className={activeTab === "processing"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-blue-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "processing" ? "bg-blue-500" : "bg-blue-400/50"}`}></span>
                In Progress
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              className={activeTab === "completed"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-emerald-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "completed" ? "bg-emerald-500" : "bg-emerald-400/50"}`}></span>
                Delivered
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="cancelled"
              className={activeTab === "cancelled"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-red-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "cancelled" ? "bg-red-500" : "bg-red-400/50"}`}></span>
                Cancelled
              </div>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="space-y-4">
          <OrdersTabContent
            orders={orders}
            totalPages={totalPages}
            currentPage={page}
            isLoading={isLoading}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={refreshOrders}
            onPageChange={handlePageChange}
          />
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <OrdersTabContent
            orders={orders}
            totalPages={totalPages}
            currentPage={page}
            isLoading={isLoading}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={refreshOrders}
            onPageChange={handlePageChange}
          />
        </TabsContent>

        <TabsContent value="processing" className="space-y-4">
          <OrdersTabContent
            orders={orders}
            totalPages={totalPages}
            currentPage={page}
            isLoading={isLoading}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={refreshOrders}
            onPageChange={handlePageChange}
          />
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <OrdersTabContent
            orders={orders}
            totalPages={totalPages}
            currentPage={page}
            isLoading={isLoading}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={refreshOrders}
            onPageChange={handlePageChange}
          />
        </TabsContent>

        <TabsContent value="cancelled" className="space-y-4">
          <OrdersTabContent
            orders={orders}
            totalPages={totalPages}
            currentPage={page}
            isLoading={isLoading}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={refreshOrders}
            onPageChange={handlePageChange}
          />
        </TabsContent>
      </Tabs>

      {/* Help Dialog */}
      <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-600" />
              Orders Management Help
            </DialogTitle>
            <DialogDescription>
              Learn how to efficiently manage and track your customer orders
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 max-h-[60vh] overflow-y-auto">
            {/* Order Status Overview */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-blue-900">Understanding Order Statuses</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-yellow-500"></span>
                  <span><strong>Pending:</strong> New orders waiting for your confirmation</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-yellow-600"></span>
                  <span><strong>Confirmed:</strong> Orders you've accepted and acknowledged</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                  <span><strong>Preparing:</strong> Orders you're currently preparing/cooking</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-indigo-500"></span>
                  <span><strong>Ready:</strong> Orders finished and ready for pickup/delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-purple-500"></span>
                  <span><strong>Out for Delivery:</strong> Orders being delivered to customer</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-emerald-500"></span>
                  <span><strong>Delivered:</strong> Orders successfully completed and delivered</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-red-500"></span>
                  <span><strong>Cancelled:</strong> Orders that were cancelled by customer or business</span>
                </div>
              </div>
            </div>

            {/* Managing Orders */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-blue-900">Managing Your Orders</h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• <strong>Acknowledge new orders quickly</strong> - Aim to confirm within 2-3 minutes</li>
                <li>• <strong>Update order status</strong> as you progress through preparation</li>
                <li>• <strong>Use the Live toggle</strong> to enable real-time notifications for new orders</li>
                <li>• <strong>Filter by status</strong> to focus on specific types of orders</li>
                <li>• <strong>Search by order number</strong> or customer details to find specific orders</li>
                <li>• <strong>Export orders</strong> for record keeping and analysis</li>
              </ul>
            </div>

            {/* Real-time Features */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-blue-900">Real-time Features</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• <strong>Live Mode:</strong> When enabled, you'll receive instant notifications for new orders</p>
                <p>• <strong>Order Counter:</strong> Red badge shows number of new orders since last refresh</p>
                <p>• <strong>Auto-refresh:</strong> Orders update automatically when changes occur</p>
                <p>• <strong>Sound Notifications:</strong> Browser notifications alert you to new orders</p>
              </div>
            </div>

            {/* Filtering and Search */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-blue-900">Filtering and Search</h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• <strong>Status Tabs:</strong> Quick filter by order status (All, Pending, In Progress, Delivered, Cancelled)</li>
                <li>• <strong>Date Range:</strong> Filter orders by specific date periods</li>
                <li>• <strong>Search Bar:</strong> Find orders by order number, customer name, or phone</li>
                <li>• <strong>Advanced Filters:</strong> Filter by delivery type, payment status, and priority</li>
              </ul>
            </div>

            {/* Order Actions */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-blue-900">Order Actions</h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• <strong>View Details:</strong> Click any order to see full order information</li>
                <li>• <strong>Update Status:</strong> Change order status as you progress</li>
                <li>• <strong>Print Order:</strong> Print order details for kitchen or preparation</li>
                <li>• <strong>Contact Customer:</strong> Access customer contact information when needed</li>
              </ul>
            </div>

            {/* Best Practices */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2 text-blue-900">Best Practices</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Confirm new orders within 2-3 minutes to maintain customer confidence</li>
                <li>• Keep order statuses updated to provide accurate delivery estimates</li>
                <li>• Use the Live mode during busy periods to catch new orders immediately</li>
                <li>• Check for overdue orders regularly and communicate with customers if delays occur</li>
                <li>• Export order data regularly for business analysis and record keeping</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsHelpDialogOpen(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
