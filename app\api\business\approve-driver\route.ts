import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      application_id, 
      action, // 'approve', 'reject', 'ban'
      reason, 
      notes,
      business_id 
    } = body

    if (!application_id || !action) {
      return NextResponse.json(
        { error: "Application ID and action are required" },
        { status: 400 }
      )
    }

    if (!['approve', 'reject', 'ban'].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'approve', 'reject', or 'ban'" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get the application to verify it exists and get business_id
    const { data: application, error: appError } = await supabase
      .from('driver_business_approvals')
      .select('id, driver_id, business_id, status')
      .eq('id', application_id)
      .single()

    if (appError || !application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      )
    }

    // Use business_id from application if not provided
    const targetBusinessId = business_id || application.business_id

    // Verify user has access to this business
    const { data: businessAccess, error: accessError } = await supabase
      .from('business_managers')
      .select('id')
      .eq('user_id', user.id)
      .eq('business_id', targetBusinessId)
      .single()

    if (accessError || !businessAccess) {
      return NextResponse.json(
        { error: "Access denied to this business" },
        { status: 403 }
      )
    }

    // Check if application is in pending status
    if (application.status !== 'pending') {
      return NextResponse.json(
        { error: `Application is already ${application.status}` },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = {
      status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'banned',
      decision_date: new Date().toISOString(),
      decision_by: user.id,
      updated_at: new Date().toISOString()
    }

    // Add reason and notes based on action
    if (action === 'reject') {
      updateData.rejection_reason = reason || 'No reason provided'
    } else if (action === 'ban') {
      updateData.ban_reason = reason || 'No reason provided'
    }

    if (notes) {
      updateData.notes = notes
    }

    // Update the application
    const { data: updatedApplication, error: updateError } = await supabase
      .from('driver_business_approvals')
      .update(updateData)
      .eq('id', application_id)
      .select(`
        id,
        driver_id,
        business_id,
        status,
        application_date,
        decision_date,
        decision_by,
        rejection_reason,
        ban_reason,
        notes
      `)
      .single()

    if (updateError) {
      console.error('Error updating application:', updateError)
      return NextResponse.json(
        { error: "Failed to update application" },
        { status: 500 }
      )
    }

    // If approved, create/activate assignment in driver_business_assignments
    if (action === 'approve') {
      const { data: existingAssignment } = await supabase
        .from('driver_business_assignments')
        .select('id')
        .eq('driver_id', application.driver_id)
        .eq('business_id', application.business_id)
        .single()

      if (existingAssignment) {
        // Reactivate existing assignment
        await supabase
          .from('driver_business_assignments')
          .update({
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingAssignment.id)
      } else {
        // Create new assignment
        await supabase
          .from('driver_business_assignments')
          .insert({
            driver_id: application.driver_id,
            business_id: application.business_id,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
      }
    } else {
      // If rejected or banned, deactivate any existing assignment
      await supabase
        .from('driver_business_assignments')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('driver_id', application.driver_id)
        .eq('business_id', application.business_id)
    }

    // Update the corresponding request status
    await supabase
      .from('driver_business_requests')
      .update({
        status: updateData.status,
        updated_at: new Date().toISOString()
      })
      .eq('driver_id', application.driver_id)
      .eq('business_id', application.business_id)

    // Get business and driver info for response
    const { data: business } = await supabase
      .from('businesses')
      .select('name')
      .eq('id', application.business_id)
      .single()

    const { data: driver } = await supabase
      .from('driver_profiles')
      .select(`
        users!driver_profiles_user_id_fkey (
          first_name,
          last_name,
          name
        )
      `)
      .eq('id', application.driver_id)
      .single()

    const actionMessages = {
      approve: `Driver ${driver?.users?.first_name || driver?.users?.name || 'Unknown'} has been approved for ${business?.name || 'your business'}`,
      reject: `Driver ${driver?.users?.first_name || driver?.users?.name || 'Unknown'} has been rejected for ${business?.name || 'your business'}`,
      ban: `Driver ${driver?.users?.first_name || driver?.users?.name || 'Unknown'} has been banned from ${business?.name || 'your business'}`
    }

    return NextResponse.json({
      success: true,
      message: actionMessages[action as keyof typeof actionMessages],
      application: updatedApplication
    })

  } catch (error) {
    console.error('Error in approve driver POST:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
