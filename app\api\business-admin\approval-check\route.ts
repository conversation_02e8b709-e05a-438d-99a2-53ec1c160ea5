import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Session found for user:", session.user.email);

    // Use the admin client to bypass RLS and check the user's role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", session.user.email)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      console.error("User profile not found for email:", session.user.email);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    console.log("Found user profile:", userProfile.name, "with role:", userProfile.role);

    // For admin users, return approved status
    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      return NextResponse.json({
        isAdminUser: true,
        isApproved: true
      });
    }

    // For business users, check if they have an approved business
    if (userProfile.role === 'business_manager' || userProfile.role === 'business_staff') {
      // Check if the user has an approved business
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .maybeSingle();

      if (managerError) {
        console.error("Error fetching business manager data:", managerError);
        return NextResponse.json({
          error: "Error fetching business manager data",
          errorDetails: managerError
        }, { status: 500 });
      }

      // If no manager relationship found, check for pending registration
      if (!managerData) {
        // Check if there's a pending business registration
        const { data: pendingBusiness, error: pendingError } = await adminClient
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .eq("status", "pending")
          .maybeSingle();

        if (pendingError) {
          console.error("Error fetching pending business:", pendingError);
          return NextResponse.json({
            error: "Error fetching pending business",
            errorDetails: pendingError
          }, { status: 500 });
        }

        if (pendingBusiness) {
          console.log("Found pending business registration:", pendingBusiness);
          return NextResponse.json({
            isPendingApproval: true,
            pendingBusiness
          });
        }

        return NextResponse.json({
          isApproved: false,
          message: "No business found for this user"
        });
      }

      // Then check if the business itself is approved
      const { data: businessData, error: businessError } = await adminClient
        .from("businesses")
        .select("is_approved")
        .eq("id", managerData.business_id)
        .single();

      if (businessError) {
        console.error("Error fetching business data:", businessError);
        return NextResponse.json({
          error: "Error fetching business data",
          errorDetails: businessError
        }, { status: 500 });
      }

      if (businessData.is_approved === false) {
        console.log("Business exists but is not approved");
        return NextResponse.json({
          isApproved: false,
          message: "Business exists but is not approved"
        });
      }

      return NextResponse.json({
        isApproved: true,
        businessId: managerData.business_id
      });
    }

    // For other users, return not approved
    return NextResponse.json({
      isApproved: false,
      message: "User role does not have business admin access"
    });

  } catch (error: any) {
    console.error("Error in GET /api/business-admin/approval-check:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
