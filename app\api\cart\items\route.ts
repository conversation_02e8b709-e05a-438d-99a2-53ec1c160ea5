import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// POST endpoint to add an item to the cart
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = cookies();
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    const { data: { user } } = await supabaseClient.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    
    if (!body.item) {
      return NextResponse.json(
        { error: 'Item data is required' },
        { status: 400 }
      );
    }
    
    const item = body.item;
    
    // Validate required fields
    if (!item.id || !item.businessId || !item.businessType || !item.name || item.price === undefined || item.quantity === undefined) {
      return NextResponse.json(
        { error: 'Missing required item fields' },
        { status: 400 }
      );
    }
    
    // Extract customizations if present
    const customizations = item.customizations || [];
    
    // Add the item to the cart
    const { data, error } = await supabase.rpc(
      'add_or_update_cart_item',
      {
        p_user_id: user.id,
        p_product_id: parseInt(item.id, 10) || 0,
        p_variant_id: item.variantId || null,
        p_quantity: item.quantity,
        p_business_id: item.businessId,
        p_business_type: item.businessType,
        p_name: item.name,
        p_price: item.price,
        p_image_url: item.imageUrl || null,
        p_customizations: customizations.length > 0 ? customizations : null
      }
    );
    
    if (error) {
      console.error('Error adding item to cart:', error);
      return NextResponse.json(
        { error: 'Failed to add item to cart', details: error.message },
        { status: 500 }
      );
    }
    
    // Get the updated cart
    const { data: updatedCart, error: cartError } = await supabase.rpc(
      'get_user_cart_enhanced',
      { p_user_id: user.id }
    );
    
    if (cartError) {
      console.error('Error fetching updated cart:', cartError);
      return NextResponse.json({
        success: true,
        item: data,
        error: 'Failed to fetch updated cart'
      });
    }
    
    return NextResponse.json({
      success: true,
      item: data,
      cart: updatedCart.cart,
      businessNames: updatedCart.businessNames
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
