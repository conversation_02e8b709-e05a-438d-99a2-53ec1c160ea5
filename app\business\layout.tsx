"use client"

import { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

interface BusinessLayoutProps {
  children: ReactNode
}

export default function BusinessLayout({ children }: BusinessLayoutProps) {
  const pathname = usePathname()

  // Only show the header on certain pages
  const showHeader = !pathname?.includes('/registration-success')

  return (
    <div className="min-h-screen bg-white">
      {showHeader && (
        <header className="bg-white border-b border-gray-200 shadow-sm">
          <div className="container-fluid py-3 flex items-center justify-between">
            <Link href="/search" className="flex items-center">
              <Button variant="ghost" className="h-10 px-4 font-medium rounded-lg">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Button>
            </Link>
          </div>
        </header>
      )}

      <main className="container-fluid py-6">
        {children}
      </main>
    </div>
  )
}
