import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Session found for user:", session.user.email);

    // Use the admin client to bypass RLS and check the user's role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", session.user.email)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      console.error("User profile not found for email:", session.user.email);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    console.log("Found user profile:", userProfile.name, "with role:", userProfile.role);

    // Get debug information
    const debugData: any = {};

    // Check business_managers table
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("*")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    debugData.businessManager = { data: managerData, error: managerError };

    // If we found a business manager entry, check the business
    if (managerData) {
      const { data: businessData, error: businessError } = await adminClient
        .from("businesses")
        .select("*")
        .eq("id", managerData.business_id)
        .single();

      debugData.business = { data: businessData, error: businessError };
    } else {
      // Try to find businesses directly
      const { data: businesses, error: businessesError } = await adminClient
        .from("businesses")
        .select("*")
        .limit(10);

      debugData.allBusinesses = { data: businesses, error: businessesError };
    }

    // Check for business registrations
    const { data: registrationData, error: registrationError } = await adminClient
      .from("business_registrations")
      .select("*")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    debugData.registration = { data: registrationData, error: registrationError };

    // Determine the status
    let status = "pending";
    if (managerData && debugData.business?.data?.is_approved === true) {
      status = "approved";
    }

    return NextResponse.json({
      status,
      debugInfo: debugData
    });

  } catch (error: any) {
    console.error("Error in GET /api/business-admin/registration-status:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
