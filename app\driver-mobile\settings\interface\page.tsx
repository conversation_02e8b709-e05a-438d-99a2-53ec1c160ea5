"use client"

import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileNavigation } from "@/components/driver-mobile-navigation"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import { DriverInterfacePreference } from "@/components/driver-interface-preference"

export default function InterfaceSettingsPage() {
  return (
    <DriverMobileContainer>
      <DriverDashboardHeader />

      <div className="p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/driver-mobile/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">Interface Preference</h1>
            <p className="text-sm text-gray-600">Choose your preferred driver interface</p>
          </div>
        </div>

        {/* Interface Preference Component */}
        <DriverInterfacePreference />

        {/* Additional Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">About Interface Options</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-800">Mobile Interface</h4>
              <p>Optimized for touch devices with:</p>
              <ul className="list-disc list-inside mt-1 space-y-1 text-xs">
                <li>Large touch-friendly buttons</li>
                <li>Real-time order notifications</li>
                <li>GPS navigation integration</li>
                <li>Quick order acceptance</li>
                <li>Offline mode support</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800">Desktop Interface</h4>
              <p>Full-featured interface with:</p>
              <ul className="list-disc list-inside mt-1 space-y-1 text-xs">
                <li>Comprehensive dashboard and analytics</li>
                <li>Business application management</li>
                <li>Detailed earnings reports</li>
                <li>Advanced order filtering</li>
                <li>Multi-window support</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Access */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Quick Access</h3>
          <div className="grid grid-cols-1 gap-3">
            <Button variant="outline" asChild>
              <Link href="/driver" target="_blank" rel="noopener noreferrer">
                Open Desktop Interface
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/driver-mobile/dashboard">
                Return to Mobile Dashboard
              </Link>
            </Button>
          </div>
        </div>

        {/* Bottom Spacing for Navigation */}
        <div className="h-20"></div>
      </div>

      <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
