"use client"

import dynamic from 'next/dynamic'

// Import the OSM multi-business delivery map component with dynamic import to avoid SSR issues with Leaflet
const OSMMultiBusinessDeliveryMap = dynamic(
  () => import('./osm-multi-business-delivery-map'),
  { ssr: false }
)

interface BusinessLocation {
  id: string;
  name: string;
  coordinates: [number, number];
  type?: string;
}

interface MultiBusinessDeliveryMapProps {
  businesses: BusinessLocation[];
  customerLng: number;
  customerLat: number;
  height?: string;
  className?: string;
  interactive?: boolean;
  showRoutes?: boolean;
}

// This is a wrapper component that dynamically imports the OSM map component
// to avoid SSR issues with Leaflet
function MultiBusinessDeliveryMap(props: MultiBusinessDeliveryMapProps) {
  return <OSMMultiBusinessDeliveryMap {...props} />
}

// Export the component
export default MultiBusinessDeliveryMap;
