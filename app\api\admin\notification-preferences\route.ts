import { NextRequest, NextResponse } from 'next/server'
import { adminClient } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    const { data: preferences, error } = await adminClient
      .from('admin_notification_preferences')
      .select('*')
      .eq('admin_user_id', userId)
      .order('table_name')

    if (error) {
      console.error('Error fetching notification preferences:', error)
      return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 })
    }

    return NextResponse.json({ preferences })
  } catch (error) {
    console.error('Error in notification preferences GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, tableName, notify_insert, notify_update, notify_delete, notify_approval_changes } = body

    if (!userId || !tableName) {
      return NextResponse.json({ error: 'User ID and table name are required' }, { status: 400 })
    }

    // Build the update object with only the fields that were provided
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (notify_insert !== undefined) updateData.notify_insert = notify_insert
    if (notify_update !== undefined) updateData.notify_update = notify_update
    if (notify_delete !== undefined) updateData.notify_delete = notify_delete
    if (notify_approval_changes !== undefined) updateData.notify_approval_changes = notify_approval_changes

    const { data, error } = await adminClient
      .from('admin_notification_preferences')
      .update(updateData)
      .eq('admin_user_id', userId)
      .eq('table_name', tableName)
      .select()

    if (error) {
      console.error('Error updating notification preference:', error)
      return NextResponse.json({ error: 'Failed to update preference' }, { status: 500 })
    }

    if (!data || data.length === 0) {
      // If no rows were updated, the preference doesn't exist yet, so create it
      const { data: newData, error: insertError } = await adminClient
        .from('admin_notification_preferences')
        .insert({
          admin_user_id: userId,
          table_name: tableName,
          notify_insert: notify_insert ?? true,
          notify_update: notify_update ?? false,
          notify_delete: notify_delete ?? true,
          notify_approval_changes: notify_approval_changes ?? true
        })
        .select()

      if (insertError) {
        console.error('Error creating notification preference:', insertError)
        return NextResponse.json({ error: 'Failed to create preference' }, { status: 500 })
      }

      return NextResponse.json({ preference: newData[0] })
    }

    return NextResponse.json({ preference: data[0] })
  } catch (error) {
    console.error('Error in notification preferences PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, tableName, notify_insert = true, notify_update = false, notify_delete = true, notify_approval_changes = true } = body

    if (!userId || !tableName) {
      return NextResponse.json({ error: 'User ID and table name are required' }, { status: 400 })
    }

    const { data, error } = await adminClient
      .from('admin_notification_preferences')
      .insert({
        admin_user_id: userId,
        table_name: tableName,
        notify_insert,
        notify_update,
        notify_delete,
        notify_approval_changes
      })
      .select()

    if (error) {
      console.error('Error creating notification preference:', error)
      return NextResponse.json({ error: 'Failed to create preference' }, { status: 500 })
    }

    return NextResponse.json({ preference: data[0] })
  } catch (error) {
    console.error('Error in notification preferences POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
