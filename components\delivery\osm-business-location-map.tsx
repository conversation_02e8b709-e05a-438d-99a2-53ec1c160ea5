"use client"

import { useState, useEffect, useRef } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface OSMBusinessLocationMapProps {
  businessName: string
  businessLocation: string
  coordinates: [number, number] // [longitude, latitude]
  height?: string
  className?: string
  interactive?: boolean
}

function OSMBusinessLocationMap({
  businessName,
  businessLocation,
  coordinates,
  height = "200px",
  className = "",
  interactive = true,
}: OSMBusinessLocationMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    // Validate coordinates
    if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2 ||
        isNaN(coordinates[0]) || isNaN(coordinates[1])) {
      console.error("Invalid coordinates provided to BusinessLocationMap:", coordinates)
      return
    }

    const [lng, lat] = coordinates

    // Create map instance - note that Leaflet uses [lat, lng] order, opposite of our [lng, lat]
    const map = L.map(mapContainer.current, {
      center: [lat, lng],
      zoom: 15,
      dragging: interactive,
      touchZoom: interactive,
      scrollWheelZoom: interactive,
      doubleClickZoom: interactive,
      boxZoom: interactive,
      keyboard: interactive,
      zoomControl: interactive,
    })

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    // Create custom business icon
    const businessIcon = L.divIcon({
      className: 'custom-business-marker',
      html: `
        <div style="
          background-color: #f97316;
          width: 30px;
          height: 30px;
          border-radius: 50% 50% 50% 0;
          transform: rotate(-45deg);
          border: 3px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div style="
            transform: rotate(45deg);
            color: white;
            font-size: 14px;
            font-weight: bold;
          ">📍</div>
        </div>
      `,
      iconSize: [30, 30],
      iconAnchor: [15, 30],
      popupAnchor: [0, -30]
    })

    // Add business marker
    const businessMarker = L.marker([lat, lng], { icon: businessIcon }).addTo(map)

    // Add popup to business marker
    businessMarker.bindPopup(`
      <div class="p-3 min-w-[200px]">
        <h3 class="font-bold text-lg mb-1">${businessName}</h3>
        <p class="text-sm text-gray-600 mb-2">${businessLocation}</p>
        <p class="text-xs text-orange-600 font-medium">📍 Pickup Location</p>
      </div>
    `)

    // Store map reference
    mapRef.current = map
    setMapLoaded(true)

    // Cleanup function
    return () => {
      if (mapRef.current) {
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [coordinates, businessName, businessLocation, interactive])

  return (
    <div className={`relative ${className}`} style={{ height, zIndex: 1 }}>
      <div
        ref={mapContainer}
        className="w-full h-full rounded-lg overflow-hidden border border-gray-200"
        style={{ zIndex: 1 }}
      />
      {!mapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default OSMBusinessLocationMap
