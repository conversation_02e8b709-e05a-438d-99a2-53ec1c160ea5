"use client"

import { useState } from "react"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import { BusinessStatusBadge } from "./business-status-badge"
import { useToast } from "@/components/ui/use-toast"

interface BusinessStatusUpdaterProps {
  orderId: number
  businessId: number
  businessName: string
  currentStatus: string
  onStatusChange: (newStatus: string) => void
}

export function BusinessStatusUpdater({
  orderId,
  businessId,
  businessName,
  currentStatus,
  onStatusChange
}: BusinessStatusUpdaterProps) {
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>(undefined)
  const [isConfirmOpen, setIsConfirmOpen] = useState(false)
  const [notes, setNotes] = useState("")
  const [isUpdating, setIsUpdating] = useState(false)
  const { toast } = useToast()

  // Status options based on current status - only show the primary next action
  const getStatusOptions = () => {
    switch (currentStatus) {
      case "pending":
        return [
          { value: "confirmed", label: "Confirm" }
        ]
      case "confirmed":
        return [
          { value: "preparing", label: "Start Preparing" }
        ]
      case "preparing":
        return [
          { value: "ready", label: "Ready for Pickup/Delivery" }
        ]
      case "ready":
        return [
          { value: "out_for_delivery", label: "Out for Delivery" }
        ]
      case "out_for_delivery":
        return [
          { value: "delivered", label: "Delivered" }
        ]
      case "delivered":
        return [] // No further status changes allowed
      case "cancelled":
        return [] // No further status changes allowed
      default:
        return [
          { value: "confirmed", label: "Confirm" }
        ]
    }
  }

  // Check if order can be cancelled
  const canCancelOrder = () => {
    const terminalStates = ["delivered", "cancelled"]
    return !terminalStates.includes(currentStatus)
  }

  const statusOptions = getStatusOptions()

  // Get default notes based on status
  const getDefaultNotes = (status: string) => {
    switch (status) {
      case "confirmed":
        return `Order confirmed by ${businessName}`
      case "preparing":
        return `${businessName} has started preparing your order`
      case "ready":
        return `Your order is ready at ${businessName}`
      case "out_for_delivery":
        return `Your order from ${businessName} is out for delivery`
      case "delivered":
        return `Your order from ${businessName} has been delivered`
      case "cancelled":
        return `Order from ${businessName} has been cancelled`
      default:
        return ""
    }
  }

  const handleStatusSelect = (status: string) => {
    setSelectedStatus(status)
    setNotes(getDefaultNotes(status))
    setIsConfirmOpen(true)
  }

  const handleConfirm = async () => {
    if (!selectedStatus) return
    
    setIsUpdating(true)
    
    try {
      const response = await fetch(`/api/orders/${orderId}/business/${businessId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: selectedStatus,
          notes: notes
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update status')
      }
      
      const data = await response.json()
      
      toast({
        title: "Status updated",
        description: `${businessName} status changed to ${selectedStatus}`,
      })
      
      // Call the parent component's callback
      onStatusChange(selectedStatus)
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error updating status",
        description: error.message || "An unexpected error occurred",
      })
    } finally {
      setIsUpdating(false)
      setIsConfirmOpen(false)
      setSelectedStatus(undefined)
    }
  }

  // If no status changes are allowed, show just the current status
  if (statusOptions.length === 0) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">Status:</span>
        <BusinessStatusBadge status={currentStatus} />
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
        <div className="flex items-center">
          <span className="text-sm font-medium mr-2">Current Status:</span>
          <BusinessStatusBadge status={currentStatus} />
        </div>

        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          {/* Primary Status Update Button */}
          {statusOptions.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleStatusSelect(statusOptions[0].value)}
              disabled={isUpdating}
              className={`
                ${statusOptions[0].value === 'confirmed' ? 'border-yellow-200 text-yellow-700 hover:bg-yellow-50' : ''}
                ${statusOptions[0].value === 'preparing' ? 'border-blue-200 text-blue-700 hover:bg-blue-50' : ''}
                ${statusOptions[0].value === 'ready' ? 'border-indigo-200 text-indigo-700 hover:bg-indigo-50' : ''}
                ${statusOptions[0].value === 'out_for_delivery' ? 'border-purple-200 text-purple-700 hover:bg-purple-50' : ''}
                ${statusOptions[0].value === 'delivered' ? 'border-emerald-200 text-emerald-700 hover:bg-emerald-50' : ''}
              `}
            >
              {isUpdating ? (
                <Loader2 className="mr-1 h-3.5 w-3.5 animate-spin" />
              ) : null}
              {statusOptions[0].label}
            </Button>
          )}

          {/* Cancel Button - shown separately if order can be cancelled */}
          {canCancelOrder() && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleStatusSelect('cancelled')}
              disabled={isUpdating}
              className="border-red-200 text-red-700 hover:bg-red-50"
            >
              {isUpdating ? (
                <Loader2 className="mr-1 h-3.5 w-3.5 animate-spin" />
              ) : null}
              Cancel
            </Button>
          )}
        </div>
      </div>

      <AlertDialog open={isConfirmOpen} onOpenChange={setIsConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Update Status to {selectedStatus}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will change the status of {businessName} for order #{orderId}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="my-4">
            <label htmlFor="notes" className="text-sm font-medium mb-1 block">
              Notes (optional)
            </label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add notes about this status change"
              className="w-full"
            />
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={(e) => {
                e.preventDefault()
                handleConfirm()
              }}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Confirm'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
