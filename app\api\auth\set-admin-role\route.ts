import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    // Get the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;
    
    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
    
    // If no session, try to use custom token
    let userEmail = session?.user?.email;
    
    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }
    
    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Setting admin role for user:", userEmail);

    // Get the user profile
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, email")
      .eq("email", userEmail)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      
      // If user not found, create a new user with admin role
      if (profileError.code === "PGRST116") {
        console.log("User not found, creating new user with admin role");
        
        const { data: newUser, error: createError } = await adminClient
          .from("users")
          .insert([{
            email: userEmail,
            name: userEmail?.split('@')[0] || "Admin User",
            role: "admin",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select()
          .single();
          
        if (createError) {
          console.error("Error creating user:", createError);
          return NextResponse.json(
            { error: "Failed to create user" },
            { status: 500 }
          );
        }
        
        return NextResponse.json({
          message: "User created with admin role",
          user: newUser
        });
      }
      
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    // Update the user's role to admin
    const { data: updatedUser, error: updateError } = await adminClient
      .from("users")
      .update({ 
        role: "admin",
        updated_at: new Date().toISOString()
      })
      .eq("id", userProfile.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating user role:", updateError);
      return NextResponse.json(
        { error: "Failed to update user role" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "User role updated to admin",
      user: updatedUser
    });
  } catch (error: any) {
    console.error("Error in set-admin-role API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
