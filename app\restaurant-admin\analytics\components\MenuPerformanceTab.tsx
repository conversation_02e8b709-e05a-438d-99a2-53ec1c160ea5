"use client"

import {
  <PERSON><PERSON>pR<PERSON>,
  ArrowDownRight,
  Per<PERSON>,
  Star,
  Edit,
  Trash2,
  Plus,
  Minus,
  Utensils,
  Soup,
  Sandwich,
  Dessert,
  Wine,
  AlertTriangle,
  BarChart2,
  CheckCircle,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import {
  topDishesData,
  menuCategoryData,
  menuTrendData,
  menuModificationData,
  menuAvailabilityData
} from "../data/mock-data"

export default function MenuPerformanceTab() {
  return (
    <>
      {/* Menu Performance Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-emerald-100 p-2 rounded-lg">
                <Utensils className="h-6 w-6 text-emerald-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                6%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Total Menu Items</p>
              <h3 className="text-2xl font-bold">42</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">5 new items this month</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Star className="h-6 w-6 text-blue-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                0.2
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <h3 className="text-2xl font-bold">4.6</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Across all menu items</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-amber-100 p-2 rounded-lg">
                <Percent className="h-6 w-6 text-amber-600" />
              </div>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                2%
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Profit Margin</p>
              <h3 className="text-2xl font-bold">68%</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Average across all items</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-red-100 p-2 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
                <ArrowDownRight className="mr-1 h-3 w-3" />
                3
              </Badge>
            </div>
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Stockouts</p>
              <h3 className="text-2xl font-bold">6</h3>
            </div>
            <div className="mt-2 text-xs text-gray-500">Last 30 days</div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Dishes */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Top Performing Dishes</CardTitle>
              <CardDescription>Your best-selling menu items</CardDescription>
            </div>
            <Select defaultValue="orders">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="orders">By Orders</SelectItem>
                <SelectItem value="revenue">By Revenue</SelectItem>
                <SelectItem value="rating">By Rating</SelectItem>
                <SelectItem value="profit">By Profit</SelectItem>
              </SelectContent>
            </Select>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left font-medium py-3 px-4">Dish</th>
                    <th className="text-center font-medium py-3 px-4">Category</th>
                    <th className="text-center font-medium py-3 px-4">Orders</th>
                    <th className="text-center font-medium py-3 px-4">Revenue</th>
                    <th className="text-center font-medium py-3 px-4">Rating</th>
                    <th className="text-center font-medium py-3 px-4">Cost %</th>
                    <th className="text-right font-medium py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {topDishesData.map((dish, index) => (
                    <tr key={index} className="border-b last:border-0 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-gray-700 font-medium text-sm">{index + 1}</span>
                          </div>
                          <span className="font-medium">{dish.name}</span>
                        </div>
                      </td>
                      <td className="text-center py-3 px-4">{dish.category}</td>
                      <td className="text-center py-3 px-4">{dish.orders}</td>
                      <td className="text-center py-3 px-4">£{dish.revenue.toFixed(2)}</td>
                      <td className="text-center py-3 px-4">
                        <div className="flex items-center justify-center">
                          <span className="mr-1">{dish.rating}</span>
                          <Star className="h-3 w-3 text-amber-500 fill-current" />
                        </div>
                      </td>
                      <td className="text-center py-3 px-4">
                        <Badge variant={dish.costPercentage > 40 ? "destructive" : dish.costPercentage > 30 ? "outline" : "secondary"}>
                          {dish.costPercentage}%
                        </Badge>
                      </td>
                      <td className="text-right py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menu Category Performance & Trend */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Category Performance</CardTitle>
            <CardDescription>Performance by menu category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {menuCategoryData.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {category.name === "Starters" && <Soup className="h-4 w-4 mr-2 text-emerald-500" />}
                      {category.name === "Mains" && <Utensils className="h-4 w-4 mr-2 text-blue-500" />}
                      {category.name === "Sides" && <Sandwich className="h-4 w-4 mr-2 text-purple-500" />}
                      {category.name === "Desserts" && <Dessert className="h-4 w-4 mr-2 text-amber-500" />}
                      {category.name === "Drinks" && <Wine className="h-4 w-4 mr-2 text-red-500" />}
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 mr-1">Orders:</span>
                        <span className="text-sm font-medium">{category.orders}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 mr-1">Revenue:</span>
                        <span className="text-sm font-medium">£{category.revenue.toFixed(2)}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 mr-1">Rating:</span>
                        <span className="text-sm font-medium">{category.avgRating}</span>
                        <Star className="h-3 w-3 text-amber-500 fill-current ml-1" />
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full"
                      style={{ width: `${(category.revenue / 1842.50) * 100}%`, backgroundColor: category.color }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Menu Category Trend</CardTitle>
            <CardDescription>Weekly orders by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full bg-gray-50 rounded-lg relative overflow-hidden">
              {/* Visual placeholder for bar chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <BarChart2 className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-[200px] flex items-end justify-around px-4">
                {menuTrendData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className="relative w-12 flex flex-col items-center">
                      {/* Stacked bars for each category */}
                      <div
                        className="w-12 bg-emerald-500 rounded-t-sm absolute bottom-0"
                        style={{ height: `${(item.starters / 25) * 150}px` }}
                      />
                      <div
                        className="w-12 bg-blue-500 rounded-t-sm absolute bottom-0"
                        style={{
                          height: `${(item.mains / 25) * 150}px`,
                          transform: `translateY(-${(item.starters / 25) * 150}px)`
                        }}
                      />
                      <div
                        className="w-12 bg-purple-500 rounded-t-sm absolute bottom-0"
                        style={{
                          height: `${(item.sides / 25) * 150}px`,
                          transform: `translateY(-${((item.starters + item.mains) / 25) * 150}px)`
                        }}
                      />
                      <div
                        className="w-12 bg-amber-500 rounded-t-sm absolute bottom-0"
                        style={{
                          height: `${(item.desserts / 25) * 150}px`,
                          transform: `translateY(-${((item.starters + item.mains + item.sides) / 25) * 150}px)`
                        }}
                      />
                      <div
                        className="w-12 bg-red-500 rounded-t-sm absolute bottom-0"
                        style={{
                          height: `${(item.drinks / 25) * 150}px`,
                          transform: `translateY(-${((item.starters + item.mains + item.sides + item.desserts) / 25) * 150}px)`
                        }}
                      />
                    </div>
                    <div className="text-xs text-gray-400 mt-2">{item.week}</div>
                  </div>
                ))}
              </div>
              <div className="absolute top-2 right-2 flex flex-wrap items-center gap-2 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-emerald-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Starters</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Mains</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-purple-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Sides</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-amber-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Desserts</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-sm mr-1"></div>
                  <span className="text-gray-500">Drinks</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menu Modifications & Stockouts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Popular Modifications</CardTitle>
            <CardDescription>Most requested menu item modifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {menuModificationData.map((mod, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                      {mod.name.includes("Extra") ? (
                        <Plus className="h-4 w-4 text-emerald-600" />
                      ) : mod.name.includes("Substitute") ? (
                        <Minus className="h-4 w-4 text-amber-600" />
                      ) : (
                        <Edit className="h-4 w-4 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{mod.name}</p>
                      <p className="text-xs text-gray-500">{mod.count} orders · £{mod.revenue.toFixed(2)} revenue</p>
                    </div>
                  </div>
                  <div>
                    <Progress value={(mod.count / 42) * 100} className="h-2 w-24" />
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 pt-4 border-t border-gray-100">
              <Button variant="outline" size="sm" className="w-full">
                View All Modifications
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Item Availability</CardTitle>
            <CardDescription>Recent stockouts and their impact</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {menuAvailabilityData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 ${item.stockouts > 0 ? 'bg-red-100' : 'bg-emerald-100'} rounded-full flex items-center justify-center mr-3`}>
                      {item.stockouts > 0 ? (
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-emerald-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-xs text-gray-500">
                        {item.stockouts > 0
                          ? `${item.stockouts} stockouts · £${item.impact.toFixed(2)} lost revenue`
                          : 'Always in stock'}
                      </p>
                    </div>
                  </div>
                  <div>
                    {item.stockouts > 0 ? (
                      <Badge variant="destructive">{item.stockouts} stockouts</Badge>
                    ) : (
                      <Badge variant="secondary">100% available</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Total Lost Revenue</p>
                  <p className="text-xs text-gray-500">Due to stockouts this month</p>
                </div>
                <div className="text-xl font-bold text-red-600">
                  £{menuAvailabilityData.reduce((sum, item) => sum + item.impact, 0).toFixed(2)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menu Optimization Suggestions */}
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Menu Optimization Suggestions</CardTitle>
            <CardDescription>Recommendations to improve your menu performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 border-l-4 border-blue-500 rounded">
                <h4 className="font-medium flex items-center">
                  <Star className="h-4 w-4 text-blue-500 mr-2" />
                  Promote High-Margin Items
                </h4>
                <p className="text-sm mt-1">
                  "Jersey Royal Potato Salad" and "Jersey Cream Brûlée" have the highest profit margins.
                  Consider featuring these items more prominently or creating combo deals.
                </p>
              </div>

              <div className="p-4 bg-amber-50 border-l-4 border-amber-500 rounded">
                <h4 className="font-medium flex items-center">
                  <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                  Address Stockout Issues
                </h4>
                <p className="text-sm mt-1">
                  "Lobster Thermidor" has had 3 stockouts this month, resulting in £89.85 of lost revenue.
                  Consider adjusting your inventory management for this item.
                </p>
              </div>

              <div className="p-4 bg-emerald-50 border-l-4 border-emerald-500 rounded">
                <h4 className="font-medium flex items-center">
                  <Plus className="h-4 w-4 text-emerald-500 mr-2" />
                  Add Modification Options
                </h4>
                <p className="text-sm mt-1">
                  "Extra Cheese" is your most requested modification (42 orders).
                  Consider adding this as a standard option to streamline ordering.
                </p>
              </div>

              <div className="p-4 bg-purple-50 border-l-4 border-purple-500 rounded">
                <h4 className="font-medium flex items-center">
                  <Utensils className="h-4 w-4 text-purple-500 mr-2" />
                  Menu Balance
                </h4>
                <p className="text-sm mt-1">
                  Your "Mains" category generates 52% of revenue but "Sides" have the highest profit margin.
                  Consider adding 2-3 more side options to increase average order value.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
