import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'

// Rename the file path but keep the route the same for backward compatibility
export async function GET() {
  try {
    // Create a Supabase client for authentication
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Create an admin client for database operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if the user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'You must be logged in to perform this action' },
        { status: 401 }
      )
    }

    // Check if the user is an admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('auth_id', user.id)
      .single()

    if (userError || !userData || (userData.role !== 'admin' && userData.role !== 'super_admin')) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'You must be an admin to perform this action' },
        { status: 403 }
      )
    }

    // Add the average_rating column to the businesses table
    try {
      // Try direct SQL with the admin client
      const { error: sqlError } = await supabaseAdmin
        .from('businesses')
        .select('id')
        .limit(1)
        .then(async () => {
          // If we can access the businesses table, try to add the column
          return await supabaseAdmin.rpc('add_average_rating_column', {})
            .catch(async () => {
              // If the RPC doesn't exist, try direct SQL
              console.log('RPC function not found, trying direct SQL')

              // Check if the rating column exists and has the correct type
              const { data: columnInfo, error: columnError } = await supabaseAdmin.query(`
                SELECT data_type, column_default
                FROM information_schema.columns
                WHERE table_name = 'businesses' AND column_name = 'rating'
              `)

              if (columnError || !columnInfo || columnInfo.length === 0) {
                // Rating column doesn't exist, create it
                await supabaseAdmin.query(`
                  ALTER TABLE businesses
                  ADD COLUMN IF NOT EXISTS rating DECIMAL(3,1) DEFAULT 0.0
                `)
              } else {
                // Column exists, check if it has the correct type
                const dataType = columnInfo[0]?.data_type
                if (dataType !== 'numeric' && dataType !== 'decimal') {
                  // Update the column type
                  await supabaseAdmin.query(`
                    ALTER TABLE businesses
                    ALTER COLUMN rating TYPE DECIMAL(3,1) USING rating::DECIMAL(3,1)
                  `)
                }

                // Ensure it has a default value
                await supabaseAdmin.query(`
                  ALTER TABLE businesses
                  ALTER COLUMN rating SET DEFAULT 0.0
                `)
              }

              // Add a comment to the column
              await supabaseAdmin.query(`
                COMMENT ON COLUMN businesses.rating IS 'Rating of the business (0.0 to 5.0)'
              `)

              // Create an index if it doesn't exist
              return await supabaseAdmin.query(`
                CREATE INDEX IF NOT EXISTS idx_businesses_rating ON businesses(rating)
              `)
            })
        })

      if (sqlError) {
        console.error('Error adding average_rating column:', sqlError)
        return NextResponse.json(
          { error: 'Database Error', message: sqlError.message },
          { status: 500 }
        )
      }
    } catch (sqlError) {
      console.error('Exception adding average_rating column:', sqlError)
      return NextResponse.json(
        { error: 'Database Error', message: sqlError instanceof Error ? sqlError.message : 'Unknown database error' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Rating column in businesses table has been updated successfully'
    })
  } catch (error) {
    console.error('Error in add-average-rating API route:', error)
    return NextResponse.json(
      { error: 'Server Error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
