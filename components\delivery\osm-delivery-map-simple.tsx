"use client"

import { useState, useEffect, useRef, memo } from "react"
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { getRoute } from "@/lib/map-utils"

interface OSMDeliveryMapSimpleProps {
  restaurantLng: number
  restaurantLat: number
  customerLng: number
  customerLat: number
  height?: string
  className?: string
  interactive?: boolean
  showRoute?: boolean
}

function OSMDeliveryMapSimple({
  restaurantLng,
  restaurantLat,
  customerLng,
  customerLat,
  height = "300px",
  className = "",
  interactive = true,
  showRoute = true,
}: OSMDeliveryMapSimpleProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const mapRef = useRef<L.Map | null>(null)
  const routeRef = useRef<L.Polyline | null>(null)
  const restaurantMarkerRef = useRef<L.Marker | null>(null)
  const customerMarkerRef = useRef<L.Marker | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)

  // Fix Leaflet icon issues in Next.js
  useEffect(() => {
    // This is needed to fix the marker icon issues with webpack
    delete (L.Icon.Default.prototype as any)._getIconUrl

    L.Icon.Default.mergeOptions({
      iconRetinaUrl: '/leaflet/marker-icon-2x.png',
      iconUrl: '/leaflet/marker-icon.png',
      shadowUrl: '/leaflet/marker-shadow.png',
    })
  }, [])

  // Initialize map when component mounts
  useEffect(() => {
    if (!mapContainer.current || mapRef.current) return

    // Create map instance
    const map = L.map(mapContainer.current, {
      center: [(restaurantLat + customerLat) / 2, (restaurantLng + customerLng) / 2],
      zoom: 11,
      dragging: interactive,
      touchZoom: interactive,
      scrollWheelZoom: interactive,
      doubleClickZoom: interactive,
      boxZoom: interactive,
      keyboard: interactive,
      zoomControl: interactive,
    })

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    mapRef.current = map
    setMapLoaded(true)

    return () => {
      if (routeRef.current) {
        routeRef.current.remove()
        routeRef.current = null
      }
      if (restaurantMarkerRef.current) {
        restaurantMarkerRef.current.remove()
        restaurantMarkerRef.current = null
      }
      if (customerMarkerRef.current) {
        customerMarkerRef.current.remove()
        customerMarkerRef.current = null
      }
      map.remove()
      mapRef.current = null
    }
  }, [interactive, restaurantLat, restaurantLng, customerLat, customerLng])

  // Add markers and route when map is loaded or when props change
  useEffect(() => {
    if (!mapRef.current || !mapLoaded) return

    const map = mapRef.current

    // Clear previous markers and route
    if (restaurantMarkerRef.current) {
      restaurantMarkerRef.current.remove()
      restaurantMarkerRef.current = null
    }
    if (customerMarkerRef.current) {
      customerMarkerRef.current.remove()
      customerMarkerRef.current = null
    }
    if (routeRef.current) {
      routeRef.current.remove()
      routeRef.current = null
    }

    // Create custom restaurant icon (orange)
    const restaurantIcon = L.divIcon({
      html: `<div style="background-color: #f97316; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.5 21h.5c.83 0 1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5h-.5"></path>
                <path d="M8.5 21h-.5c-.83 0-1.5-.67-1.5-1.5v-7c0-.83.67-1.5 1.5-1.5h.5"></path>
                <path d="M3 9V5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v4"></path>
                <path d="M12 21v-9"></path>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Create custom customer icon (green)
    const customerIcon = L.divIcon({
      html: `<div style="background-color: #10b981; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>`,
      className: '',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })

    // Add restaurant marker
    const restaurantMarker = L.marker([restaurantLat, restaurantLng], { icon: restaurantIcon }).addTo(map)
    restaurantMarkerRef.current = restaurantMarker

    // Add customer marker
    const customerMarker = L.marker([customerLat, customerLng], { icon: customerIcon }).addTo(map)
    customerMarkerRef.current = customerMarker

    // Add route if requested
    if (showRoute) {
      const fetchAndDisplayRoute = async () => {
        try {
          const route = await getRoute(
            restaurantLng,
            restaurantLat,
            customerLng,
            customerLat
          )

          if (route && mapRef.current) {
            // Create a polyline from the route coordinates
            const routeCoordinates = route.geometry.coordinates.map(
              (coord: [number, number]) => [coord[1], coord[0]] // Convert [lng, lat] to [lat, lng] for Leaflet
            )

            const polyline = L.polyline(routeCoordinates, {
              color: '#0077ff',
              weight: 5,
              opacity: 0.75
            }).addTo(mapRef.current)

            routeRef.current = polyline

            // Fit bounds to the polyline
            mapRef.current.fitBounds(polyline.getBounds(), { padding: [50, 50] })
          }
        } catch (error) {
          console.error("Error fetching route:", error)
          
          // Fallback to simple line if route fetching fails
          if (mapRef.current) {
            const polyline = L.polyline(
              [
                [restaurantLat, restaurantLng],
                [customerLat, customerLng]
              ],
              { color: '#0077ff', weight: 5, opacity: 0.75 }
            ).addTo(mapRef.current)
            
            routeRef.current = polyline
            
            // Fit bounds to the polyline
            mapRef.current.fitBounds(polyline.getBounds(), { padding: [50, 50] })
          }
        }
      }

      fetchAndDisplayRoute()
    } else {
      // If not showing route, fit bounds to include both markers
      const bounds = L.latLngBounds(
        [restaurantLat, restaurantLng],
        [customerLat, customerLng]
      )
      map.fitBounds(bounds, { padding: [50, 50] })
    }

  }, [mapLoaded, restaurantLat, restaurantLng, customerLat, customerLng, showRoute])

  return (
    <div
      ref={mapContainer}
      className={`rounded-md overflow-hidden ${className}`}
      style={{ height }}
    />
  )
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(OSMDeliveryMapSimple)
