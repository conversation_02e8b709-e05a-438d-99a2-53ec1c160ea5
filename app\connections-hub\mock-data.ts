import { Connection, UserProfile, Message, UserRole } from './types'

// Mock user profiles with different role capabilities
export const mockProfiles: UserProfile[] = [
  {
    id: '1',
    user_id: 'user-1',
    display_name: 'Pizza Palace',
    bio: 'Authentic Italian pizza since 1995. We specialize in wood-fired pizzas and fresh ingredients.',
    avatar_url: '/api/placeholder/64/64',
    role_capabilities: {
      can_be_customer: false,
      can_be_rider: false,
      owns_business: true
    },
    specialties: {
      cuisine_types: ['italian', 'pizza'],
      dietary_options: ['vegetarian', 'vegan', 'gluten-free'],
      peak_hours: ['12:00-14:00', '18:00-21:00']
    },
    average_rating: 4.8,
    total_ratings: 156,
    is_public: true,
    allow_direct_messages: true
  },
  {
    id: '2',
    user_id: 'user-2',
    display_name: '<PERSON>',
    bio: 'Reliable delivery rider with 3 years experience. Available evenings and weekends.',
    avatar_url: '/api/placeholder/64/64',
    role_capabilities: {
      can_be_customer: true,
      can_be_rider: true,
      owns_business: false
    },
    specialties: {
      vehicle_type: 'bicycle',
      areas: ['St. Helier', 'St. Saviour'],
      availability: ['weekday_evenings', 'weekends'],
      languages: ['english', 'french']
    },
    average_rating: 4.9,
    total_ratings: 234,
    is_public: true,
    allow_direct_messages: true
  },
  {
    id: '3',
    user_id: 'user-3',
    display_name: 'Sarah Chen',
    bio: 'Food enthusiast with dietary restrictions. Love trying new restaurants!',
    avatar_url: '/api/placeholder/64/64',
    role_capabilities: {
      can_be_customer: true,
      can_be_rider: false,
      owns_business: false
    },
    specialties: {
      dietary_restrictions: ['gluten-free', 'dairy-free'],
      favorite_cuisines: ['asian', 'mediterranean'],
      order_frequency: 'weekly'
    },
    average_rating: 4.7,
    total_ratings: 45,
    is_public: true,
    allow_direct_messages: true
  },
  {
    id: '4',
    user_id: 'user-4',
    display_name: 'Coffee Corner',
    bio: 'Premium coffee and light bites. Perfect for your morning caffeine fix.',
    avatar_url: '/api/placeholder/64/64',
    role_capabilities: {
      can_be_customer: false,
      can_be_rider: false,
      owns_business: true
    },
    specialties: {
      cuisine_types: ['coffee', 'breakfast', 'pastries'],
      opening_hours: '06:00-15:00',
      special_services: ['early_morning', 'office_catering']
    },
    average_rating: 4.6,
    total_ratings: 89,
    is_public: true,
    allow_direct_messages: true
  },
  {
    id: '5',
    user_id: 'user-5',
    display_name: 'Alex Rodriguez',
    bio: 'Part-time rider and full-time student. Quick and careful with deliveries.',
    avatar_url: '/api/placeholder/64/64',
    role_capabilities: {
      can_be_customer: true,
      can_be_rider: true,
      owns_business: false
    },
    specialties: {
      vehicle_type: 'motorbike',
      areas: ['St. Helier', 'St. Clement', 'Grouville'],
      availability: ['afternoons', 'evenings'],
      student_friendly: true
    },
    average_rating: 4.8,
    total_ratings: 127,
    is_public: true,
    allow_direct_messages: true
  }
]

// Mock connections showing different relationship types
export const mockConnections: Connection[] = [
  {
    id: 'conn-1',
    connection_type: 'customer-business',
    status: 'active',
    other_user_id: 'user-1',
    is_favorite: true,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:22:00Z',
    notes: 'Great pizza, always on time',
    other_user: mockProfiles[0]
  },
  {
    id: 'conn-2',
    connection_type: 'customer-rider',
    status: 'active',
    other_user_id: 'user-2',
    is_favorite: true,
    created_at: '2024-01-18T16:45:00Z',
    updated_at: '2024-01-25T09:15:00Z',
    notes: 'Very reliable, good communication',
    other_user: mockProfiles[1]
  },
  {
    id: 'conn-3',
    connection_type: 'customer-business',
    status: 'active',
    other_user_id: 'user-4',
    is_favorite: false,
    created_at: '2024-02-01T08:20:00Z',
    updated_at: '2024-02-01T08:20:00Z',
    notes: 'Good coffee for morning orders',
    other_user: mockProfiles[3]
  },
  {
    id: 'conn-4',
    connection_type: 'business-rider',
    status: 'pending',
    other_user_id: 'user-5',
    is_favorite: false,
    created_at: '2024-02-10T12:00:00Z',
    updated_at: '2024-02-10T12:00:00Z',
    notes: 'Interested in evening delivery coverage',
    other_user: mockProfiles[4]
  }
]

// Mock messages showing different channel types and roles
export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    sender_id: 'current-user',
    recipient_id: 'user-1',
    content: 'Hi! Can I get extra cheese on my margherita pizza? I have a dairy allergy so please use the vegan cheese.',
    channel_type: 'customer_enquiries',
    message_type: 'chat',
    thread_id: 'thread-1',
    is_read: true,
    is_urgent: false,
    created_at: '2024-02-15T12:30:00Z',
    sender_role: 'customer',
    recipient_role: 'business',
    sender_color: 'blue',
    recipient_color: 'green'
  },
  {
    id: 'msg-2',
    sender_id: 'user-1',
    recipient_id: 'current-user',
    content: 'Of course! We have excellent vegan cheese. That will be £2 extra. Your pizza will be ready in 15 minutes.',
    channel_type: 'customer_enquiries',
    message_type: 'response',
    thread_id: 'thread-1',
    is_read: true,
    is_urgent: false,
    created_at: '2024-02-15T12:32:00Z',
    sender_role: 'business',
    recipient_role: 'customer',
    sender_color: 'green',
    recipient_color: 'blue'
  },
  {
    id: 'msg-3',
    sender_id: 'user-2',
    recipient_id: 'current-user',
    content: 'Hi! I\'m on my way to deliver your pizza. Should be there in about 8 minutes. Is the address correct: 15 Queen Street?',
    channel_type: 'active_order_delivery',
    message_type: 'status_update',
    thread_id: 'thread-2',
    is_read: false,
    is_urgent: false,
    created_at: '2024-02-15T13:15:00Z',
    sender_role: 'rider',
    recipient_role: 'customer',
    sender_color: 'yellow',
    recipient_color: 'blue'
  }
]

// Helper function to get role-specific color
export const getRoleColor = (role: UserRole): string => {
  switch (role) {
    case 'customer': return 'blue'
    case 'business': return 'green'
    case 'rider': return 'yellow'
    default: return 'gray'
  }
}

// Helper function to get role-specific badge variant
export const getRoleBadgeVariant = (role: UserRole): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (role) {
    case 'customer': return 'default'
    case 'business': return 'secondary'
    case 'rider': return 'outline'
    default: return 'outline'
  }
}

// Helper function to determine user role in a connection
export const getUserRoleInConnection = (connectionType: string, isOtherUser: boolean = false): UserRole => {
  const [role1, role2] = connectionType.split('-') as [UserRole, UserRole]
  return isOtherUser ? role2 : role1
}

// Mock function to simulate API calls
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
