import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in order-items API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in order-items API")
      return { authorized: true };
    } else {
      return { 
        authorized: false, 
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in order-items API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in order-items API:", error)
      return { 
        authorized: false, 
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in order-items API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in order-items API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return { 
        authorized: false, 
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in order-items API")
      return { authorized: true };
    }
  }
}

// POST endpoint to create order items
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    
    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { error: 'Items array is required' },
        { status: 400 }
      );
    }

    console.log(`Creating ${body.items.length} order items`);

    // Insert the order items
    const { data, error } = await supabase
      .from('order_items')
      .insert(body.items)
      .select();

    if (error) {
      console.error('Error creating order items:', error);
      return NextResponse.json(
        { error: 'Failed to create order items', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      items: data
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch order items
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the URL parameters
    const url = new URL(request.url);
    const orderId = url.searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Fetch the order items
    const { data, error } = await supabase
      .from('order_items')
      .select('*')
      .eq('order_id', orderId);

    if (error) {
      console.error('Error fetching order items:', error);
      return NextResponse.json(
        { error: 'Failed to fetch order items', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      items: data || []
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
