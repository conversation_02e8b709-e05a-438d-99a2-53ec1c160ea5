import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// GET handler to fetch business details with managers, staff, and categories
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has admin permissions
    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Session found for user:", session.user.email);

    // Use the admin client to bypass RLS and check the user's role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("role, name")
      .eq("email", session.user.email)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      console.error("User profile not found for email:", session.user.email);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    console.log("Found user profile:", userProfile.name, "with role:", userProfile.role);

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", session.user.email, "with role:", userProfile.role);
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      );
    }

    console.log("Admin access verified for user:", session.user.email, "with role:", userProfile.role);

    const businessId = parseInt(params.id, 10);

    if (isNaN(businessId)) {
      return NextResponse.json(
        { error: `Invalid business ID: ${params.id}` },
        { status: 400 }
      );
    }

    // Fetch business details
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select("*")
      .eq("id", businessId)
      .single();

    if (businessError) {
      console.error("Error fetching business:", businessError);
      return NextResponse.json(
        { error: `Failed to fetch business: ${businessError.message}` },
        { status: 500 }
      );
    }

    if (!business) {
      return NextResponse.json(
        { error: `Business with ID ${businessId} not found` },
        { status: 404 }
      );
    }

    // Fetch business type
    let businessType = "Unknown";
    if (business.business_type_id) {
      const { data: typeData } = await adminClient
        .from("business_types")
        .select("name")
        .eq("id", business.business_type_id)
        .single();

      if (typeData) {
        businessType = typeData.name;
      }
    }

    // Fetch managers
    const { data: managersData, error: managersError } = await adminClient
      .from("business_managers")
      .select("id, user_id, is_primary")
      .eq("business_id", businessId);

    let managers = [];
    if (managersError) {
      console.error("Error fetching managers:", managersError);
    } else if (managersData && managersData.length > 0) {
      // Get user details for managers
      const userIds = managersData.map(manager => manager.user_id);
      const { data: usersData } = await adminClient
        .from("users")
        .select("id, name, email")
        .in("id", userIds);

      if (usersData) {
        // Create a map of user IDs to user details
        const userMap = new Map();
        usersData.forEach(user => {
          userMap.set(user.id, user);
        });

        // Transform managers data with user details
        managers = managersData.map(manager => {
          const user = userMap.get(manager.user_id) || { name: "Unknown", email: "<EMAIL>" };
          return {
            id: manager.id,
            user_id: manager.user_id,
            name: user.name,
            email: user.email,
            is_primary: manager.is_primary
          };
        });
      }
    }

    // Fetch staff
    const { data: staffData, error: staffError } = await adminClient
      .from("business_staff")
      .select("id, user_id, role, is_active")
      .eq("business_id", businessId);

    let staff = [];
    if (staffError) {
      console.error("Error fetching staff:", staffError);
    } else if (staffData && staffData.length > 0) {
      // Get user details for staff
      const userIds = staffData.map(staff => staff.user_id);
      const { data: usersData } = await adminClient
        .from("users")
        .select("id, name, email")
        .in("id", userIds);

      if (usersData) {
        // Create a map of user IDs to user details
        const userMap = new Map();
        usersData.forEach(user => {
          userMap.set(user.id, user);
        });

        // Transform staff data with user details
        staff = staffData.map(staffMember => {
          const user = userMap.get(staffMember.user_id) || { name: "Unknown", email: "<EMAIL>" };
          return {
            id: staffMember.id,
            user_id: staffMember.user_id,
            name: user.name,
            email: user.email,
            role: staffMember.role || "Staff",
            is_active: staffMember.is_active !== false
          };
        });
      }
    }

    // Fetch categories
    const { data: categoriesData, error: categoriesError } = await adminClient
      .from("business_categories")
      .select("category_id, is_primary")
      .eq("business_id", businessId);

    let categories = [];
    if (categoriesError) {
      console.error("Error fetching categories:", categoriesError);
    } else if (categoriesData && categoriesData.length > 0) {
      console.log("Found categories for business:", categoriesData);

      // Get category details
      const categoryIds = categoriesData.map(cat => cat.category_id);
      const { data: categoryDetailsData } = await adminClient
        .from("categories")
        .select("id, name, slug")
        .in("id", categoryIds);

      if (categoryDetailsData) {
        console.log("Category details:", categoryDetailsData);

        // Create a map of category IDs to category details
        const categoryMap = new Map();
        categoryDetailsData.forEach(category => {
          categoryMap.set(category.id, category);
        });

        // Transform categories data with category details
        categories = categoriesData.map(cat => {
          const category = categoryMap.get(cat.category_id) || { name: "Unknown", slug: "unknown" };
          return {
            id: cat.category_id, // Use category_id as the id
            name: category.name,
            slug: category.slug,
            is_primary: cat.is_primary
          };
        });
      }
    } else {
      console.log("No categories found for business ID:", businessId);
    }

    // Get product count
    const { count: productCount } = await adminClient
      .from("products")
      .select("id", { count: "exact", head: true })
      .eq("business_id", businessId);

    // Combine all data
    const businessDetails = {
      ...business,
      business_type: businessType,
      managers,
      staff,
      categories,
      product_count: productCount || 0
    };

    return NextResponse.json(businessDetails);
  } catch (error: any) {
    console.error("Error in GET /api/admin/businesses/[id]:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// PATCH handler to update business approval status
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has admin permissions
    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("Session found for user:", session.user.email);

    // Use the admin client to bypass RLS and check the user's role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("role, name")
      .eq("email", session.user.email)
      .single();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      console.error("User profile not found for email:", session.user.email);
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    console.log("Found user profile:", userProfile.name, "with role:", userProfile.role);

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", session.user.email, "with role:", userProfile.role);
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      );
    }

    console.log("Admin access verified for user:", session.user.email, "with role:", userProfile.role);

    const businessId = parseInt(params.id, 10);
    const requestBody = await request.json();
    const { is_approved } = requestBody;

    if (isNaN(businessId)) {
      return NextResponse.json(
        { error: `Invalid business ID: ${params.id}` },
        { status: 400 }
      );
    }

    // Update business approval status
    const { data, error } = await adminClient
      .from("businesses")
      .update({
        is_approved,
        updated_at: new Date().toISOString()
      })
      .eq("id", businessId)
      .select()
      .single();

    if (error) {
      console.error("Error updating business:", error);
      return NextResponse.json(
        { error: `Failed to update business: ${error.message}` },
        { status: 500 }
      );
    }

    // If the business is being approved, generate coordinates
    if (is_approved === true && data) {
      try {
        const { geocodeAndUpdateBusinessCoordinates } = await import('@/lib/address-utils');
        if (data.address) {
          const { success, coordinates } = await geocodeAndUpdateBusinessCoordinates(
            businessId.toString(),
            data.address,
            data.postcode
          );

          if (success) {
            console.log("Business coordinates updated successfully:", coordinates);
          } else {
            console.warn("Failed to update business coordinates, but business was approved");
          }
        }
      } catch (geocodeError) {
        console.error("Error geocoding business address:", geocodeError);
        // Continue anyway, as the business was approved
      }
    }

    return NextResponse.json({
      message: `Business ${is_approved ? "approved" : "rejected"} successfully`,
      data
    });
  } catch (error: any) {
    console.error("Error in PATCH /api/admin/businesses/[id]:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
