"use client"

import { useAuth } from "@/context/unified-auth-context"

export default function DebugAuthPage() {
  const { user, userProfile, isLoading, session, userRole } = useAuth()

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Auth Debug Page</h1>
        
        <div className="grid gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Auth State</h2>
            <div className="space-y-2">
              <p><strong>isLoading:</strong> {isLoading.toString()}</p>
              <p><strong>user:</strong> {user ? 'Present' : 'Null'}</p>
              <p><strong>user email:</strong> {user?.email || 'N/A'}</p>
              <p><strong>session:</strong> {session ? 'Present' : 'Null'}</p>
              <p><strong>userProfile:</strong> {userProfile ? 'Present' : 'Null'}</p>
              <p><strong>userRole:</strong> {userRole || 'N/A'}</p>
            </div>
          </div>

          {user && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">User Details</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}

          {userProfile && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">User Profile</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(userProfile, null, 2)}
              </pre>
            </div>
          )}

          {session && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Session Info</h2>
              <div className="space-y-2">
                <p><strong>Access Token:</strong> {session.access_token ? 'Present' : 'Missing'}</p>
                <p><strong>Refresh Token:</strong> {session.refresh_token ? 'Present' : 'Missing'}</p>
                <p><strong>Expires At:</strong> {session.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'N/A'}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
