'use client'

import { useState } from 'react'
import { OrderSession } from '@/types/customer-orders'
import { BusinessOrderCard } from './business-order-card'
import { customerOrdersService } from '@/services/customer-orders-service'
import { printSessionReceipt } from '@/utils/receipt-generator'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ChevronDown,
  ChevronUp,
  MapPin,
  Calendar,
  ShoppingBag,
  RotateCcw,
  Receipt,
  Phone,
  Clock
} from 'lucide-react'

interface OrderSessionCardProps {
  session: OrderSession
  onReorder: (sessionId: string) => void
  isReordering?: boolean
  searchQuery?: string
}

export function OrderSessionCard({ session, onReorder, isReordering = false, searchQuery = '' }: OrderSessionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Format session data
  const formatted = customerOrdersService.formatOrderSession(session)

  // Get session status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'all delivered':
      case 'delivered':
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'in progress':
      case 'preparing':
      case 'ready':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'pending':
      case 'confirmed':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'cancelled':
      case 'partially cancelled':
        return 'bg-red-50 text-red-700 border-red-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  return (
    <Card className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* Condensed Row Layout */}
      <div
        className="p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          {/* Left: Order Info */}
          <div className="flex-1 min-w-0">
            {/* Order Number & Date */}
            <div className="flex items-center gap-3 mb-2">
              <div className="text-sm font-medium text-gray-900">
                {session.business_orders.length === 1
                  ? `Order #${session.business_orders[0].order_number}`
                  : `Session #${session.session_id.slice(-8).toUpperCase()}`
                }
              </div>
              <div className="text-xs text-gray-500">
                {formatted.displayDate}
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                {formatted.displayTime}
              </div>
            </div>

            {/* Status & Business Count */}
            <div className="flex items-center gap-2 mb-1">
              <Badge
                variant="outline"
                className={`text-xs px-2 py-0.5 ${getStatusColor(formatted.statusSummary)}`}
              >
                {formatted.statusSummary}
              </Badge>
              <span className="text-xs text-gray-500">
                {session.business_count} business{session.business_count !== 1 ? 'es' : ''}
              </span>
            </div>
          </div>

          {/* Right: Amount & Expand */}
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                £{session.session_total.toFixed(2)}
              </div>
            </div>
            <ChevronDown
              className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              }`}
            />
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* Customer & Address Info */}
          <div className="p-4 space-y-4">
            {/* Customer Details */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Customer
                </div>
                <div className="text-sm text-gray-900">{session.customer_name}</div>
                {session.customer_phone && (
                  <div className="flex items-center gap-1 text-sm text-gray-600 mt-1">
                    <Phone className="h-3 w-3" />
                    {session.customer_phone}
                  </div>
                )}
              </div>

              <div>
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Order Date
                </div>
                <div className="text-sm text-gray-900">{formatted.displayDate}</div>
                <div className="text-sm text-gray-600">{formatted.displayTime}</div>
              </div>
            </div>

            {/* Address */}
            {session.customer_address && (
              <div>
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                  Delivery Address
                </div>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                  <div>
                    <div className="text-sm text-gray-900">{session.customer_address}</div>
                    {session.parish && session.postcode && (
                      <div className="text-sm text-gray-600">{session.parish}, {session.postcode}</div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onReorder(session.session_id)
                }}
                disabled={isReordering}
                className="text-xs"
              >
                <RotateCcw className={`h-3 w-3 mr-1 ${isReordering ? 'animate-spin' : ''}`} />
                {isReordering ? 'Reordering...' : 'Reorder'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  printSessionReceipt(session)
                }}
                className="text-xs"
              >
                <Receipt className="h-3 w-3 mr-1" />
                Receipt
              </Button>
            </div>
          </div>
        </div>
      )}

          {/* Business Orders */}
          {isExpanded && session.business_orders.length > 0 && (
            <div className="border-t border-gray-100">
              <div className="p-4">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
                  Business Orders ({session.business_count})
                </div>

                <div className="space-y-3">
                  {session.business_orders.map((order, index) => (
                    <BusinessOrderCard
                      key={`${order.order_id}-${index}`}
                      order={order}
                      session={session}
                      isLast={index === session.business_orders.length - 1}
                      searchQuery={searchQuery}
                    />
                  ))}
                </div>

                {/* Session Total */}
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">Total</span>
                    <span className="text-lg font-semibold text-gray-900">
                      £{session.session_total.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
    </Card>
  )
}
